<mxfile host="65bd71144e">
    <diagram id="component-interaction" name="Component Interaction">
        <mxGraphModel dx="654" dy="459" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="Frontend Components" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="40" y="40" width="320" height="400" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="Pages" style="swimlane;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="2" vertex="1">
                    <mxGeometry x="20" y="40" width="280" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="DoiSoatPage" style="rounded=1;whiteSpace=wrap;html=1;" parent="3" vertex="1">
                    <mxGeometry x="20" y="30" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="ReportsPage" style="rounded=1;whiteSpace=wrap;html=1;" parent="3" vertex="1">
                    <mxGeometry x="20" y="70" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="Components" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="2" vertex="1">
                    <mxGeometry x="20" y="180" width="280" height="200" as="geometry">
                        <mxRectangle x="20" y="180" width="110" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="7" value="FileUpload" style="rounded=1;whiteSpace=wrap;html=1;" parent="6" vertex="1">
                    <mxGeometry x="20" y="30" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="DataTable" style="rounded=1;whiteSpace=wrap;html=1;" parent="6" vertex="1">
                    <mxGeometry x="20" y="70" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="ReportViewer" style="rounded=1;whiteSpace=wrap;html=1;" parent="6" vertex="1">
                    <mxGeometry x="20" y="110" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="FilterPanel" style="rounded=1;whiteSpace=wrap;html=1;" parent="6" vertex="1">
                    <mxGeometry x="20" y="150" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="Backend Services" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="400" y="40" width="320" height="400" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="API Endpoints" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="11" vertex="1">
                    <mxGeometry x="20" y="40" width="280" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="POST /api/v1/doi-soat/upload" style="rounded=1;whiteSpace=wrap;html=1;" parent="12" vertex="1">
                    <mxGeometry x="20" y="30" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="GET /api/v1/doi-soat/{id}" style="rounded=1;whiteSpace=wrap;html=1;" parent="12" vertex="1">
                    <mxGeometry x="20" y="70" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="GET /api/v1/reports/summary" style="rounded=1;whiteSpace=wrap;html=1;" parent="12" vertex="1">
                    <mxGeometry x="20" y="110" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="Services" style="swimlane;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="11" vertex="1">
                    <mxGeometry x="20" y="220" width="280" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="FileProcessingService" style="rounded=1;whiteSpace=wrap;html=1;" parent="16" vertex="1">
                    <mxGeometry x="20" y="30" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="DataValidationService" style="rounded=1;whiteSpace=wrap;html=1;" parent="16" vertex="1">
                    <mxGeometry x="20" y="70" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="ReportGenerationService" style="rounded=1;whiteSpace=wrap;html=1;" parent="16" vertex="1">
                    <mxGeometry x="20" y="110" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="Database" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="760" y="40" width="320" height="400" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="Models" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="20" vertex="1">
                    <mxGeometry x="20" y="40" width="280" height="340" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="DoiSoatCuoc" style="rounded=1;whiteSpace=wrap;html=1;" parent="21" vertex="1">
                    <mxGeometry x="20" y="30" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="DauSoDichVuCuoc" style="rounded=1;whiteSpace=wrap;html=1;" parent="21" vertex="1">
                    <mxGeometry x="20" y="70" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="TongKetCuoc" style="rounded=1;whiteSpace=wrap;html=1;" parent="21" vertex="1">
                    <mxGeometry x="20" y="110" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="DoiSoatCoDinh" style="rounded=1;whiteSpace=wrap;html=1;" parent="21" vertex="1">
                    <mxGeometry x="20" y="150" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="CuocGoi" style="rounded=1;whiteSpace=wrap;html=1;" parent="21" vertex="1">
                    <mxGeometry x="20" y="190" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="HTTP/REST" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="2" target="11" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="370" y="240" as="sourcePoint"/>
                        <mxPoint x="420" y="190" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="28" value="SQL" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="11" target="20" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="730" y="240" as="sourcePoint"/>
                        <mxPoint x="780" y="190" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="29" value="Uses" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="3" target="6" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="200" y="240" as="sourcePoint"/>
                        <mxPoint x="250" y="190" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="30" value="Calls" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="12" target="16" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="560" y="240" as="sourcePoint"/>
                        <mxPoint x="610" y="190" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>