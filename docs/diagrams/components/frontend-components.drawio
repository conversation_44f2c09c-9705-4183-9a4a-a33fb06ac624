<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-04-02T10:00:00.000Z">
<diagram id="fec_frontend_components" name="Frontend Components">
<mxGraphModel>
<root>
<mxCell id="fec_0"/>
<mxCell id="fec_1" parent="fec_0"/>

<!-- App Root -->
<mxCell id="fec_app_root" value="App Root" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;" vertex="1" parent="fec_1">
<mxGeometry x="360" y="40" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Layout Components -->
<mxCell id="fec_layout" value="Layout" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="fec_1">
<mxGeometry x="360" y="140" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Navbar -->
<mxCell id="fec_navbar" value="Navbar" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="fec_1">
<mxGeometry x="200" y="240" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Sidebar -->
<mxCell id="fec_sidebar" value="Sidebar" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="fec_1">
<mxGeometry x="360" y="240" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Footer -->
<mxCell id="fec_footer" value="Footer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="fec_1">
<mxGeometry x="520" y="240" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Pages -->
<mxCell id="fec_pages" value="Pages" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="fec_1">
<mxGeometry x="360" y="340" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Dashboard -->
<mxCell id="fec_dashboard" value="Dashboard" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="fec_1">
<mxGeometry x="200" y="440" width="120" height="60" as="geometry"/>
</mxCell>

<!-- DoiSoat -->
<mxCell id="fec_doisoat" value="DoiSoat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="fec_1">
<mxGeometry x="360" y="440" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Reports -->
<mxCell id="fec_reports" value="Reports" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="fec_1">
<mxGeometry x="520" y="440" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Shared Components -->
<mxCell id="fec_shared" value="Shared Components" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;" vertex="1" parent="fec_1">
<mxGeometry x="360" y="540" width="120" height="60" as="geometry"/>
</mxCell>

<!-- UI Components -->
<mxCell id="fec_ui" value="UI Components" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;" vertex="1" parent="fec_1">
<mxGeometry x="200" y="640" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Form Components -->
<mxCell id="fec_form" value="Form Components" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;" vertex="1" parent="fec_1">
<mxGeometry x="360" y="640" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Data Components -->
<mxCell id="fec_data" value="Data Components" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;" vertex="1" parent="fec_1">
<mxGeometry x="520" y="640" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Connections -->
<mxCell id="fec_app_to_layout" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fec_1" source="fec_app_root" target="fec_layout">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fec_layout_to_navbar" value="" style="endArrow=classic;html=1;exitX=0.25;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fec_1" source="fec_layout" target="fec_navbar">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fec_layout_to_sidebar" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fec_1" source="fec_layout" target="fec_sidebar">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fec_layout_to_footer" value="" style="endArrow=classic;html=1;exitX=0.75;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fec_1" source="fec_layout" target="fec_footer">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fec_layout_to_pages" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fec_1" source="fec_sidebar" target="fec_pages">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fec_pages_to_dashboard" value="" style="endArrow=classic;html=1;exitX=0.25;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fec_1" source="fec_pages" target="fec_dashboard">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fec_pages_to_doisoat" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fec_1" source="fec_pages" target="fec_doisoat">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fec_pages_to_reports" value="" style="endArrow=classic;html=1;exitX=0.75;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fec_1" source="fec_pages" target="fec_reports">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fec_pages_to_shared" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fec_1" source="fec_doisoat" target="fec_shared">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fec_shared_to_ui" value="" style="endArrow=classic;html=1;exitX=0.25;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fec_1" source="fec_shared" target="fec_ui">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fec_shared_to_form" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fec_1" source="fec_shared" target="fec_form">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fec_shared_to_data" value="" style="endArrow=classic;html=1;exitX=0.75;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fec_1" source="fec_shared" target="fec_data">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<!-- Notes -->
<mxCell id="fec_note_1" value="UI Components:&#10;- Button&#10;- Input&#10;- Table&#10;- Modal&#10;- Card&#10;- Alert" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="fec_1">
<mxGeometry x="40" y="630" width="120" height="120" as="geometry"/>
</mxCell>

<mxCell id="fec_note_2" value="Form Components:&#10;- FileUpload&#10;- DatePicker&#10;- Select&#10;- Checkbox&#10;- Radio" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="fec_1">
<mxGeometry x="360" y="730" width="120" height="100" as="geometry"/>
</mxCell>

<mxCell id="fec_note_3" value="Data Components:&#10;- DataTable&#10;- Chart&#10;- Summary&#10;- Statistics" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="fec_1">
<mxGeometry x="680" y="630" width="120" height="100" as="geometry"/>
</mxCell>

</root>
</mxGraphModel>
</diagram>
</mxfile> 