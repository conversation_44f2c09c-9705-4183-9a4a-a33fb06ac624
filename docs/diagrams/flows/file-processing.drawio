<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="65bd71144e">
    <diagram id="file-processing" name="File Processing Flow">
        <mxGraphModel dx="1386" dy="784" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                
                <!-- Start -->
                <mxCell id="2" value="Start" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
                    <mxGeometry x="360" y="40" width="120" height="80" as="geometry"/>
                </mxCell>
                
                <!-- Upload File -->
                <mxCell id="3" value="Upload File" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
                    <mxGeometry x="360" y="160" width="120" height="60" as="geometry"/>
                </mxCell>
                
                <!-- Validate File -->
                <mxCell id="4" value="Validate File" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
                    <mxGeometry x="360" y="260" width="120" height="120" as="geometry"/>
                </mxCell>
                
                <!-- Invalid File -->
                <mxCell id="5" value="Return Error" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
                    <mxGeometry x="560" y="290" width="120" height="60" as="geometry"/>
                </mxCell>
                
                <!-- Determine File Type -->
                <mxCell id="6" value="Determine&#10;File Type" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
                    <mxGeometry x="360" y="420" width="120" height="120" as="geometry"/>
                </mxCell>
                
                <!-- DSC Processing -->
                <mxCell id="7" value="DSC Processing" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
                    <mxGeometry x="40" y="580" width="200" height="200" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="Extract Month/Year" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="7">
                    <mxGeometry x="20" y="40" width="160" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="Extract Partners" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="7">
                    <mxGeometry x="20" y="90" width="160" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="Process DSC Data" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="7">
                    <mxGeometry x="20" y="140" width="160" height="30" as="geometry"/>
                </mxCell>
                
                <!-- DSCD Processing -->
                <mxCell id="11" value="DSCD Processing" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
                    <mxGeometry x="320" y="580" width="200" height="200" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="Extract Month/Year" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="11">
                    <mxGeometry x="20" y="40" width="160" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="Extract Partner Name" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="11">
                    <mxGeometry x="20" y="90" width="160" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="Process DSCD Data" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="11">
                    <mxGeometry x="20" y="140" width="160" height="30" as="geometry"/>
                </mxCell>
                
                <!-- DST Processing -->
                <mxCell id="15" value="DST Processing" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
                    <mxGeometry x="600" y="580" width="200" height="200" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="Extract Month/Year" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="15">
                    <mxGeometry x="20" y="40" width="160" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="Process 1800/1900 Data" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="15">
                    <mxGeometry x="20" y="90" width="160" height="30" as="geometry"/>
                </mxCell>
                
                <!-- Save to Database -->
                <mxCell id="18" value="Save to Database" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
                    <mxGeometry x="360" y="820" width="120" height="60" as="geometry"/>
                </mxCell>
                
                <!-- End -->
                <mxCell id="19" value="End" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
                    <mxGeometry x="360" y="920" width="120" height="80" as="geometry"/>
                </mxCell>
                
                <!-- Connections -->
                <mxCell id="20" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="2" target="3">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="450" as="sourcePoint"/>
                        <mxPoint x="440" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="21" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="4">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="450" as="sourcePoint"/>
                        <mxPoint x="440" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="22" value="Invalid" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="5">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="450" as="sourcePoint"/>
                        <mxPoint x="440" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="23" value="Valid" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="6">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="450" as="sourcePoint"/>
                        <mxPoint x="440" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="24" value="DSC" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="6" target="7">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="450" as="sourcePoint"/>
                        <mxPoint x="440" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="25" value="DSCD" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="6" target="11">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="450" as="sourcePoint"/>
                        <mxPoint x="440" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="26" value="DST" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="6" target="15">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="450" as="sourcePoint"/>
                        <mxPoint x="440" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="27" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="7" target="18">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="850" as="sourcePoint"/>
                        <mxPoint x="440" y="800" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="28" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="11" target="18">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="850" as="sourcePoint"/>
                        <mxPoint x="440" y="800" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="29" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="15" target="18">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="850" as="sourcePoint"/>
                        <mxPoint x="440" y="800" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="30" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="18" target="19">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="390" y="850" as="sourcePoint"/>
                        <mxPoint x="440" y="800" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile> 