<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-04-02T10:00:00.000Z">
<diagram id="dsp_dsc_processing" name="DSC Processing">
<mxGraphModel>
<root>
<mxCell id="dsp_0"/>
<mxCell id="dsp_1" parent="dsp_0"/>

<!-- Start -->
<mxCell id="dsp_start" value="Start DSC Processing" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;" vertex="1" parent="dsp_1">
<mxGeometry x="360" y="40" width="120" height="80" as="geometry"/>
</mxCell>

<!-- Extract Month/Year -->
<mxCell id="dsp_extract_date" value="Extract Month/Year" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="dsp_1">
<mxGeometry x="360" y="160" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Extract Partners -->
<mxCell id="dsp_extract_partners" value="Extract Partners" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="dsp_1">
<mxGeometry x="360" y="260" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Validate Basic Info -->
<mxCell id="dsp_validate_basic" value="Validate&#10;Basic Info" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="dsp_1">
<mxGeometry x="360" y="360" width="120" height="120" as="geometry"/>
</mxCell>

<!-- Invalid Basic Information -->
<mxCell id="dsp_invalid_basic" value="Invalid Basic&#10;Information" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;" vertex="1" parent="dsp_1">
<mxGeometry x="560" y="390" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Process Sheet Structure -->
<mxCell id="dsp_process_sheet" value="Process Sheet&#10;Structure" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="dsp_1">
<mxGeometry x="360" y="520" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Extract Service Numbers -->
<mxCell id="dsp_extract_numbers" value="Extract Service&#10;Numbers" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="dsp_1">
<mxGeometry x="360" y="620" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Process Volume and Amount -->
<mxCell id="dsp_process_volume" value="Process Volume&#10;and Amount" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="dsp_1">
<mxGeometry x="360" y="720" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Calculate Total -->
<mxCell id="dsp_calculate_total" value="Calculate Total" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="dsp_1">
<mxGeometry x="360" y="820" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Validate Results -->
<mxCell id="dsp_validate_results" value="Validate&#10;Results" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="dsp_1">
<mxGeometry x="360" y="920" width="120" height="120" as="geometry"/>
</mxCell>

<!-- Processing Error -->
<mxCell id="dsp_processing_error" value="Processing&#10;Error" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;" vertex="1" parent="dsp_1">
<mxGeometry x="560" y="950" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Save to Database -->
<mxCell id="dsp_save" value="Save to Database" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="dsp_1">
<mxGeometry x="360" y="1080" width="120" height="60" as="geometry"/>
</mxCell>

<!-- End -->
<mxCell id="dsp_end" value="End" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;" vertex="1" parent="dsp_1">
<mxGeometry x="360" y="1180" width="120" height="80" as="geometry"/>
</mxCell>

<!-- Connections -->
<mxCell id="dsp_start_to_extract_date" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dsp_1" source="dsp_start" target="dsp_extract_date">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="dsp_extract_date_to_partners" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dsp_1" source="dsp_extract_date" target="dsp_extract_partners">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="dsp_partners_to_validate" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dsp_1" source="dsp_extract_partners" target="dsp_validate_basic">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="dsp_validate_to_invalid" value="Invalid" style="endArrow=classic;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="dsp_1" source="dsp_validate_basic" target="dsp_invalid_basic">
<mxGeometry x="-0.3333" y="10" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="dsp_validate_to_process" value="Valid" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dsp_1" source="dsp_validate_basic" target="dsp_process_sheet">
<mxGeometry x="-0.2" y="20" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="dsp_process_to_extract" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dsp_1" source="dsp_process_sheet" target="dsp_extract_numbers">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="dsp_extract_to_volume" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dsp_1" source="dsp_extract_numbers" target="dsp_process_volume">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="dsp_volume_to_total" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dsp_1" source="dsp_process_volume" target="dsp_calculate_total">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="dsp_total_to_validate" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dsp_1" source="dsp_calculate_total" target="dsp_validate_results">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="dsp_validate_to_error" value="Invalid" style="endArrow=classic;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="dsp_1" source="dsp_validate_results" target="dsp_processing_error">
<mxGeometry x="-0.3333" y="10" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="dsp_validate_to_save" value="Valid" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dsp_1" source="dsp_validate_results" target="dsp_save">
<mxGeometry x="-0.2" y="20" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="dsp_save_to_end" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dsp_1" source="dsp_save" target="dsp_end">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<!-- Notes -->
<mxCell id="dsp_note_1" value="Validate:&#10;- Month/Year format&#10;- Partner codes&#10;- Required fields" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="dsp_1">
<mxGeometry x="160" y="380" width="160" height="80" as="geometry"/>
</mxCell>

<mxCell id="dsp_note_2" value="Process:&#10;- VNM data&#10;- Viettel data&#10;- VNPT data" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="dsp_1">
<mxGeometry x="160" y="710" width="160" height="80" as="geometry"/>
</mxCell>

</root>
</mxGraphModel>
</diagram>
</mxfile> 