<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-04-02T10:00:00.000Z">
<diagram id="api_integration_flow" name="API Integration Flow">
<mxGraphModel>
<root>
<mxCell id="api_0"/>
<mxCell id="api_1" parent="api_0"/>

<!-- Frontend -->
<mxCell id="api_frontend" value="Frontend&#10;(React/TypeScript)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;" vertex="1" parent="api_1">
<mxGeometry x="360" y="40" width="120" height="60" as="geometry"/>
</mxCell>

<!-- API Request -->
<mxCell id="api_request" value="API Request" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="api_1">
<mxGeometry x="360" y="140" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Validate Request -->
<mxCell id="api_validate_request" value="Validate Request" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="api_1">
<mxGeometry x="360" y="240" width="120" height="120" as="geometry"/>
</mxCell>

<!-- Invalid Request -->
<mxCell id="api_invalid_request" value="Invalid Request" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;" vertex="1" parent="api_1">
<mxGeometry x="560" y="270" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Process Request -->
<mxCell id="api_process_request" value="Process Request" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="api_1">
<mxGeometry x="360" y="400" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Database Operation -->
<mxCell id="api_database" value="Database&#10;Operation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;" vertex="1" parent="api_1">
<mxGeometry x="360" y="500" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Validate Response -->
<mxCell id="api_validate_response" value="Validate Response" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="api_1">
<mxGeometry x="360" y="600" width="120" height="120" as="geometry"/>
</mxCell>

<!-- Error Response -->
<mxCell id="api_error_response" value="Error Response" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;" vertex="1" parent="api_1">
<mxGeometry x="560" y="630" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Success Response -->
<mxCell id="api_success_response" value="Success Response" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="api_1">
<mxGeometry x="360" y="760" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Update UI -->
<mxCell id="api_update_ui" value="Update UI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;" vertex="1" parent="api_1">
<mxGeometry x="360" y="860" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Connections -->
<mxCell id="api_frontend_to_request" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="api_1" source="api_frontend" target="api_request">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="api_request_to_validate" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="api_1" source="api_request" target="api_validate_request">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="api_validate_to_invalid" value="Invalid" style="endArrow=classic;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="api_1" source="api_validate_request" target="api_invalid_request">
<mxGeometry x="-0.3333" y="10" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="api_validate_to_process" value="Valid" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="api_1" source="api_validate_request" target="api_process_request">
<mxGeometry x="-0.2" y="20" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="api_process_to_database" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="api_1" source="api_process_request" target="api_database">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="api_database_to_validate" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="api_1" source="api_database" target="api_validate_response">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="api_validate_to_error" value="Error" style="endArrow=classic;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="api_1" source="api_validate_response" target="api_error_response">
<mxGeometry x="-0.3333" y="10" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="api_validate_to_success" value="Success" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="api_1" source="api_validate_response" target="api_success_response">
<mxGeometry x="-0.2" y="20" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="api_success_to_ui" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="api_1" source="api_success_response" target="api_update_ui">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<!-- Notes -->
<mxCell id="api_note_1" value="Request Types:&#10;- GET /api/v1/...&#10;- POST /api/v1/...&#10;- PUT /api/v1/...&#10;- DELETE /api/v1/..." style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="api_1">
<mxGeometry x="160" y="130" width="160" height="80" as="geometry"/>
</mxCell>

<mxCell id="api_note_2" value="Validation:&#10;- Request format&#10;- Required fields&#10;- Data types&#10;- Business rules" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="api_1">
<mxGeometry x="160" y="260" width="160" height="80" as="geometry"/>
</mxCell>

<mxCell id="api_note_3" value="Database:&#10;- CRUD operations&#10;- Transactions&#10;- Constraints" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="api_1">
<mxGeometry x="160" y="490" width="160" height="80" as="geometry"/>
</mxCell>

</root>
</mxGraphModel>
</diagram>
</mxfile> 