<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-04-02T10:00:00.000Z">
<diagram id="fc_file_classification" name="File Classification">
<mxGraphModel>
<root>
<mxCell id="fc_0"/>
<mxCell id="fc_1" parent="fc_0"/>

<!-- Start -->
<mxCell id="fc_start" value="Start" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;" vertex="1" parent="fc_1">
<mxGeometry x="360" y="40" width="120" height="80" as="geometry"/>
</mxCell>

<!-- Upload File -->
<mxCell id="fc_upload" value="Upload File" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="fc_1">
<mxGeometry x="360" y="160" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Validate File -->
<mxCell id="fc_validate" value="Validate File" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="fc_1">
<mxGeometry x="360" y="260" width="120" height="120" as="geometry"/>
</mxCell>

<!-- Return Error -->
<mxCell id="fc_error" value="Return Error" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;" vertex="1" parent="fc_1">
<mxGeometry x="560" y="290" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Determine File Type -->
<mxCell id="fc_determine_type" value="Determine&#10;File Type" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="fc_1">
<mxGeometry x="360" y="420" width="120" height="120" as="geometry"/>
</mxCell>

<!-- DSC Processing -->
<mxCell id="fc_dsc_process" value="DSC Processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="fc_1">
<mxGeometry x="160" y="560" width="120" height="60" as="geometry"/>
</mxCell>

<!-- DSCD Processing -->
<mxCell id="fc_dscd_process" value="DSCD Processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="fc_1">
<mxGeometry x="360" y="560" width="120" height="60" as="geometry"/>
</mxCell>

<!-- DST Processing -->
<mxCell id="fc_dst_process" value="DST Processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="fc_1">
<mxGeometry x="560" y="560" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Save to Database -->
<mxCell id="fc_save" value="Save to Database" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="fc_1">
<mxGeometry x="360" y="680" width="120" height="60" as="geometry"/>
</mxCell>

<!-- End -->
<mxCell id="fc_end" value="End" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;" vertex="1" parent="fc_1">
<mxGeometry x="360" y="780" width="120" height="80" as="geometry"/>
</mxCell>

<!-- Connections -->
<mxCell id="fc_start_to_upload" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fc_1" source="fc_start" target="fc_upload">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fc_upload_to_validate" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fc_1" source="fc_upload" target="fc_validate">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fc_validate_to_error" value="Invalid" style="endArrow=classic;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="fc_1" source="fc_validate" target="fc_error">
<mxGeometry x="-0.3333" y="10" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="fc_validate_to_determine" value="Valid" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fc_1" source="fc_validate" target="fc_determine_type">
<mxGeometry x="-0.2" y="20" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="fc_determine_to_dsc" value="DSC" style="endArrow=classic;html=1;exitX=0;exitY=0.5;entryX=0.5;entryY=0;" edge="1" parent="fc_1" source="fc_determine_type" target="fc_dsc_process">
<mxGeometry x="-0.3333" y="-10" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="fc_determine_to_dscd" value="DSCD" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fc_1" source="fc_determine_type" target="fc_dscd_process">
<mxGeometry x="-0.2" y="20" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="fc_determine_to_dst" value="DST" style="endArrow=classic;html=1;exitX=1;exitY=0.5;entryX=0.5;entryY=0;" edge="1" parent="fc_1" source="fc_determine_type" target="fc_dst_process">
<mxGeometry x="-0.3333" y="10" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="fc_dsc_to_save" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0;entryY=0.5;" edge="1" parent="fc_1" source="fc_dsc_process" target="fc_save">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fc_dscd_to_save" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fc_1" source="fc_dscd_process" target="fc_save">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fc_dst_to_save" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=1;entryY=0.5;" edge="1" parent="fc_1" source="fc_dst_process" target="fc_save">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="fc_save_to_end" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="fc_1" source="fc_save" target="fc_end">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

</root>
</mxGraphModel>
</diagram>
</mxfile> 