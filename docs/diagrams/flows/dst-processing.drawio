<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-04-02T10:00:00.000Z">
<diagram id="dtp_dst_processing" name="DST Processing">
<mxGraphModel>
<root>
<mxCell id="dtp_0"/>
<mxCell id="dtp_1" parent="dtp_0"/>

<!-- Start -->
<mxCell id="dtp_start" value="Start DST Processing" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;" vertex="1" parent="dtp_1">
<mxGeometry x="360" y="40" width="120" height="80" as="geometry"/>
</mxCell>

<!-- Process 1800 Data -->
<mxCell id="dtp_process_1800" value="Process 1800 Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="dtp_1">
<mxGeometry x="360" y="160" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Process 1900 Data -->
<mxCell id="dtp_process_1900" value="Process 1900 Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="dtp_1">
<mxGeometry x="360" y="260" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Calculate Revenue -->
<mxCell id="dtp_calculate_revenue" value="Calculate Revenue" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="dtp_1">
<mxGeometry x="360" y="360" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Apply Pricing Rules -->
<mxCell id="dtp_apply_pricing" value="Apply Pricing Rules" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="dtp_1">
<mxGeometry x="360" y="460" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Validate Results -->
<mxCell id="dtp_validate_results" value="Validate&#10;Results" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="dtp_1">
<mxGeometry x="360" y="560" width="120" height="120" as="geometry"/>
</mxCell>

<!-- Processing Error -->
<mxCell id="dtp_processing_error" value="Processing&#10;Error" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;" vertex="1" parent="dtp_1">
<mxGeometry x="560" y="590" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Save to Database -->
<mxCell id="dtp_save" value="Save to Database" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="dtp_1">
<mxGeometry x="360" y="720" width="120" height="60" as="geometry"/>
</mxCell>

<!-- End -->
<mxCell id="dtp_end" value="End" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;" vertex="1" parent="dtp_1">
<mxGeometry x="360" y="820" width="120" height="80" as="geometry"/>
</mxCell>

<!-- Connections -->
<mxCell id="dtp_start_to_1800" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dtp_1" source="dtp_start" target="dtp_process_1800">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="dtp_1800_to_1900" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dtp_1" source="dtp_process_1800" target="dtp_process_1900">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="dtp_1900_to_revenue" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dtp_1" source="dtp_process_1900" target="dtp_calculate_revenue">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="dtp_revenue_to_pricing" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dtp_1" source="dtp_calculate_revenue" target="dtp_apply_pricing">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="dtp_pricing_to_validate" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dtp_1" source="dtp_apply_pricing" target="dtp_validate_results">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="dtp_validate_to_error" value="Invalid" style="endArrow=classic;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="dtp_1" source="dtp_validate_results" target="dtp_processing_error">
<mxGeometry x="-0.3333" y="10" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="dtp_validate_to_save" value="Valid" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dtp_1" source="dtp_validate_results" target="dtp_save">
<mxGeometry x="-0.2" y="20" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="dtp_save_to_end" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="dtp_1" source="dtp_save" target="dtp_end">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<!-- Notes -->
<mxCell id="dtp_note_1" value="1800 Data:&#10;- Call volume&#10;- Duration&#10;- Service type" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="dtp_1">
<mxGeometry x="160" y="150" width="160" height="80" as="geometry"/>
</mxCell>

<mxCell id="dtp_note_2" value="1900 Data:&#10;- Call volume&#10;- Duration&#10;- Service type" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="dtp_1">
<mxGeometry x="160" y="250" width="160" height="80" as="geometry"/>
</mxCell>

<mxCell id="dtp_note_3" value="Pricing Rules:&#10;- Different rates&#10;  per service&#10;- Time blocks" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="dtp_1">
<mxGeometry x="160" y="450" width="160" height="80" as="geometry"/>
</mxCell>

</root>
</mxGraphModel>
</diagram>
</mxfile> 