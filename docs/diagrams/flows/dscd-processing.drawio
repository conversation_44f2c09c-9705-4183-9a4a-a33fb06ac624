<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-04-02T10:00:00.000Z">
<diagram id="cdp_dscd_processing" name="DSCD Processing">
<mxGraphModel>
<root>
<mxCell id="cdp_0"/>
<mxCell id="cdp_1" parent="cdp_0"/>

<!-- Start -->
<mxCell id="cdp_start" value="Start DSCD Processing" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;" vertex="1" parent="cdp_1">
<mxGeometry x="360" y="40" width="120" height="80" as="geometry"/>
</mxCell>

<!-- Extract Month/Year -->
<mxCell id="cdp_extract_date" value="Extract Month/Year" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="cdp_1">
<mxGeometry x="360" y="160" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Extract Partner Name -->
<mxCell id="cdp_extract_partner" value="Extract Partner Name" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="cdp_1">
<mxGeometry x="360" y="260" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Validate Basic Info -->
<mxCell id="cdp_validate_basic" value="Validate&#10;Basic Info" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="cdp_1">
<mxGeometry x="360" y="360" width="120" height="120" as="geometry"/>
</mxCell>

<!-- Invalid Basic Information -->
<mxCell id="cdp_invalid_basic" value="Invalid Basic&#10;Information" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;" vertex="1" parent="cdp_1">
<mxGeometry x="560" y="390" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Process Sheet Structure -->
<mxCell id="cdp_process_sheet" value="Process Sheet&#10;Structure" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="cdp_1">
<mxGeometry x="360" y="520" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Extract Call Types -->
<mxCell id="cdp_extract_call_types" value="Extract Call Types" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="cdp_1">
<mxGeometry x="360" y="620" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Process Each Call Type -->
<mxCell id="cdp_process_call_types" value="Process Each&#10;Call Type" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="cdp_1">
<mxGeometry x="360" y="720" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Calculate Pricing -->
<mxCell id="cdp_calculate_pricing" value="Calculate Pricing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="cdp_1">
<mxGeometry x="360" y="820" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Validate Results -->
<mxCell id="cdp_validate_results" value="Validate&#10;Results" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" vertex="1" parent="cdp_1">
<mxGeometry x="360" y="920" width="120" height="120" as="geometry"/>
</mxCell>

<!-- Processing Error -->
<mxCell id="cdp_processing_error" value="Processing&#10;Error" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;" vertex="1" parent="cdp_1">
<mxGeometry x="560" y="950" width="120" height="60" as="geometry"/>
</mxCell>

<!-- Save to Database -->
<mxCell id="cdp_save" value="Save to Database" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" vertex="1" parent="cdp_1">
<mxGeometry x="360" y="1080" width="120" height="60" as="geometry"/>
</mxCell>

<!-- End -->
<mxCell id="cdp_end" value="End" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;" vertex="1" parent="cdp_1">
<mxGeometry x="360" y="1180" width="120" height="80" as="geometry"/>
</mxCell>

<!-- Connections -->
<mxCell id="cdp_start_to_extract_date" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="cdp_1" source="cdp_start" target="cdp_extract_date">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="cdp_extract_date_to_partner" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="cdp_1" source="cdp_extract_date" target="cdp_extract_partner">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="cdp_partner_to_validate" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="cdp_1" source="cdp_extract_partner" target="cdp_validate_basic">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="cdp_validate_to_invalid" value="Invalid" style="endArrow=classic;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="cdp_1" source="cdp_validate_basic" target="cdp_invalid_basic">
<mxGeometry x="-0.3333" y="10" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="cdp_validate_to_process" value="Valid" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="cdp_1" source="cdp_validate_basic" target="cdp_process_sheet">
<mxGeometry x="-0.2" y="20" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="cdp_process_to_extract" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="cdp_1" source="cdp_process_sheet" target="cdp_extract_call_types">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="cdp_extract_to_process" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="cdp_1" source="cdp_extract_call_types" target="cdp_process_call_types">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="cdp_process_to_calculate" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="cdp_1" source="cdp_process_call_types" target="cdp_calculate_pricing">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="cdp_calculate_to_validate" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="cdp_1" source="cdp_calculate_pricing" target="cdp_validate_results">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="cdp_validate_to_error" value="Invalid" style="endArrow=classic;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="cdp_1" source="cdp_validate_results" target="cdp_processing_error">
<mxGeometry x="-0.3333" y="10" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="cdp_validate_to_save" value="Valid" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="cdp_1" source="cdp_validate_results" target="cdp_save">
<mxGeometry x="-0.2" y="20" relative="1" as="geometry">
<mxPoint as="offset"/>
</mxGeometry>
</mxCell>

<mxCell id="cdp_save_to_end" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="cdp_1" source="cdp_save" target="cdp_end">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<!-- Notes -->
<mxCell id="cdp_note_1" value="Call Types:&#10;- Cố định nội hạt&#10;- Cố định liên tỉnh&#10;- Di động&#10;- Quốc tế" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="cdp_1">
<mxGeometry x="160" y="710" width="160" height="80" as="geometry"/>
</mxCell>

<mxCell id="cdp_note_2" value="Pricing Rules:&#10;- Block 6s&#10;- Different rates&#10;  per call type" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="cdp_1">
<mxGeometry x="160" y="810" width="160" height="80" as="geometry"/>
</mxCell>

</root>
</mxGraphModel>
</diagram>
</mxfile> 