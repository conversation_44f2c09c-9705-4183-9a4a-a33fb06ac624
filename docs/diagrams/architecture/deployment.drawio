<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-04-02T10:00:00.000Z">
<diagram id="deployment-architecture" name="Deployment Architecture">
<mxGraphModel>
<root>
<mxCell id="0"/>
<mxCell id="1" parent="0"/>

<!-- Production Environment -->
<mxCell id="prod" value="Production Environment" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#f5f5f5;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="40" y="40" width="720" height="400" as="geometry"/>
</mxCell>

<!-- Frontend Container -->
<mxCell id="frontend_container" value="Frontend Container&#10;--&#10;+ Nginx&#10;+ React App&#10;+ Static Files" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=100;fillColor=#dae8fc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="80" y="100" width="160" height="160" as="geometry"/>
</mxCell>

<!-- Backend Container -->
<mxCell id="backend_container" value="Backend Container&#10;--&#10;+ FastAPI&#10;+ Gunicorn&#10;+ Worker Processes" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=100;fillColor=#d5e8d4;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="320" y="100" width="160" height="160" as="geometry"/>
</mxCell>

<!-- Database Container -->
<mxCell id="db_container" value="Database Container&#10;--&#10;+ PostgreSQL&#10;+ Volumes&#10;+ Backups" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=100;fillColor=#ffe6cc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="560" y="100" width="160" height="160" as="geometry"/>
</mxCell>

<!-- Development Environment -->
<mxCell id="dev" value="Development Environment" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#f5f5f5;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="40" y="480" width="720" height="400" as="geometry"/>
</mxCell>

<!-- Frontend Dev Container -->
<mxCell id="frontend_dev" value="Frontend Dev&#10;--&#10;+ Vite Dev Server&#10;+ Hot Reload&#10;+ TypeScript Watch" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=100;fillColor=#dae8fc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="80" y="540" width="160" height="160" as="geometry"/>
</mxCell>

<!-- Backend Dev Container -->
<mxCell id="backend_dev" value="Backend Dev&#10;--&#10;+ FastAPI&#10;+ Uvicorn&#10;+ Auto Reload" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=100;fillColor=#d5e8d4;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="320" y="540" width="160" height="160" as="geometry"/>
</mxCell>

<!-- Database Dev Container -->
<mxCell id="db_dev" value="Database Dev&#10;--&#10;+ PostgreSQL&#10;+ Test Data&#10;+ Migrations" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=100;fillColor=#ffe6cc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="560" y="540" width="160" height="160" as="geometry"/>
</mxCell>

<!-- Shared Components -->
<mxCell id="shared" value="Shared Components" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#e1d5e7;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="40" y="920" width="720" height="160" as="geometry"/>
</mxCell>

<!-- Docker Compose -->
<mxCell id="docker_compose" value="Docker Compose&#10;--&#10;+ Environment Variables&#10;+ Volume Mounts&#10;+ Network Config" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=100;fillColor=#e1d5e7;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="80" y="980" width="160" height="160" as="geometry"/>
</mxCell>

<!-- Scripts -->
<mxCell id="scripts" value="Scripts&#10;--&#10;+ Start Scripts&#10;+ Migration Scripts&#10;+ Backup Scripts" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=100;fillColor=#e1d5e7;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="320" y="980" width="160" height="160" as="geometry"/>
</mxCell>

<!-- Configuration -->
<mxCell id="config" value="Configuration&#10;--&#10;+ .env Files&#10;+ nginx.conf&#10;+ alembic.ini" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=100;fillColor=#e1d5e7;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="560" y="980" width="160" height="160" as="geometry"/>
</mxCell>

<!-- Connections -->
<mxCell id="" value="" style="endArrow=classic;html=1;" edge="1" parent="1" source="frontend_container" target="backend_container">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="" value="" style="endArrow=classic;html=1;" edge="1" parent="1" source="backend_container" target="db_container">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="" value="" style="endArrow=classic;html=1;" edge="1" parent="1" source="frontend_dev" target="backend_dev">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="" value="" style="endArrow=classic;html=1;" edge="1" parent="1" source="backend_dev" target="db_dev">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

</root>
</mxGraphModel>
</diagram>
</mxfile> 