<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-04-02T10:00:00.000Z">
<diagram id="database-schema-complete" name="Database Schema">
<mxGraphModel>
<root>
<mxCell id="0"/>
<mxCell id="1" parent="0"/>

<!-- Core Tables -->
<mxCell id="user" value="User&#10;--&#10;+ id: UUID PK&#10;+ username: String&#10;+ email: String&#10;+ hashed_password: String&#10;+ is_active: Boolean&#10;+ created_at: DateTime&#10;+ updated_at: DateTime" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=120;fillColor=#f5f5f5;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="40" y="40" width="200" height="180" as="geometry"/>
</mxCell>

<!-- Partner Tables -->
<mxCell id="partner" value="Partner&#10;--&#10;+ id: UUID PK&#10;+ name: String&#10;+ code: String&#10;+ type: PartnerType&#10;+ is_active: Boolean&#10;+ created_at: DateTime&#10;+ updated_at: DateTime" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=120;fillColor=#dae8fc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="300" y="40" width="200" height="180" as="geometry"/>
</mxCell>

<!-- DSC Tables -->
<mxCell id="dsc" value="DoiSoatCuoc&#10;--&#10;+ id: UUID PK&#10;+ thang_doi_soat: Date&#10;+ tu_mang: String&#10;+ den_doi_tac: String&#10;+ created_at: DateTime&#10;+ updated_at: DateTime" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=120;fillColor=#d5e8d4;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="40" y="280" width="200" height="180" as="geometry"/>
</mxCell>

<mxCell id="dau_so_dich_vu_cuoc" value="DauSoDichVuCuoc&#10;--&#10;+ id: UUID PK&#10;+ doi_soat_cuoc_id: UUID FK&#10;+ dau_so: String&#10;+ vnm_san_luong: Decimal&#10;+ vnm_thanh_tien: Decimal&#10;+ viettel_san_luong: Decimal&#10;+ viettel_thanh_tien: Decimal&#10;+ vnpt_san_luong: Decimal&#10;+ vnpt_thanh_tien: Decimal" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=160;fillColor=#d5e8d4;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="300" y="280" width="200" height="220" as="geometry"/>
</mxCell>

<!-- DSCD Tables -->
<mxCell id="dscd" value="DoiSoatCoDinh&#10;--&#10;+ id: UUID PK&#10;+ thang_doi_soat: Date&#10;+ doi_tac: String&#10;+ created_at: DateTime" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=100;fillColor=#ffe6cc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="40" y="520" width="200" height="160" as="geometry"/>
</mxCell>

<mxCell id="cuoc_goi" value="CuocGoi&#10;--&#10;+ id: UUID PK&#10;+ dscd_id: UUID FK&#10;+ loai_cuoc: String&#10;+ san_luong: Decimal&#10;+ don_gia: Decimal&#10;+ thanh_tien: Decimal" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=120;fillColor=#ffe6cc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="300" y="520" width="200" height="180" as="geometry"/>
</mxCell>

<!-- DST Tables -->
<mxCell id="dst" value="DST1800_1900&#10;--&#10;+ id: UUID PK&#10;+ thang_doi_soat: Date&#10;+ loai_dich_vu: String&#10;+ created_at: DateTime" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=100;fillColor=#e1d5e7;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="40" y="760" width="200" height="160" as="geometry"/>
</mxCell>

<!-- Supporting Tables -->
<mxCell id="template" value="Template&#10;--&#10;+ id: UUID PK&#10;+ name: String&#10;+ type: TemplateType&#10;+ file_path: String&#10;+ created_at: DateTime" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=120;fillColor=#f8cecc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="560" y="40" width="200" height="180" as="geometry"/>
</mxCell>

<mxCell id="pricing" value="Pricing&#10;--&#10;+ id: UUID PK&#10;+ partner_id: UUID FK&#10;+ service_type: String&#10;+ billing_method: String&#10;+ price_per_unit: Decimal&#10;+ created_at: DateTime" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=120;fillColor=#f8cecc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
<mxGeometry x="560" y="280" width="200" height="180" as="geometry"/>
</mxCell>

<!-- Relationships -->
<mxCell id="rel1" value="" style="edgeStyle=entityRelationEdgeStyle;endArrow=ERzeroToMany;startArrow=ERone;endFill=1;startFill=0;" edge="1" parent="1" source="dsc" target="dau_so_dich_vu_cuoc">
<mxGeometry width="100" height="100" relative="1" as="geometry"/>
</mxCell>

<mxCell id="rel2" value="" style="edgeStyle=entityRelationEdgeStyle;endArrow=ERzeroToMany;startArrow=ERone;endFill=1;startFill=0;" edge="1" parent="1" source="dscd" target="cuoc_goi">
<mxGeometry width="100" height="100" relative="1" as="geometry"/>
</mxCell>

<mxCell id="rel3" value="" style="edgeStyle=entityRelationEdgeStyle;endArrow=ERzeroToMany;startArrow=ERone;endFill=1;startFill=0;" edge="1" parent="1" source="partner" target="pricing">
<mxGeometry width="100" height="100" relative="1" as="geometry"/>
</mxCell>

</root>
</mxGraphModel>
</diagram>
</mxfile> 