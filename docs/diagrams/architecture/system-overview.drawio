<mxfile host="65bd71144e">
    <diagram id="so_system_overview" name="System Overview">
        <mxGraphModel dx="769" dy="1367" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="so_0"/>
                <mxCell id="so_1" parent="so_0"/>
                <mxCell id="so_frontend_layer" value="Frontend Layer" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#dae8fc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" parent="so_1" vertex="1">
                    <mxGeometry x="210" y="-10" width="200" height="200" as="geometry"/>
                </mxCell>
                <mxCell id="so_react_ui" value="React TypeScript UI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;" parent="so_1" vertex="1">
                    <mxGeometry x="230" y="30" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="so_core_components" value="Core Components" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;" parent="so_1" vertex="1">
                    <mxGeometry x="230" y="80" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="so_auth_components" value="Auth Components" style="rounded=1;whiteSpace=wrap;html=1;" parent="so_core_components" vertex="1">
                    <mxGeometry x="20" y="30" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="so_dsc_dscd_dst_components" value="DSC/DSCD/DST Components" style="rounded=1;whiteSpace=wrap;html=1;" parent="so_core_components" vertex="1">
                    <mxGeometry x="20" y="70" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="so_report_components" value="Report Components" style="rounded=1;whiteSpace=wrap;html=1;" parent="so_core_components" vertex="1">
                    <mxGeometry x="20" y="110" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="so_shared_components" value="Shared Components" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;" parent="so_1" vertex="1">
                    <mxGeometry x="230" y="130" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="so_ui_utils_custom_hooks_types" value="UI Utils | Custom Hooks | Types" style="rounded=1;whiteSpace=wrap;html=1;" parent="so_shared_components" vertex="1">
                    <mxGeometry x="20" y="40" width="240" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="so_backend_layer" value="Backend Layer" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#d5e8d4;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" parent="so_1" vertex="1">
                    <mxGeometry x="40" y="280" width="200" height="200" as="geometry"/>
                </mxCell>
                <mxCell id="so_fastapi_app" value="FastAPI Application" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" parent="so_1" vertex="1">
                    <mxGeometry x="60" y="320" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="so_api_endpoints" value="API Endpoints" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" parent="so_1" vertex="1">
                    <mxGeometry x="60" y="370" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="so_api_v1_auth" value="/api/v1/auth" style="rounded=1;whiteSpace=wrap;html=1;" parent="so_api_endpoints" vertex="1">
                    <mxGeometry x="20" y="30" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="so_api_v1_doi_soat" value="/api/v1/doi-soat" style="rounded=1;whiteSpace=wrap;html=1;" parent="so_api_endpoints" vertex="1">
                    <mxGeometry x="20" y="70" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="so_api_v1_reports" value="/api/v1/reports" style="rounded=1;whiteSpace=wrap;html=1;" parent="so_api_endpoints" vertex="1">
                    <mxGeometry x="20" y="110" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="so_core_services" value="Core Services" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;" parent="so_1" vertex="1">
                    <mxGeometry x="60" y="420" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="so_file_processors_data_validators_utils" value="File Processors | Data Validators | Utils" style="rounded=1;whiteSpace=wrap;html=1;" parent="so_core_services" vertex="1">
                    <mxGeometry x="20" y="40" width="240" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="so_database_layer" value="Database Layer" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#ffe6cc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" parent="so_1" vertex="1">
                    <mxGeometry x="40" y="520" width="200" height="200" as="geometry"/>
                </mxCell>
                <mxCell id="so_postgresql" value="PostgreSQL" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;" parent="so_1" vertex="1">
                    <mxGeometry x="60" y="560" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="so_core_tables" value="Core Tables" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#ffe6cc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" parent="so_1" vertex="1">
                    <mxGeometry x="60" y="610" width="160" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="so_table_users" value="Users" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" parent="so_core_tables" vertex="1">
                    <mxGeometry y="30" width="160" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="so_table_partners" value="Partners" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" parent="so_core_tables" vertex="1">
                    <mxGeometry y="50" width="160" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="so_table_dsc" value="DSC Data" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" parent="so_core_tables" vertex="1">
                    <mxGeometry y="70" width="160" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="so_table_dscd" value="DSCD Data" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" parent="so_core_tables" vertex="1">
                    <mxGeometry y="90" width="160" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="so_table_dst" value="DST Data" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" parent="so_core_tables" vertex="1">
                    <mxGeometry y="110" width="160" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="so_frontend_to_backend" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" parent="so_1" source="so_frontend_layer" target="so_backend_layer" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="so_backend_to_database" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" parent="so_1" source="so_backend_layer" target="so_database_layer" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>