<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-04-02T10:00:00.000Z">
<diagram id="bea_backend_architecture" name="Backend Architecture">
<mxGraphModel>
<root>
<mxCell id="bea_0"/>
<mxCell id="bea_1" parent="bea_0"/>

<!-- API Layer -->
<mxCell id="bea_api_layer" value="API Layer" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#dae8fc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="bea_1">
<mxGeometry x="360" y="40" width="160" height="120" as="geometry"/>
</mxCell>

<mxCell id="bea_api_endpoints" value="+ /api/v1/auth&#10;+ /api/v1/doi-soat&#10;+ /api/v1/partners&#10;+ /api/v1/templates&#10;+ /api/v1/users" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="bea_api_layer">
<mxGeometry y="30" width="160" height="90" as="geometry"/>
</mxCell>

<!-- Middleware Layer -->
<mxCell id="bea_middleware_layer" value="Middleware Layer" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#d5e8d4;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="bea_1">
<mxGeometry x="360" y="200" width="160" height="120" as="geometry"/>
</mxCell>

<mxCell id="bea_middleware_components" value="+ Authentication&#10;+ Error Handling&#10;+ Logging&#10;+ CORS&#10;+ Rate Limiting" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="bea_middleware_layer">
<mxGeometry y="30" width="160" height="90" as="geometry"/>
</mxCell>

<!-- Service Layer -->
<mxCell id="bea_service_layer" value="Service Layer" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#ffe6cc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="bea_1">
<mxGeometry x="360" y="360" width="160" height="30" as="geometry"/>
</mxCell>

<!-- File Processing Services -->
<mxCell id="bea_file_services" value="File Processing Services" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#ffe6cc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="bea_1">
<mxGeometry x="160" y="440" width="160" height="120" as="geometry"/>
</mxCell>

<mxCell id="bea_file_services_list" value="+ DSC Processor&#10;+ DSCD Processor&#10;+ DST Processor&#10;+ Template Service" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="bea_file_services">
<mxGeometry y="30" width="160" height="90" as="geometry"/>
</mxCell>

<!-- Business Services -->
<mxCell id="bea_business_services" value="Business Services" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#ffe6cc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="bea_1">
<mxGeometry x="360" y="440" width="160" height="120" as="geometry"/>
</mxCell>

<mxCell id="bea_business_services_list" value="+ Auth Service&#10;+ Partner Service&#10;+ Pricing Service&#10;+ Report Service" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="bea_business_services">
<mxGeometry y="30" width="160" height="90" as="geometry"/>
</mxCell>

<!-- Utility Services -->
<mxCell id="bea_utility_services" value="Utility Services" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#ffe6cc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="bea_1">
<mxGeometry x="560" y="440" width="160" height="120" as="geometry"/>
</mxCell>

<mxCell id="bea_utility_services_list" value="+ Excel Service&#10;+ PDF Service&#10;+ Email Service&#10;+ Cache Service" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="bea_utility_services">
<mxGeometry y="30" width="160" height="90" as="geometry"/>
</mxCell>

<!-- Data Access Layer -->
<mxCell id="bea_data_layer" value="Data Access Layer" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#e1d5e7;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="bea_1">
<mxGeometry x="360" y="600" width="160" height="120" as="geometry"/>
</mxCell>

<mxCell id="bea_data_layer_components" value="+ Models&#10;+ Schemas&#10;+ Repositories&#10;+ Migrations" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="bea_data_layer">
<mxGeometry y="30" width="160" height="90" as="geometry"/>
</mxCell>

<!-- Database Layer -->
<mxCell id="bea_database_layer" value="Database Layer" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;fillColor=#f8cecc;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="bea_1">
<mxGeometry x="360" y="760" width="160" height="120" as="geometry"/>
</mxCell>

<mxCell id="bea_database_components" value="+ PostgreSQL&#10;+ Redis Cache&#10;+ File Storage" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="bea_database_layer">
<mxGeometry y="30" width="160" height="90" as="geometry"/>
</mxCell>

<!-- Connections -->
<mxCell id="bea_api_to_middleware" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="bea_1" source="bea_api_layer" target="bea_middleware_layer">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="bea_middleware_to_service" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="bea_1" source="bea_middleware_layer" target="bea_service_layer">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="bea_service_to_file" value="" style="endArrow=classic;html=1;exitX=0.25;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="bea_1" source="bea_service_layer" target="bea_file_services">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="bea_service_to_business" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="bea_1" source="bea_service_layer" target="bea_business_services">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="bea_service_to_utility" value="" style="endArrow=classic;html=1;exitX=0.75;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="bea_1" source="bea_service_layer" target="bea_utility_services">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="bea_services_to_data" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="bea_1" source="bea_business_services" target="bea_data_layer">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="bea_data_to_database" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="bea_1" source="bea_data_layer" target="bea_database_layer">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<!-- Notes -->
<mxCell id="bea_note_1" value="FastAPI Application&#10;--&#10;- Dependency Injection&#10;- OpenAPI/Swagger&#10;- Type Validation&#10;- Error Handling" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="bea_1">
<mxGeometry x="40" y="40" width="160" height="100" as="geometry"/>
</mxCell>

<mxCell id="bea_note_2" value="Service Layer&#10;--&#10;- Business Logic&#10;- File Processing&#10;- Data Validation&#10;- Error Handling" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="bea_1">
<mxGeometry x="40" y="360" width="160" height="100" as="geometry"/>
</mxCell>

<mxCell id="bea_note_3" value="Data Layer&#10;--&#10;- SQLAlchemy ORM&#10;- Pydantic Models&#10;- Repository Pattern&#10;- Migrations" style="shape=note;whiteSpace=wrap;html=1;size=14;fillColor=#fff2cc;" vertex="1" parent="bea_1">
<mxGeometry x="40" y="600" width="160" height="100" as="geometry"/>
</mxCell>

</root>
</mxGraphModel>
</diagram>
</mxfile> 