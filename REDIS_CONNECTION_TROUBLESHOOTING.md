# Redis Connection Troubleshooting for AuditCall

## Problem

The following error occurs in the Celery worker:

```
celery_worker-1  | [2025-05-26 19:40:33,828: CRITICAL/MainProcess] Unrecoverable error: ResponseError('UNBLOCKED force unblock from blocking operation, instance state changed (master -> replica?)')
```

This error indicates that the Redis connection was interrupted while Celery was performing a blocking operation. This can happen due to:

1. Redis server failover (if using Redis Sentinel)
2. Network interruptions
3. Redis memory issues
4. Redis server restart

## Solution

We've implemented several changes to make the Celery worker more resilient to Redis connection issues:

### 1. Enhanced Celery Configuration

The Celery configuration in `backend/src/worker/celery_app.py` has been updated to:

- Reduce socket timeouts from 30s to 15s
- Enable TCP keepalive for persistent connections
- Add robust retry policies for broker and result backend connections
- Configure automatic connection retry on startup
- Set proper broker connection timeout and max retries

### 2. Improved Redis Configuration

The Redis configuration in `docker-compose.prod.yml` has been enhanced with:

- Data persistence with Append-Only File (AOF) mode
- Memory limits to prevent OOM errors (1GB max memory)
- LRU eviction policy for cache management
- Proper snapshot configuration
- Container restart policy
- Resource limitations

### 3. Celery Worker Restart Utility

A utility script `scripts/restart_celery_worker.sh` has been created to safely restart the Celery worker container when needed.

## How to Apply the Changes

1. Deploy the updated configuration files to your production environment:
   - `backend/src/worker/celery_app.py`
   - `docker-compose.prod.yml`
   - `scripts/restart_celery_worker.sh`

2. Make the restart script executable:
   ```bash
   chmod +x scripts/restart_celery_worker.sh
   ```

3. Restart the services to apply the changes:
   ```bash
   docker-compose -f docker-compose.prod.yml down
   docker-compose -f docker-compose.prod.yml up -d
   ```

## Using the Restart Script

If you encounter Redis connection issues, you can use the restart script to safely restart the Celery worker:

```bash
./scripts/restart_celery_worker.sh
```

The script will:
1. Check if the worker is running
2. Confirm before proceeding
3. Stop the Celery worker
4. Start it again
5. Show recent logs

## Additional Recommendations

### 1. Monitor Redis Memory Usage

Set up monitoring for Redis memory usage to prevent OOM issues:

```bash
# Check Redis memory usage
docker-compose -f docker-compose.prod.yml exec redis redis-cli info memory | grep used_memory_human
```

### 2. Redis Persistence Verification

Ensure Redis persistence is working correctly:

```bash
# Check if AOF is enabled
docker-compose -f docker-compose.prod.yml exec redis redis-cli config get appendonly
```

### 3. Regular Redis Backups

Set up regular Redis backups:

```bash
# Create a Redis backup
docker-compose -f docker-compose.prod.yml exec redis redis-cli save
```

### 4. Regular Celery Worker Restarts

Consider scheduling regular Celery worker restarts during low-usage periods to prevent memory leaks:

```bash
# Add to crontab (e.g., restart at 3 AM every day)
0 3 * * * cd /path/to/project && ./scripts/restart_celery_worker.sh
```

### 5. Celery Monitoring

Consider implementing Celery monitoring with Flower:

```yaml
# Add to docker-compose.prod.yml
flower:
  build:
    context: ./backend
    dockerfile: Dockerfile
  command: celery -A src.worker.celery_app flower --port=5555
  ports:
    - "5555:5555"
  depends_on:
    - celery_worker
    - redis
  env_file:
    - ./backend/.env.production
  networks:
    - audit-network
```

## Troubleshooting

If you continue to experience Redis connection issues:

1. Check Redis logs for errors:
   ```bash
   docker-compose -f docker-compose.prod.yml logs redis
   ```

2. Check Celery worker logs:
   ```bash
   docker-compose -f docker-compose.prod.yml logs celery_worker
   ```

3. Verify Redis is running properly:
   ```bash
   docker-compose -f docker-compose.prod.yml exec redis redis-cli ping
   ```

4. Consider upgrading Redis:
   ```bash
   # Update in docker-compose.prod.yml
   redis:
     image: redis:7.2-alpine  # Use latest stable version
   ```

5. Consider implementing Redis Sentinel for high availability.
