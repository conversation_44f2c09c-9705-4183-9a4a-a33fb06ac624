"""
Test script to verify that all files in the DSC folder are correctly classified as DSC
"""
import os
import re
import sys
from enum import Enum, auto

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Define a minimal TemplateType enum to match the original
class TemplateType(Enum):
    DSCD = auto()  # Đối soát cố định
    DSC = auto()   # Đối soát cước
    CKN = auto()   # <PERSON><PERSON><PERSON><PERSON> kết nối
    DST_1800_1900 = auto()  # Đối soát tổng đài 1800/1900

def normalize_text(text):
    # Chuyển về chữ thường
    text = text.lower()
    
    # Thay thế trực tiếp các ký tự tiếng Việt phổ biến
    vietnamese_chars = {
        'á': 'a', 'à': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ắ': 'a', 'ằ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ấ': 'a', 'ầ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'é': 'e', 'è': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ế': 'e', 'ề': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'í': 'i', 'ì': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ó': 'o', 'ò': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ố': 'o', 'ồ': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ớ': 'o', 'ờ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ú': 'u', 'ù': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ứ': 'u', 'ừ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ý': 'y', 'ỳ': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd'  # Quan trọng: Chuyển đổi đ thành d
    }
    
    for vietnamese, latin in vietnamese_chars.items():
        text = text.replace(vietnamese, latin)
    
    # Loại bỏ tất cả ký tự không phải a-z và 0-9 (bao gồm khoảng trắng)
    text = re.sub(r'[^a-z0-9]', '', text)
    
    return text

# Define search patterns
DSCD_SIMPLE_STRINGS = [
    "codinh", "cd_", "_cd", "_cd_", "dscd", "dscodinh", 
    "thuebao", "tbcodinh", "dscodinh",
    "doisoatcodinh", "cdinh",
    # Các mẫu đặc biệt
    "bidv", "doctorcheck", "shield" 
]

DSC_SIMPLE_STRINGS = [
    "cuoc", "doanhthu", "chitietcuocgoi", "itc", "dsc", "dscuoc",
    "giacuoc", "phi", "dthu"
]

def classify_from_filename(filename):
    basename = os.path.basename(filename)
    filename_lower = basename.lower()
    filename_norm = normalize_text(basename)
    
    # Kiểm tra chuỗi đặc trưng mạnh trước
    # Kiểm tra DSC (đối soát cước) trước nếu có từ "cuoc" trong tên file
    if "cuoc" in filename_norm or "cước" in filename_lower:
        for simple_str in DSC_SIMPLE_STRINGS:
            if simple_str in filename_norm:
                return TemplateType.DSC
    
    # Kiểm tra DSCD
    for simple_str in DSCD_SIMPLE_STRINGS:
        # Kiểm tra cho chuỗi "cd" - phải là chuỗi riêng biệt hoặc được phân cách
        if simple_str == "cd":
            # Tìm "cd" như một từ riêng biệt hoặc trong các ngữ cảnh cụ thể
            patterns = [r'\bcd\b', r'_cd_', r'_cd\b', r'\bcd_']
            if any(re.search(pattern, filename_norm) for pattern in patterns):
                return TemplateType.DSCD
        elif simple_str in filename_norm:
            return TemplateType.DSCD
                
    # Kiểm tra DSC 
    for simple_str in DSC_SIMPLE_STRINGS:
        if simple_str in filename_norm:
            return TemplateType.DSC
    
    return None

# Path to DSC folder
dsc_folder = "/home/<USER>/Documents/Job/AuditCall/backend/BBDS/DSC"

# Format results as a table
print("=" * 100)
print(f"{'Filename':<50} | {'Normalized':<40} | {'Classification':<15} | {'Correct?':<8}")
print("-" * 100)

# Test all files in the DSC folder
all_correct = True
for filename in os.listdir(dsc_folder):
    if filename.endswith(".xlsx"):
        full_path = os.path.join(dsc_folder, filename)
        normalized = normalize_text(filename)
        result = classify_from_filename(filename)
        
        # DSC files should be classified as TemplateType.DSC
        is_correct = result == TemplateType.DSC
        if not is_correct:
            all_correct = False
        
        print(f"{filename[:48]:<50} | {normalized[:38]:<40} | {str(result):<15} | {'✓' if is_correct else '✗'}")

print("=" * 100)
print(f"Overall result: {'All files correctly classified as DSC' if all_correct else 'Some files were misclassified'}")
print("=" * 100) 