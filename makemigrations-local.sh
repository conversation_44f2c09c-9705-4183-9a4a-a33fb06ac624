#!/bin/bash

# Check if migration message is provided
if [ -z "$1" ]; then
    echo "Error: Migration message is required"
    echo "Usage: ./makemigrations-local.sh <migration_message>"
    exit 1
fi

# Change to backend directory
cd backend

# Copy .env.migration to .env for local migration
cp .env.migration .env

echo "Checking Python and environment..."
# Đ<PERSON>m bảo sử dụng Python trong venv
PYTHON_PATH="venv/bin/python"

if [ ! -f "$PYTHON_PATH" ]; then
    echo "Error: Python not found in venv. Is the virtual environment set up correctly?"
    exit 1
fi

# Xem venv Python ở đâu
echo "Using Python: $($PYTHON_PATH -c 'import sys; print(sys.executable)')"

# Verify SQLAlchemy version trong venv
$PYTHON_PATH -c "import sqlalchemy; print(f'Using SQLAlchemy version: {sqlalchemy.__version__}')"

# Cài đặt alembic vào venv nếu chưa có
if ! $PYTHON_PATH -m pip list | grep -q alembic; then
    echo "Alembic not found in venv, installing..."
    $PYTHON_PATH -m pip install alembic
fi

# Sử dụng module alembic từ venv
echo "Using alembic from venv"
$PYTHON_PATH -m alembic revision --autogenerate -m "$1"

# Check if generation was successful
if [ $? -eq 0 ]; then
    echo "Migration file generated successfully!"
else
    echo "Failed to generate migration file!"
    exit 1
fi

# Restore .env.local for docker
cp .env.local .env 