#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import unicodedata
import re

def normalize_text(text):
    """
    Chuẩn hóa văn bản để dễ so sánh bằng cách:
    - <PERSON><PERSON><PERSON><PERSON> về chữ thường
    - <PERSON>ại bỏ dấu từ các ký tự Unicode (như ố -> o, ư -> u)
    - Loại bỏ ký tự đặc biệt
    """
    # Chuyển về chữ thường
    text = text.lower()
    # Loại bỏ dấu
    text = unicodedata.normalize('NFKD', text).encode('ASCII', 'ignore').decode('ASCII')
    # Loại bỏ ký tự đặc biệt
    text = re.sub(r'[^\w\s]', ' ', text)
    # Thay thế nhiều khoảng trắng bằng một khoảng trắng
    text = re.sub(r'\s+', ' ', text).strip()
    return text

def main():
    print("=== Bắt đầu debug phân loại file DS cố định VNTEL_T1_2025.xlsx ===")
    
    # Tên file của chúng ta
    filename = "DS cố định VNTEL_T1_2025.xlsx"
    
    # Danh sách các chuỗi đơn giản cho DSCD (không dùng regex)
    DSCD_SIMPLE_STRINGS = [
        "cố định", "cd", "dscd", "ds cd", "ds-cd", 
        "thuê bao", "tb cố định", "ds cố định"
    ]
    
    # Pattern nhận dạng cho DoiSoatCoDinh (DSCD)
    DSCD_PATTERNS = [
        r'cố\s*định',
        r'co\s*dinh',
        r'thuê\s*bao',
        r'thue\s*bao',
        r'TB',
        r'CDHTC',      # Thêm pattern từ mẫu CDHTC
        r'CD',         # CD có thể là viết tắt của cố định
        r'_CD_',       # CD trong tên file
        r'HTC_[0-9]+', # Các mẫu HTC_xxx thường là cố định
        r'DS\s*cố\s*định', # DS cố định 
        r'DSCD',       # Viết tắt của Đối Soát Cố Định
        r'DS CD',      # DS CD
        r'DS-CD',      # DS-CD
    ]
    
    # Kiểm tra normalize_text
    print("\n=== Kiểm tra normalize_text ===")
    normalized = normalize_text(filename)
    print(f"Original: '{filename}'")
    print(f"Normalized: '{normalized}'")
    
    # Kiểm tra simple string matching
    print("\n=== Kiểm tra khớp chuỗi đơn giản ===")
    for simple_str in DSCD_SIMPLE_STRINGS:
        normalized_str = normalize_text(simple_str)
        is_match = normalized_str in normalized
        print(f"  '{simple_str}' ('{normalized_str}') in '{normalized}': {is_match}")
    
    # Kiểm tra regex matching
    print("\n=== Kiểm tra khớp regex pattern ===")
    for pattern in DSCD_PATTERNS:
        match = re.search(pattern, filename, re.IGNORECASE)
        is_match = match is not None
        matched_text = match.group(0) if match else "N/A"
        print(f"  Pattern '{pattern}': {is_match}, Matched: '{matched_text}'")
    
    print("\n=== Kết thúc debug ===")

if __name__ == "__main__":
    main() 