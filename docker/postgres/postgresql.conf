# PostgreSQL configuration file
listen_addresses = '*'
log_timezone = 'Asia/Ho_<PERSON>_<PERSON>'
timezone = 'Asia/Ho_Chi_Minh'

# Security settings
log_connections = on
log_disconnections = on

# Disable dangerous features
password_encryption = 'scram-sha-256'

# Disable dangerous features
# Using enabled_extensions instead of preload libraries for PostgreSQL 15
#local_preload_libraries = ''
#session_preload_libraries = ''
#shared_preload_libraries = ''

# Disable remote COPY FROM PROGRAM if available in this version
# Note: Not all PostgreSQL versions support this setting directly
# copy_program_execution = false
