#!/usr/bin/env python3

from src.database.session import <PERSON><PERSON>ocal
from src.models.call_log import CallLog, NumberType
from src.worker.call_log_tasks import classify_phone_number
from sqlalchemy import func

def check_phone_numbers():
    db = SessionLocal()
    try:
        # Kiểm tra phân phối loại số điện thoại
        print('===== PHÂN PHỐI LOẠI SỐ ĐIỆN THOẠI =====')
        caller_types = db.query(CallLog.caller_type, func.count(CallLog.id)).group_by(CallLog.caller_type).all()
        for t in caller_types:
            print(f'Type: {t[0]}, Count: {t[1]}')
        
        # Kiểm tra các đầu số cụ thể
        print('\n===== KIỂM TRA ĐẦU SỐ DI ĐỘNG =====')
        mobile_prefixes = ['090', '091', '092', '093', '094', '096', '097', '098', '099']
        for prefix in mobile_prefixes:
            samples = db.query(CallLog).filter(CallLog.caller.like(f'{prefix}%')).limit(1).all()
            for sample in samples:
                correct_type = classify_phone_number(sample.caller)
                print(f'Prefix {prefix}: {sample.caller} (DB Type: {sample.caller_type}, Should be: {correct_type})')
        
        # Kiểm tra mã vùng
        print('\n===== KIỂM TRA MÃ VÙNG =====')
        area_samples = db.query(CallLog).filter(CallLog.caller.like('024%')).limit(1).all()
        area_samples.extend(db.query(CallLog).filter(CallLog.caller.like('028%')).limit(1).all())
        for sample in area_samples:
            correct_type = classify_phone_number(sample.caller)
            print(f'Area code in {sample.caller} (DB Type: {sample.caller_type}, Should be: {correct_type})')
        
        # Kiểm tra số ngẫu nhiên
        print('\n===== 10 SỐ NGẪU NHIÊN =====')
        samples = db.query(CallLog).order_by(func.random()).limit(10).all()
        for sample in samples:
            correct_type = classify_phone_number(sample.caller)
            print(f'Random: {sample.caller} (DB Type: {sample.caller_type}, Should be: {correct_type})')
            
    finally:
        db.close()

if __name__ == "__main__":
    check_phone_numbers() 