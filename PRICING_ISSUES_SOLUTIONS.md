# Vấn đề và Giải pháp cho Pricing Rules

## Tổng quan

Tài liệu này tóm tắt các vấn đề đã phát hiện và giải pháp đã thực hiện liên quan đến việc cập nhật và hiển thị pricing rules trong hệ thống.

## Vấn đề đã phát hiện

### 1. Vấn đề với trường `type` trong frontend

Khi chỉnh sửa một quy tắc pricing, trường `type` (revenue/cost) không được giữ nguyên, dẫn đến việc hiển thị sai loại quy tắc trong dialog chỉnh sửa.

### 2. Không có quy tắc chi (cost pricing) trong database

Ban đầu, database chỉ có các quy tắc thu (revenue pricing) mà không có quy tắc chi (cost pricing).

### 3. Vấn đề khi cập nhật quy tắc

Khi cập nhật một quy tắc, c<PERSON> thể xảy ra lỗi do trường `type` không được truyền đúng từ frontend đến backend.

## Giải pháp đã thực hiện

### 1. Sửa lỗi trường `type` trong frontend

- Đã sửa hàm `handleServiceClick` trong `PricingTable.tsx` để đảm bảo trường `type` được gán đúng cho mỗi rule:
  ```typescript
  const revenueRules = revenuePricing.filter(
    (rule) => rule.service_type?.id === serviceId
  ).map(rule => ({...rule, type: 'revenue'})) // Đảm bảo type là 'revenue'
  
  const costRules = costPricing.filter(
    (rule) => rule.service_type?.id === serviceId
  ).map(rule => ({...rule, type: 'cost'})) // Đảm bảo type là 'cost'
  ```

- Đã sửa hàm `handleEditRule` trong `PricingTable.tsx` để đảm bảo trường `type` được gán đúng khi gọi `onEditRule`:
  ```typescript
  const ruleWithType = {
    ...rule,
    type: type || rule.type || (revenuePricing.some(r => r.id === rule.id) ? 'revenue' : 'cost')
  };
  onEditRule(ruleWithType);
  ```

- Đã sửa hàm `handleEditRule` trong `PricingPage.tsx` để đảm bảo trường `type` được giữ nguyên từ rule gốc:
  ```typescript
  type: rule.type || activeTab,
  ```

### 2. Tạo quy tắc chi (cost pricing) trong database

Đã tạo script `update_pricing.py` để:
- Kiểm tra dữ liệu trong bảng `revenue_pricing` và `cost_pricing`
- Tạo một quy tắc chi mới với thông số:
  - partner_id=1
  - service_type_id=1
  - volume_range_id=1
  - price=100.00
  - billing_method='block_6s'

### 3. Kiểm tra và cập nhật quy tắc thông qua API

Đã tạo script `test_api.py` để:
- Kiểm tra các API endpoints liên quan đến pricing
- Cập nhật giá của quy tắc thu và quy tắc chi
- Xác nhận rằng các API endpoints hoạt động đúng

## Kết quả

- Đã tạo thành công quy tắc chi (cost pricing) với ID=2
- Đã cập nhật thành công giá của quy tắc thu ID=1 từ 150.00 lên 200.00
- Đã cập nhật thành công giá của quy tắc chi ID=2 từ 100.00 lên 150.00
- Đã sửa các vấn đề liên quan đến trường `type` trong frontend

## Khuyến nghị

1. **Thêm validation trong frontend**: Đảm bảo trường `type` luôn được gán đúng giá trị trước khi gửi request đến backend.

2. **Thêm logging**: Bổ sung logging chi tiết hơn trong frontend và backend để dễ dàng debug các vấn đề tương tự trong tương lai.

3. **Cải thiện UX**: Hiển thị rõ ràng loại quy tắc (thu/chi) trong giao diện người dùng để tránh nhầm lẫn.

4. **Tự động hóa kiểm tra**: Tạo các test tự động để kiểm tra các API endpoints và luồng dữ liệu trong hệ thống.

## Tài liệu tham khảo

- `backend/src/api/v1/endpoints/pricing.py`: Chứa các API endpoints liên quan đến pricing
- `frontend/src/app/pricing/page.tsx`: Chứa logic chính cho trang pricing
- `frontend/src/components/pricing/PricingTable.tsx`: Hiển thị bảng pricing rules
- `frontend/src/components/pricing/PricingDialog.tsx`: Dialog chỉnh sửa pricing rule
- `frontend/src/components/pricing/PricingForm.tsx`: Form chỉnh sửa pricing rule 