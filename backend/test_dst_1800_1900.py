import os
import sys
import re
import logging
from typing import Dict, List, Any, Optional, Tuple

# Cấu hình logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Các mẫu regex để nhận dạng file đối soát 1800/1900
DST_1800_1900_PATTERNS = [
    r'18001900',
    r'1800_1900',
    r'1800[-_\s]1900',
    r'1900[-_\s]1800',
    r'1819', # Pattern cho 1800/1900 viết gọn
    r'1900',
    r'1800'
]

def is_dst_1800_1900_file(filename: str) -> bool:
    """
    Kiểm tra xem file có phải là file đối soát 1800/1900 không
    
    Args:
        filename: Tên file cần kiểm tra
        
    Returns:
        True nếu là file đối soát 1800/1900, False nếu không phải
    """
    filename = os.path.basename(filename).lower()
    for pattern in DST_1800_1900_PATTERNS:
        if re.search(pattern, filename, re.IGNORECASE):
            return True
    return False

def extract_month_year(filename: str) -> Tuple[Optional[int], Optional[int]]:
    """
    Trích xuất tháng và năm từ tên file
    
    Args:
        filename: Tên file đối soát 1800/1900
        
    Returns:
        Tuple chứa (tháng, năm) hoặc (None, None) nếu không trích xuất được
    """
    filename = os.path.basename(filename)
    
    # Mẫu 1: T1_2025, Tháng 1-2025, Tháng 1_2025
    pattern1 = r'(T|Th[áa]ng)[_\s-]*(\d{1,2})[-_\.\s](\d{4})'
    match = re.search(pattern1, filename, re.IGNORECASE)
    if match:
        return int(match.group(2)), int(match.group(3))
    
    # Mẫu 2: 012025, 202501 (năm tháng hoặc tháng năm)
    pattern2 = r'_?(\d{2})(\d{4})'
    match = re.search(pattern2, filename)
    if match:
        if int(match.group(1)) <= 12:
            return int(match.group(1)), int(match.group(2))
        else:
            # Định dạng năm trước tháng sau
            year_str = match.group(1) + match.group(2)[:2]
            month_str = match.group(2)[2:]
            if int(month_str) <= 12:
                return int(month_str), int(year_str)
    
    # Mẫu 3: định dạng như "18001900_VNM_HTC_012025.xlsx"
    pattern3 = r'_0?(\d{1,2})[-_\.]?(\d{4})\b'
    match = re.search(pattern3, filename)
    if match:
        return int(match.group(1)), int(match.group(2))
    
    # Mẫu 4: với định dạng có dấu chấm như "T1.2025"
    pattern4 = r'T(\d{1,2})\.(\d{4})'
    match = re.search(pattern4, filename, re.IGNORECASE)
    if match:
        return int(match.group(1)), int(match.group(2))
    
    return None, None

def extract_partners(filename: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
    """
    Trích xuất thông tin đối tác và loại dịch vụ từ tên file
    
    Args:
        filename: Tên file đối soát 1800/1900
        
    Returns:
        Tuple chứa (từ_mạng, đến_đối_tác, loại_dịch_vụ) hoặc (None, None, None) nếu không trích xuất được
    """
    filename = os.path.basename(filename)
    
    # Xác định loại dịch vụ
    service_type = None
    if re.search(r'18001900|1800[-_\s]1900|1900[-_\s]1800|1819', filename, re.IGNORECASE):
        service_type = "1800xxxx/1900xxxx"
    elif re.search(r'1900', filename, re.IGNORECASE):
        service_type = "1900xxxx"
    elif re.search(r'1800', filename, re.IGNORECASE):
        service_type = "1800xxxx"
    
    # Xác định từ mạng và đến đối tác
    partners = re.findall(r'([A-Za-z]{2,6})', filename.upper())
    if len(partners) >= 2:
        from_partner, to_partner = None, None
        
        # Kiểm tra mẫu HTC_PARTNER hoặc PARTNER_HTC
        htc_index = None
        for i, partner in enumerate(partners):
            if partner == "HTC":
                htc_index = i
                break
                
        if htc_index is not None:
            # Nếu HTC đứng trước, HTC là from_partner
            if htc_index == 0 and len(partners) > 1:
                from_partner = "HTC"
                to_partner = partners[1]
            # Nếu HTC đứng sau, HTC là to_partner
            elif htc_index > 0:
                from_partner = partners[htc_index - 1]
                to_partner = "HTC"
        else:
            # Nếu không có HTC, giả định 2 đối tác đầu tiên
            from_partner = partners[0]
            to_partner = partners[1] if len(partners) > 1 else None
        
        return from_partner, to_partner, service_type
    
    return None, None, service_type

def test_recognize_files(directory_path: str) -> None:
    """
    Kiểm tra khả năng nhận dạng các file đối soát 1800/1900 trong thư mục
    
    Args:
        directory_path: Đường dẫn đến thư mục chứa các file cần kiểm tra
    """
    if not os.path.exists(directory_path):
        logger.error(f"Thư mục {directory_path} không tồn tại!")
        return
    
    # Kết quả nhận dạng
    recognized_files = []
    unrecognized_files = []
    
    # Duyệt qua tất cả các file trong thư mục
    for root, _, files in os.walk(directory_path):
        for filename in files:
            file_path = os.path.join(root, filename)
            
            # Bỏ qua các thư mục
            if os.path.isdir(file_path):
                continue
                
            # Chỉ xử lý các file Excel
            if not (filename.endswith('.xlsx') or filename.endswith('.xls')):
                unrecognized_files.append({
                    "file": filename,
                    "reason": "Không phải file Excel"
                })
                continue
            
            # Kiểm tra xem có phải file đối soát 1800/1900 không
            is_dst_file = is_dst_1800_1900_file(filename)
            
            if is_dst_file:
                # Trích xuất thông tin từ tên file
                month, year = extract_month_year(filename)
                tu_mang, den_doi_tac, loai_dich_vu = extract_partners(filename)
                
                recognized_files.append({
                    "file": filename,
                    "month": month,
                    "year": year,
                    "tu_mang": tu_mang,
                    "den_doi_tac": den_doi_tac,
                    "loai_dich_vu": loai_dich_vu
                })
            else:
                unrecognized_files.append({
                    "file": filename,
                    "reason": "Không nhận dạng được là file đối soát 1800/1900"
                })
    
    # In kết quả
    logger.info(f"Tổng số file Excel: {len(recognized_files) + sum(1 for f in unrecognized_files if f['reason'] != 'Không phải file Excel')}")
    logger.info(f"Số file được nhận dạng là đối soát 1800/1900: {len(recognized_files)}")
    logger.info(f"Số file không được nhận dạng: {len(unrecognized_files)}")
    
    # In chi tiết các file được nhận dạng
    logger.info("\nCác file được nhận dạng là đối soát 1800/1900:")
    for idx, file_info in enumerate(recognized_files, 1):
        logger.info(f"{idx}. {file_info['file']}")
        logger.info(f"   - Tháng/Năm: {file_info['month']}/{file_info['year']}")
        logger.info(f"   - Từ mạng: {file_info['tu_mang']}")
        logger.info(f"   - Đến đối tác: {file_info['den_doi_tac']}")
        logger.info(f"   - Loại dịch vụ: {file_info['loai_dich_vu']}")
    
    # In chi tiết các file không được nhận dạng
    logger.info("\nCác file không được nhận dạng là đối soát 1800/1900:")
    excel_files = [f for f in unrecognized_files if f['reason'] != 'Không phải file Excel']
    for idx, file_info in enumerate(excel_files, 1):
        logger.info(f"{idx}. {file_info['file']} - Lý do: {file_info['reason']}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        directory_path = sys.argv[1]
    else:
        directory_path = "BBDS"  # Mặc định thư mục BBDS
    
    logger.info(f"Bắt đầu kiểm tra các file trong thư mục: {directory_path}")
    test_recognize_files(directory_path) 