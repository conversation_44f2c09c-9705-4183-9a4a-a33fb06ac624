#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import re
import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, NamedTuple

# Cấu hình logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Classes để thay thế schemas.dsc
class DauSoDichVuCuoc(NamedTuple):
    dau_so: str
    stt: int = 0
    vnm_san_luong: float = 0
    vnm_ty_le_cp: float = 0
    vnm_thanh_tien: float = 0
    viettel_san_luong: float = 0
    viettel_ty_le_cp: float = 0
    viettel_thanh_tien: float = 0
    vnpt_san_luong: float = 0
    vnpt_ty_le_cp: float = 0
    vnpt_thanh_tien: float = 0
    vms_san_luong: float = 0
    vms_ty_le_cp: float = 0
    vms_thanh_tien: float = 0
    khac_san_luong: float = 0
    khac_ty_le_cp: float = 0
    khac_thanh_tien: float = 0
    tong_thanh_toan: float = 0

class TongKetCuoc(NamedTuple):
    cong_tien_dich_vu: float
    tien_thue_gtgt: float
    tong_cong_tien: float

class DoiSoatCuoc(NamedTuple):
    thang_doi_soat: str
    tu_mang: str
    den_doi_tac: str
    file_name: str
    hop_dong_so: Optional[str] = None
    du_lieu: List[DauSoDichVuCuoc] = []
    tong_ket: Optional[TongKetCuoc] = None

# Các hàm từ dsc_processor.py

# Các mẫu regex để nhận dạng file đối soát cước
DSC_PATTERNS = [
    r'Doi\s*soat\s*cuoc',
    r'DoiSoatCuoc',
    r'Đối\s*soát\s*cước',
    r'Doanh\s*thu\s*cước',
    r'DoanhThu.*\.xlsx$',  # Thêm mẫu DoanhThu
]

def is_dsc_file(filename: str) -> bool:
    """
    Kiểm tra xem file có phải là file đối soát cước không
    """
    # Chuyển filename thành lowercase để so sánh không phân biệt chữ hoa/thường
    filename_lower = filename.lower()
    
    # Kiểm tra các mẫu regex
    for pattern in DSC_PATTERNS:
        if re.search(pattern, filename, re.IGNORECASE):
            return True
    
    return False

def extract_month_year(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Trích xuất thông tin tháng và năm từ tên file
    """
    filename = os.path.basename(filename)
    
    # Kiểm tra mẫu "T1_2025" hoặc "T1-2025" hoặc "T1/2025"
    pattern1 = r'[Tt](\d{1,2})[\s_\-\/](\d{4})'
    matches = re.search(pattern1, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Kiểm tra mẫu "thang 1 nam 2025" hoặc "thang1/2025"
    pattern2 = r'[Tt]h[aá]ng\s*(\d{1,2})[\s_\-\/]*(?:[Nn][aă]m\s*)?(\d{4})'
    matches = re.search(pattern2, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Kiểm tra mẫu "1-2025" hoặc "1/2025" hoặc "01_2025"
    pattern3 = r'(\d{1,2})[\-\/\_](\d{4})(?:\.|$)'
    matches = re.search(pattern3, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Kiểm tra mẫu "_01_2025" hoặc "_1_2025"
    pattern4 = r'_(\d{1,2})_(\d{4})'
    matches = re.search(pattern4, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    return None, None

def extract_partners(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Trích xuất thông tin đối tác từ tên file
    """
    filename = os.path.basename(filename)
    
    # Mẫu HTC_TO_ITEL hoặc HTC TO ITEL
    pattern_to = r'([A-Za-z0-9]+)[_\-\s]+[Tt][Oo][_\-\s]+([A-Za-z0-9]+)'
    matches = re.search(pattern_to, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Mẫu cụ thể: Doi soat cuoc_HTC_ITEL_T1_2025.xlsx
    pattern_dsc = r'Doi\s*soat\s*cuoc[_\-\s]+([A-Za-z0-9]+)[_\-\s]+([A-Za-z0-9]+)'
    matches = re.search(pattern_dsc, filename, re.IGNORECASE)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Kiểm tra mẫu phổ biến: HTC_<ĐỐI TÁC>_<THÔNG TIN KHÁC>
    pattern_partner = r'([A-Za-z0-9]+)[_\-]([A-Za-z0-9]+)'
    matches = re.search(pattern_partner, filename)
    if matches:
        partner1, partner2 = matches.groups()
        
        # Thường HTC là mạng nguồn
        if partner1.upper() == "HTC":
            return partner1, partner2
        elif partner2.upper() == "HTC":
            return partner2, partner1
        
        # Nếu không có HTC, giả định partner1 là mạng nguồn
        return partner1, partner2
    
    return None, None

def process_excel_file(file_path: str) -> DoiSoatCuoc:
    """
    Xử lý đơn giản file đối soát cước
    """
    try:
        file_name = os.path.basename(file_path)
        
        # Đọc file Excel
        df = pd.read_excel(file_path)
        print(f"Đã đọc file Excel: {file_name} - {df.shape[0]} hàng, {df.shape[1]} cột")
        
        # Trích xuất thông tin cơ bản
        month, year = extract_month_year(file_name)
        thang_doi_soat = f"{month}/{year}" if month and year else "Không xác định"
        
        tu_mang, den_doi_tac = extract_partners(file_name)
        tu_mang = tu_mang or "HTC"
        den_doi_tac = den_doi_tac or "KHÔNG XÁC ĐỊNH"
        
        # Tạo dữ liệu mẫu
        du_lieu = []
        
        # Lấy các cột và dòng quan trọng
        # Tìm cột dữ liệu
        df_columns = df.columns.tolist()
        headers_text = " ".join([str(col).upper() for col in df_columns])
        
        # Chỉ xử lý cơ bản một số dòng dữ liệu
        for i in range(8, min(20, df.shape[0])):
            try:
                # Lấy các giá trị cơ bản
                dau_so = str(df.iloc[i, 1]) if not pd.isna(df.iloc[i, 1]) else ""
                
                # Bỏ qua dòng không có đầu số
                if not dau_so or not re.match(r'\d+', dau_so.strip()):
                    continue
                
                # Lấy giá trị thanh toán (thường ở cột cuối cùng)
                tong_thanh_toan = float(df.iloc[i, df.shape[1]-1]) if not pd.isna(df.iloc[i, df.shape[1]-1]) else 0
                
                # Tạo đối tượng dòng dữ liệu
                row_data = DauSoDichVuCuoc(
                    stt=i-7,
                    dau_so=dau_so.strip(),
                    tong_thanh_toan=tong_thanh_toan
                )
                
                du_lieu.append(row_data)
            except Exception as e:
                logger.warning(f"Lỗi khi xử lý dòng {i}: {str(e)}")
        
        # Tìm phần tổng kết
        tong_cong_tien = 0
        cong_tien_dich_vu = 0
        tien_thue_gtgt = 0
        
        # Quét các dòng cuối để tìm tổng tiền
        for i in range(df.shape[0] - 10, df.shape[0]):
            if i < 0:
                continue
                
            try:
                cell_value = str(df.iloc[i, 0]).strip() if not pd.isna(df.iloc[i, 0]) else ""
                
                if "TỔNG CỘNG" in cell_value.upper() or "TONG CONG" in cell_value.upper():
                    tong_cong_tien = float(df.iloc[i, df.shape[1]-1]) if not pd.isna(df.iloc[i, df.shape[1]-1]) else 0
                    
                if "THUẾ" in cell_value.upper() or "THUE" in cell_value.upper() or "VAT" in cell_value.upper() or "GTGT" in cell_value.upper():
                    tien_thue_gtgt = float(df.iloc[i, df.shape[1]-1]) if not pd.isna(df.iloc[i, df.shape[1]-1]) else 0
            except:
                pass
        
        # Nếu không tìm thấy, tính từ dữ liệu
        if tong_cong_tien == 0:
            # Tính từ các dòng dữ liệu
            cong_tien_dich_vu = sum(item.tong_thanh_toan for item in du_lieu)
            tien_thue_gtgt = round(cong_tien_dich_vu * 0.1, 2)  # Giả định VAT 10%
            tong_cong_tien = cong_tien_dich_vu + tien_thue_gtgt
        
        # Tạo đối tượng tổng kết
        tong_ket = TongKetCuoc(
            cong_tien_dich_vu=cong_tien_dich_vu,
            tien_thue_gtgt=tien_thue_gtgt,
            tong_cong_tien=tong_cong_tien
        )
        
        # Tạo đối tượng DoiSoatCuoc cuối cùng
        return DoiSoatCuoc(
            thang_doi_soat=thang_doi_soat,
            tu_mang=tu_mang,
            den_doi_tac=den_doi_tac,
            file_name=file_name,
            du_lieu=du_lieu,
            tong_ket=tong_ket
        )
        
    except Exception as e:
        logger.error(f"Lỗi khi xử lý file: {str(e)}")
        raise ValueError(f"Không thể xử lý file đối soát cước: {str(e)}")

def test_dsc_file(file_path: str):
    """Hàm test đơn giản cho file đối soát cước"""
    try:
        # Kiểm tra file
        if not os.path.exists(file_path):
            print(f"❌ File không tồn tại: {file_path}")
            return
            
        file_name = os.path.basename(file_path)
        print(f"Đang kiểm tra file: {file_name}")
        
        # Kiểm tra định dạng file
        if not is_dsc_file(file_name):
            print(f"❌ File không được nhận dạng là file đối soát cước: {file_name}")
            return
            
        print(f"✅ File được nhận dạng là file đối soát cước")
        
        # Trích xuất thông tin
        month, year = extract_month_year(file_name)
        print(f"Tháng/Năm: {month}/{year}")
        
        tu_mang, den_doi_tac = extract_partners(file_name)
        print(f"Đối tác: {tu_mang} → {den_doi_tac}")
        
        # Xử lý file
        print("\nĐang xử lý file Excel...")
        result = process_excel_file(file_path)
        
        # Hiển thị kết quả
        print("\nKẾT QUẢ XỬ LÝ:")
        print(f"- Tháng đối soát: {result.thang_doi_soat}")
        print(f"- Từ mạng: {result.tu_mang}")
        print(f"- Đến đối tác: {result.den_doi_tac}")
        print(f"- Số đầu số dịch vụ: {len(result.du_lieu)}")
        if result.tong_ket:
            print(f"- Tổng cộng tiền: {result.tong_ket.tong_cong_tien:,.2f} VND")
        
        # Hiển thị một số đầu số dịch vụ
        if result.du_lieu:
            print("\nĐầu số dịch vụ (5 đầu tiên):")
            for i, item in enumerate(result.du_lieu[:5], 1):
                print(f"  {i}. {item.dau_so} - Tổng thanh toán: {item.tong_thanh_toan:,.2f} VND")
        
        return result
    
    except Exception as e:
        print(f"❌ Lỗi khi xử lý file: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # Sử dụng file từ tham số dòng lệnh hoặc file mẫu mặc định
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # Sử dụng file mẫu chuẩn DSC
        file_path = "/home/<USER>/Documents/Job/AuditCall/backend/BBDS/DSC/Doi soat cuoc_HTC_VADCOM_T1_2025.xlsx"
    
    # Chạy test
    test_dsc_file(file_path) 