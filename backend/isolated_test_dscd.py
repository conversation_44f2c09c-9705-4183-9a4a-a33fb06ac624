#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import re
import logging
import pandas as pd

# Cấu hình logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Classes để thay thế schemas
class CuocGoi:
    def __init__(self, thoi_gian_goi=0, cuoc=0):
        self.thoi_gian_goi = thoi_gian_goi
        self.cuoc = cuoc

class CuocThueBao:
    def __init__(self, thue_bao_thang=0, cam_ket_thang=0, tra_truoc_thang=0):
        self.thue_bao_thang = thue_bao_thang
        self.cam_ket_thang = cam_ket_thang
        self.tra_truoc_thang = tra_truoc_thang

class DauSoDichVu:
    def __init__(self, dau_so, stt=0, co_dinh_noi_hat=None, co_dinh_lien_tinh=None, 
                 di_dong=None, cuoc_1900=None, quoc_te=None, cuoc_thue_bao=None,
                 cuoc_thu_khach=0, cuoc_tra_htc=0):
        self.dau_so = dau_so
        self.stt = stt
        self.co_dinh_noi_hat = co_dinh_noi_hat or CuocGoi()
        self.co_dinh_lien_tinh = co_dinh_lien_tinh or CuocGoi()
        self.di_dong = di_dong or CuocGoi()
        self.cuoc_1900 = cuoc_1900 or CuocGoi()
        self.quoc_te = quoc_te or CuocGoi()
        self.cuoc_thue_bao = cuoc_thue_bao or CuocThueBao()
        self.cuoc_thu_khach = cuoc_thu_khach
        self.cuoc_tra_htc = cuoc_tra_htc

class TongKet:
    def __init__(self, cong_tien_dich_vu, tien_thue_gtgt, tong_cong_tien):
        self.cong_tien_dich_vu = cong_tien_dich_vu
        self.tien_thue_gtgt = tien_thue_gtgt
        self.tong_cong_tien = tong_cong_tien

class DoiSoatCoDinh:
    def __init__(self, thang_doi_soat, hop_dong_so=None, file_name="", du_lieu=None, tong_ket=None):
        self.thang_doi_soat = thang_doi_soat
        self.hop_dong_so = hop_dong_so
        self.file_name = file_name
        self.du_lieu = du_lieu or []
        self.tong_ket = tong_ket

# Các mẫu regex để nhận dạng file DSCD
DSCD_PATTERNS = [
    r'ĐS\s*CĐ',
    r'ĐS\s*CỐ\s*ĐỊNH',
    r'DS\s*cố\s*định',
    r'Đối\s*soát\s*cước\s*cố\s*định',
    r'Doi\s*soat\s*so\s*co\s*dinh',
    r'DS\s*cuoc\s*co\s*dinh',
    r'BIDV',
    r'Doctor-check',
    r'Shield',
    r'HGC'
]

def is_dscd_file(filename):
    """
    Kiểm tra xem file có phải là file đối soát cố định không
    """
    filename = os.path.basename(filename)
    print("Kiểm tra file: '{}'".format(filename))
    
    for pattern in DSCD_PATTERNS:
        match = re.search(pattern, filename, re.IGNORECASE)
        if match:
            print("Khớp với pattern: {}".format(pattern))
            return True
    
    # Các trường hợp đặc biệt
    if "thai binh duong" in filename.lower() or "thái bình dương" in filename.lower():
        print("Nhận dạng tên đặc biệt: THÁI BÌNH DƯƠNG")
        return True
        
    if "realtime" in filename.lower():
        print("Nhận dạng tên đặc biệt: Realtime")
        return True
        
    return False

def extract_month_year(filename):
    """
    Trích xuất tháng và năm từ tên file
    """
    filename = os.path.basename(filename)
    
    # Mẫu 1: T1_2025, Tháng 1-2025, Tháng 1_2025
    pattern1 = r'(T|Th[áa]ng)\s*(\d{1,2})[-_\.\s](\d{4})'
    match = re.search(pattern1, filename, re.IGNORECASE)
    if match:
        return int(match.group(2)), int(match.group(3))
    
    # Mẫu 2: 012025, 202501 (năm tháng hoặc tháng năm)
    pattern2 = r'(\d{2})(\d{4})'
    match = re.search(pattern2, filename)
    if match:
        if int(match.group(1)) <= 12:
            return int(match.group(1)), int(match.group(2))
        else:
            # Định dạng năm trước tháng sau
            year_str = match.group(1) + match.group(2)[:2]
            month_str = match.group(2)[2:]
            if int(month_str) <= 12:
                return int(month_str), int(year_str)
    
    # Mẫu 3: thang 1_2025
    pattern3 = r'thang\s*(\d{1,2})[-_\.\s](\d{4})'
    match = re.search(pattern3, filename, re.IGNORECASE)
    if match:
        return int(match.group(1)), int(match.group(2))
    
    return None, None

def process_excel_file(file_path):
    """
    Xử lý đơn giản file đối soát cố định
    """
    try:
        file_name = os.path.basename(file_path)
        
        # Đọc file Excel
        df = pd.read_excel(file_path)
        print("Đã đọc file Excel: {} - {} hàng, {} cột".format(file_name, df.shape[0], df.shape[1]))
        
        # Trích xuất thông tin cơ bản
        month, year = extract_month_year(file_name)
        thang_doi_soat = "{}/{}".format(month, year) if month and year else "Không xác định"
        
        # Tìm số hợp đồng
        hop_dong_so = None
        try:
            for i in range(5):
                if i >= df.shape[0]:
                    break
                cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                if "HỢP ĐỒNG SỐ" in cell_value.upper():
                    hop_dong_match = re.search(r'HỢP ĐỒNG SỐ\s*[.:…]\s*(\S+)', cell_value)
                    if hop_dong_match:
                        hop_dong_so = hop_dong_match.group(1)
                        break
        except:
            pass
            
        # Tạo dữ liệu mẫu
        du_lieu = []
        
        # Chỉ xử lý cơ bản một số dòng dữ liệu
        for i in range(8, min(20, df.shape[0])):
            try:
                # Cột đầu số thường là cột thứ 2 (index 1)
                dau_so = str(df.iloc[i, 1]) if df.shape[1] > 1 and not pd.isna(df.iloc[i, 1]) else ""
                
                # Bỏ qua dòng không có đầu số
                if not dau_so or not re.search(r'\d{3,}', dau_so.strip()):
                    continue
                
                # Lấy cước thu khách và trả HTC (thường ở các cột cuối)
                cuoc_thu_khach = float(df.iloc[i, min(df.shape[1]-2, 15)]) if df.shape[1] > 15 and not pd.isna(df.iloc[i, min(df.shape[1]-2, 15)]) else 0
                cuoc_tra_htc = float(df.iloc[i, min(df.shape[1]-1, 16)]) if df.shape[1] > 16 and not pd.isna(df.iloc[i, min(df.shape[1]-1, 16)]) else 0
                
                # Tạo đối tượng dòng dữ liệu
                row_data = DauSoDichVu(
                    stt=i-7,
                    dau_so=dau_so.strip(),
                    cuoc_thu_khach=cuoc_thu_khach,
                    cuoc_tra_htc=cuoc_tra_htc
                )
                
                du_lieu.append(row_data)
            except Exception as e:
                logger.warning("Lỗi khi xử lý dòng {}: {}".format(i, str(e)))
        
        # Tìm phần tổng kết
        tong_cong_tien = 0
        cong_tien_dich_vu = 0
        tien_thue_gtgt = 0
        
        # Quét các dòng cuối để tìm tổng tiền
        for i in range(df.shape[0] - 10, df.shape[0]):
            if i < 0:
                continue
                
            try:
                cell_value = str(df.iloc[i, 0]).strip() if not pd.isna(df.iloc[i, 0]) else ""
                
                if "CỘNG TIỀN" in cell_value.upper() or "TỔNG TIỀN" in cell_value.upper():
                    cong_tien_dich_vu = float(df.iloc[i, min(df.shape[1]-2, 15)]) if not pd.isna(df.iloc[i, min(df.shape[1]-2, 15)]) else 0
                    
                if "THUẾ" in cell_value.upper() or "THUE" in cell_value.upper() or "VAT" in cell_value.upper() or "GTGT" in cell_value.upper():
                    tien_thue_gtgt = float(df.iloc[i, min(df.shape[1]-2, 15)]) if not pd.isna(df.iloc[i, min(df.shape[1]-2, 15)]) else 0
                    
                if "TỔNG CỘNG" in cell_value.upper() or "TONG CONG" in cell_value.upper():
                    tong_cong_tien = float(df.iloc[i, min(df.shape[1]-2, 15)]) if not pd.isna(df.iloc[i, min(df.shape[1]-2, 15)]) else 0
            except:
                pass
        
        # Nếu không tìm thấy, tính từ dữ liệu
        if tong_cong_tien == 0:
            # Tính từ các dòng dữ liệu
            cong_tien_dich_vu = sum(item.cuoc_thu_khach for item in du_lieu)
            tien_thue_gtgt = round(cong_tien_dich_vu * 0.1, 2)  # Giả định VAT 10%
            tong_cong_tien = cong_tien_dich_vu + tien_thue_gtgt
        
        # Tạo đối tượng tổng kết
        tong_ket = TongKet(
            cong_tien_dich_vu=cong_tien_dich_vu,
            tien_thue_gtgt=tien_thue_gtgt,
            tong_cong_tien=tong_cong_tien
        )
        
        # Tạo đối tượng DoiSoatCoDinh cuối cùng
        return DoiSoatCoDinh(
            thang_doi_soat=thang_doi_soat,
            hop_dong_so=hop_dong_so,
            file_name=file_name,
            du_lieu=du_lieu,
            tong_ket=tong_ket
        )
        
    except Exception as e:
        logger.error("Lỗi khi xử lý file: {}".format(str(e)))
        raise ValueError("Không thể xử lý file đối soát cố định: {}".format(str(e)))

def test_dscd_file(file_path):
    """Hàm test đơn giản cho file đối soát cố định"""
    try:
        # Kiểm tra file
        if not os.path.exists(file_path):
            print("❌ File không tồn tại: {}".format(file_path))
            return
            
        file_name = os.path.basename(file_path)
        print("Đang kiểm tra file: {}".format(file_name))
        
        # Kiểm tra định dạng file
        if not is_dscd_file(file_name):
            print("❌ File không được nhận dạng là file đối soát cố định: {}".format(file_name))
            return
            
        print("✅ File được nhận dạng là file đối soát cố định")
        
        # Trích xuất thông tin
        month, year = extract_month_year(file_name)
        print("Tháng/Năm: {}/{}".format(month, year))
        
        # Xử lý file
        print("\nĐang xử lý file Excel...")
        result = process_excel_file(file_path)
        
        # Hiển thị kết quả
        print("\nKẾT QUẢ XỬ LÝ:")
        print("- Tháng đối soát: {}".format(result.thang_doi_soat))
        print("- Hợp đồng số: {}".format(result.hop_dong_so))
        print("- Số đầu số dịch vụ: {}".format(len(result.du_lieu)))
        if result.tong_ket:
            print("- Tổng cộng tiền: {:,.2f} VND".format(result.tong_ket.tong_cong_tien))
        
        # Hiển thị một số đầu số dịch vụ
        if result.du_lieu:
            print("\nĐầu số dịch vụ (5 đầu tiên):")
            for i, item in enumerate(result.du_lieu[:5], 1):
                print("  {}. {} - Cước thu khách: {:,.2f} VND".format(i, item.dau_so, item.cuoc_thu_khach))
        
        return result
    
    except Exception as e:
        print("❌ Lỗi khi xử lý file: {}".format(str(e)))
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # Sử dụng file từ tham số dòng lệnh hoặc file mẫu mặc định
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # Sử dụng file thật trực tiếp
        file_path = "/home/<USER>/Documents/Job/AuditCall/backend/BBDS/DSCD/Doi soat so co dinh HGC_T1_2025.xlsx"
        
        if not os.path.exists(file_path):
            print("❌ File mẫu không tồn tại: {}".format(file_path))
            sys.exit(1)
    
    # Chạy test
    test_dscd_file(file_path) 