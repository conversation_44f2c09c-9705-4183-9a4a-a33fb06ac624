import asyncio
import os
import sys
from logging.config import fileConfig

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import pool, create_engine
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config

from alembic import context

# Import all models here
from src.database.base import Base
from src.core.config import settings

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

def process_revision_directives(context, revision, directives):
    # Extract current revision numbers
    migration_script = directives[0]
    versions_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'migrations', 'versions')
    
    # Get existing migration files
    existing_files = os.listdir(versions_dir) if os.path.exists(versions_dir) else []
    existing_numbers = [
        int(f[:3]) for f in existing_files 
        if f.endswith('.py') and f[:3].isdigit()
    ]
    
    # Calculate next number
    next_number = 1 if not existing_numbers else max(existing_numbers) + 1
    
    # Format new revision id
    new_rev_id = f"{next_number:03d}"
    migration_script.rev_id = new_rev_id

def get_url():
    return str(settings.SQLALCHEMY_DATABASE_URI)

def do_run_migrations(connection: Connection) -> None:
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        compare_type=True,
        process_revision_directives=process_revision_directives
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = get_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        process_revision_directives=process_revision_directives
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = config.attributes.get("connection", None)

    if connectable is None:
        # Create engine directly using SQLAlchemy
        connectable = create_engine(
            get_url(),
            poolclass=pool.NullPool,
        )

    with connectable.connect() as connection:
        do_run_migrations(connection)


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online() 