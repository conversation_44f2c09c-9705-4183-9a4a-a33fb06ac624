import os
import sys
import logging
from src.utils.dscd_processor import process_dscd_file, is_dscd_file
from pprint import pprint

# Cấu hình logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dscd_processor(file_path):
    """Test DSCD processor với file mẫu"""
    logger.info(f"Kiểm tra file: {file_path}")
    
    # Kiểm tra file có phải là DSCD không
    if not is_dscd_file(file_path):
        logger.warning(f"File không được nhận dạng là file đối soát cố định: {file_path}")
        print(f"❌ {os.path.basename(file_path)} - KHÔNG phải file đối soát cố định")
        return
    
    try:
        # Xử lý file
        logger.info("Đang xử lý file đối soát cố định...")
        result = process_dscd_file(file_path)
        
        # In kết quả
        print(f"✅ {os.path.basename(file_path)} - X<PERSON> lý thành công")
        print(f"- Tháng đối soát: {result.thang_doi_soat}")
        print(f"- Hợp đồng số: {result.hop_dong_so}")
        print(f"- Số đầu số dịch vụ: {len(result.du_lieu)}")
        print(f"- Tổng cộng tiền: {result.tong_ket.tong_cong_tien:,.2f} VND")
        
        # 5 đầu số đầu tiên
        if result.du_lieu:
            print("\nĐầu số dịch vụ (5 đầu tiên):")
            for i, dau_so in enumerate(result.du_lieu[:5]):
                print(f"  {i+1}. {dau_so.dau_so} - Cước thu khách: {dau_so.cuoc_thu_khach:,.2f}")
        
        return result
    
    except Exception as e:
        logger.error(f"Lỗi khi xử lý file: {str(e)}")
        print(f"❌ {os.path.basename(file_path)} - LỖI: {str(e)}")
        return None

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        test_dscd_processor(file_path)
    else:
        print("Vui lòng cung cấp đường dẫn đến file cần kiểm tra") 