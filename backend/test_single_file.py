# -*- coding: utf-8 -*-
import os
import sys
import logging
import pandas as pd

# Thêm đường dẫn gốc vào sys.path để import module
# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) # Removed

# Use imports relative to the /app directory (project root inside container)
from src.utils.dsc_processor import is_dsc_file, process_dsc_file
from src.utils.dscd_processor import is_dscd_file, process_dscd_file
try:
    from src.utils.dst_1800_1900_processor import is_dst_1800_1900_file, process_dst_1800_1900_file
except ImportError:
    print("WARNING: dst_1800_1900_processor không khả dụng, sẽ bỏ qua processor này")
    is_dst_1800_1900_file = lambda x: False
    process_dst_1800_1900_file = lambda x: None

# Cấu hình logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def display_file_info(file_path):
    """Hiển thị thông tin cơ bản về file"""
    file_size = os.path.getsize(file_path) / 1024  # Kích thước KB
    file_name = os.path.basename(file_path)
    print(f"Thông tin file: {file_name}")
    print(f"Kích thước: {file_size:.2f} KB")
    
    # Kiểm tra xem có phải file Excel không
    if file_path.endswith(('.xlsx', '.xls')):
        try:
            df = pd.read_excel(file_path)
            print(f"Số hàng: {df.shape[0]}, Số cột: {df.shape[1]}")
        except Exception as e:
            print(f"Không thể đọc file Excel: {str(e)}")

def test_file(file_path):
    """Test tất cả processor với file"""
    try:
        file_name = os.path.basename(file_path)
        display_file_info(file_path)
        print(f"\n{'-'*60}")
        
        # Test DSC processor
        print("\n1. DSC processor:")
        if is_dsc_file(file_name):
            try:
                print("   Đang xử lý file đối soát cước...")
                result = process_dsc_file(file_path)
                
                # In kết quả
                print(f"   ✅ Xử lý thành công")
                print(f"   - Tháng đối soát: {result.thang_doi_soat}")
                print(f"   - Từ mạng: {result.tu_mang}")
                print(f"   - Đến đối tác: {result.den_doi_tac}")
                print(f"   - Số đầu số dịch vụ: {len(result.du_lieu)}")
                print(f"   - Tổng cộng tiền: {result.tong_ket.tong_cong_tien:,.2f} VND")
                
                # 3 đầu số đầu tiên
                if result.du_lieu:
                    print("\n   Đầu số dịch vụ (3 đầu tiên):")
                    for i, dau_so in enumerate(result.du_lieu[:3]):
                        print(f"     {i+1}. {dau_so.dau_so} - Tổng thanh toán: {dau_so.tong_thanh_toan:,.2f}")
            except Exception as e:
                print(f"   ❌ Lỗi khi xử lý file: {str(e)}")
        else:
            print("   ⏩ Bỏ qua: Không phải file đối soát cước")
        
        # Test DSCD processor
        print("\n2. DSCD processor:")
        if is_dscd_file(file_name):
            try:
                print("   Đang xử lý file đối soát cố định...")
                result = process_dscd_file(file_path)
                
                # In kết quả
                print(f"   ✅ Xử lý thành công")
                print(f"   - Tháng đối soát: {result.thang_doi_soat}")
                print(f"   - Hợp đồng số: {result.hop_dong_so}")
                print(f"   - Số đầu số dịch vụ: {len(result.du_lieu)}")
                print(f"   - Tổng cộng tiền: {result.tong_ket.tong_cong_tien:,.2f} VND")
                
                # 3 đầu số đầu tiên
                if result.du_lieu:
                    print("\n   Đầu số dịch vụ (3 đầu tiên):")
                    for i, dau_so in enumerate(result.du_lieu[:3]):
                        print(f"     {i+1}. {dau_so.dau_so} - Cước thu khách: {dau_so.cuoc_thu_khach:,.2f}")
            except Exception as e:
                print(f"   ❌ Lỗi khi xử lý file: {str(e)}")
        else:
            print("   ⏩ Bỏ qua: Không phải file đối soát cố định")
        
        # Test DST 1800/1900 processor
        print("\n3. DST 1800/1900 processor:")
        if is_dst_1800_1900_file is not None and is_dst_1800_1900_file(file_name):
            try:
                print("   Đang xử lý file đối soát 1800/1900...")
                result = process_dst_1800_1900_file(file_path)
                
                # In kết quả
                print(f"   ✅ Xử lý thành công")
                print(f"   - Tháng đối soát: {result.thang_doi_soat}")
                print(f"   - Từ mạng: {result.tu_mang}")
                print(f"   - Đến đối tác: {result.den_doi_tac}")
                print(f"   - Loại dịch vụ: {result.loai_dich_vu}")
                print(f"   - Hợp đồng số: {result.hop_dong_so}")
                print(f"   - Số nhóm dịch vụ: {len(result.nhom_dich_vu)}")
                
                # Thông tin thanh toán
                if result.thanh_toan:
                    print(f"   - Doanh thu bên A thanh toán: {result.thanh_toan.doanh_thu_ben_a_thanh_toan:,.2f}")
                    print(f"   - Doanh thu bên B thanh toán: {result.thanh_toan.doanh_thu_ben_b_thanh_toan:,.2f}")
                    print(f"   - Sau bù trừ: {result.thanh_toan.sau_bu_tru:,.2f}")
            except Exception as e:
                print(f"   ❌ Lỗi khi xử lý file: {str(e)}")
        else:
            print("   ⏩ Bỏ qua: Không phải file đối soát 1800/1900")
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        test_file(file_path)
    else:
        # File mẫu
        test_file("/home/<USER>/Documents/Job/AuditCall/backend/BBDS/DoanhThu_HTC_VNM_012025.xlsx") 