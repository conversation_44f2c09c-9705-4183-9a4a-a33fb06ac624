#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
import importlib.util

# Cấu hình logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def import_module_from_file(file_path):
    """Nhập module trực tiếp từ file"""
    module_name = os.path.basename(file_path).replace('.py', '')
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

def test_dsc_processor(file_path):
    """Test DSC processor với file mẫu"""
    try:
        # Nhập trực tiếp module từ file
        dsc_processor_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                         'src/utils/dsc_processor.py')
        print(f"<PERSON>ang tìm module tại: {dsc_processor_path}")
        
        if not os.path.exists(dsc_processor_path):
            # Thử đường dẫn khác
            dsc_processor_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                             '../backend/src/utils/dsc_processor.py')
            print(f"Thử đường dẫn khác: {dsc_processor_path}")
        
        if not os.path.exists(dsc_processor_path):
            print(f"❌ Không tìm thấy file module: {dsc_processor_path}")
            return
            
        dsc_module = import_module_from_file(dsc_processor_path)
        
        # Kiểm tra file
        file_name = os.path.basename(file_path)
        print(f"Kiểm tra file: {file_name}")
        
        # Kiểm tra nếu là DSC file
        if not dsc_module.is_dsc_file(file_name):
            print(f"❌ {file_name} - KHÔNG phải file đối soát cước")
            return None
        
        # Xử lý file
        print("Đang xử lý file đối soát cước...")
        result = dsc_module.process_dsc_file(file_path)
        
        # In thông tin
        print(f"✅ Xử lý thành công")
        print(f"- Tháng đối soát: {result.thang_doi_soat}")
        print(f"- Từ mạng: {result.tu_mang}")
        print(f"- Đến đối tác: {result.den_doi_tac}")
        print(f"- Số đầu số dịch vụ: {len(result.du_lieu)}")
        print(f"- Tổng cộng tiền: {result.tong_ket.tong_cong_tien:,.2f}")
        
        # 3 đầu số đầu tiên
        if result.du_lieu:
            print("\nĐầu số dịch vụ (3 đầu tiên):")
            for i, dau_so in enumerate(result.du_lieu[:3]):
                print(f"  {i+1}. {dau_so.dau_so} - Tổng thanh toán: {dau_so.tong_thanh_toan:,.2f}")
        
        return result
    
    except Exception as e:
        logger.error(f"Lỗi: {str(e)}")
        print(f"❌ Lỗi: {str(e)}")
        return None

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # File mẫu mặc định
        file_path = "/home/<USER>/Documents/Job/AuditCall/backend/BBDS/DoanhThu_HTC_VNM_012025.xlsx"
    
    # Kiểm tra file
    print(f"Kiểm tra file: {file_path}")
    if not os.path.exists(file_path):
        print(f"❌ File không tồn tại: {file_path}")
        sys.exit(1)
        
    # Chạy test
    test_dsc_processor(file_path) 