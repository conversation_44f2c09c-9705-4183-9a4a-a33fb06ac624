import os
import re
import pandas as pd
import json

def detect_service_sections(df):
    """Phát hiện các đoạn dịch vụ trong file Excel"""
    service_sections = []
    for i in range(df.shape[0]):
        for j in range(min(5, df.shape[1])):
            if j >= df.shape[1]:
                continue
                
            cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
            
            # Mẫu tiêu đề đặc biệt của BB_1900_1800
            if re.search(r'(I{1,3})\.\s*\w+\s*sử\s*dụng\s*(1[89]00)', cell_value, re.IGNORECASE):
                match = re.search(r'(I{1,3})\.\s*\w+\s*sử\s*dụng\s*(1[89]00)', cell_value, re.IGNORECASE)
                section = match.group(2)
                service_sections.append({"row": i, "col": j, "type": section, "format": "special"})
            # Mẫu tiêu đề chuẩn
            elif re.search(r'(I{1,3})\.\s*Dịch\s*vụ\s*(1[89]00)', cell_value, re.IGNORECASE):
                match = re.search(r'(I{1,3})\.\s*Dịch\s*vụ\s*(1[89]00)', cell_value, re.IGNORECASE)
                section = match.group(2)
                service_sections.append({"row": i, "col": j, "type": section, "format": "standard"})
            # Tìm kiếm các chuỗi như "DỊCH VỤ 19002xxx"
            elif re.search(r'DỊCH\s*VỤ\s*(1[89]00\d+)', cell_value, re.IGNORECASE):
                match = re.search(r'DỊCH\s*VỤ\s*(1[89]00\d+)', cell_value, re.IGNORECASE)
                service_num = match.group(1)
                service_type = service_num[:4]  # Lấy 1800 hoặc 1900
                service_sections.append({"row": i, "col": j, "type": service_type, "format": "specific"})
            # Tìm kiếm các chuỗi như "TỪ... ĐẾN SỐ DỊCH VỤ 1900 CỦA HTC"
            elif re.search(r'ĐẾN\s+SỐ\s+DỊCH\s+VỤ\s+(1[89]00)\s+CỦA', cell_value, re.IGNORECASE):
                match = re.search(r'ĐẾN\s+SỐ\s+DỊCH\s+VỤ\s+(1[89]00)\s+CỦA', cell_value, re.IGNORECASE)
                service_type = match.group(1)
                service_sections.append({"row": i, "col": j, "type": service_type, "format": "vnm"})
    
    return service_sections

def extract_service_groups(df, section_info):
    """Trích xuất các nhóm dịch vụ từ một đoạn dịch vụ"""
    service_groups = []
    start_row = section_info["row"]
    service_type = section_info["type"]
    
    # Tìm vị trí bảng dữ liệu - tìm dòng chứa "STT"
    table_start = None
    for i in range(start_row, min(start_row + 15, df.shape[0])):
        for j in range(min(5, df.shape[1])):
            cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
            if cell_value.strip().upper() == "STT":
                table_start = i + 1  # Bỏ qua header
                break
        if table_start is not None:
            break
    
    if table_start is None:
        return service_groups
    
    # Tìm cột chứa "Nhóm dịch vụ"
    group_col = None
    for j in range(min(5, df.shape[1])):
        if table_start < df.shape[0] and j < df.shape[1]:
            cell_value = str(df.iloc[table_start, j]) if not pd.isna(df.iloc[table_start, j]) else ""
            if "NHÓM" in cell_value.upper() and "DỊCH VỤ" in cell_value.upper():
                group_col = j
                break
    
    if group_col is None:
        # Thử tìm cột khác có thể chứa thông tin dịch vụ
        for j in range(min(5, df.shape[1])):
            if table_start < df.shape[0] and j < df.shape[1]:
                cell_value = str(df.iloc[table_start, j]) if not pd.isna(df.iloc[table_start, j]) else ""
                if any(keyword in cell_value.upper() for keyword in ["DỊCH VỤ", "SỐ", "NHÓM"]):
                    group_col = j
                    break
        
        if group_col is None:
            group_col = 1  # Mặc định là cột 1 nếu không tìm thấy
    
    # Đọc dữ liệu các nhóm dịch vụ
    i = table_start + 1  # Bỏ qua dòng header
    while i < df.shape[0]:
        # Kiểm tra xem có phải đã đến phần tiếp theo 
        if i < df.shape[0]:
            row_content = " ".join([str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else "" for j in range(min(5, df.shape[1]))])
            if "DOANH THU" in row_content.upper() or "TỔNG CỘNG" in row_content.upper():
                break
        
        # Lấy giá trị STT và nhóm dịch vụ
        stt_val = None
        for j in range(min(2, df.shape[1])):
            cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
            if cell_value.strip() and re.match(r'^\d+$', cell_value.strip()):
                stt_val = cell_value.strip()
                break
        
        # Lấy giá trị nhóm dịch vụ
        group_val = ""
        if i < df.shape[0] and group_col < df.shape[1]:
            group_val = str(df.iloc[i, group_col]) if not pd.isna(df.iloc[i, group_col]) else ""
        
        # Xử lý trường hợp đặc biệt của BB_1900_1800
        if stt_val is not None and section_info["format"] == "special":
            # Tìm kiếm "Nhóm giá" trong tất cả các cột
            for j in range(min(5, df.shape[1])):
                if j < df.shape[1]:
                    cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                    if "Nhóm giá" in cell_value:
                        group_val = cell_value
                        break
        
        if stt_val is not None and group_val.strip():
            # Xử lý các trường hợp đặc biệt
            if service_type == "1900" and "Nhóm giá" in group_val:
                # Nhóm giá đặc biệt trong 1900
                group_match = re.search(r'Nhóm\s+giá\s+(\d+)', group_val)
                if group_match:
                    price_group = group_match.group(1)
                    
                    if price_group == "909":
                        service_groups.append({
                            "name": group_val,
                            "mapped_numbers": ["1900909xxx"],
                            "has_revenue_share": True
                        })
                    elif price_group == "2727":
                        service_groups.append({
                            "name": group_val,
                            "mapped_numbers": ["19002727xx"],
                            "has_revenue_share": True
                        })
                    else:
                        service_groups.append({
                            "name": group_val,
                            "mapped_numbers": [f"1900{price_group}xx"],
                            "has_revenue_share": True
                        })
            elif service_type == "1800" and "1800" in group_val:
                # Dịch vụ 1800 đơn giản
                service_groups.append({
                    "name": group_val,
                    "mapped_numbers": ["1800xxxx"],
                    "has_revenue_share": False  # Giả định 1800 không có doanh thu ăn chia
                })
            elif service_type == "1800" and section_info["format"] == "special":
                # Xử lý đặc biệt cho 1800 trong BB_1900_1800
                service_groups.append({
                    "name": "1800",
                    "mapped_numbers": ["1800xxxx"],
                    "has_revenue_share": False
                })
            elif service_type == "1900" and section_info["format"] == "special":
                # Tìm nhóm giá trong các cột
                price_group = None
                for j in range(min(7, df.shape[1])):
                    if j < df.shape[1]:
                        cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                        if "giá" in cell_value.lower():
                            match = re.search(r'(\d+)', cell_value)
                            if match:
                                price_group = match.group(1)
                                break
                
                if price_group:
                    service_groups.append({
                        "name": f"Nhóm giá {price_group}",
                        "mapped_numbers": [f"1900{price_group}xxx"],
                        "has_revenue_share": True
                    })
                else:
                    service_groups.append({
                        "name": group_val,
                        "original_text": group_val,
                        "has_revenue_share": True
                    })
            else:
                # Trường hợp thông thường
                service_groups.append({
                    "name": group_val,
                    "original_text": group_val,
                    "has_revenue_share": True
                })
        
        i += 1
    
    return service_groups

def check_revenue_sharing(df, section_info):
    """Kiểm tra xem dịch vụ có doanh thu ăn chia không"""
    
    has_revenue_share = False
    revenue_keywords = ["DOANH THU", "BÙ TRỪ", "THANH TOÁN"]
    start_row = section_info["row"]
    service_type = section_info["type"]
    
    # Tìm trong 20 dòng tiếp theo từ dòng dịch vụ
    for i in range(start_row, min(start_row + 20, df.shape[0])):
        for j in range(df.shape[1]):
            cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
            # Kiểm tra các từ khóa liên quan đến doanh thu
            if any(keyword in cell_value.upper() for keyword in revenue_keywords):
                has_revenue_share = True
                break
    
    # Kiểm tra đặc biệt dựa trên loại dịch vụ
    if service_type == "1900":
        # Dịch vụ 1900 thường có doanh thu ăn chia
        return True
    elif service_type == "1800":
        # Cần kiểm tra kỹ hơn cho 1800
        return has_revenue_share
    
    return has_revenue_share

def analyze_file_structure(file_path):
    """Phân tích cấu trúc file Excel"""
    try:
        df = pd.read_excel(file_path, header=None)
        
        # Tìm kiếm các từ khóa quan trọng
        keywords = {
            "service_types": [],  # 1800, 1900
            "title_rows": [],     # Dòng chứa tiêu đề
            "table_headers": [],  # Dòng chứa header bảng
            "nhom_gia": False     # Có chứa "Nhóm giá" không
        }
        
        for i in range(min(30, df.shape[0])):
            for j in range(min(10, df.shape[1])):
                if j < df.shape[1]:
                    cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                    
                    # Tìm loại dịch vụ
                    service_match = re.search(r'(1[89]00)', cell_value)
                    if service_match and service_match.group(1) not in keywords["service_types"]:
                        keywords["service_types"].append(service_match.group(1))
                    
                    # Tìm tiêu đề
                    if re.search(r'(I{1,3})\.\s*', cell_value):
                        keywords["title_rows"].append(i)
                    
                    # Tìm header bảng
                    if cell_value.strip().upper() == "STT":
                        keywords["table_headers"].append(i)
                    
                    # Kiểm tra "Nhóm giá"
                    if "Nhóm giá" in cell_value:
                        keywords["nhom_gia"] = True
        
        return keywords
    except Exception as e:
        print(f"Lỗi khi phân tích cấu trúc file: {str(e)}")
        return None

def find_service_numbers_in_file(df, service_type):
    """Tìm kiếm các số dịch vụ trong file Excel"""
    service_numbers = []
    
    # Mẫu số dịch vụ 1800/1900
    pattern = re.compile(rf'{service_type}\d+')
    
    # Mẫu khoảng số dịch vụ 
    range_pattern = re.compile(rf'{service_type}\d+\s*-\s*{service_type}\d+')
    
    # Mẫu cho định dạng XX
    xx_pattern = re.compile(rf'{service_type}\d+xx')
    
    for i in range(df.shape[0]):
        for j in range(df.shape[1]):
            if j < df.shape[1]:
                cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                
                # Xử lý định dạng XX
                xx_matches = xx_pattern.findall(cell_value)
                for match in xx_matches:
                    if match not in service_numbers:
                        service_numbers.append(match)
                
                # Tìm khoảng số dịch vụ
                range_matches = range_pattern.findall(cell_value)
                for match in range_matches:
                    if match not in service_numbers:
                        service_numbers.append(match)
                
                # Tìm số dịch vụ đơn lẻ
                matches = pattern.findall(cell_value)
                for match in matches:
                    # Loại bỏ các số đã xuất hiện trong khoảng hoặc định dạng XX
                    if all(match not in range_match for range_match in range_matches) and all(match not in xx_match for xx_match in xx_matches):
                        if match not in service_numbers:
                            service_numbers.append(match)
    
    return service_numbers

def extract_service_numbers_from_vnm(df):
    """Trích xuất số dịch vụ từ file VNM có cấu trúc cụ thể"""
    service_numbers = []
    
    # Tìm cột chứa nhóm dịch vụ
    group_col = None
    for i in range(min(10, df.shape[0])):
        for j in range(min(5, df.shape[1])):
            if j < df.shape[1]:
                cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                if "NHÓM DỊCH VỤ" in cell_value.upper():
                    group_col = j
                    break
        if group_col is not None:
            break
    
    if group_col is None:
        return service_numbers
    
    # Đọc các giá trị từ cột dịch vụ
    for i in range(group_col + 1, df.shape[0]):
        if i < df.shape[0] and group_col < df.shape[1]:
            cell_value = str(df.iloc[i, group_col]) if not pd.isna(df.iloc[i, group_col]) else ""
            
            if cell_value.strip() and any(x in cell_value for x in ["1900", "1800"]):
                # Xử lý các mẫu XX
                if "xx" in cell_value.lower():
                    service_numbers.append(cell_value.strip())
                # Xử lý khoảng số với dấu gạch nối
                elif "-" in cell_value:
                    parts = cell_value.split(";")
                    for part in parts:
                        part = part.strip()
                        if part:
                            service_numbers.append(part)
                # Xử lý danh sách với dấu chấm phẩy
                elif ";" in cell_value:
                    parts = cell_value.split(";")
                    for part in parts:
                        part = part.strip()
                        if part:
                            service_numbers.append(part)
                else:
                    service_numbers.append(cell_value.strip())
    
    return service_numbers

def process_special_format_file(file_path):
    """Xử lý file có định dạng đặc biệt"""
    try:
        # Đọc file Excel
        df = pd.read_excel(file_path, header=None)
        
        print(f"File: {os.path.basename(file_path)}")
        print(f"Shape: {df.shape}")
        
        # Phân tích cấu trúc file
        structure = analyze_file_structure(file_path)
        if structure:
            print(f"Loại dịch vụ phát hiện: {', '.join(structure['service_types'])}")
            print(f"Số dòng tiêu đề: {len(structure['title_rows'])}")
            print(f"Có nhóm giá: {'Có' if structure['nhom_gia'] else 'Không'}")
        
        # Tìm các đoạn dịch vụ
        service_sections = detect_service_sections(df)
        print(f"Số đoạn dịch vụ phát hiện: {len(service_sections)}")
        for i, section in enumerate(service_sections):
            print(f"  Đoạn {i+1}: {section['type']} (Dòng {section['row']})")
        
        result = {
            "file_name": os.path.basename(file_path),
            "services": []
        }
        
        # Kiểm tra nếu là file VNM đặc biệt
        is_vnm_file = "vnm" in os.path.basename(file_path).lower()
        
        # Xử lý từng đoạn dịch vụ
        for section in service_sections:
            # Kiểm tra doanh thu ăn chia
            has_revenue = check_revenue_sharing(df, section)
            
            # Trích xuất các nhóm dịch vụ
            service_groups = extract_service_groups(df, section)
            
            # Thêm vào kết quả
            result["services"].append({
                "service_type": section["type"],
                "has_revenue_sharing": has_revenue,
                "format": section.get("format", "unknown"),
                "groups": service_groups
            })
        
        # Nếu là file VNM đặc biệt, xử lý riêng
        if is_vnm_file and len(service_sections) == 0:
            vnm_numbers = extract_service_numbers_from_vnm(df)
            if vnm_numbers:
                result["services"].append({
                    "service_type": "1900",  # Giả định là 1900 cho VNM
                    "has_revenue_sharing": True,
                    "format": "vnm",
                    "groups": [{"name": num, "original_text": num, "has_revenue_share": True} for num in vnm_numbers]
                })
        
        # Nếu không tìm thấy đoạn dịch vụ nhưng biết có dịch vụ từ cấu trúc
        if len(service_sections) == 0 and len(result["services"]) == 0 and structure and structure["service_types"]:
            for service_type in structure["service_types"]:
                # Tìm kiếm dữ liệu dịch vụ trong file
                service_numbers = find_service_numbers_in_file(df, service_type)
                
                if service_numbers:
                    result["services"].append({
                        "service_type": service_type,
                        "has_revenue_sharing": True,
                        "format": "extracted",
                        "groups": [{"name": num, "original_text": num, "has_revenue_share": True} for num in service_numbers]
                    })
        
        return result
    except Exception as e:
        print(f"Lỗi: {str(e)}")
        return {
            "file_name": os.path.basename(file_path),
            "error": str(e)
        }

# Chạy thử với file đặc biệt
if __name__ == "__main__":
    # Xử lý file đặc biệt
    special_file = "BBDS/1800_1900/1/BB_1900_1800 MBC den HTC 202501.xlsx"
    # Xử lý file chuẩn để so sánh
    standard_file = "BBDS/1800_1900/HTC_ITEL_1900_12025.xlsx"
    # File VNM đặc biệt
    vnm_file = "BBDS/1800_1900/1/18001900_VNM_HTC_012025.xlsx"
    
    if os.path.exists(special_file):
        print("\n=== XỬ LÝ FILE ĐẶC BIỆT MBC ===")
        special_result = process_special_format_file(special_file)
        print("\nKết quả chi tiết:")
        print(json.dumps(special_result, indent=2, ensure_ascii=False))
    else:
        print(f"File không tồn tại: {special_file}")
    
    if os.path.exists(standard_file):
        print("\n=== XỬ LÝ FILE CHUẨN ITEL ===")
        standard_result = process_special_format_file(standard_file)
        print("\nKết quả chi tiết:")
        print(json.dumps(standard_result, indent=2, ensure_ascii=False))
    else:
        print(f"File không tồn tại: {standard_file}")
        
    if os.path.exists(vnm_file):
        print("\n=== XỬ LÝ FILE ĐẶC BIỆT VNM ===")
        vnm_result = process_special_format_file(vnm_file)
        print("\nKết quả chi tiết:")
        print(json.dumps(vnm_result, indent=2, ensure_ascii=False))
    else:
        print(f"File không tồn tại: {vnm_file}") 