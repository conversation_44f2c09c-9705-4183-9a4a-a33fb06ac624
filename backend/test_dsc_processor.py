import os
import sys
import logging
from src.utils.dsc_processor import process_dsc_file, is_dsc_file
from pprint import pprint

# Cấu hình logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dsc_processor(file_path):
    """Test DSC processor với file mẫu"""
    logger.info(f"Kiểm tra file: {file_path}")
    
    # Kiểm tra file có phải là DSC không
    if not is_dsc_file(file_path):
        logger.warning(f"File không được nhận dạng là file đối soát cước: {file_path}")
        print(f"❌ {os.path.basename(file_path)} - KHÔNG phải file đối soát cước")
        return
    
    try:
        # Xử lý file
        logger.info("Đang xử lý file đối soát cước...")
        result = process_dsc_file(file_path)
        
        # In kết quả
        print(f"✅ {os.path.basename(file_path)} - Xử lý thành công")
        print(f"- Th<PERSON>g đối soát: {result.thang_doi_soat}")
        print(f"- Từ mạng: {result.tu_mang}")
        print(f"- Đến đối tác: {result.den_doi_tac}")
        print(f"- Số đầu số dịch vụ: {len(result.du_lieu)}")
        print(f"- Tổng cộng tiền: {result.tong_ket.tong_cong_tien:,.2f} VND")
        
        # 5 đầu số đầu tiên
        if result.du_lieu:
            print("\nĐầu số dịch vụ (5 đầu tiên):")
            for i, dau_so in enumerate(result.du_lieu[:5]):
                print(f"  {i+1}. {dau_so.dau_so} - Tổng thanh toán: {dau_so.tong_thanh_toan:,.2f}")
        
        return result
    
    except Exception as e:
        logger.error(f"Lỗi khi xử lý file: {str(e)}")
        print(f"❌ {os.path.basename(file_path)} - LỖI: {str(e)}")
        return None

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        test_dsc_processor(file_path)
    else:
        print("Vui lòng cung cấp đường dẫn đến file cần kiểm tra") 