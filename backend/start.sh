#!/bin/bash

# Wait for database to be ready
echo "Waiting for database..."
while ! nc -z db 5432; do
  sleep 1
done
echo "Database is ready!"

# Wait a bit more to ensure database is fully initialized
sleep 5

# <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> thư mục uploads tồn tại
mkdir -p /app/uploads/call_logs
mkdir -p /app/uploads/templates
chmod -R 777 /app/uploads

# Install package in development mode
echo "Installing package in development mode..."
cd /app && pip install -e .

# Run migrations are now handled by FastAPI startup event
# echo "Running database migrations..."
# alembic upgrade head

# Create sample data (Optional, can be run manually or via API if needed)
# echo "Creating sample data..."
# python -m src.scripts.create_sample_data
# if [ $? -ne 0 ]; then
#     echo "Sample data creation failed with status: $?"
#     # Decide if failure should stop the container. For dev, maybe continue.
#     # exit 1
# fi

# Start the application
echo "Starting application..."
cd /app && uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload 