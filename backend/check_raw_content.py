import pandas as pd
import os
import sys
import re
import logging

# C<PERSON>u hình logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_raw_content(file_path: str) -> None:
    """
    Hiển thị nội dung gốc của tất cả các ô có chứa thông tin về số dịch vụ 1900/1800
    
    Args:
        file_path: Đường dẫn đến file Excel cần kiểm tra
    """
    try:
        logger.info(f"Đang phân tích file: {os.path.basename(file_path)}")
        
        # Đọc file Excel
        xls = pd.ExcelFile(file_path)
        sheet_name = xls.sheet_names[0]  # Lấy sheet đầu tiên
        df = pd.read_excel(xls, sheet_name=sheet_name, header=None)
        
        logger.info(f"Kích thước dữ liệu: {df.shape[0]} dòng x {df.shape[1]} cột")
        
        # T<PERSON><PERSON> dòng bắt đầu của bảng dịch vụ
        table_start_row = None
        for i in range(df.shape[0]):
            row_text = " ".join([str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else "" for j in range(min(5, df.shape[1]))])
            if "STT" in row_text.upper() and ("SỐ DỊCH VỤ" in row_text.upper() or "ĐẦU SỐ" in row_text.upper()):
                table_start_row = i
                logger.info(f"Tìm thấy tiêu đề bảng dịch vụ tại dòng {i+1}: {row_text}")
                break
        
        if table_start_row is None:
            logger.warning("Không tìm thấy bảng dịch vụ với tiêu đề chuẩn!")
        
        # Tìm cột chứa số dịch vụ
        service_number_col = None
        if table_start_row is not None:
            for j in range(min(10, df.shape[1])):
                if j >= df.shape[1]:
                    continue
                
                header_text = str(df.iloc[table_start_row, j]) if not pd.isna(df.iloc[table_start_row, j]) else ""
                if "SỐ DỊCH VỤ" in header_text.upper() or "ĐẦU SỐ" in header_text.upper() or "MÃ DỊCH VỤ" in header_text.upper():
                    service_number_col = j
                    logger.info(f"Tìm thấy cột số dịch vụ tại cột {j+1}: {header_text}")
                    break
        
        logger.info("\n=== CÁC SỐ DỊCH VỤ TRONG BẢNG ===")
        if table_start_row is not None and service_number_col is not None:
            # Tìm dòng kết thúc bảng
            table_end_row = None
            for i in range(table_start_row + 1, min(table_start_row + 50, df.shape[0])):
                if i >= df.shape[0]:
                    break
                    
                cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                if "TỔNG CỘNG" in cell_value.upper() or "CỘNG" in cell_value.upper():
                    table_end_row = i
                    break
            
            if table_end_row is None:
                table_end_row = min(table_start_row + 30, df.shape[0])
            
            # Trích xuất tất cả các giá trị trong cột số dịch vụ
            service_numbers = []
            for i in range(table_start_row + 1, table_end_row):
                if i >= df.shape[0] or service_number_col >= df.shape[1]:
                    continue
                    
                value = df.iloc[i, service_number_col]
                if pd.isna(value):
                    continue
                    
                value_str = str(value).strip()
                if value_str and ("1900" in value_str or "1800" in value_str):
                    logger.info(f"Dòng {i+1}: {value_str}")
                    service_numbers.append(value_str)
        
        logger.info("\n=== TẤT CẢ CÁC Ô CHỨA THÔNG TIN SỐ DỊCH VỤ ===")
        all_service_cells = []
        
        # Quét qua tất cả các ô trong file
        for i in range(df.shape[0]):
            for j in range(min(10, df.shape[1])):
                if j >= df.shape[1]:
                    continue
                    
                cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                cell_value = cell_value.strip()
                
                if not cell_value or len(cell_value) < 4:
                    continue
                
                # Tìm các ô có chứa dịch vụ 1900 hoặc 1800
                if ("1900" in cell_value or "1800" in cell_value) and (
                    "dịch vụ" not in cell_value.lower() or 
                    any(x in cell_value for x in ['>', '-', '→', '~', '–'])
                ):
                    logger.info(f"Dòng {i+1}, Cột {j+1}: {cell_value}")
                    all_service_cells.append((i, j, cell_value))
        
        # Tìm các dòng có thể chứa thông tin trừ
        logger.info("\n=== CÁC DÒNG CÓ THÔNG TIN TRỪ ===")
        for i in range(df.shape[0]):
            row_text = " ".join([str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else "" for j in range(min(10, df.shape[1]))])
            if "trừ" in row_text.lower() and ("1900" in row_text or "1800" in row_text):
                logger.info(f"Dòng {i+1}: {row_text}")
        
        logger.info("\n=== TỔNG KẾT ===")
        logger.info(f"- Tổng số ô chứa thông tin số dịch vụ: {len(all_service_cells)}")
        if table_start_row is not None and service_number_col is not None:
            logger.info(f"- Số dịch vụ trong bảng chính: {len(service_numbers)}")
        
    except Exception as e:
        logger.error(f"Lỗi khi kiểm tra file {file_path}: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        if os.path.exists(file_path):
            check_raw_content(file_path)
        else:
            logger.error(f"File {file_path} không tồn tại!")
    else:
        logger.error("Vui lòng cung cấp đường dẫn đến file Excel cần kiểm tra") 