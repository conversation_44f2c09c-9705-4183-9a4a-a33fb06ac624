import pandas as pd
import os
import sys
import re
import logging

# Cấu hình logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def normalize_service_number(text: str) -> str:
    """
    Chuẩn hóa định dạng số dịch vụ
    
    Args:
        text: Chuỗi chứa số dịch vụ
        
    Returns:
        Chuỗi số dịch vụ đã được chuẩn hóa
    """
    text = text.strip()
    
    # Thay thế các ký tự đặc biệt bằng dấu -
    text = re.sub(r'->|→|~|–|:|;', '-', text)
    
    return text

def extract_service_ranges(text: str) -> list:
    """
    Trích xuất các dải số dịch vụ từ văn bản
    
    Args:
        text: Chuỗi văn bản có thể chứa nhiều dải số dịch vụ
        
    Returns:
        Danh sách các dải số dịch vụ đã được trích xuất
    """
    # Chuẩn hóa chuỗi đầu vào
    text = normalize_service_number(text)
    
    # Phân tách thành các dòng khác nhau nếu có xuống dòng
    lines = text.split('\n')
    
    results = []
    
    for line in lines:
        # Bỏ qua các phần có từ "trừ", "sau bù trừ", v.v.
        if "trừ" in line.lower() or "sau bù trừ" in line.lower() or "thanh toán" in line.lower():
            # Nhưng vẫn tìm các dải số trong các phần này
            range_matches = re.findall(r'(\d{5,})\s*-\s*(\d{5,})', line)
            for start, end in range_matches:
                if len(start) >= 5 and start.startswith("1900"):
                    results.append(f"{start}-{end}")
            continue
        
        # Tách thành các phần bằng dấu chấm phẩy hoặc dấu phẩy
        parts = re.split(r'[;,]', line)
        
        for part in parts:
            # Tìm dải số dạng "19002xxx-19002yyy"
            range_match = re.search(r'(\d{5,})\s*-\s*(\d{5,})', part)
            if range_match:
                start = range_match.group(1)
                end = range_match.group(2)
                if len(start) >= 5 and start.startswith("1900"):
                    results.append(f"{start}-{end}")
            
            # Tìm số dịch vụ đơn lẻ dạng "19002xxx"
            single_matches = re.findall(r'(1900\d{5,})\b', part)
            for num in single_matches:
                if num not in results:
                    results.append(num)
    
    return results

def check_file_content(file_path: str) -> None:
    """
    Kiểm tra và in ra nội dung chi tiết của file Excel, tập trung vào các số dịch vụ 1900xxxx
    
    Args:
        file_path: Đường dẫn đến file Excel cần kiểm tra
    """
    try:
        logger.info(f"Đang phân tích file: {os.path.basename(file_path)}")
        
        # Đọc file Excel
        xls = pd.ExcelFile(file_path)
        sheet_name = xls.sheet_names[0]  # Lấy sheet đầu tiên
        df = pd.read_excel(xls, sheet_name=sheet_name, header=None)
        
        logger.info(f"Kích thước dữ liệu: {df.shape[0]} dòng x {df.shape[1]} cột")
        logger.info("=== TÌM CÁC NHÓM DỊCH VỤ ===")
        
        # Tìm các dòng có tiêu đề nhóm dịch vụ
        service_headers = []
        for i in range(df.shape[0]):
            cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
            if "DỊCH VỤ" in cell_value.upper() and ("1900" in cell_value or "1800" in cell_value):
                logger.info(f"Dòng {i+1}: {cell_value}")
                service_headers.append((i, cell_value))
        
        logger.info("=== TÌM CÁC TIÊU ĐỀ BẢNG DỊCH VỤ ===")
        
        # Tìm các dòng có tiêu đề bảng (STT, Số dịch vụ, v.v.)
        table_headers = []
        for i in range(df.shape[0]):
            row_content = " ".join([str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else "" for j in range(min(5, df.shape[1]))])
            if "STT" in row_content.upper() and ("SỐ DỊCH VỤ" in row_content.upper() or "ĐẦU SỐ" in row_content.upper()):
                logger.info(f"Dòng {i+1}: {row_content}")
                table_headers.append(i)
        
        logger.info("=== TẤT CẢ CÁC PHẠM VI SỐ DỊCH VỤ 1900xxxx TRONG FILE ===")
        
        all_service_ranges = []
        
        # Quét qua từng ô để tìm phạm vi số dịch vụ
        for i in range(df.shape[0]):
            for j in range(min(10, df.shape[1])):
                if j >= df.shape[1]:
                    continue
                    
                cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                
                # Bỏ qua các ô không có nội dung hoặc quá ngắn
                if len(cell_value) < 4:
                    continue
                
                # Chỉ xử lý ô có dịch vụ 1900
                if "1900" in cell_value:
                    # Tìm tất cả các phạm vi số dịch vụ từ nội dung ô
                    service_ranges = extract_service_ranges(cell_value)
                    
                    if service_ranges:
                        logger.info(f"Dòng {i+1}, Cột {j+1} - Nội dung gốc: {cell_value}")
                        logger.info(f"  Các số dịch vụ: {', '.join(service_ranges)}")
                        
                        for range_str in service_ranges:
                            if range_str not in all_service_ranges:
                                all_service_ranges.append(range_str)
        
        logger.info("=== DANH SÁCH TẤT CẢ CÁC SỐ DỊCH VỤ ĐƯỢC TÌM THẤY ===")
        
        # Lọc và hiển thị các số dịch vụ duy nhất
        unique_service_ranges = list(set(all_service_ranges))
        
        # Sắp xếp để dễ đọc
        unique_service_ranges.sort()
        
        for idx, num in enumerate(unique_service_ranges, 1):
            logger.info(f"{idx}. {num}")
        
        logger.info(f"=== TỔNG KẾT ===")
        logger.info(f"Tổng số nhóm dịch vụ: {len(service_headers)}")
        logger.info(f"Tổng số tiêu đề bảng: {len(table_headers)}")
        logger.info(f"Tổng số phạm vi số dịch vụ: {len(unique_service_ranges)}")
        
    except Exception as e:
        logger.error(f"Lỗi khi kiểm tra file {file_path}: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        if os.path.exists(file_path):
            check_file_content(file_path)
        else:
            logger.error(f"File {file_path} không tồn tại!")
    else:
        logger.error("Vui lòng cung cấp đường dẫn đến file Excel cần kiểm tra") 