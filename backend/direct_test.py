#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import pandas as pd
import importlib.util

# Đường dẫn đến file cần test
TEST_FILE = "/home/<USER>/Documents/Job/AuditCall/backend/BBDS/DoanhThu_HTC_VNM_012025.xlsx"

# Đường dẫn đến module dsc_processor
MODULE_PATH = "/home/<USER>/Documents/Job/AuditCall/backend/src/utils/dsc_processor.py"

def import_module(module_path):
    """Import module from file path"""
    module_name = os.path.basename(module_path).replace('.py', '')
    spec = importlib.util.spec_from_file_location(module_name, module_path)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)
    return module

def display_file_info(file_path):
    """Hi<PERSON><PERSON> thị thông tin cơ bản về file"""
    if not os.path.exists(file_path):
        print(f"File không tồn tại: {file_path}")
        return False
        
    print(f"Thông tin file: {os.path.basename(file_path)}")
    print(f"Kích thước: {os.path.getsize(file_path) / 1024:.2f} KB")
    
    try:
        df = pd.read_excel(file_path)
        print(f"Số hàng: {df.shape[0]}, Số cột: {df.shape[1]}")
        return True
    except Exception as e:
        print(f"Lỗi đọc file: {str(e)}")
        return False

def main():
    # Kiểm tra file test
    print("KIỂM TRA PROCESSOR ĐỐI SOÁT\n")
    
    # Kiểm tra file dữ liệu
    if not display_file_info(TEST_FILE):
        print("❌ Không thể đọc file test")
        return
    
    # Import module
    if not os.path.exists(MODULE_PATH):
        print(f"❌ Không tìm thấy module: {MODULE_PATH}")
        return
    
    try:
        # Import module
        print(f"\nĐang import module: {MODULE_PATH}")
        dsc_module = import_module(MODULE_PATH)
        
        # Test processor
        print("\nKIỂM TRA FILE BẰNG DSC PROCESSOR")
        filename = os.path.basename(TEST_FILE)
        
        # Kiểm tra định dạng file
        if dsc_module.is_dsc_file(filename):
            print("✅ File được nhận dạng là file đối soát cước")
            
            # Trích xuất thông tin từ tên file
            month, year = dsc_module.extract_month_year(filename)
            print(f"Tháng/Năm: {month}/{year}")
            
            partners = dsc_module.extract_partners(filename)
            print(f"Đối tác: {partners}")
            
            # Xử lý file
            print("\nĐang xử lý file...")
            result = dsc_module.process_dsc_file(TEST_FILE)
            
            # Hiển thị kết quả
            print("\nKẾT QUẢ XỬ LÝ:")
            print(f"- Tháng đối soát: {result.thang_doi_soat}")
            print(f"- Từ mạng: {result.tu_mang}")
            print(f"- Đến đối tác: {result.den_doi_tac}")
            print(f"- Hợp đồng số: {result.hop_dong_so}")
            print(f"- Số đầu số dịch vụ: {len(result.du_lieu)}")
            print(f"- Tổng cộng tiền: {result.tong_ket.tong_cong_tien:,.2f} VND")
            
            # Hiển thị một số đầu số dịch vụ
            if result.du_lieu:
                print("\nĐầu số dịch vụ (5 đầu tiên):")
                for i, item in enumerate(result.du_lieu[:5], 1):
                    print(f"  {i}. {item.dau_so} - Tổng thanh toán: {item.tong_thanh_toan:,.2f} VND")
            
            # Tóm tắt
            print("\nĐÁNH GIÁ:")
            print("✅ DSC Processor hoạt động tốt với file mẫu!")
            
        else:
            print("❌ File không được nhận dạng là file đối soát cước")
            
    except Exception as e:
        print(f"❌ Lỗi khi xử lý: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 