#!/usr/bin/env python3
"""
Script để chạy migration và cập nhật database để thêm các trường area_prefix và area_name
"""
import os
import sys
import subprocess
from pathlib import Path

# Đảm bảo đường dẫn chính xác
backend_dir = Path(__file__).parent.absolute()
os.chdir(backend_dir)

def run_command(command):
    """Chạy lệnh và hiển thị output"""
    print(f"Chạy lệnh: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if result.stdout:
        print(f"Output:\n{result.stdout}")
    
    if result.stderr:
        print(f"Error:\n{result.stderr}")
    
    if result.returncode != 0:
        print(f"Lỗi: Lệnh trả về mã {result.returncode}")
        sys.exit(1)
    
    return result

def main():
    # Kiểm tra xem migration file đã tồn tại chưa
    migration_file = backend_dir / "migrations" / "versions" / "003_add_area_info.py"
    if not migration_file.exists():
        print(f"Migration file không tồn tại: {migration_file}")
        print("Vui lòng đảm bảo đã tạo file migration 003_add_area_info.py")
        sys.exit(1)
    
    print("Bắt đầu cập nhật database...")
    
    # Chạy migration
    run_command("alembic upgrade head")
    
    print("Cập nhật database thành công!")
    print("\nCập nhật mã nguồn...")
    
    # Kiểm tra file tối ưu đã tồn tại chưa
    optimized_file = backend_dir / "src" / "worker" / "call_log_tasks_optimized.py"
    if not optimized_file.exists():
        print(f"File tối ưu không tồn tại: {optimized_file}")
        print("Vui lòng đảm bảo đã tạo file call_log_tasks_optimized.py")
        sys.exit(1)
    
    # Copy file tối ưu thành file chính
    original_file = backend_dir / "src" / "worker" / "call_log_tasks.py"
    backup_file = backend_dir / "src" / "worker" / "call_log_tasks.py.bak"
    
    # Tạo backup của file gốc
    run_command(f"cp {original_file} {backup_file}")
    
    # Thay thế file gốc bằng file tối ưu
    run_command(f"cp {optimized_file} {original_file}")
    
    print("Đã thay thế file call_log_tasks.py bằng phiên bản tối ưu!")
    print("Backup của file gốc được lưu tại: call_log_tasks.py.bak")
    
    print("\nHoàn tất cập nhật! Hệ thống đã sẵn sàng xử lý call logs với các trường mới.")
    print("Để khôi phục file gốc, chạy lệnh:")
    print(f"  cp {backup_file} {original_file}")

if __name__ == "__main__":
    main() 