from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import joinedload, outerjoin

from ..models.template import ReconciliationTemplate, TemplateType
from ..models.partner import Partner
from ..schemas.template import ReconciliationTemplateCreate, ReconciliationTemplateUpdate
from .base import CRUDBase

class CRUDReconciliationTemplate(
    CRUDBase[ReconciliationTemplate, ReconciliationTemplateCreate, ReconciliationTemplateUpdate]
):
    def create_with_owner(
        self, db: Session, *, obj_in: ReconciliationTemplateCreate, owner_id: int
    ) -> ReconciliationTemplate:
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data, uploaded_by=owner_id, status="pending")
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get_multi_with_filter(
        self, 
        db: Session, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        template_type: Optional[TemplateType] = None,
        is_active: Optional[bool] = None,
        status: Optional[str] = None,
        partner_id: Optional[int] = None
    ) -> List[ReconciliationTemplate]:
        query = db.query(self.model, Partner).outerjoin(
            Partner, self.model.partner_id == Partner.id
        )
        
        if template_type:
            query = query.filter(self.model.template_type == template_type)
        
        if is_active is not None:
            query = query.filter(self.model.is_active == is_active)
            
        if status:
            query = query.filter(self.model.status == status)
            
        if partner_id is not None:
            query = query.filter(self.model.partner_id == partner_id)
        
        results_with_partner = query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()
        
        templates = []
        for template, partner in results_with_partner:
            template.partner = partner
            templates.append(template)
        return templates
    
    def count_with_filter(
        self,
        db: Session,
        *,
        template_type: Optional[TemplateType] = None,
        is_active: Optional[bool] = None,
        status: Optional[str] = None,
        partner_id: Optional[int] = None
    ) -> int:
        query = db.query(self.model.id)
        
        if template_type:
            query = query.filter(self.model.template_type == template_type)
        
        if is_active is not None:
            query = query.filter(self.model.is_active == is_active)
            
        if status:
            query = query.filter(self.model.status == status)
            
        if partner_id is not None:
            query = query.filter(self.model.partner_id == partner_id)
        
        return query.count()
    
    def update_status(
        self,
        db: Session,
        *,
        db_obj: ReconciliationTemplate,
        status: str,
        task_id: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> ReconciliationTemplate:
        db_obj.status = status
        
        if task_id:
            db_obj.task_id = task_id
            
        if error_message:
            db_obj.error_message = error_message
            
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

crud_template = CRUDReconciliationTemplate(ReconciliationTemplate) 