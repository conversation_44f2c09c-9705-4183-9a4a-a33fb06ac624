from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import date

from ..models.call_log import CallLogFile, CallLog, FileType, CallType, FileStatus

def create_call_log_file(db: Session, filename: str, file_type: FileType, user_id: int, file_path: str) -> CallLogFile:
    """
    Tạo bản ghi file upload mới
    """
    file_record = CallLogFile(
        filename=filename,
        file_type=file_type,
        uploaded_by=user_id,
        status=FileStatus.PENDING,
        file_path=file_path
    )
    
    db.add(file_record)
    db.commit()
    db.refresh(file_record)
    
    return file_record

def get_call_log_file(db: Session, file_id: int) -> Optional[CallLogFile]:
    """
    Lấy thông tin file upload theo ID
    """
    return db.query(CallLogFile).filter(CallLogFile.id == file_id).first()

def update_call_log_file(db: Session, file_id: int, update_data: Dict[str, Any]) -> Optional[CallLogFile]:
    """
    Cập nhật thông tin file upload
    """
    file_record = db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
    
    if not file_record:
        return None
    
    for key, value in update_data.items():
        setattr(file_record, key, value)
    
    db.commit()
    db.refresh(file_record)
    
    return file_record

def get_call_log_files(
    db: Session, 
    user_id: Optional[int] = None, 
    file_type: Optional[FileType] = None,
    status: Optional[FileStatus] = None,
    skip: int = 0, 
    limit: int = 100
) -> List[CallLogFile]:
    """
    Lấy danh sách file upload với các bộ lọc
    """
    query = db.query(CallLogFile)
    
    if user_id is not None:
        query = query.filter(CallLogFile.uploaded_by == user_id)
    
    if file_type is not None:
        query = query.filter(CallLogFile.file_type == file_type)
    
    if status is not None:
        query = query.filter(CallLogFile.status == status)
    
    return query.order_by(CallLogFile.uploaded_at.desc()).offset(skip).limit(limit).all()

def count_call_log_files(
    db: Session, 
    user_id: Optional[int] = None, 
    file_type: Optional[FileType] = None,
    status: Optional[FileStatus] = None
) -> int:
    """
    Đếm số lượng file upload với các bộ lọc
    """
    query = db.query(func.count(CallLogFile.id))
    
    if user_id is not None:
        query = query.filter(CallLogFile.uploaded_by == user_id)
    
    if file_type is not None:
        query = query.filter(CallLogFile.file_type == file_type)
    
    if status is not None:
        query = query.filter(CallLogFile.status == status)
    
    return query.scalar()

def delete_call_log_file(db: Session, file_id: int) -> bool:
    """
    Xóa file upload và dữ liệu liên quan
    """
    file_record = db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
    
    if not file_record:
        return False
    
    # Xóa các bản ghi call log liên quan
    db.query(CallLog).filter(CallLog.file_id == file_id).delete()
    
    # Xóa bản ghi file
    db.delete(file_record)
    db.commit()
    
    return True

def get_call_logs(
    db: Session,
    file_id: Optional[int] = None,
    call_type: Optional[CallType] = None,
    caller: Optional[str] = None,
    callee: Optional[str] = None,
    call_date_from: Optional[date] = None,
    call_date_to: Optional[date] = None,
    caller_type: Optional[str] = None,
    callee_type: Optional[str] = None,
    sort_field: str = "begin_time",
    sort_order: str = "desc",
    skip: int = 0,
    limit: int = 100
) -> List[CallLog]:
    """
    Lấy danh sách call logs với các bộ lọc
    """
    query = db.query(CallLog)
    
    # Tối ưu truy vấn: Thêm các join cần thiết trước khi áp dụng bộ lọc
    
    # Áp dụng các bộ lọc
    if file_id is not None:
        query = query.filter(CallLog.file_id == file_id)
    
    if call_type is not None:
        query = query.filter(CallLog.call_type == call_type)
    
    if caller is not None:
        # Tối ưu tìm kiếm số điện thoại
        if caller.isdigit() and len(caller) >= 5:
            # Nếu là số điện thoại đầy đủ thì tìm chính xác
            query = query.filter(CallLog.caller == caller)
        else:
            # Nếu nhập một phần thì tìm mờ
            query = query.filter(CallLog.caller.ilike(f"%{caller}%"))
    
    if callee is not None:
        if callee.isdigit() and len(callee) >= 5:
            query = query.filter(CallLog.callee == callee)
        else:
            query = query.filter(CallLog.callee.ilike(f"%{callee}%"))
    
    # Tối ưu tìm kiếm theo ngày
    if call_date_from is not None:
        query = query.filter(CallLog.call_date >= call_date_from)
    
    if call_date_to is not None:
        query = query.filter(CallLog.call_date <= call_date_to)
    
    # Thêm bộ lọc theo loại số điện thoại
    if caller_type is not None:
        query = query.filter(CallLog.caller_type == caller_type)
    
    if callee_type is not None:
        query = query.filter(CallLog.callee_type == callee_type)
    
    # Sắp xếp động
    from sqlalchemy import asc, desc
    sort_columns = {
        "begin_time": CallLog.begin_time,
        "end_time": CallLog.end_time,
        "duration": CallLog.duration,
        "call_date": CallLog.call_date,
        "caller": CallLog.caller,
        "callee": CallLog.callee
    }
    
    sort_column = sort_columns.get(sort_field, CallLog.begin_time)
    sort_func = desc if sort_order.lower() == "desc" else asc
    query = query.order_by(sort_func(sort_column))
    
    # Tối ưu: Thêm id vào sắp xếp để đảm bảo thứ tự ổn định
    query = query.order_by(sort_func(CallLog.id))
    
    # Tạo truy vấn phân trang
    return query.offset(skip).limit(limit).all()

def count_call_logs(
    db: Session,
    file_id: Optional[int] = None,
    call_type: Optional[CallType] = None,
    caller: Optional[str] = None,
    callee: Optional[str] = None,
    call_date_from: Optional[date] = None,
    call_date_to: Optional[date] = None,
    caller_type: Optional[str] = None,
    callee_type: Optional[str] = None
) -> int:
    """
    Đếm số lượng call logs với các bộ lọc
    """
    # Tối ưu query đếm: Chỉ select id để tăng hiệu suất
    query = db.query(func.count(CallLog.id))
    
    if file_id is not None:
        query = query.filter(CallLog.file_id == file_id)
    
    if call_type is not None:
        query = query.filter(CallLog.call_type == call_type)
    
    if caller is not None:
        if caller.isdigit() and len(caller) >= 5:
            query = query.filter(CallLog.caller == caller)
        else:
            query = query.filter(CallLog.caller.ilike(f"%{caller}%"))
    
    if callee is not None:
        if callee.isdigit() and len(callee) >= 5:
            query = query.filter(CallLog.callee == callee)
        else:
            query = query.filter(CallLog.callee.ilike(f"%{callee}%"))
    
    if call_date_from is not None:
        query = query.filter(CallLog.call_date >= call_date_from)
    
    if call_date_to is not None:
        query = query.filter(CallLog.call_date <= call_date_to)
    
    if caller_type is not None:
        query = query.filter(CallLog.caller_type == caller_type)
    
    if callee_type is not None:
        query = query.filter(CallLog.callee_type == callee_type)
    
    return query.scalar() 