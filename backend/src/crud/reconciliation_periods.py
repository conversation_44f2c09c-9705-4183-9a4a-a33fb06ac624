from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from fastapi.encoders import jsonable_encoder

from ..models.reconciliation_period import ReconciliationPeriod
from ..schemas.reconciliation_period import ReconciliationPeriodCreate, ReconciliationPeriodUpdate
from .base import CRUDBase

class CRUDReconciliationPeriod(
    CRUDBase[ReconciliationPeriod, ReconciliationPeriodCreate, ReconciliationPeriodUpdate]
):
    def create_with_owner(
        self, db: Session, *, obj_in: ReconciliationPeriodCreate, user_id: int
    ) -> ReconciliationPeriod:
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data, created_by=user_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get_by_year_and_month(
        self, db: Session, *, year: int, month: int
    ) -> Optional[ReconciliationPeriod]:
        return db.query(self.model).filter(
            self.model.year == year,
            self.model.month == month
        ).first()
    
    def get_multi_with_filters(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        year: Optional[int] = None,
        month: Optional[int] = None,
        status: Optional[str] = None
    ) -> List[ReconciliationPeriod]:
        query = db.query(self.model)
        
        if year is not None:
            query = query.filter(self.model.year == year)
        if month is not None:
            query = query.filter(self.model.month == month)
        
        if status:
            query = query.filter(self.model.status == status)
        
        return query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()
    
    def count_with_filters(
        self,
        db: Session,
        *,
        year: Optional[int] = None,
        month: Optional[int] = None,
        status: Optional[str] = None
    ) -> int:
        query = db.query(self.model)
        
        if year is not None:
            query = query.filter(self.model.year == year)
        if month is not None:
            query = query.filter(self.model.month == month)
        
        if status:
            query = query.filter(self.model.status == status)
        
        return query.count()

crud_reconciliation_period = CRUDReconciliationPeriod(ReconciliationPeriod) 