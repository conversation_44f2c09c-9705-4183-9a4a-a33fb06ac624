from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func

from .base import CRUDBase
from ..models.partner import Partner
from ..schemas.partner import PartnerCreate, PartnerUpdate

class CRUDPartner(CRUDBase[Partner, PartnerCreate, PartnerUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[Partner]:
        return db.query(Partner).filter(Partner.name == name).first()
    
    def get_by_raw_name(self, db: Session, *, raw_name: str) -> Optional[Partner]:
        return db.query(Partner).filter(Partner.raw_name == raw_name).first()
    
    def get_active(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Partner]:
        return db.query(Partner).filter(Partner.is_active == True).order_by(Partner.name).offset(skip).limit(limit).all()
    
    def get_by_type(self, db: Session, *, type: str, skip: int = 0, limit: int = 100) -> List[Partner]:
        return db.query(Partner).filter(Partner.type == type).order_by(Partner.name).offset(skip).limit(limit).all()

    def get_multi_paginated(
        self, db: Session, *, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None
    ) -> Tuple[List[Partner], int]:
        query = db.query(self.model)
        count_query = db.query(func.count(self.model.id))
        
        if is_active is not None:
            query = query.filter(self.model.is_active == is_active)
            count_query = count_query.filter(self.model.is_active == is_active)
            
        total = count_query.scalar()
        items = query.order_by(self.model.name).offset(skip).limit(limit).all()
        
        return items, total

crud_partner = CRUDPartner(Partner) 