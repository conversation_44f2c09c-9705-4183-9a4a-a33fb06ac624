from sqlalchemy.orm import Session
import logging
from datetime import datetime
from sqlalchemy.orm import joinedload, selectinload
from typing import Optional, Dict, Any, List

from ..models.dsc import DoiSoatCuoc
from ..models.template import ReconciliationTemplate # Import ReconciliationTemplate
from ..models.enums import ReconciliationStatus
from ..core.exceptions import NotFoundError # Import NotFoundError
from ..schemas.dsc import DauSoDichVuCuoc as DauSoDichVuCuocSchema, TongKetCuoc as TongKetCuocSchema
from ..schemas.doi_soat import MinimalPartnerInfo
from ..models.partner import Partner # Import Partner
from ..schemas.dsc import DoiSoatCuocAdjustmentsPayload
from ..models.dsc import DauSoDichVuCuoc, TongKetCuoc # Import models
from ..core.exceptions import InvalidOperationError
from sqlalchemy import select

logger = logging.getLogger(__name__)

def create_initial_dsc_reconciliation(
    db: Session, 
    ky_doi_soat: str, 
    template_id: int, 
    partner_id: int
) -> DoiSoatCuoc:
    """
    Tạo một bản ghi DoiSoatCuoc ban đầu cho quá trình đối soát mới.
    
    Bản ghi này sẽ có is_template_data=False và status=PROCESSING.
    Nó sao chép metadata VÀ CẤU TRÚC CHI TIẾT (với giá trị bằng 0) từ template.

    Args:
        db: Database session.
        ky_doi_soat: Kỳ đối soát (YYYY-MM).
        template_id: ID của ReconciliationTemplate gốc.
        partner_id: ID của Partner liên kết.

    Returns:
        Đối tượng DoiSoatCuoc vừa được tạo.
        
    Raises:
        NotFoundError: Nếu template hoặc dữ liệu template DSC không tồn tại.
    """
    logger.info(f"Creating initial DSC reconciliation for period '{ky_doi_soat}', template_id={template_id}, partner_id={partner_id}")

    # 1. Lấy thông tin từ template gốc ReconciliationTemplate (để kiểm tra tồn tại)
    template = db.query(ReconciliationTemplate).filter(ReconciliationTemplate.id == template_id).first()
    if not template:
        logger.error(f"Template with ID {template_id} not found.")
        raise NotFoundError(f"Template with ID {template_id} not found")

    # --- Lấy bản ghi DoiSoatCuoc mẫu VÀ dữ liệu con của nó --- 
    template_dsc_data = db.query(DoiSoatCuoc).options(
        selectinload(DoiSoatCuoc.du_lieu), # Eager load dữ liệu chi tiết
        joinedload(DoiSoatCuoc.tong_ket)   # Eager load tổng kết
    ).filter(
        DoiSoatCuoc.template_id == template_id,
        DoiSoatCuoc.is_template_data == True
    ).first()
    
    if not template_dsc_data:
        logger.error(f"Template DSC data not found for template_id {template_id}. Cannot create reconciliation.")
        raise NotFoundError(f"Template DSC data for template ID {template_id} not found. Required for creating reconciliation.")
        
    tu_mang_from_template = template_dsc_data.tu_mang
    den_doi_tac_from_template = template_dsc_data.den_doi_tac
    hop_dong_so_from_template = template_dsc_data.hop_dong_so
    file_name_from_template = template_dsc_data.file_name

    # 2. Tạo đối tượng DoiSoatCuoc mới (cha)
    new_dsc_reconciliation = DoiSoatCuoc(
        thang_doi_soat=ky_doi_soat,
        template_id=template_id,
        partner_id=partner_id,
        is_template_data=False,
        status=ReconciliationStatus.PROCESSING,
        tu_mang=tu_mang_from_template, 
        den_doi_tac=den_doi_tac_from_template,
        hop_dong_so=hop_dong_so_from_template,
        file_name=file_name_from_template,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )

    # 3. Sao chép cấu trúc DauSoDichVuCuoc (con)
    new_du_lieu_list: List[DauSoDichVuCuoc] = []
    if template_dsc_data.du_lieu:
        for template_du_lieu_item in template_dsc_data.du_lieu:
            new_du_lieu_item = DauSoDichVuCuoc(
                # Không cần gán doi_soat_id, SQLAlchemy sẽ xử lý qua relationship
                stt=template_du_lieu_item.stt,
                dau_so=template_du_lieu_item.dau_so,
                # Khởi tạo các giá trị số liệu bằng 0
                vnm_san_luong=0.0,
                vnm_ty_le_cp=template_du_lieu_item.vnm_ty_le_cp, # Sao chép tỷ lệ CP nếu cần
                vnm_thanh_tien=0.0,
                viettel_san_luong=0.0,
                viettel_ty_le_cp=template_du_lieu_item.viettel_ty_le_cp,
                viettel_thanh_tien=0.0,
                vnpt_san_luong=0.0,
                vnpt_ty_le_cp=template_du_lieu_item.vnpt_ty_le_cp,
                vnpt_thanh_tien=0.0,
                vms_san_luong=0.0,
                vms_ty_le_cp=template_du_lieu_item.vms_ty_le_cp,
                vms_thanh_tien=0.0,
                khac_san_luong=0.0,
                khac_ty_le_cp=template_du_lieu_item.khac_ty_le_cp,
                khac_thanh_tien=0.0,
                tong_thanh_toan=0.0,
                # Sao chép timestamps nếu cần hoặc để default
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            new_du_lieu_list.append(new_du_lieu_item)
            
    # 4. Sao chép cấu trúc TongKetCuoc (con)
    new_tong_ket = None
    if template_dsc_data.tong_ket:
        new_tong_ket = TongKetCuoc(
            # Không cần gán doi_soat_id
            # Khởi tạo các giá trị số liệu bằng 0
            cong_tien_dich_vu=0.0,
            tien_thue_gtgt=0.0,
            tong_cong_tien=0.0,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

    # 5. Gán các cấu trúc con đã sao chép vào đối tượng cha
    new_dsc_reconciliation.du_lieu = new_du_lieu_list
    if new_tong_ket:
        new_dsc_reconciliation.tong_ket = new_tong_ket
    
    # 6. Thêm đối tượng cha vào session (SQLAlchemy sẽ xử lý cascade cho các con)
    db.add(new_dsc_reconciliation)
    db.flush() # Flush để lấy ID và kiểm tra ràng buộc
    # Lưu ý: Không commit ở đây. Việc commit sẽ diễn ra ở tầng API sau khi gửi task Celery.

    logger.info(f"Initial DSC reconciliation record created with ID: {new_dsc_reconciliation.id} including copied structure, status: {new_dsc_reconciliation.status}")
    
    # 7. Trả về đối tượng mới tạo
    return new_dsc_reconciliation

# --- START: CRUD function to get DSC reconciliation details ---

def get_dsc_reconciliation_by_id(db: Session, dsc_id: int) -> Optional[Dict[str, Any]]:
    """
    Lấy thông tin chi tiết của một bản ghi DoiSoatCuoc đã đối soát theo ID.
    
    Args:
        db: Database session.
        dsc_id: ID của bản ghi DoiSoatCuoc.

    Returns:
        Một dictionary chứa thông tin chi tiết, hoặc None nếu không tìm thấy.
    """
    logger.debug(f"Fetching DSC reconciliation details for ID: {dsc_id}")
    
    result = db.query(DoiSoatCuoc).options(
        selectinload(DoiSoatCuoc.du_lieu), # Eager load du_lieu (list)
        joinedload(DoiSoatCuoc.tong_ket),   # Eager load tong_ket (one-to-one)
        joinedload(DoiSoatCuoc.partner)    # Eager load partner info
    ).filter(
        DoiSoatCuoc.id == dsc_id,
        DoiSoatCuoc.is_template_data == False # Chỉ lấy bản ghi đã đối soát
    ).first()

    if not result:
        logger.warning(f"Processed DSC reconciliation with ID {dsc_id} not found.")
        return None

    # Chuyển đổi dữ liệu con sang schema Pydantic nếu cần (hoặc để orm_mode xử lý)
    du_lieu_list = [
        DauSoDichVuCuocSchema.model_validate(item, from_attributes=True) 
        for item in result.du_lieu
    ]
    tong_ket_data = TongKetCuocSchema.model_validate(result.tong_ket, from_attributes=True) if result.tong_ket else None
    partner_data = MinimalPartnerInfo.model_validate(result.partner, from_attributes=True) if result.partner else None

    # Tạo dictionary trả về, đảm bảo cấu trúc khớp với DoiSoatCuocDetailResponse
    response_dict = {
        "id": result.id,
        "thang_doi_soat": result.thang_doi_soat,
        "tu_mang": result.tu_mang,
        "den_doi_tac": result.den_doi_tac,
        "partner_id": result.partner_id,
        "partner": partner_data,
        "file_name": result.file_name,
        "hop_dong_so": result.hop_dong_so,
        "template_id": result.template_id,
        "period_id": result.period_id,
        "is_template_data": result.is_template_data,
        "status": result.status,
        "task_id": result.task_id,
        "error_message": result.error_message,
        "du_lieu": du_lieu_list,
        "tong_ket": tong_ket_data,
        "created_at": result.created_at,
        "updated_at": result.updated_at,
    }
    
    logger.debug(f"Successfully retrieved details for DSC ID: {dsc_id}")
    return response_dict

# --- END: CRUD function to get DSC reconciliation details ---

# --- START: CRUD function to update DSC adjustments ---

def update_dsc_adjustments(
    db: Session, 
    dsc_id: int, 
    adjustments: DoiSoatCuocAdjustmentsPayload
) -> DoiSoatCuoc:
    """
    Cập nhật các giá trị hiệu chỉnh cho một bản ghi DoiSoatCuoc.

    Args:
        db: Database session.
        dsc_id: ID của DoiSoatCuoc cần cập nhật.
        adjustments: Payload chứa dữ liệu hiệu chỉnh.

    Returns:
        Đối tượng DoiSoatCuoc đã được cập nhật.
        
    Raises:
        NotFoundError: Nếu bản ghi DoiSoatCuoc không tồn tại.
        InvalidOperationError: Nếu bản ghi ở trạng thái không cho phép hiệu chỉnh.
        ValueError: Nếu ID DauSoDichVuCuoc trong payload không hợp lệ.
    """
    logger.info(f"Attempting to update adjustments for DSC ID: {dsc_id}")

    # 1. Lấy bản ghi DoiSoatCuoc và các con cần thiết
    dsc_record = db.query(DoiSoatCuoc).options(
        selectinload(DoiSoatCuoc.du_lieu), # Load dữ liệu con để cập nhật
        joinedload(DoiSoatCuoc.tong_ket)   # Load tổng kết để cập nhật
    ).filter(
        DoiSoatCuoc.id == dsc_id,
        DoiSoatCuoc.is_template_data == False
    ).first()

    if not dsc_record:
        raise NotFoundError(f"Processed DSC reconciliation with ID {dsc_id} not found.")

    # 2. Kiểm tra trạng thái
    if dsc_record.status in [ReconciliationStatus.FINALIZED]: # Có thể thêm ERROR nếu muốn
        raise InvalidOperationError(f"Cannot adjust DSC record in status '{dsc_record.status.value}'")
        
    has_changes = False
    total_cost_after_adjustment = 0.0

    # 3. Xử lý hiệu chỉnh cho du_lieu (DauSoDichVuCuoc)
    if adjustments.du_lieu:
        # Tạo dict để tra cứu nhanh các bản ghi con hiện có theo ID
        existing_children = {child.id: child for child in dsc_record.du_lieu}
        
        for adj_item in adjustments.du_lieu:
            child_record = existing_children.get(adj_item.id)
            if not child_record:
                # Ghi log và có thể bỏ qua hoặc raise lỗi tùy yêu cầu
                logger.warning(f"DauSoDichVuCuoc with ID {adj_item.id} not found for DSC ID {dsc_id}. Skipping adjustment.")
                continue # Hoặc raise ValueError(...) 
                
            item_changed = False
            # Cập nhật từng trường nếu có giá trị trong payload
            if adj_item.vnm_san_luong is not None:
                child_record.vnm_san_luong = adj_item.vnm_san_luong
                item_changed = True
            if adj_item.vnm_thanh_tien is not None:
                child_record.vnm_thanh_tien = adj_item.vnm_thanh_tien
                item_changed = True
            # Làm tương tự cho VIETTEL, VNPT, VMS, KHAC
            if adj_item.viettel_san_luong is not None:
                child_record.viettel_san_luong = adj_item.viettel_san_luong
                item_changed = True
            if adj_item.viettel_thanh_tien is not None:
                child_record.viettel_thanh_tien = adj_item.viettel_thanh_tien
                item_changed = True
            if adj_item.vnpt_san_luong is not None:
                child_record.vnpt_san_luong = adj_item.vnpt_san_luong
                item_changed = True
            if adj_item.vnpt_thanh_tien is not None:
                child_record.vnpt_thanh_tien = adj_item.vnpt_thanh_tien
                item_changed = True
            if adj_item.vms_san_luong is not None:
                child_record.vms_san_luong = adj_item.vms_san_luong
                item_changed = True
            if adj_item.vms_thanh_tien is not None:
                child_record.vms_thanh_tien = adj_item.vms_thanh_tien
                item_changed = True
            if adj_item.khac_san_luong is not None:
                child_record.khac_san_luong = adj_item.khac_san_luong
                item_changed = True
            if adj_item.khac_thanh_tien is not None:
                child_record.khac_thanh_tien = adj_item.khac_thanh_tien
                item_changed = True
                
            if item_changed:
                has_changes = True
                # Tính lại tong_thanh_toan cho bản ghi con này
                child_record.tong_thanh_toan = sum(filter(None, [
                    child_record.vnm_thanh_tien, 
                    child_record.viettel_thanh_tien, 
                    child_record.vnpt_thanh_tien, 
                    child_record.vms_thanh_tien, 
                    child_record.khac_thanh_tien
                ]))
                logger.debug(f"Updated DauSoDichVuCuoc ID {child_record.id}. New tong_thanh_toan: {child_record.tong_thanh_toan}")
                db.add(child_record) # Đánh dấu là thay đổi

    # 4. Tính toán lại tổng cộng tiền dịch vụ sau khi hiệu chỉnh du_lieu
    # Phải tính lại ngay cả khi adjustments.du_lieu là None (để đảm bảo tính nhất quán nếu chỉ hiệu chỉnh tổng kết)
    total_cost_after_adjustment = sum(child.tong_thanh_toan for child in dsc_record.du_lieu if child.tong_thanh_toan is not None)

    # 5. Xử lý/Tính toán lại TongKetCuoc 
    # Tìm hoặc tạo TongKetCuoc nếu chưa có (trường hợp hiếm nhưng nên xử lý)
    tong_ket_record = dsc_record.tong_ket
    if not tong_ket_record:
        logger.warning(f"TongKetCuoc record not found for DSC ID {dsc_id}. Creating a new one.")
        tong_ket_record = TongKetCuoc(doi_soat_id=dsc_id)
        dsc_record.tong_ket = tong_ket_record # Gán vào relationship
        db.add(tong_ket_record)
        has_changes = True # Đánh dấu có thay đổi vì tạo mới
    
    # Cập nhật TongKetCuoc dựa trên tổng đã tính lại
    # Bỏ qua hiệu chỉnh trực tiếp từ payload.tong_ket như đã thảo luận
    new_cong_tien = total_cost_after_adjustment
    VAT_RATE = 0.10 # Lấy từ config
    new_tien_thue = new_cong_tien * VAT_RATE
    new_tong_cong = new_cong_tien * (1 + VAT_RATE)
    
    # Chỉ cập nhật nếu có thay đổi thực sự trong tổng kết
    if (tong_ket_record.cong_tien_dich_vu != new_cong_tien or
        tong_ket_record.tien_thue_gtgt != new_tien_thue or
        tong_ket_record.tong_cong_tien != new_tong_cong):
        
        tong_ket_record.cong_tien_dich_vu = new_cong_tien
        tong_ket_record.tien_thue_gtgt = new_tien_thue
        tong_ket_record.tong_cong_tien = new_tong_cong
        has_changes = True
        db.add(tong_ket_record) # Đánh dấu là thay đổi
        logger.debug(f"Updated TongKetCuoc for DSC ID {dsc_id}. New tong_cong_tien: {tong_ket_record.tong_cong_tien}")

    # 6. Cập nhật trạng thái nếu có thay đổi
    if has_changes:
        dsc_record.status = ReconciliationStatus.ADJUSTED
        dsc_record.updated_at = datetime.utcnow()
        # Có thể lưu ghi chú hiệu chỉnh nếu cần
        # if adjustments.ghi_chu_hieu_chinh:
        #     dsc_record.adjustment_notes = adjustments.ghi_chu_hieu_chinh # Giả sử có trường này
        db.add(dsc_record)
        logger.info(f"DSC record ID {dsc_id} status updated to ADJUSTED.")
    else:
        logger.info(f"No effective changes made for DSC ID {dsc_id}. Status remains {dsc_record.status.value}.")

    # 7. Commit thay đổi (nếu có)
    if has_changes:
        try:
            db.commit()
            db.refresh(dsc_record) # Refresh để lấy trạng thái/timestamp mới nhất
            # Refresh cả các con nếu cần trả về dữ liệu mới nhất của chúng
            for child in dsc_record.du_lieu:
                db.refresh(child)
            if dsc_record.tong_ket:
                 db.refresh(dsc_record.tong_ket)
        except Exception as e:
            db.rollback()
            logger.error(f"Database error committing adjustments for DSC ID {dsc_id}: {e}")
            raise
    
    return dsc_record

# --- END: CRUD function to update DSC adjustments ---

# --- START: CRUD function to finalize DSC reconciliation ---
def finalize_dsc_reconciliation(db: Session, dsc_id: int) -> DoiSoatCuoc:
    """
    Chốt một bản ghi DoiSoatCuoc, cập nhật trạng thái thành FINALIZED.

    Args:
        db: Database session.
        dsc_id: ID của DoiSoatCuoc cần chốt.

    Returns:
        Đối tượng DoiSoatCuoc đã được chốt.

    Raises:
        NotFoundError: Nếu bản ghi không tồn tại.
        InvalidOperationError: Nếu bản ghi không ở trạng thái có thể chốt.
    """
    logger.info(f"Attempting to finalize DSC ID: {dsc_id}")
    
    dsc_record = db.query(DoiSoatCuoc).filter(
        DoiSoatCuoc.id == dsc_id,
        DoiSoatCuoc.is_template_data == False
    ).first()

    if not dsc_record:
        raise NotFoundError(f"Processed DSC reconciliation with ID {dsc_id} not found.")

    # Kiểm tra trạng thái hiện tại
    allowed_statuses = [ReconciliationStatus.CALCULATED, ReconciliationStatus.ADJUSTED]
    if dsc_record.status not in allowed_statuses:
        raise InvalidOperationError(
            f"Cannot finalize DSC record. Current status is '{dsc_record.status.value}'. Allowed statuses: {[s.value for s in allowed_statuses]}"
        )

    # Cập nhật trạng thái
    dsc_record.status = ReconciliationStatus.FINALIZED
    dsc_record.updated_at = datetime.utcnow()
    db.add(dsc_record)
    
    try:
        db.commit()
        db.refresh(dsc_record)
        logger.info(f"Successfully finalized DSC record ID: {dsc_id}")
        return dsc_record
    except Exception as e:
        db.rollback()
        logger.error(f"Database error finalizing DSC ID {dsc_id}: {e}")
        raise

# --- END: CRUD function to finalize DSC reconciliation ---

# --- START: CRUD function to delete DSC reconciliation ---
def delete_dsc_reconciliation(db: Session, dsc_id: int) -> bool:
    """
    Xóa một bản ghi DoiSoatCuoc đã đối soát.

    Args:
        db: Database session.
        dsc_id: ID của DoiSoatCuoc cần xóa.

    Returns:
        True nếu xóa thành công, False nếu không tìm thấy.
        
    Raises:
        InvalidOperationError: Nếu bản ghi ở trạng thái không cho phép xóa (ví dụ: FINALIZED).
    """
    logger.info(f"Attempting to delete DSC ID: {dsc_id}")
    
    dsc_record = db.query(DoiSoatCuoc).filter(
        DoiSoatCuoc.id == dsc_id,
        DoiSoatCuoc.is_template_data == False
    ).first()

    if not dsc_record:
        logger.warning(f"Processed DSC reconciliation with ID {dsc_id} not found for deletion.")
        return False # Hoặc raise NotFoundError tùy theo cách xử lý ở API

    # Kiểm tra trạng thái (ví dụ: không cho xóa nếu đã chốt)
    if dsc_record.status == ReconciliationStatus.FINALIZED:
        raise InvalidOperationError(
            f"Cannot delete DSC record. Record is already finalized."
        )

    # Xóa bản ghi (SQLAlchemy sẽ xử lý cascade delete cho du_lieu và tong_ket nếu cấu hình)
    try:
        db.delete(dsc_record)
        db.commit()
        logger.info(f"Successfully deleted DSC record ID: {dsc_id}")
        return True
    except Exception as e:
        db.rollback()
        logger.error(f"Database error deleting DSC ID {dsc_id}: {e}")
        raise # Re-raise để API có thể xử lý 

# --- END: CRUD function to delete DSC reconciliation --- 