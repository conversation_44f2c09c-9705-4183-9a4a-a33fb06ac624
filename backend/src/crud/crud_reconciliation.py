# backend/src/crud/crud_reconciliation.py
import logging
from typing import List, Optional, Dict, Any, Union

from sqlalchemy.orm import Session, joinedload, Query
from sqlalchemy import desc, asc, func, select, literal_column
from sqlalchemy.sql import union_all # Import union_all

from .. import schemas # Ensure schemas is imported correctly
from ..models.dscd import DoiSoatCoDinh # Import DSCD model
from ..models.dsc import DoiSoatCuoc # Import DSC model
from ..models.partner import Partner # Assuming Partner model exists
from ..models.enums import ReconciliationStatus, ReconciliationType

log = logging.getLogger(__name__)

# --- Define Base Query Components --- 

def _get_dscd_base_query(db: Session) -> Query:
    """Creates the base query for DSCD records for the unified list."""
    return (
        db.query(
            DoiSoatCoDinh.id.label("id"),
            literal_column(f"'{ReconciliationType.DSCD.value}'").label("reconciliation_type"), # Add type literal
            DoiSoatCoDinh.thang_doi_soat.label("ky_doi_soat"),
            DoiSoatCoDinh.status.label("status"),
            DoiSoatCoDinh.created_at.label("created_at"),
            DoiSoatCoDinh.updated_at.label("updated_at"),
            DoiSoatCoDinh.partner_id.label("partner_id") # Keep partner_id for filtering
            # Add other common fields if needed, joining TongKet might be needed for total_amount
        )
        .filter(DoiSoatCoDinh.is_template_data == False) # Exclude templates by default
        # We will join partner later based on the combined query
    )

# --- START: Add base query function for DSC ---
def _get_dsc_base_query(db: Session) -> Query:
    """Creates the base query for DSC records for the unified list."""
    return (
        db.query(
            DoiSoatCuoc.id.label("id"),
            literal_column(f"'{ReconciliationType.DSC.value}'").label("reconciliation_type"),
            DoiSoatCuoc.thang_doi_soat.label("ky_doi_soat"),
            DoiSoatCuoc.status.label("status"),
            DoiSoatCuoc.created_at.label("created_at"),
            DoiSoatCuoc.updated_at.label("updated_at"),
            DoiSoatCuoc.partner_id.label("partner_id")
        )
        .filter(DoiSoatCuoc.is_template_data == False) # Exclude template data
    )
# --- END: Add base query function for DSC ---

# --- Map sort fields to actual columns/labels in the combined query --- 
# Note: These labels MUST match the labels used in the base queries
_SORTABLE_FIELDS_UNIFIED = {
    "id": "id",
    "reconciliation_type": "reconciliation_type",
    "ky_doi_soat": "ky_doi_soat",
    "status": "status",
    "created_at": "created_at",
    "updated_at": "updated_at",
    "partner_name": Partner.name # Sorting by partner name requires a join later
}

def get_all_reconciliations(
    db: Session,
    *,
    # Filters
    reconciliation_type: Optional[ReconciliationType] = None,
    ky_doi_soat: Optional[str] = None,
    partner_id: Optional[int] = None,
    status: Optional[ReconciliationStatus] = None,
    # Sorting & Pagination
    sort_by: str = "created_at",
    sort_order: str = "desc",
    skip: int = 0,
    limit: int = 10
) -> Dict[str, Any]:
    """
    Lấy danh sách thống nhất tất cả các bản ghi đối soát với tùy chọn lọc, sắp xếp, phân trang.
    Hiện tại chỉ hỗ trợ DSCD, sẽ mở rộng sau.
    """
    log.debug(f"Fetching unified list: skip={skip}, limit={limit}, sort='{sort_by}:{sort_order}', filters={{type:{reconciliation_type}, month:{ky_doi_soat}, partner:{partner_id}, status:{status}}}")

    # --- Build Individual Queries based on type filter --- 
    queries_to_union: List[Query] = []
    
    # Add DSCD query if no type filter or type is DSCD
    if reconciliation_type is None or reconciliation_type == ReconciliationType.DSCD:
        dscd_query = _get_dscd_base_query(db)
        if ky_doi_soat:
            dscd_query = dscd_query.filter(DoiSoatCoDinh.thang_doi_soat == ky_doi_soat)
        if partner_id is not None:
            dscd_query = dscd_query.filter(DoiSoatCoDinh.partner_id == partner_id)
        if status:
             dscd_query = dscd_query.filter(DoiSoatCoDinh.status == status)
        queries_to_union.append(dscd_query)

    # --- START: Add DSC query based on filter --- 
    if reconciliation_type is None or reconciliation_type == ReconciliationType.DSC:
        dsc_query = _get_dsc_base_query(db)
        # Apply common filters to DSC query
        if ky_doi_soat:
            dsc_query = dsc_query.filter(DoiSoatCuoc.thang_doi_soat == ky_doi_soat)
        if partner_id is not None:
            dsc_query = dsc_query.filter(DoiSoatCuoc.partner_id == partner_id)
        if status:
            dsc_query = dsc_query.filter(DoiSoatCuoc.status == status)
        queries_to_union.append(dsc_query)
    # --- END: Add DSC query based on filter --- 

    # Check if any queries are available before union
    if not queries_to_union:
        log.warning("No matching reconciliation types found for the given filter.")
        return {"items": [], "total": 0, "page": 1, "size": limit, "pages": 0}

    # --- Combine Queries using UNION ALL --- 
    combined_query = union_all(*[q.statement for q in queries_to_union])
    combined_cte = combined_query.cte("combined_reconciliations") # Use a CTE
    
    # --- Build Final Query on CTE --- 
    final_query_base = db.query(combined_cte)
    
    # --- Calculate Total Count --- 
    # Count directly from the CTE
    count_query = select(func.count()).select_from(combined_cte)
    total_count = db.execute(count_query).scalar_one_or_none() or 0
    log.debug(f"Total unified records matching filters: {total_count}")

    # --- Apply Sorting on CTE --- 
    sort_column_label = _SORTABLE_FIELDS_UNIFIED.get(sort_by)
    sort_on_partner = sort_by == "partner_name" # Check if sorting by partner name

    if sort_column_label is None and not sort_on_partner:
        log.warning(f"Invalid sort_by field: '{sort_by}'. Defaulting to created_at.")
        sort_column_label = "created_at"
        sort_expression = combined_cte.c.created_at # Sort on CTE column
    elif sort_on_partner:
         # Need to join Partner table for sorting by name
         # Handled later after the join
         sort_expression = Partner.name
    else:
        sort_expression = getattr(combined_cte.c, sort_column_label)
        
    # Apply sorting direction
    order_func = desc if sort_order.lower() == "desc" else asc
    
    # Select final columns explicitly, join Partner info
    final_query = (
        db.query(
            combined_cte.c.id.label("id"), # Chọn tường minh các cột từ CTE
            combined_cte.c.reconciliation_type.label("reconciliation_type"),
            combined_cte.c.ky_doi_soat.label("ky_doi_soat"),
            combined_cte.c.status.label("status"),
            combined_cte.c.created_at.label("created_at"),
            combined_cte.c.updated_at.label("updated_at"),
            Partner # Vẫn chọn đối tượng Partner
        )
        # Sử dụng outerjoin (LEFT JOIN) để tránh lỗi nếu partner_id null hoặc không tồn tại
        .outerjoin(Partner, combined_cte.c.partner_id == Partner.id) 
    )

    # Apply sorting (partner name sorting is applied here after join)
    # If sorting by partner name, use Partner.name, otherwise use CTE column
    sort_target = Partner.name if sort_on_partner else getattr(combined_cte.c, sort_column_label or "created_at")
    final_query = final_query.order_by(order_func(sort_target))

    # Apply pagination
    final_query = final_query.offset(skip).limit(limit)

    # --- Execute Final Query --- 
    results = final_query.all()
    log.debug(f"Retrieved {len(results)} unified records for the current page.")

    # --- Serialize Results --- 
    items_serialized = []
    for row in results: 
        # row is now a Row object with labeled columns + Partner object at the end
        # Access columns by label (row.id, row.ky_doi_soat, ...)
        partner_data = row.Partner # Access the Partner object by its implicit label

        # Handle potential Enum errors during serialization
        try:
            status_enum = ReconciliationStatus(row.status)
        except ValueError:
            log.warning(f"Invalid status value '{row.status}' found for ID {row.id}. Using default.")
            status_enum = ReconciliationStatus.CALCULATED # Or handle differently

        try:
            type_enum = ReconciliationType(row.reconciliation_type)
        except ValueError:
             log.warning(f"Invalid type value '{row.reconciliation_type}' found for ID {row.id}. Using default.")
             type_enum = ReconciliationType.DSCD # Or handle differently

        item_dict = {
            "id": row.id,
            "reconciliation_type": type_enum,
            "ky_doi_soat": row.ky_doi_soat,
            "status": status_enum,
            "created_at": row.created_at,
            "updated_at": row.updated_at,
            "partner": schemas.doi_soat.MinimalPartnerInfo.model_validate(partner_data, from_attributes=True) if partner_data else None
        }
        items_serialized.append(schemas.doi_soat.ReconciliationListItem(**item_dict))
        
    return {
        "items": items_serialized,
        "total": total_count,
        "page": (skip // limit) + 1 if limit > 0 else 1,
        "size": limit,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
    } 