from typing import List, Optional, Union, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import select, and_
from datetime import date

from ..crud.base import CRUDBase
from ..models.volume_range import VolumeRange
from ..models.pricing import RevenuePricing, CostPricing, BillingMethod
from ..models.enums import ServiceType, SERVICE_TYPE_NAMES
from ..schemas.pricing import (
    VolumeRangeCreate, VolumeRangeUpdate,
    RevenuePricingCreate, CostPricingCreate,
    BulkRevenuePricingCreate, BulkCostPricingCreate
)

class CRUDVolumeRange(CRUDBase[VolumeRange, VolumeRangeCreate, VolumeRangeUpdate]):
    pass

class CRUDRevenuePricing(CRUDBase[RevenuePricing, RevenuePricingCreate, RevenuePricingCreate]):
    def get_active_by_partner(
        self, 
        db: Session, 
        *, 
        partner_id: int,
        billing_method: Optional[BillingMethod] = None
    ) -> List[RevenuePricing]:
        query = db.query(self.model).filter(
            self.model.partner_id == partner_id,
            self.model.is_active == True
        )
        if billing_method:
            query = query.filter(self.model.billing_method == billing_method)
        return query.all()

    def create_bulk(
        self,
        db: Session,
        *,
        obj_in: BulkRevenuePricingCreate
    ) -> List[RevenuePricing]:
        pricing_objects = []
        for item in obj_in.items:
            pricing_data = {
                "partner_id": obj_in.partner_id,
                "billing_method": obj_in.billing_method,
                "service_type": item.service_type,
                "volume_range_id": item.volume_range_id,
                "price": item.price,
                "is_active": True
            }
            pricing_obj = RevenuePricing(**pricing_data)
            db.add(pricing_obj)
            pricing_objects.append(pricing_obj)
        
        db.commit()
        for obj in pricing_objects:
            db.refresh(obj)
        
        return pricing_objects

class CRUDCostPricing(CRUDBase[CostPricing, CostPricingCreate, CostPricingCreate]):
    def get_active_by_partner(
        self, 
        db: Session, 
        *, 
        partner_id: int,
        billing_method: Optional[BillingMethod] = None
    ) -> List[CostPricing]:
        query = db.query(self.model).filter(
            self.model.partner_id == partner_id,
            self.model.is_active == True
        )
        if billing_method:
            query = query.filter(self.model.billing_method == billing_method)
        return query.all()

    def create_bulk(
        self,
        db: Session,
        *,
        obj_in: BulkCostPricingCreate
    ) -> List[CostPricing]:
        pricing_objects = []
        for item in obj_in.items:
            pricing_data = {
                "partner_id": obj_in.partner_id,
                "billing_method": obj_in.billing_method,
                "service_type": item.service_type,
                "volume_range_id": item.volume_range_id,
                "price": item.price,
                "description": item.description,
                "is_active": True
            }
            pricing_obj = CostPricing(**pricing_data)
            db.add(pricing_obj)
            pricing_objects.append(pricing_obj)
        
        db.commit()
        for obj in pricing_objects:
            db.refresh(obj)
        
        return pricing_objects

crud_volume_range = CRUDVolumeRange(VolumeRange)
crud_revenue_pricing = CRUDRevenuePricing(RevenuePricing)
crud_cost_pricing = CRUDCostPricing(CostPricing)

__all__ = [
    "crud_volume_range",
    "crud_revenue_pricing",
    "crud_cost_pricing"
] 