# backend/src/crud/crud_dscd.py

from sqlalchemy.orm import Session, joinedload, contains_eager, selectinload
from sqlalchemy import desc, asc, func, select, cast, String
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy import select, update
from typing import Optional, Dict, Any, List

from ..models.dscd import DoiSoatCoDinh, DauSoDichVu, TongKet, CuocThueBao, CuocGoi
from ..models.enums import ServiceType, DauSoType, ReconciliationStatus
from ..models.call_log import NumberType
from .. import schemas
from ..core.exceptions import NotFoundError, InvalidOperationError, BadRequestError
from ..models.partner import Partner
from ..schemas.dscd import DoiSoatCoDinhAdjustmentsPayload

log = logging.getLogger(__name__)

# Mapping đã xác định ở bước trước
SERVICE_TYPE_TO_LOAI_CUOC_MAP = {
    ServiceType.FIXED_NOI_HAT: 'co_dinh_noi_hat',
    ServiceType.FIXED_LIEN_TINH_KHAC_MANG: 'co_dinh_lien_tinh',
    ServiceType.MOBILE_NORMAL: 'di_dong',
    ServiceType.SERVICE_1900_VOICE_VAS: 'cuoc_1900',
    ServiceType.INTL_OUTBOUND: 'quoc_te',
}

# Function to map model fields to SQLAlchemy columns for sorting
_SORTABLE_FIELDS_DSCD = {
    "created_at": DoiSoatCoDinh.created_at,
    "updated_at": DoiSoatCoDinh.updated_at,
    "thang_doi_soat": DoiSoatCoDinh.thang_doi_soat,
    "partner_id": DoiSoatCoDinh.partner_id,
    "status": DoiSoatCoDinh.status,
    "id": DoiSoatCoDinh.id
}

def create_dscd_reconciliation(
    db: Session,
    *,
    ky_doi_soat: str,
    template_id: Optional[int],
    partner_id: int,
    processed_results: List[Dict[str, Any]],
    summary_totals: Dict[str, Any]
) -> DoiSoatCoDinh:
    """
    Creates a new DSCD reconciliation record along with its detailed DauSoDichVu 
    entries and summary Totals.

    Args:
        db: Database session.
        ky_doi_soat: The reconciliation period (YYYY-MM).
        template_id: The ID of the template used, if any.
        partner_id: The ID of the partner.
        processed_results: A list of dictionaries, each representing a DauSoDichVu entry.
        summary_totals: A dictionary containing the summary totals.

    Returns:
        The created DoiSoatCoDinh object.
        
    Raises:
        IntegrityError: If partner_id does not exist or other constraints fail.
    """
    log.info(f"Attempting to create DSCD reconciliation for period '{ky_doi_soat}', partner ID {partner_id}, template ID {template_id}")
    
    # --- START: Add Check for Existing Record --- 
    existing_record = db.query(DoiSoatCoDinh).filter(
        DoiSoatCoDinh.thang_doi_soat == ky_doi_soat,
        DoiSoatCoDinh.partner_id == partner_id,
        # Consider template_id as well if it's a defining factor for uniqueness
        DoiSoatCoDinh.template_id == template_id 
    ).first()
    
    if existing_record:
        log.warning(f"DSCD reconciliation already exists for period '{ky_doi_soat}', partner {partner_id}, template {template_id}. Aborting creation.")
        # Có thể raise lỗi cụ thể hơn ở đây
        raise IntegrityError("Duplicate reconciliation record exists", params=None, orig=None)
    # --- END: Add Check for Existing Record --- 
    
    try:
        # 1. Create the main DoiSoatCoDinh record
        new_doi_soat = DoiSoatCoDinh(
            thang_doi_soat=ky_doi_soat,
            partner_id=partner_id,
            template_id=template_id,
            is_template_data=False, # Explicitly set to False for actual data
            status=ReconciliationStatus.CALCULATED # Initial status after calculation
            # created_at, updated_at will be set automatically by the model
        )
        db.add(new_doi_soat)
        db.flush() # Flush to get the ID for FK relationships
        log.debug(f"Created main DoiSoatCoDinh record draft with temp ID.")

        # 2. Create TongKet record
        new_tong_ket = TongKet(
            doi_soat_id=new_doi_soat.id,
            cong_tien_dich_vu=summary_totals.get('total_cost_before_vat', 0.0),
            tien_thue_gtgt=summary_totals.get('total_vat', 0.0),
            tong_cong_tien=summary_totals.get('total_cost_after_vat', 0.0)
            # Add other summary fields if available
        )
        db.add(new_tong_ket)
        log.debug(f"Created TongKet record for DSCD ID {new_doi_soat.id}")

        # 3. Create DauSoDichVu records
        dau_so_dich_vu_objects = []
        for result_item in processed_results:
            dau_so_obj = DauSoDichVu(
                doi_soat_co_dinh_id=new_doi_soat.id,
                # Lấy các trường từ result_item, đảm bảo tên khớp
                raw_dau_so=result_item.get('raw_dau_so_from_template'), # Lấy raw string nếu có
                standardized_display=result_item.get('dau_so_display'),
                dau_so_type=result_item.get('dau_so_type'), # Enum value
                start_num_str=result_item.get('start_num_str'),
                end_num_str=result_item.get('end_num_str'),
                prefix=result_item.get('prefix'),
                number_count=result_item.get('dau_so_count_template'),
                service_type=result_item.get('service_type'), # Enum value
                so_luong_cuoc_goi=result_item.get('call_count', 0),
                tong_thoi_luong_giay=result_item.get('total_duration_seconds', 0),
                cuoc_truoc_vat=result_item.get('total_cost_before_vat', 0.0),
                thue_vat=result_item.get('vat_amount', 0.0),
                cuoc_sau_vat=result_item.get('total_cost_after_vat', 0.0),
                # Thêm các trường khác nếu có trong processed_results
                # Ví dụ: lưu ID của DauSoDichVu gốc từ template nếu cần
                source_dau_so_id = result_item.get('dau_so_id_from_template_data') 
            )
            dau_so_dich_vu_objects.append(dau_so_obj)
            
        if dau_so_dich_vu_objects:
            db.add_all(dau_so_dich_vu_objects)
            log.debug(f"Added {len(dau_so_dich_vu_objects)} DauSoDichVu records for DSCD ID {new_doi_soat.id}")
        else:
            log.warning(f"No DauSoDichVu records to add for DSCD ID {new_doi_soat.id}")

        # 4. Commit the transaction
        db.commit()
        db.refresh(new_doi_soat) # Refresh to get the final state with relationships
        log.info(f"Successfully created and committed DSCD reconciliation with ID: {new_doi_soat.id}")
        
        return new_doi_soat

    except IntegrityError as e:
        db.rollback() # Rollback on integrity errors (like FK violation)
        log.error(f"Database integrity error creating DSCD reconciliation: {e}")
        raise # Re-raise the specific error
    except Exception as e:
        db.rollback() # Rollback on any other error
        log.exception(f"Unexpected error creating DSCD reconciliation. Rolling back.")
        raise # Re-raise the general exception

def get_dscd_reconciliations(
    db: Session,
    *,
    thang_doi_soat: Optional[str] = None,
    partner_id: Optional[int] = None,
    is_template_data: Optional[bool] = None,
    sort_by: str = "created_at",
    sort_order: str = "desc",
    skip: int = 0,
    limit: int = 10,
) -> Dict[str, Any]:
    """
    Lấy danh sách đối soát cố định với các điều kiện lọc và phân trang.
    
    Args:
        db: Database session
        thang_doi_soat: Lọc theo tháng đối soát (format YYYY-MM)
        partner_id: Lọc theo ID đối tác
        is_template_data: True để lấy dữ liệu template, False cho dữ liệu đối soát thực tế, None để lấy cả hai
        sort_by: Trường để sắp xếp (mặc định: created_at)
        sort_order: Thứ tự sắp xếp (asc/desc)
        skip: Số bản ghi bỏ qua (phân trang)
        limit: Số bản ghi tối đa trả về (phân trang)
        
    Returns:
        Dictionary chứa tổng số bản ghi và danh sách các bản ghi
    """
    try:
        # Tạo query cơ bản
        query = db.query(DoiSoatCoDinh)
        
        # Join với Partner nếu cần lấy thêm thông tin đối tác
        query = query.outerjoin(DoiSoatCoDinh.partner)
        
        # Join với TongKet để lấy thông tin tổng cộng tiền
        query = query.outerjoin(DoiSoatCoDinh.tong_ket)
        
        # Thêm các điều kiện lọc
        if thang_doi_soat:
            query = query.filter(DoiSoatCoDinh.thang_doi_soat == thang_doi_soat)
        
        if partner_id is not None:
            query = query.filter(DoiSoatCoDinh.partner_id == partner_id)
        
        if is_template_data is not None:
            query = query.filter(DoiSoatCoDinh.is_template_data == is_template_data)
        
        # Đếm tổng số bản ghi thỏa mãn điều kiện (trước khi phân trang)
        total_items = query.count()
        
        # Thêm subquery để đếm số lượng đầu số cho mỗi đối soát
        from sqlalchemy import func
        dau_so_count_subquery = (
            db.query(
                DauSoDichVu.doi_soat_id,
                func.count(DauSoDichVu.id).label('dau_so_count')
            )
            .group_by(DauSoDichVu.doi_soat_id)
            .subquery()
        )
        
        # Join với subquery để lấy số lượng đầu số
        query = query.outerjoin(
            dau_so_count_subquery,
            DoiSoatCoDinh.id == dau_so_count_subquery.c.doi_soat_id
        )
        
        # Sắp xếp kết quả
        valid_sort_fields = {
            'created_at', 'updated_at', 'thang_doi_soat', 'id'
        }
        
        if sort_by not in valid_sort_fields:
            log.warning(f"Invalid sort field: {sort_by}. Defaulting to 'created_at'")
            sort_by = 'created_at'
        
        sort_column = getattr(DoiSoatCoDinh, sort_by)
        if sort_order.lower() == 'asc':
            query = query.order_by(asc(sort_column))
        else:
            query = query.order_by(desc(sort_column))
        
        # Thêm phân trang
        query = query.offset(skip).limit(limit)
        
        # Eager load relationships để tránh N+1 queries
        query = query.options(
            joinedload(DoiSoatCoDinh.tong_ket),
            joinedload(DoiSoatCoDinh.partner)
        )
        
        # Thực thi truy vấn
        items = query.all()
        
        # Tính tổng số trang
        page_size = limit
        pages = (total_items + page_size - 1) // page_size if page_size > 0 else 0
        current_page = (skip // page_size) + 1 if page_size > 0 else 1
        
        # Chuyển đổi items thành schema
        result_items = []
        for item in items:
            tong_tien = item.tong_ket.tong_cong_tien if item.tong_ket else 0.0
            tong_dau_so = len(item.du_lieu) if item.du_lieu else 0
            
            partner_name = item.partner.name if item.partner else None
            
            # Convert to Pydantic schema
            result_item = {
                "id": item.id,
                "thang_doi_soat": item.thang_doi_soat,
                "hop_dong_so": item.hop_dong_so,
                "tu_mang": item.tu_mang,
                "den_doi_tac": item.den_doi_tac,
                "partner_id": item.partner_id,
                "partner_name": partner_name,
                "file_name": item.file_name,
                "tong_tien": tong_tien,
                "tong_dau_so": tong_dau_so,
                "created_at": item.created_at,
                "updated_at": item.updated_at
            }
            result_items.append(result_item)
        
        # Trả về kết quả
        return {
            "total": total_items,
            "items": result_items,
            "page": current_page,
            "page_size": page_size,
            "pages": pages
        }
        
    except Exception as e:
        log.exception(f"Error fetching DSCD reconciliations: {e}")
        raise

def get_dscd_reconciliation_by_id(
    db: Session,
    *,
    dscd_id: int
) -> Optional[Dict[str, Any]]:
    """
    Lấy thông tin chi tiết của một bản ghi đối soát cố định bao gồm các đầu số và thông tin cước.
    
    Args:
        db: Database session
        dscd_id: ID của bản ghi đối soát cố định
        
    Returns:
        Dictionary chứa thông tin chi tiết hoặc None nếu không tìm thấy
    """
    try:
        # Query với eager loading tất cả các relations
        query = (
            db.query(DoiSoatCoDinh)
            .options(
                joinedload(DoiSoatCoDinh.du_lieu).joinedload(DauSoDichVu.co_dinh_noi_hat),
                joinedload(DoiSoatCoDinh.du_lieu).joinedload(DauSoDichVu.co_dinh_lien_tinh),
                joinedload(DoiSoatCoDinh.du_lieu).joinedload(DauSoDichVu.di_dong),
                joinedload(DoiSoatCoDinh.du_lieu).joinedload(DauSoDichVu.cuoc_1900),
                joinedload(DoiSoatCoDinh.du_lieu).joinedload(DauSoDichVu.quoc_te),
                joinedload(DoiSoatCoDinh.du_lieu).joinedload(DauSoDichVu.cuoc_thue_bao),
                joinedload(DoiSoatCoDinh.tong_ket),
                joinedload(DoiSoatCoDinh.partner)
            )
            .filter(DoiSoatCoDinh.id == dscd_id)
        )
        
        item = query.first()
        
        if not item:
            log.warning(f"DSCD reconciliation with ID {dscd_id} not found")
            return None
        
        partner_name = item.partner.name if item.partner else None
        
        du_lieu_list = []
        for dau_so in item.du_lieu:
            du_lieu_item = {
                "id": dau_so.id,
                "stt": dau_so.stt,
                "raw_dau_so": dau_so.raw_dau_so,
                "standardized_display": dau_so.standardized_display,
                "dau_so_type": dau_so.dau_so_type,
                "number_count": dau_so.number_count,
                "cuoc_thu_khach": dau_so.cuoc_thu_khach,
                "cuoc_tra_htc": dau_so.cuoc_tra_htc,
                # Thêm adjusted fields
                "cuoc_thu_khach_adjusted": dau_so.cuoc_thu_khach_adjusted,
                "cuoc_tra_htc_adjusted": dau_so.cuoc_tra_htc_adjusted,
            }
            
            if dau_so.co_dinh_noi_hat:
                du_lieu_item["co_dinh_noi_hat"] = {
                    "thoi_gian_goi": dau_so.co_dinh_noi_hat.thoi_gian_goi,
                    "cuoc": dau_so.co_dinh_noi_hat.cuoc,
                    "thoi_gian_goi_adjusted": dau_so.co_dinh_noi_hat.thoi_gian_goi_adjusted,
                    "cuoc_adjusted": dau_so.co_dinh_noi_hat.cuoc_adjusted
                }
            
            if dau_so.co_dinh_lien_tinh:
                du_lieu_item["co_dinh_lien_tinh"] = {
                    "thoi_gian_goi": dau_so.co_dinh_lien_tinh.thoi_gian_goi,
                    "cuoc": dau_so.co_dinh_lien_tinh.cuoc,
                    "thoi_gian_goi_adjusted": dau_so.co_dinh_lien_tinh.thoi_gian_goi_adjusted,
                    "cuoc_adjusted": dau_so.co_dinh_lien_tinh.cuoc_adjusted
                }
            
            if dau_so.di_dong:
                du_lieu_item["di_dong"] = {
                    "thoi_gian_goi": dau_so.di_dong.thoi_gian_goi,
                    "cuoc": dau_so.di_dong.cuoc,
                    "thoi_gian_goi_adjusted": dau_so.di_dong.thoi_gian_goi_adjusted,
                    "cuoc_adjusted": dau_so.di_dong.cuoc_adjusted
                }
            
            if dau_so.cuoc_1900:
                du_lieu_item["cuoc_1900"] = {
                    "thoi_gian_goi": dau_so.cuoc_1900.thoi_gian_goi,
                    "cuoc": dau_so.cuoc_1900.cuoc,
                    "thoi_gian_goi_adjusted": dau_so.cuoc_1900.thoi_gian_goi_adjusted,
                    "cuoc_adjusted": dau_so.cuoc_1900.cuoc_adjusted
                }
            
            if dau_so.quoc_te:
                du_lieu_item["quoc_te"] = {
                    "thoi_gian_goi": dau_so.quoc_te.thoi_gian_goi,
                    "cuoc": dau_so.quoc_te.cuoc,
                    "thoi_gian_goi_adjusted": dau_so.quoc_te.thoi_gian_goi_adjusted,
                    "cuoc_adjusted": dau_so.quoc_te.cuoc_adjusted
                }
            
            if dau_so.cuoc_thue_bao:
                du_lieu_item["cuoc_thue_bao"] = {
                    "thue_bao_thang": dau_so.cuoc_thue_bao.thue_bao_thang,
                    "cam_ket_thang": dau_so.cuoc_thue_bao.cam_ket_thang,
                    "tra_truoc_thang": dau_so.cuoc_thue_bao.tra_truoc_thang,
                    "thue_bao_thang_adjusted": dau_so.cuoc_thue_bao.thue_bao_thang_adjusted,
                    "cam_ket_thang_adjusted": dau_so.cuoc_thue_bao.cam_ket_thang_adjusted,
                    "tra_truoc_thang_adjusted": dau_so.cuoc_thue_bao.tra_truoc_thang_adjusted
                }
            
            du_lieu_list.append(du_lieu_item)
        
        tong_ket_data = None
        if item.tong_ket:
            tong_ket_data = {
                "cong_tien_dich_vu": item.tong_ket.cong_tien_dich_vu,
                "tien_thue_gtgt": item.tong_ket.tien_thue_gtgt,
                "tong_cong_tien": item.tong_ket.tong_cong_tien,
                # Thêm adjusted fields
                "cong_tien_dich_vu_adjusted": item.tong_ket.cong_tien_dich_vu_adjusted,
                "tien_thue_gtgt_adjusted": item.tong_ket.tien_thue_gtgt_adjusted,
                "tong_cong_tien_adjusted": item.tong_ket.tong_cong_tien_adjusted
            }
        
        result = {
            "id": item.id,
            "thang_doi_soat": item.thang_doi_soat,
            "tu_mang": item.tu_mang,
            "den_doi_tac": item.den_doi_tac,
            "hop_dong_so": item.hop_dong_so,
            "partner_id": item.partner_id,
            "partner_name": partner_name,
            "template_id": item.template_id,
            "file_name": item.file_name,
            "created_at": item.created_at,
            "updated_at": item.updated_at,
            "du_lieu": du_lieu_list,
            "tong_ket": tong_ket_data,
            "status": item.status.value if item.status else None
        }
        
        return result
        
    except Exception as e:
        log.exception(f"Error fetching DSCD reconciliation with ID {dscd_id}: {e}")
        raise

# --- START: Add CRUD function for updating DSCD reconciliation ---
def update_dscd_reconciliation(
    db: Session,
    *,
    dscd_id: int,
    processed_results: Optional[List[Dict[str, Any]]] = None,
    summary_totals: Optional[Dict[str, Any]] = None,
    status: Optional[ReconciliationStatus] = None,
    error_message: Optional[str] = None
) -> Optional[DoiSoatCoDinh]:
    """Update DSCD reconciliation with processed results or status"""
    doi_soat = db.query(DoiSoatCoDinh).filter(DoiSoatCoDinh.id == dscd_id).first()
    if not doi_soat:
        return None
        
    if processed_results is not None:
        # Create DauSoDichVu records
        dau_so_objects = []
        for result in processed_results:
            dau_so = DauSoDichVu(
                doi_soat_id=dscd_id,
                standardized_display=result['dau_so_display'],
                dau_so_type=result['dau_so_type'],
                service_type=result['service_type'],
                total_duration=result['total_duration_seconds'],
                total_cost=result['total_cost_before_vat'],
                call_count=result['call_count'],
                start_num_str=result['start_num_str'],
                end_num_str=result['end_num_str'],
                prefix=result.get('prefix'),
                raw_dau_so=result.get('raw_dau_so_from_template')
            )
            dau_so_objects.append(dau_so)
            
        doi_soat.du_lieu = dau_so_objects
        
    if summary_totals is not None:
        # Update or create TongKet
        if not doi_soat.tong_ket:
            doi_soat.tong_ket = TongKet()
            
        doi_soat.tong_ket.cong_tien_dich_vu = summary_totals['total_cost_before_vat']
        doi_soat.tong_ket.tien_thue_gtgt = summary_totals['total_vat']
        doi_soat.tong_ket.tong_cong_tien = summary_totals['total_cost_after_vat']
        
    if status is not None:
        doi_soat.status = status
        
    if error_message is not None:
        doi_soat.error_message = error_message
        
    db.commit()
    db.refresh(doi_soat)
    return doi_soat

def _deep_copy_dscd_details(
    db: Session,
    template_dscd: DoiSoatCoDinh,
    new_dscd_id: int
):
    """Helper function to deep copy details from template to new record."""
    log.debug(f"Starting deep copy of details from template DSCD ID {template_dscd.id} to new DSCD ID {new_dscd_id}")
    
    dau_so_map = {} # Store mapping from old DauSoDichVu ID to new DauSoDichVu object
    objects_to_add = []

    # 1. Copy TongKet
    if template_dscd.tong_ket:
        new_tong_ket = TongKet(
            doi_soat_id=new_dscd_id,
            cong_tien_dich_vu=template_dscd.tong_ket.cong_tien_dich_vu,
            tien_thue_gtgt=template_dscd.tong_ket.tien_thue_gtgt,
            tong_cong_tien=template_dscd.tong_ket.tong_cong_tien
            # Copy adjusted fields if needed for initial state?
            # cong_tien_dich_vu_adjusted=template_dscd.tong_ket.cong_tien_dich_vu_adjusted,
            # tien_thue_gtgt_adjusted=template_dscd.tong_ket.tien_thue_gtgt_adjusted,
            # tong_cong_tien_adjusted=template_dscd.tong_ket.tong_cong_tien_adjusted
        )
        objects_to_add.append(new_tong_ket)
        log.debug(f"Copied TongKet details.")
    else:
        log.warning(f"Template DSCD ID {template_dscd.id} has no TongKet data to copy.")

    # 2. Copy DauSoDichVu and their children (CuocGoi, CuocThueBao)
    template_details = db.query(DauSoDichVu).options(
        joinedload(DauSoDichVu.co_dinh_noi_hat),
        joinedload(DauSoDichVu.co_dinh_lien_tinh),
        joinedload(DauSoDichVu.di_dong),
        joinedload(DauSoDichVu.cuoc_1900),
        joinedload(DauSoDichVu.quoc_te),
        joinedload(DauSoDichVu.cuoc_thue_bao)
    ).filter(DauSoDichVu.doi_soat_id == template_dscd.id).all()
    
    log.debug(f"Found {len(template_details)} DauSoDichVu records in template to copy.")

    for template_dau_so in template_details:
        # Create new DauSoDichVu
        new_dau_so = DauSoDichVu(
            # Removed doi_soat_id, let relationship handle it or assign later if needed. But new_doi_soat should handle it via backref/cascade.
            stt=template_dau_so.stt,
            raw_dau_so=template_dau_so.raw_dau_so,
            standardized_display=template_dau_so.standardized_display,
            dau_so_type=template_dau_so.dau_so_type,
            number_count=template_dau_so.number_count,
            start_num_str=template_dau_so.start_num_str,
            end_num_str=template_dau_so.end_num_str,
            prefix=template_dau_so.prefix,
            cuoc_thu_khach=template_dau_so.cuoc_thu_khach,
            cuoc_tra_htc=template_dau_so.cuoc_tra_htc
            # Do NOT copy adjusted fields initially
        )
        # Assign to the parent's relationship list BEFORE adding new_dau_so to objects_to_add
        # Assuming new_doi_soat is the parent object passed or accessible
        # Let's adjust the function signature or how we access new_doi_soat later if needed.
        # For now, assume new_doi_soat object exists and has relationship 'du_lieu'
        # new_doi_soat.du_lieu.append(new_dau_so) # Let SQLAlchemy handle linking via relationship

        # We will add new_dau_so to objects_to_add list instead, which seems to be the current pattern.
        # SQLAlchemy should still handle the relationship linking when new_doi_soat is flushed/committed
        # as long as new_doi_soat.id is set correctly before this function is called,
        # and new_dau_so has doi_soat_id set correctly. Let's set doi_soat_id here.
        new_dau_so.doi_soat_id = new_dscd_id
        
        log.debug(f"Prepared DauSoDichVu copy (old ID: {template_dau_so.id}).")

        # Copy CuocGoi children and attach to new_dau_so
        cuoc_goi_relations = [
            (template_dau_so.co_dinh_noi_hat, 'co_dinh_noi_hat'),
            (template_dau_so.co_dinh_lien_tinh, 'co_dinh_lien_tinh'),
            (template_dau_so.di_dong, 'di_dong'),
            (template_dau_so.cuoc_1900, 'cuoc_1900'),
            (template_dau_so.quoc_te, 'quoc_te')
        ]
        for template_cuoc, loai_cuoc_attr in cuoc_goi_relations:
            if template_cuoc:
                new_cuoc = CuocGoi(
                    # Removed dau_so_id assignment
                    loai_cuoc=template_cuoc.loai_cuoc, # Use loai_cuoc from template object
                    thoi_gian_goi=template_cuoc.thoi_gian_goi,
                    cuoc=template_cuoc.cuoc
                    # Do not copy adjusted fields
                )
                # Assign the new CuocGoi object to the relationship attribute on new_dau_so
                setattr(new_dau_so, loai_cuoc_attr, new_cuoc) 
                log.debug(f"  Prepared CuocGoi copy: {loai_cuoc_attr}")
                # Removed: objects_to_add.append(new_cuoc) - let cascade handle saving

        # Copy CuocThueBao child and attach to new_dau_so
        if template_dau_so.cuoc_thue_bao:
            new_thue_bao = CuocThueBao(
                # Removed dau_so_id assignment
                thue_bao_thang=template_dau_so.cuoc_thue_bao.thue_bao_thang,
                cam_ket_thang=template_dau_so.cuoc_thue_bao.cam_ket_thang,
                tra_truoc_thang=template_dau_so.cuoc_thue_bao.tra_truoc_thang
                # Do not copy adjusted fields
            )
            # Assign the new CuocThueBao object to the relationship attribute on new_dau_so
            new_dau_so.cuoc_thue_bao = new_thue_bao
            log.debug(f"  Prepared CuocThueBao copy.")
            # Removed: objects_to_add.append(new_thue_bao) - let cascade handle saving

        # Add the fully prepared new_dau_so (with children attached) to the list
        objects_to_add.append(new_dau_so)
        # Removed: db.flush() 
        # Removed: dau_so_map - not used downstream currently

    # 3. Add all copied objects to the session
    if objects_to_add:
        db.add_all(objects_to_add)
        log.info(f"Added {len(objects_to_add)} copied detail objects (TongKet, DauSo, CuocGoi, CuocThueBao) to session for DSCD ID {new_dscd_id}.")
    else:
        log.warning(f"No details (TongKet, DauSoDichVu) found in template {template_dscd.id} to copy for DSCD ID {new_dscd_id}.")

def create_initial_dscd_reconciliation(
    db: Session,
    *,
    ky_doi_soat: str,
    template_id: int,
    partner_id: int
) -> DoiSoatCoDinh:
    """
    Create initial DSCD reconciliation record by deep copying from the template.
    Sets status to PROCESSING initially.
    """
    log.info(f"Creating initial DSCD reconciliation for period '{ky_doi_soat}', partner {partner_id} from template {template_id}")

    # 1. Find the template record
    template_dscd = db.query(DoiSoatCoDinh).filter(
        DoiSoatCoDinh.template_id == template_id, # Match template ID
        DoiSoatCoDinh.is_template_data == True    # Ensure it's the template marker
    ).options(
        joinedload(DoiSoatCoDinh.tong_ket) # Load tong_ket for copying
        # We load DauSoDichVu and children inside the copy helper
    ).first()

    if not template_dscd:
        log.error(f"Template DSCD data not found for template_id {template_id}")
        raise ValueError(f"Template data (is_template_data=True) not found for template ID {template_id}")
        
    # 2. Check for existing record for the target period/partner/template
    existing_record = db.query(DoiSoatCoDinh).filter(
        DoiSoatCoDinh.thang_doi_soat == ky_doi_soat,
        DoiSoatCoDinh.partner_id == partner_id,
        DoiSoatCoDinh.template_id == template_id,
        DoiSoatCoDinh.is_template_data == False # Look for actual data record
    ).first()
    
    if existing_record:
        log.warning(f"Actual DSCD reconciliation already exists for period '{ky_doi_soat}', partner {partner_id}, template {template_id}. ID: {existing_record.id}")
        # Depending on desired behavior, either return the existing one or raise error
        # For now, let's raise an error to prevent duplicates if creation is attempted again
        raise IntegrityError(
            f"Duplicate actual reconciliation record exists for {ky_doi_soat}, partner {partner_id}, template {template_id}",
            params=None, orig=None
        )

    try:
        # 3. Create the main DoiSoatCoDinh record for the new period
        new_doi_soat = DoiSoatCoDinh(
            thang_doi_soat=ky_doi_soat,
            partner_id=partner_id,
            template_id=template_id,
            is_template_data=False, # Mark as actual reconciliation data
            status=ReconciliationStatus.PROCESSING, # Start in PROCESSING state
            # Copy some metadata fields from template if desired
            hop_dong_so=template_dscd.hop_dong_so,
            tu_mang=template_dscd.tu_mang, # Assuming these are stable
            den_doi_tac=template_dscd.den_doi_tac # Assuming these are stable
            # file_name might be specific to the template upload, maybe don't copy?
        )
        db.add(new_doi_soat)
        db.flush() # Flush to get the new_doi_soat.id
        log.info(f"Created new DoiSoatCoDinh record with ID: {new_doi_soat.id} for period {ky_doi_soat}")

        # 4. Perform the deep copy of details
        _deep_copy_dscd_details(db, template_dscd, new_doi_soat.id)

        # 5. Commit the transaction
        db.commit()
        db.refresh(new_doi_soat) # Refresh to load the copied relationships
        log.info(f"Successfully created and deep copied DSCD reconciliation ID: {new_doi_soat.id}")
        
        return new_doi_soat

    except IntegrityError as ie: # Catch specific integrity errors (like duplicate check)
        db.rollback()
        log.error(f"Database integrity error during DSCD creation/copy: {ie}")
        raise
    except Exception as e:
        db.rollback()
        log.exception(f"Unexpected error creating/copying DSCD reconciliation: {e}")
        raise

# --- END: Add CRUD function for updating DSCD reconciliation ---

# --- START: Add CRUD function for finalizing DSCD reconciliation ---
def finalize_dscd_reconciliation(db: Session, dscd_id: int) -> Optional[DoiSoatCoDinh]:
    """
    Chốt một bản ghi đối soát cố định bằng cách cập nhật trạng thái thành FINALIZED.

    Args:
        db: Database session.
        dscd_id: ID của bản ghi DoiSoatCoDinh cần chốt.

    Returns:
        Đối tượng DoiSoatCoDinh đã được chốt, hoặc None nếu không tìm thấy.
        
    Raises:
        InvalidOperationError: Nếu bản ghi không ở trạng thái hợp lệ để chốt (e.g., đã chốt, là template).
    """
    log.info(f"Attempting to finalize DSCD reconciliation with ID: {dscd_id}")
    try:
        # Fetch the record
        doi_soat = db.query(DoiSoatCoDinh).filter(DoiSoatCoDinh.id == dscd_id).first()

        if not doi_soat:
            log.warning(f"Cannot finalize: DSCD reconciliation with ID {dscd_id} not found.")
            # raise NotFoundError("DoiSoatCoDinh", dscd_id)
            return None # Return None if not found

        # Check current status
        if doi_soat.status == ReconciliationStatus.FINALIZED:
            log.info(f"DSCD reconciliation ID {dscd_id} is already FINALIZED.")
            return doi_soat # Return the already finalized record
        
        if doi_soat.status == ReconciliationStatus.TEMPLATE:
            log.warning(f"Cannot finalize a TEMPLATE record (ID: {dscd_id}).")
            raise InvalidOperationError(f"Cannot finalize a template record (ID: {dscd_id}).")

        if doi_soat.status not in [ReconciliationStatus.CALCULATED, ReconciliationStatus.ADJUSTED]:
            log.warning(f"Cannot finalize DSCD ID {dscd_id}. Invalid current status: {doi_soat.status.value}")
            raise InvalidOperationError(f"Cannot finalize reconciliation with status '{doi_soat.status.value}'. Must be CALCULATED or ADJUSTED.")

        # Update status and timestamp
        doi_soat.status = ReconciliationStatus.FINALIZED
        doi_soat.updated_at = datetime.utcnow()
        
        # Commit changes
        db.commit()
        db.refresh(doi_soat) # Refresh to get the updated state
        
        log.info(f"Successfully finalized DSCD reconciliation ID: {dscd_id}")
        return doi_soat

    except InvalidOperationError: # Re-raise specific errors
        raise
    except Exception as e:
        log.exception(f"Database error finalizing DSCD reconciliation ID {dscd_id}. Rolling back.")
        db.rollback()
        raise # Re-raise other exceptions
# --- END: Add CRUD function for finalizing DSCD reconciliation ---

# --- START: Add CRUD function for deleting DSCD reconciliation ---
def delete_dscd_reconciliation(db: Session, dscd_id: int) -> bool:
    """
    Xóa một bản ghi đối soát cố định.

    Args:
        db: Database session.
        dscd_id: ID của bản ghi DoiSoatCoDinh cần xóa.

    Returns:
        True nếu xóa thành công, False nếu không tìm thấy.
        
    Raises:
        InvalidOperationError: Nếu bản ghi không thể bị xóa (ví dụ: đã được chốt - tùy thuộc logic).
    """
    log.info(f"Attempting to delete DSCD reconciliation with ID: {dscd_id}")
    try:
        # Fetch the record
        doi_soat = db.query(DoiSoatCoDinh).filter(DoiSoatCoDinh.id == dscd_id).first()

        if not doi_soat:
            log.warning(f"Cannot delete: DSCD reconciliation with ID {dscd_id} not found.")
            return False # Indicate not found

        # --- Optional: Status Check --- 
        # Add business logic here if deletion should be prevented based on status
        # Example: Prevent deletion of finalized records
        if doi_soat.status == ReconciliationStatus.FINALIZED:
            log.warning(f"Attempted to delete a FINALIZED DSCD reconciliation (ID: {dscd_id}). Operation denied.")
            raise InvalidOperationError(f"Cannot delete a finalized reconciliation (ID: {dscd_id}).")
        # ----------------------------- 

        # Delete the record
        # SQLAlchemy's cascade delete should handle related records (TongKet, DauSoDichVu, etc.)
        # if configured correctly in the models.
        db.delete(doi_soat)
        db.commit()
        
        log.info(f"Successfully deleted DSCD reconciliation ID: {dscd_id}")
        return True

    except InvalidOperationError:
         db.rollback() # Rollback if status check fails
         raise # Re-raise the specific error
    except Exception as e:
        log.exception(f"Database error deleting DSCD reconciliation ID {dscd_id}. Rolling back.")
        db.rollback()
        raise # Re-raise other exceptions
# --- END: Add CRUD function for deleting DSCD reconciliation ---

def update_dscd_adjustments(
    db: Session,
    *,
    dscd_id: int,
    adjustments: DoiSoatCoDinhAdjustmentsPayload
) -> Optional[DoiSoatCoDinh]:
    """
    Updates the adjusted fields for a DSCD reconciliation record. (Synchronous version)

    Args:
        db: Database session.
        dscd_id: The ID of the DSCD reconciliation to update.
        adjustments: The payload containing the adjusted values.

    Returns:
        The updated DoiSoatCoDinh object, or None if not found.

    Raises:
        NotFoundError: If the reconciliation record is not found.
        InvalidOperationError: If the data is invalid or status is FINALIZED.
    """
    log.info(f"Attempting to update adjustments for DSCD ID: {dscd_id}")

    reconciliation = None
    try:
        # 1. Fetch the main reconciliation record
        # log.info(f"[INFO] Executing query...") # Removed Log
        reconciliation = db.query(DoiSoatCoDinh).options(
            joinedload(DoiSoatCoDinh.tong_ket)
        ).filter(DoiSoatCoDinh.id == dscd_id).first()
        # log.info(f"[INFO] Query finished...") # Removed Log
    except Exception as query_exc:
        log.exception(f"!!! EXCEPTION DURING RECONCILIATION FETCH for ID {dscd_id} !!!")
        raise query_exc

    if not reconciliation:
        # log.warning(...) # Removed Log
        raise NotFoundError(f"Đối soát DSCD với ID {dscd_id} không tồn tại.")

    if reconciliation.status == ReconciliationStatus.FINALIZED:
        # log.warning(...) # Removed Log
        raise InvalidOperationError("Không thể hiệu chỉnh đối soát đã được chốt.")

    adjustment_made = False

    # 2. Update TongKet adjusted fields
    if adjustments.tong_ket and reconciliation.tong_ket:
        update_data = adjustments.tong_ket.dict(exclude_unset=True)
        if update_data:
             # log.info(f"[INFO] Updating tong_ket...") # Removed Log
             for field, value in update_data.items():
                 if getattr(reconciliation.tong_ket, field) != value:
                     setattr(reconciliation.tong_ket, field, value)
                     adjustment_made = True
             if adjustment_made:
                 db.add(reconciliation.tong_ket)

    # 3. Update DauSoDichVu adjusted fields
    # log.info(f"[INFO] Checking adjustments.du_lieu...") # Removed Log
    if adjustments.du_lieu:
        detail_ids = [item.id for item in adjustments.du_lieu if item.id is not None]
        # log.info(f"[INFO] Extracted detail_ids...") # Removed Log

        if detail_ids:
            existing_details_query = (
                select(DauSoDichVu)
                .options(
                    selectinload(DauSoDichVu.co_dinh_noi_hat),
                    selectinload(DauSoDichVu.co_dinh_lien_tinh),
                    selectinload(DauSoDichVu.di_dong),
                    selectinload(DauSoDichVu.cuoc_1900),
                    selectinload(DauSoDichVu.quoc_te),
                    selectinload(DauSoDichVu.cuoc_thue_bao)
                )
                .filter(DauSoDichVu.doi_soat_id == dscd_id)
                .filter(DauSoDichVu.id.in_(detail_ids))
            )
            # log.info(f"[INFO] Executing query...") # Removed Log
            existing_details = db.execute(existing_details_query).scalars().unique().all()
            existing_details_map = {detail.id: detail for detail in existing_details}
            # log.info(f"[INFO] Found {len...}") # Removed Log

            # log.info(f"[INFO] Starting loop...") # Removed Log
            for adj_item in adjustments.du_lieu:
                # log.info(f"[INFO] Processing adj_item...") # Removed Log
                detail_item = existing_details_map.get(adj_item.id)
                if detail_item:
                    item_changed = False
                    item_update_data = adj_item.dict(exclude={'id'}, exclude_unset=True)

                    for field, value in item_update_data.items():
                        if isinstance(value, dict):
                            nested_obj = getattr(detail_item, field, None)
                            nested_update_data = value
                            
                            if nested_obj:
                                # log.info(f"  [INFO] Updating existing...") # Removed Log
                                for nested_field, nested_value in nested_update_data.items():
                                    if nested_field.endswith('_adjusted'):
                                         current_value = getattr(nested_obj, nested_field, 'ATTRIBUTE_NOT_FOUND')
                                         # log.info(f"    [INFO] Comparing...") # Removed Log
                                         if current_value != nested_value:
                                             # log.info(f"      [INFO] Values differ...") # Removed Log
                                             setattr(nested_obj, nested_field, nested_value)
                                             item_changed = True
                                             # log.info(f"      [INFO] item_changed...") # Removed Log
                            else:
                                # log.info(f"Nested object not found...") # Removed Log
                                # ... (logic creating new nested object) ...
                                if new_nested_obj:
                                    # ... (logic assigning values to new nested object) ...
                                    for nested_field, nested_value in nested_update_data.items():
                                        if nested_field.endswith('_adjusted') and nested_value is not None:
                                            # log.info(f"    [INFO] Setting new...") # Removed Log
                                            setattr(new_nested_obj, nested_field, nested_value)
                                            item_changed = True
                                            # log.info(f"      [INFO] item_changed...") # Removed Log
                                    # log.info(f"  [INFO] Created...") # Removed Log
                                # else: log.warning(...) # Removed Log

                        elif field.endswith('_adjusted'):
                             current_value = getattr(detail_item, field, 'ATTRIBUTE_NOT_FOUND')
                             # log.info(f"  [INFO] Comparing direct...") # Removed Log
                             if current_value != value:
                                # log.info(f"    [INFO] Values differ...") # Removed Log
                                setattr(detail_item, field, value)
                                item_changed = True
                                # log.info(f"    [INFO] item_changed...") # Removed Log

                    if item_changed:
                         adjustment_made = True 
                         # log.info(f"  [INFO] >>> adjustment_made...") # Removed Log
                         db.add(detail_item)
                # else: log.warning(...) # Removed Log
        # else: log.info(...) # Removed Log

    # 4. Update reconciliation status
    if adjustment_made:
        # ... (update status logic) ...
        log.info(f"Adjustments applied successfully...")
    else:
         log.info(f"No effective adjustments...")

    try:
        if adjustment_made:
            db.commit()
            # ... (refresh logic) ...
    except Exception as e:
        db.rollback()
        log.error(f"Database error during adjustment update...", exc_info=True)
        raise

    return reconciliation