{"name": "<PERSON><PERSON><PERSON><PERSON> bản đối soát sản lư<PERSON><PERSON> và doanh thu cước dịch vụ GTGT từ Viettel đến số dịch vụ 19004, 18004 của HTC", "partner_type": "TELCO", "partner_name": "VIETTEL", "config": {"header": {"title": "BIÊN BẢN ĐỐI SOÁT SẢN LƯỢNG VÀ DOANH THU CƯỚC DỊCH VỤ GIÁ TRỊ GIA TĂNG", "subtitle": "TỪ VIETTEL ĐẾN SỐ DỊCH VỤ 19004, 18004 CỦA HTC", "period": "Tháng {{MONTH}}/{{YEAR}}", "legal_entities": [{"name": "TỔNG CÔNG TY DỊCH VỤ VIỄN THÔNG (VIETTEL TELECOM)", "representative": "Ông: {{PARTNER_REPRESENTATIVE}}", "position": "<PERSON><PERSON><PERSON> vụ: {{PARTNER_POSITION}}", "address": "Địa chỉ: <PERSON><PERSON> 1, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, Việt Nam", "tax_code": "Mã số thuế: 0104831387"}, {"name": "CÔNG TY CỔ PHẦN VIỄN THÔNG HÀ NỘI", "representative": "Bà: {{COMPANY_REPRESENTATIVE}}", "position": "<PERSON><PERSON><PERSON> vụ: {{COMPANY_POSITION}}", "address": "Địa chỉ: <PERSON><PERSON> 18A, <PERSON><PERSON> 201, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, Việt Nam", "tax_code": "Mã số thuế: 0101111589"}], "basis": ["- <PERSON><PERSON><PERSON> cứ và<PERSON> c<PERSON>c <PERSON>ợp đồng kết nối giữa Tổng công ty Dịch v<PERSON> <PERSON><PERSON>ễn thông (VIETTEL TELECOM) và Công ty Cổ phần V<PERSON>ễn thông HÀ NỘI;", "- <PERSON><PERSON><PERSON> cứ và<PERSON> c<PERSON> lục giữa Tổng công ty Dịch v<PERSON> Viễn thông (VIETTEL TELECOM) và Công ty Cổ phần Viễn thông HÀ NỘI;", "- <PERSON><PERSON><PERSON> cứ vào số liệu của Tổng công ty Dịch vụ <PERSON>iễn thông (VIETTEL TELECOM) và Công ty Cổ phần Viễn thông HÀ NỘI đưa ra."], "agreement": "Hôm nay, ngày {{SIGNED_DAY}} tháng {{SIGNED_MONTH}} năm {{SIGNED_YEAR}}, chúng tôi gồm đại diện của hai bên đã cùng tiến hành đối soát số liệu sản lượng và doanh thu cước dịch vụ giữa hai mạng viễn thông như sau:"}, "sections": {"viettel_to_htc_19004": {"title": "<PERSON><PERSON> <PERSON><PERSON><PERSON> lư<PERSON> và doanh thu dịch vụ 19004 từ VTL đến HTC", "services": [{"id": "19004_group1", "name": "19004000xx, 190046 (00-25 ; 90-99), 190045(00-39); 19004567, 190047(40-99);190046(26-49)", "price": 909, "stat_type": "DV_19004_G1"}, {"id": "19004_group2", "name": "19004001xx", "price": 1818, "stat_type": "DV_19004_G4"}, {"id": "19004_group3", "name": "190047(00-39)", "price": 2727, "stat_type": "DV_19004_G5"}, {"id": "19004_group4", "name": "19004002xx, 19004003xx, 19004004xx, 190045(56-80); trừ 19004567, 190046 (50-89)", "price": 4545, "stat_type": "DV_19004_G4"}, {"id": "19004_group5", "name": "190045(40-55; 81-99)", "price": 7273, "stat_type": "DV_19004_G5"}], "table_columns": [{"key": "stt", "label": "STT", "align": "center", "width": "50px"}, {"key": "service_name", "label": "Nhóm d<PERSON> vụ", "align": "left", "width": "200px"}, {"key": "volume", "label": "<PERSON><PERSON><PERSON>", "children": [{"key": "viettel_volume", "label": "VIETTEL", "align": "center", "width": "80px"}, {"key": "htc_volume", "label": "HTC", "align": "center", "width": "80px"}, {"key": "diff_percent", "label": "Tỷ lệ chênh lệch (%)", "align": "center", "width": "120px"}]}, {"key": "final_volume", "label": "SL tính cước", "align": "center", "width": "80px"}, {"key": "price", "label": "<PERSON><PERSON><PERSON> thu khách hàng", "align": "center", "width": "100px"}, {"key": "revenue", "label": "<PERSON><PERSON><PERSON> thu ăn chia (VNĐ)", "children": [{"key": "total_revenue", "label": "100% VTL thu kh<PERSON>ch hàng (VNĐ)", "align": "right", "width": "150px"}, {"key": "viettel_revenue", "label": "50% <PERSON><PERSON>h thu ăn chia Viettel hưởng", "align": "right", "width": "150px"}, {"key": "htc_revenue", "label": "50% <PERSON><PERSON>h thu ăn chia HTC hưởng", "align": "right", "width": "150px"}]}]}, "viettel_to_htc_18004": {"title": "II. <PERSON><PERSON><PERSON> l<PERSON> và doanh thu CKN 18004 từ VTL đến HTC", "services": [{"id": "18004_fixed", "name": "18004xxx - <PERSON><PERSON>", "price": 270, "stat_type": "DV_18004_FIXED"}, {"id": "18004_mobile", "name": "18004xxx - <PERSON>", "price": 415, "stat_type": "DV_18004_MOBILE"}], "table_columns": [{"key": "stt", "label": "STT", "align": "center", "width": "50px"}, {"key": "service_name", "label": "Nhóm d<PERSON> vụ", "align": "left", "width": "200px"}, {"key": "volume", "label": "<PERSON><PERSON><PERSON>", "children": [{"key": "viettel_volume", "label": "VIETTEL", "align": "center", "width": "80px"}, {"key": "htc_volume", "label": "HTC", "align": "center", "width": "80px"}, {"key": "diff_percent", "label": "Tỷ lệ chênh lệch (%)", "align": "center", "width": "120px"}]}, {"key": "final_volume", "label": "<PERSON><PERSON><PERSON> t<PERSON> c<PERSON>", "align": "center", "width": "120px"}, {"key": "price", "label": "Đơn giá CKN", "align": "center", "width": "80px"}, {"key": "htc_payment", "label": "HTC phải thanh toán CKN cho VTL", "children": [{"key": "revenue_before_tax", "label": "<PERSON><PERSON><PERSON> thu tr<PERSON><PERSON><PERSON> thuế", "align": "right", "width": "120px"}, {"key": "tax", "label": "Thuế GTGT", "align": "right", "width": "100px"}, {"key": "total_payment", "label": "<PERSON><PERSON><PERSON> cộng", "align": "right", "width": "120px"}]}]}, "payment_summary": {"title": "III. <PERSON><PERSON>", "table_columns": [{"key": "stt", "label": "STT", "align": "center", "width": "50px"}, {"key": "item", "label": "THÀNH TIỀN", "align": "left", "width": "200px"}, {"key": "viettel_to_htc", "label": "I. VTL phải trả cho HTC (VNĐ)", "align": "right", "width": "180px"}, {"key": "htc_to_viettel", "label": "II. HTC phải trả cho VTL (VNĐ)", "align": "right", "width": "180px"}, {"key": "final_diff", "label": "<PERSON><PERSON> tr<PERSON> (I - II) (VNĐ)", "align": "right", "width": "180px"}], "items": [{"id": "pre_tax", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> thuế"}, {"id": "tax", "name": "Thuế GTGT"}, {"id": "total", "name": "<PERSON><PERSON><PERSON> cộng"}]}}, "calculations": {"diff_percent": "(partner_volume - our_volume) / partner_volume * 100", "final_volume": "partner_volume", "revenue_19004": "final_volume * price", "revenue_share_19004": "revenue_19004 / 2", "revenue_18004": "final_volume * price", "tax": "revenue * 0.1", "total": "revenue + tax"}, "validations": {"diff_percent_warning": 5, "volume_min": 0}, "footer": {"notes": ["<PERSON><PERSON><PERSON> v<PERSON>y sau bù trừ, Viettel phải trả HTC tổng số tiền là:", "Bằng chữ: {{AMOUNT_IN_WORDS}}"], "signatures": [{"label": "TỔNG CÔNG TY VIỄN THÔNG VIETTEL", "positions": ["TUQ.TỔNG GIÁM ĐỐC", "P.ĐỐI SOÁT"]}, {"label": "CÔNG TY CỔ PHẦN VIỄN THÔNG HÀ NỘI", "positions": ["P.ĐỐI SOÁT", "TUQ. TỔNG GIÁM ĐỐC"]}], "location": "<PERSON><PERSON> n<PERSON>, ng<PERSON>y {{SIGNED_DAY}} tháng {{SIGNED_MONTH}} năm {{SIGNED_YEAR}}"}}}