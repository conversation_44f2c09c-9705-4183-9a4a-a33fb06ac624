{"name": "<PERSON><PERSON><PERSON><PERSON> bản số liệu và doanh thu cư<PERSON><PERSON> kết nối giữa Viettel và HTC", "partner_type": "TELCO", "partner_name": "VIETTEL", "config": {"header": {"title": "BIÊN BẢN ĐỐI SOÁT SỐ LIỆU VÀ DOANH THU CƯỚC KẾT NỐI", "subtitle": "GIỮA VIETTEL VÀ HTC", "period": "Tháng {{MONTH}}/{{YEAR}}", "legal_entities": [{"name": "TỔNG CÔNG TY DỊCH VỤ VIỄN THÔNG (VIETTEL TELECOM)", "representative": "Ông: {{PARTNER_REPRESENTATIVE}}", "position": "<PERSON><PERSON><PERSON> vụ: {{PARTNER_POSITION}}", "address": "Địa chỉ: <PERSON><PERSON> 1, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, Việt Nam", "tax_code": "Mã số thuế: 0104831387"}, {"name": "CÔNG TY CỔ PHẦN VIỄN THÔNG HÀ NỘI", "representative": "Bà: {{COMPANY_REPRESENTATIVE}}", "position": "<PERSON><PERSON><PERSON> vụ: {{COMPANY_POSITION}}", "address": "Địa chỉ: <PERSON><PERSON> 18A, <PERSON><PERSON> 201, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, Việt Nam", "tax_code": "Mã số thuế: 0101111589"}], "basis": ["- <PERSON><PERSON><PERSON> cứ và<PERSON> c<PERSON>c <PERSON>ợp đồng kết nối giữa Tổng công ty Dịch v<PERSON> <PERSON><PERSON>ễn thông (VIETTEL TELECOM) và Công ty Cổ phần V<PERSON>ễn thông HÀ NỘI;", "- <PERSON><PERSON><PERSON> cứ và<PERSON> c<PERSON> lục giữa Tổng công ty Dịch v<PERSON> Viễn thông (VIETTEL TELECOM) và Công ty Cổ phần Viễn thông HÀ NỘI;", "- <PERSON><PERSON><PERSON> cứ vào số liệu của Tổng công ty Dịch vụ <PERSON>iễn thông (VIETTEL TELECOM) và Công ty Cổ phần Viễn thông HÀ NỘI đưa ra."], "agreement": "Hôm nay, ngày {{SIGNED_DAY}} tháng {{SIGNED_MONTH}} năm {{SIGNED_YEAR}}, chúng tôi gồm đại diện của hai bên đã cùng tiến hành đối soát số liệu sản lượng và doanh thu cước kết nối giữa hai mạng viễn thông như sau:"}, "sections": {"htc_to_viettel": {"title": "<PERSON><PERSON> <PERSON><PERSON> liệu t<PERSON>h cư<PERSON>c hướng từ cố định HTC đến Viettel", "services": [{"id": "cd_to_cd", "name": "Từ CD HTC tới CDViettel", "price": 270, "stat_type": "CD_TO_CD"}, {"id": "cd_to_dd", "name": "Từ CD HTC đến DD Viettel", "price": 270, "stat_type": "CD_TO_DD"}, {"id": "cd_port_out", "name": "Từ CD HTC đến thuê bao di động Viettel đã Port_out ra mạng ngoài", "price": 470, "stat_type": "CD_PORT_OUT"}, {"id": "cd_intl", "name": "CD Viettel sử dụng dịch vụ HTC gọi đi <PERSON>uố<PERSON> tế (phút)", "price": 550, "stat_type": "CD_INTL"}], "table_columns": [{"key": "stt", "label": "STT", "align": "center", "width": "50px"}, {"key": "service_name", "label": "<PERSON><PERSON>i dung", "align": "left", "width": "300px"}, {"key": "volume", "label": "<PERSON><PERSON><PERSON>", "children": [{"key": "viettel_volume", "label": "VIETTEL", "align": "center", "width": "80px"}, {"key": "htc_volume", "label": "HTC", "align": "center", "width": "80px"}, {"key": "diff_percent", "label": "Tỷ lệ chênh lệch (%)", "align": "center", "width": "120px"}]}, {"key": "final_volume", "label": "SL tính cước", "align": "center", "width": "80px"}, {"key": "price", "label": "Đơn giá CKN", "align": "center", "width": "80px"}, {"key": "revenue", "label": "<PERSON><PERSON><PERSON>u <PERSON> (VNĐ)", "children": [{"key": "revenue_before_tax", "label": "<PERSON><PERSON><PERSON> thu tr<PERSON><PERSON><PERSON> thuế", "align": "right", "width": "120px"}, {"key": "tax", "label": "Thuế GTGT", "align": "right", "width": "100px"}, {"key": "total_revenue", "label": "<PERSON><PERSON><PERSON> cộng", "align": "right", "width": "120px"}]}, {"key": "payer", "label": "<PERSON><PERSON><PERSON> tr<PERSON>", "align": "center", "width": "80px"}]}, "viettel_to_htc": {"title": "II. <PERSON><PERSON> li<PERSON> t<PERSON>h cư<PERSON>c hướng từ Viettel đến HTC", "services": [{"id": "cd_to_cd", "name": "Từ CDViettel đến CD HTC (phút)", "price": 270, "stat_type": "CD_TO_CD_VTL"}, {"id": "dd_to_cd", "name": "Từ DD Viettel đến CD HTC (phút)", "price": 270, "stat_type": "DD_TO_CD"}, {"id": "cd_intl", "name": "CD HTC sử dụng dịch vụ Viettel gọi đi <PERSON>uố<PERSON> tế (phút)", "price": 550, "stat_type": "CD_INTL_VTL"}], "table_columns": [{"key": "stt", "label": "STT", "align": "center", "width": "50px"}, {"key": "service_name", "label": "<PERSON><PERSON>i dung", "align": "left", "width": "300px"}, {"key": "volume", "label": "<PERSON><PERSON><PERSON>", "children": [{"key": "viettel_volume", "label": "VIETTEL", "align": "center", "width": "80px"}, {"key": "htc_volume", "label": "HTC", "align": "center", "width": "80px"}, {"key": "diff_percent", "label": "Tỷ lệ chênh lệch (%)", "align": "center", "width": "120px"}]}, {"key": "final_volume", "label": "SL tính cước", "align": "center", "width": "80px"}, {"key": "price", "label": "Đơn giá CKN", "align": "center", "width": "80px"}, {"key": "revenue", "label": "<PERSON><PERSON><PERSON>u <PERSON> (VNĐ)", "children": [{"key": "revenue_before_tax", "label": "<PERSON><PERSON><PERSON> thu tr<PERSON><PERSON><PERSON> thuế", "align": "right", "width": "120px"}, {"key": "tax", "label": "Thuế GTGT", "align": "right", "width": "100px"}, {"key": "total_revenue", "label": "<PERSON><PERSON><PERSON> cộng", "align": "right", "width": "120px"}]}, {"key": "payer", "label": "<PERSON><PERSON><PERSON> tr<PERSON>", "align": "center", "width": "80px"}]}, "payment_summary": {"title": "III. <PERSON><PERSON>", "table_columns": [{"key": "stt", "label": "STT", "align": "center", "width": "50px"}, {"key": "item", "label": "THÀNH TIỀN", "align": "left", "width": "200px"}, {"key": "viettel_to_htc", "label": "I. VTL phải trả cho HTC (VNĐ)", "align": "right", "width": "180px"}, {"key": "htc_to_viettel", "label": "II. HTC phải trả cho VTL (VNĐ)", "align": "right", "width": "180px"}, {"key": "final_diff", "label": "<PERSON><PERSON> tr<PERSON> (I - II) (VNĐ)", "align": "right", "width": "180px"}], "items": [{"id": "pre_tax", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> thuế"}, {"id": "tax", "name": "Thuế GTGT"}, {"id": "total", "name": "<PERSON><PERSON><PERSON> cộng"}]}}, "calculations": {"diff_percent": "(partner_volume - our_volume) / partner_volume * 100", "final_volume": "our_volume", "revenue": "final_volume * price", "vat": "revenue * 0.1", "total": "revenue + vat"}, "validations": {"diff_percent_warning": 5, "volume_min": 0}, "footer": {"notes": ["<PERSON><PERSON><PERSON> v<PERSON>y sau bù trừ, {{PAYER}} phải trả {{PAYEE}} tổng số tiền là: {{AMOUNT}}", "Bằng chữ: {{AMOUNT_IN_WORDS}}"], "signatures": [{"label": "TỔNG CÔNG TY VIỄN THÔNG VIETTEL", "positions": ["TUQ.TỔNG GIÁM ĐỐC", "P.ĐỐI SOÁT"]}, {"label": "CÔNG TY CỔ PHẦN VIỄN THÔNG HÀ NỘI", "positions": ["P.ĐỐI SOÁT", "TUQ. TỔNG GIÁM ĐỐC"]}], "location": "<PERSON><PERSON> n<PERSON>, ng<PERSON>y {{SIGNED_DAY}} tháng {{SIGNED_MONTH}} năm {{SIGNED_YEAR}}"}}}