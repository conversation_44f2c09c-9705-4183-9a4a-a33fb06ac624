"""
<PERSON><PERSON><PERSON> to create sample data for the application.
This script adds sample volume ranges to the database.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import from src
sys.path.append(str(Path(__file__).parent.parent.parent))

from sqlalchemy.orm import Session
from sqlalchemy import select, func
from ..database.session import get_db
from ..models.volume_range import VolumeRange, VolumeUnit
from ..models.enums import ServiceType  # Import enum mới

def create_volume_ranges(db: Session):
    """Create sample volume ranges if they don't exist."""
    # Check if we already have volume ranges using select()
    existing_check_stmt = select(VolumeRange.id).limit(1)
    does_any_exist = db.scalar(existing_check_stmt) is not None
    
    if does_any_exist:
        print(f"Volume ranges already exist. Skipping creation.")
        return
    
    # Sample volume ranges
    volume_ranges = [
        # Phút/tháng ranges
        VolumeRange(
            min_value=0,
            max_value=1000,
            unit=VolumeUnit.MINUTE_PER_MONTH,
            description="0-1,000 phút/tháng"
        ),
        VolumeRange(
            min_value=1001,
            max_value=5000,
            unit=VolumeUnit.MINUTE_PER_MONTH,
            description="1,001-5,000 phút/tháng"
        ),
        VolumeRange(
            min_value=5001,
            max_value=10000,
            unit=VolumeUnit.MINUTE_PER_MONTH,
            description="5,001-10,000 phút/tháng"
        ),
        VolumeRange(
            min_value=10001,
            max_value=50000,
            unit=VolumeUnit.MINUTE_PER_MONTH,
            description="10,001-50,000 phút/tháng"
        ),
        VolumeRange(
            min_value=50001,
            max_value=None,
            unit=VolumeUnit.MINUTE_PER_MONTH,
            description="Trên 50,000 phút/tháng"
        ),
        
        # VND-based ranges
        VolumeRange(
            min_value=0,
            max_value=50000000,
            unit=VolumeUnit.VND,
            description="0-50,000,000 VNĐ"
        ),
        VolumeRange(
            min_value=50000001,
            max_value=100000000,
            unit=VolumeUnit.VND,
            description="50,000,001-100,000,000 VNĐ"
        ),
        VolumeRange(
            min_value=100000001,
            max_value=None,
            unit=VolumeUnit.VND,
            description=">100,000,000 VNĐ"
        ),
    ]
    
    # Add to database
    for volume_range in volume_ranges:
        db.add(volume_range)
    
    db.commit()
    print(f"Created {len(volume_ranges)} volume ranges.")

def main():
    """Main function to create sample data."""
    # Get database session
    db = next(get_db())
    
    try:
        # Create volume ranges
        create_volume_ranges(db)
        
        print("Sample data created successfully!")
    except Exception as e:
        print(f"Error creating sample data: {e}")
        db.rollback()
        sys.exit(1)
    finally:
        db.close()

if __name__ == "__main__":
    main() 