import sys
import psycopg2
import os

def check_templates():
    try:
        # Connect to database - use 'db' as hostname which is the service name in docker-compose
        conn = psycopg2.connect(
            host="db",
            database="audit_call",
            user="postgres",
            password="hieuda87",
            port="5432"
        )
        
        # Create cursor
        cur = conn.cursor()
        
        # Execute query - fix table name to reconciliation_templates
        cur.execute("SELECT id, name, partner_name, is_active FROM reconciliation_templates")
        
        # Fetch results
        templates = cur.fetchall()
        
        print("Total templates: {}".format(len(templates)))
        print("\nTemplate details:")
        for i, template in enumerate(templates, 1):
            print("{}. ID: {}".format(i, template[0]))
            print("   Name: {}".format(template[1]))
            print("   Partner: {}".format(template[2]))
            print("   Active: {}".format(template[3]))
            print("")
        
        # Close cursor and connection
        cur.close()
        conn.close()
        
    except Exception as e:
        print("Error checking templates: {}".format(str(e)))

if __name__ == "__main__":
    check_templates() 