import psycopg2

def list_tables():
    try:
        # Connect to database - use 'db' as hostname which is the service name in docker-compose
        conn = psycopg2.connect(
            host="db",
            database="audit_call",
            user="postgres",
            password="hieuda87",
            port="5432"
        )
        
        # Create cursor
        cur = conn.cursor()
        
        # Execute query to list all tables
        cur.execute("SELECT tablename FROM pg_catalog.pg_tables WHERE schemaname='public'")
        
        # Fetch results
        tables = cur.fetchall()
        
        print("Tables in database:")
        for table in tables:
            print(table[0])
        
        # Close cursor and connection
        cur.close()
        conn.close()
        
    except Exception as e:
        print("Error listing tables: {}".format(str(e)))

if __name__ == "__main__":
    list_tables() 