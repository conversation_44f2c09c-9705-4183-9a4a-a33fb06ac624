from fastapi import <PERSON><PERSON><PERSON>, API<PERSON>outer, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON>2Password<PERSON>earer
import subprocess
from sqlalchemy.orm import Session, configure_mappers
import sys
import logging
from .api.v1.api import api_router
from .core.config import settings
from .database.session import SessionLocal, engine, get_db
from .core.security import get_password_hash
from .core.seed_partners import seed_partners
from sqlalchemy import select
from .database import base
from .models.user import User
from . import models

# Cấu hình logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Tạo logger cho mỗi module
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Run migrations
def run_migrations():
    try:
        subprocess.run(["alembic", "upgrade", "head"], check=True)
        print("Migrations completed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"Error running migrations: {e}")
        raise e

# Modified: Create default admin user (takes db session)
def create_default_admin(db: Session) -> None:
    try:
        # Check if admin exists using SQLAlchemy 2.0 style, querying only ID
        stmt = select(User.id).where(User.email == "<EMAIL>")
        admin_exists = db.scalar(stmt) is not None
        
        if not admin_exists:
            print("Creating default admin user...")
            admin = User(
                email="<EMAIL>",
                full_name="Admin User",
                hashed_password=get_password_hash("LXic7FhXlHMm0aJ"),
                is_admin=True,
                is_active=True
            )
            db.add(admin)
            print("Default admin user prepared.")
        else:
            print("Default admin user already exists.")
    except Exception as e:
        print(f"Error during default admin check/creation: {str(e)}")
        raise

# New: Dependency to run initial setup
def run_initial_setup(db: Session = Depends(get_db)):
    print("Running initial setup dependency...")
    try:
        create_default_admin(db)
        seed_partners(db)
        db.commit()
        print("Initial setup committed.")
    except Exception as e:
        print(f"Error during initial setup dependency: {str(e)}")
        db.rollback()

# Create FastAPI app
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    swagger_ui_init_oauth={
        "usePkceWithAuthorizationCodeGrant": True,
        "clientId": "swagger",
    },
    dependencies=[Depends(run_initial_setup)]
)

# Set all CORS enabled origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(api_router, prefix=settings.API_V1_STR)

# --- Define the main startup event --- 
def startup_event_main():
    """Logic to run on application startup."""
    logger.info("Running application startup logic...")
    try:
        # Option 1: Run Alembic migrations (Recommended for production)
        # logger.info("Running database migrations via Alembic...")
        # try:
        #     command = ["alembic", "upgrade", "head"]
        #     # Run in a way that works within the container
        #     # Consider using alembic's programmatic API if subprocess is problematic
        #     subprocess.run(command, check=True, text=True, capture_output=True)
        #     logger.info("Alembic migrations completed.")
        # except Exception as alembic_err:
        #     logger.exception("Alembic migration failed!")
        #     # Decide how to proceed - maybe raise to stop startup?

        # Option 2: Create tables directly (Simpler, good for tests/dev)
        logger.info("Creating database tables if they don't exist (using create_all)...")
        base.Base.metadata.create_all(bind=engine)
        logger.info("Database tables checked/created.")

        # Configure mappers AFTER tables are ensured and models are loaded
        logger.info("Configuring SQLAlchemy mappers...")
        configure_mappers()
        logger.info("SQLAlchemy mappers configured.")
        
        # Note: Seeding (admin, partners) is handled by the `run_initial_setup` dependency
        logger.info("Startup event finished. Initial data seeding will run via dependency.")
        
    except Exception as e:
        logger.exception("Critical error during startup event.")
        raise

# Register the main startup event
app.on_event("startup")(startup_event_main)

# --- Common part: Routers and CORS ---

# Xóa khối code CORS thứ hai bị lặp ở dưới

app.include_router(api_router, prefix=settings.API_V1_STR)

# Optional: Configure mappers after routers are included, as an alternative placement
# logger.info("Configuring SQLAlchemy mappers after router inclusion...")
# configure_mappers()
# logger.info("SQLAlchemy mappers configured (after router)." )
