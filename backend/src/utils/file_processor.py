import os
import logging
import asyncio
from typing import Optional, Dict, Any
import shutil
from uuid import uuid4

from .doi_soat_classifier import DoiSoatClassifier
from .dst_1800_1900_processor import process_dst_1800_1900_file
from .dsc_processor import process_dsc_file  # <PERSON><PERSON><PERSON> sử hàm này tồn tại
from .dscd_processor import process_dscd_file  # <PERSON><PERSON><PERSON> sử hàm này tồn tại

# Cấu hình logging
logger = logging.getLogger(__name__)

# Thư mục lưu file
UPLOAD_DIR = "uploads"
DOI_SOAT_DIR = os.path.join(UPLOAD_DIR, "doi_soat")
os.makedirs(DOI_SOAT_DIR, exist_ok=True)

class ProcessingResult:
    """Kết quả xử lý file"""
    def __init__(
        self,
        success: bool,
        file_id: str,
        doi_soat_type: str,
        processing_status: str,
        message: str,
        detail: Optional[Dict[str, Any]] = None
    ):
        self.success = success
        self.file_id = file_id
        self.doi_soat_type = doi_soat_type
        self.processing_status = processing_status
        self.message = message
        self.detail = detail or {}

def save_upload_file(upload_file, prefix: str = "") -> str:
    """
    Lưu file upload vào thư mục tạm và trả về đường dẫn
    
    Args:
        upload_file: UploadFile từ FastAPI
        prefix: Tiền tố cho tên file (tùy chọn)
        
    Returns:
        Đường dẫn đến file đã lưu
    """
    # Tạo tên file duy nhất
    file_extension = os.path.splitext(upload_file.filename)[1]
    unique_filename = f"{prefix}{uuid4()}{file_extension}"
    
    # Đường dẫn đầy đủ
    file_path = os.path.join(DOI_SOAT_DIR, unique_filename)
    
    # Lưu file
    try:
        with open(file_path, "wb") as f:
            shutil.copyfileobj(upload_file.file, f)
        
        return file_path
    except Exception as e:
        logger.error(f"Lỗi khi lưu file: {str(e)}")
        raise e

async def process_file_async(
    file_path: str, 
    file_id: str, 
    doi_soat_type: str, 
    partner_id: Optional[int] = None
) -> ProcessingResult:
    """
    Xử lý file đối soát bất đồng bộ
    
    Args:
        file_path: Đường dẫn đến file cần xử lý
        file_id: ID của file
        doi_soat_type: Loại đối soát (CO_DINH, CUOC, 1800_1900)
        partner_id: ID của đối tác liên quan (tùy chọn)
        
    Returns:
        ProcessingResult chứa kết quả xử lý
    """
    try:
        # Xử lý dựa trên loại đối soát
        result = None
        
        if doi_soat_type == "1800_1900":
            # Xử lý đối soát 1800_1900
            result = process_dst_1800_1900_file(file_path, partner_id=partner_id)
            
        elif doi_soat_type == "CO_DINH":
            # Xử lý đối soát cố định
            result = process_dscd_file(file_path, partner_id=partner_id)
            
        elif doi_soat_type == "CUOC":
            # Xử lý đối soát cước
            result = process_dsc_file(file_path, partner_id=partner_id)
            
        else:
            # Loại không xác định, thử phát hiện lại
            detected_type, confidence = DoiSoatClassifier.classify(file_path)
            
            if detected_type == "1800_1900":
                result = process_dst_1800_1900_file(file_path, partner_id=partner_id)
                doi_soat_type = "1800_1900"
            elif detected_type == "CO_DINH":
                result = process_dscd_file(file_path, partner_id=partner_id)
                doi_soat_type = "CO_DINH"
            elif detected_type == "CUOC":
                result = process_dsc_file(file_path, partner_id=partner_id)
                doi_soat_type = "CUOC"
            else:
                return ProcessingResult(
                    success=False,
                    file_id=file_id,
                    doi_soat_type="UNKNOWN",
                    processing_status="ERROR",
                    message="Không thể phát hiện loại đối soát",
                    detail={"confidence": confidence, "partner_id": partner_id}
                )
        
        # Lưu kết quả vào database (phần này sẽ triển khai sau)
        # save_result_to_db(file_id, result, doi_soat_type, partner_id)
        
        # Trả về kết quả
        return ProcessingResult(
            success=True,
            file_id=file_id,
            doi_soat_type=doi_soat_type,
            processing_status="COMPLETED",
            message=f"Đã xử lý thành công file {file_id} loại {doi_soat_type}",
            detail={
                "result": result.dict() if hasattr(result, "dict") else None,
                "partner_id": partner_id
            }
        )
        
    except Exception as e:
        logger.error(f"Lỗi khi xử lý file {file_id}: {str(e)}")
        return ProcessingResult(
            success=False,
            file_id=file_id,
            doi_soat_type=doi_soat_type,
            processing_status="ERROR",
            message=f"Lỗi khi xử lý file: {str(e)}",
            detail={"error": str(e), "partner_id": partner_id}
        ) 