from sqlalchemy.orm import Session
import logging
from typing import Optional

from ..models.dsc import Doi<PERSON>oat<PERSON>uo<PERSON>, DauSoDichVuCuoc, TongKetCuoc
from ..models.dscd import DoiSoatCoDinh, DauSoDichVu, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TongKet
from ..models.dst_1800_1900 import (
    DoiSoat1800_1900, NhomDichVu1800_1900, ChiTietDichVu1800_1900, 
    TongKet1800_1900, ThanhToan1800_1900
)
from ..models.template import ReconciliationTemplate
from ..schemas.dsc import DoiSoatCuoc as DoiSoatCuocSchema
from ..schemas.dscd import DoiSoatCoDinh as DoiSoatCoDinhSchema
from ..schemas.dst_1800_1900 import DoiSoat1800_1900 as DoiSoat1800_1900Schema

# Cấu hình logging
logger = logging.getLogger(__name__)

def save_doi_soat_cuoc_to_db(db: Session, template_id: int, schema_data: DoiSoatCuocSchema) -> DoiSoatCuoc:
    """
    L<PERSON>u kết quả đối soát cước vào database
    
    Args:
        db: Database session
        template_id: ID của template gốc
        schema_data: Dữ liệu đối soát cước (schema)
        
    Returns:
        Đối tượng DoiSoatCuoc đã lưu
    """
    # <<< Log chi tiết kiểu và nội dung của dữ liệu nhận được >>>
    logger.info(f"[save_doi_soat_cuoc_to_db] Received schema_data type: {type(schema_data)}")
    try:
        # Cố gắng log nội dung dictionary nếu là Pydantic model
        logger.info(f"[save_doi_soat_cuoc_to_db] Received schema_data content (dict): {schema_data.dict()}")
        logger.info(f"[save_doi_soat_cuoc_to_db] Checking for 'dau_so_dich_vu' attribute: {hasattr(schema_data, 'dau_so_dich_vu')}")
        if hasattr(schema_data, 'dau_so_dich_vu') and schema_data.dau_so_dich_vu is not None:
             logger.info(f"[save_doi_soat_cuoc_to_db] schema_data.dau_so_dich_vu type: {type(schema_data.dau_so_dich_vu)}, length: {len(schema_data.dau_so_dich_vu)}")
        else:
             logger.info(f"[save_doi_soat_cuoc_to_db] schema_data.dau_so_dich_vu is None or attribute doesn't exist.")
             
    except Exception as log_err:
        logger.error(f"[save_doi_soat_cuoc_to_db] Error logging schema_data details: {log_err}")
    # <<< Kết thúc log chi tiết >>>
    
    try:
        # 1. Tạo đối tượng DoiSoatCuoc model instance trống
        doi_soat = DoiSoatCuoc()
        # Gán các thuộc tính từ schema_data
        doi_soat.thang_doi_soat = schema_data.thang_doi_soat
        doi_soat.tu_mang = schema_data.tu_mang
        doi_soat.den_doi_tac = schema_data.den_doi_tac
        doi_soat.file_name = schema_data.file_name
        doi_soat.hop_dong_so = schema_data.hop_dong_so
        doi_soat.template_id = template_id
        doi_soat.is_template_data = True
        doi_soat.partner_id = schema_data.partner_id
        
        # 2. Tạo danh sách các đối tượng con DauSoDichVuCuoc từ schema
        child_models = []
        if hasattr(schema_data, 'dau_so_dich_vu') and schema_data.dau_so_dich_vu:
            for item in schema_data.dau_so_dich_vu:
                # Tạo instance trống
                du_lieu_model = DauSoDichVuCuoc()
                # Gán thuộc tính
                du_lieu_model.stt = item.stt
                du_lieu_model.dau_so = item.dau_so
                du_lieu_model.vnm_san_luong = item.vnm_san_luong
                du_lieu_model.vnm_ty_le_cp = item.vnm_ty_le_cp
                du_lieu_model.vnm_thanh_tien = item.vnm_thanh_tien
                du_lieu_model.viettel_san_luong = item.viettel_san_luong
                du_lieu_model.viettel_ty_le_cp = item.viettel_ty_le_cp
                du_lieu_model.viettel_thanh_tien = item.viettel_thanh_tien
                du_lieu_model.vnpt_san_luong = item.vnpt_san_luong
                du_lieu_model.vnpt_ty_le_cp = item.vnpt_ty_le_cp
                du_lieu_model.vnpt_thanh_tien = item.vnpt_thanh_tien
                du_lieu_model.vms_san_luong = item.vms_san_luong
                du_lieu_model.vms_ty_le_cp = item.vms_ty_le_cp
                du_lieu_model.vms_thanh_tien = item.vms_thanh_tien
                du_lieu_model.khac_san_luong = item.khac_san_luong
                du_lieu_model.khac_ty_le_cp = item.khac_ty_le_cp
                du_lieu_model.khac_thanh_tien = item.khac_thanh_tien
                du_lieu_model.tong_thanh_toan = item.tong_thanh_toan
                
                child_models.append(du_lieu_model)
        
        # 3. Tạo đối tượng TongKetCuoc từ schema
        tong_ket_model = None
        if schema_data.tong_ket:
            # Tạo instance trống
            tong_ket_model = TongKetCuoc()
            # Gán thuộc tính
            tong_ket_model.cong_tien_dich_vu = schema_data.tong_ket.cong_tien_dich_vu
            tong_ket_model.tien_thue_gtgt = schema_data.tong_ket.tien_thue_gtgt
            tong_ket_model.tong_cong_tien = schema_data.tong_ket.tong_cong_tien
            
        # 4. Gán trực tiếp danh sách con và tổng kết vào relationship attribute của cha
        #    Làm điều này TRƯỚC KHI thêm đối tượng cha vào session
        doi_soat.du_lieu = child_models
        if tong_ket_model:
            doi_soat.tong_ket = tong_ket_model
        
        # 5. Thêm đối tượng cha (đã có relationship được thiết lập) vào session
        db.add(doi_soat)
        
        # 6. Flush session để lấy ID và đảm bảo không có lỗi constraint
        db.flush()
        
        logger.info(f"Đã tạo DoiSoatCuoc (ID: {doi_soat.id}) với {len(doi_soat.du_lieu)} đầu số")
        return doi_soat
        
    except Exception as e:
        logger.error(f"Lỗi khi lưu đối soát cước: {str(e)}")
        # Log traceback để có thêm chi tiết
        import traceback
        logger.error(traceback.format_exc())
        raise

def save_doi_soat_co_dinh_to_db(db: Session, template_id: int, data: DoiSoatCoDinhSchema) -> DoiSoatCoDinh:
    """
    Lưu kết quả đối soát cố định vào database
    
    Args:
        db: Database session
        template_id: ID của template gốc
        data: Dữ liệu đối soát cố định (schema)
        
    Returns:
        Đối tượng DoiSoatCoDinh đã lưu
    """
    try:
        # Lấy thông tin template để trích xuất partner_id
        template = db.query(ReconciliationTemplate).filter(ReconciliationTemplate.id == template_id).first()
        
        # Tạo đối tượng DoiSoatCoDinh với đầy đủ thông tin
        doi_soat = DoiSoatCoDinh(
            thang_doi_soat=data.thang_doi_soat,
            hop_dong_so=data.hop_dong_so,
            file_name=data.file_name,
            tu_mang=data.tu_mang,
            den_doi_tac=data.den_doi_tac,
            template_id=template_id,
            partner_id=template.partner_id if template else None,
            is_template_data=True
        )
        db.add(doi_soat)
        db.flush()  # Lấy ID mới
        
        # Thêm dữ liệu chi tiết
        for item in data.du_lieu:
            # Tạo đối tượng DauSoDichVu model từ schema item
            dau_so_model = DauSoDichVu(
                doi_soat_id=doi_soat.id,
                stt=item.stt,
                # Sử dụng các trường đã chuẩn hóa từ schema
                raw_dau_so=item.raw_dau_so,
                standardized_display=item.standardized_display,
                dau_so_type=item.dau_so_type,
                number_count=item.number_count,
                start_num_str=item.start_num_str,
                end_num_str=item.end_num_str,
                prefix=item.prefix,
                cuoc_thu_khach=item.cuoc_thu_khach,
                cuoc_tra_htc=item.cuoc_tra_htc
            )
            # Gắn các đối tượng con (CuocGoi, CuocThueBao) vào relationship
            # của đối tượng dau_so_model TRƯỚC KHI add vào session
            
            # Tạo và gán CuocGoi
            cuoc_goi_models = [
                CuocGoi(
                    # dau_so_id sẽ được SQLAlchemy tự động gán khi flush
                    loai_cuoc="co_dinh_noi_hat",
                    thoi_gian_goi=item.co_dinh_noi_hat.thoi_gian_goi,
                    cuoc=item.co_dinh_noi_hat.cuoc
                ),
                CuocGoi(
                    loai_cuoc="co_dinh_lien_tinh",
                    thoi_gian_goi=item.co_dinh_lien_tinh.thoi_gian_goi,
                    cuoc=item.co_dinh_lien_tinh.cuoc
                ),
                CuocGoi(
                    loai_cuoc="di_dong",
                    thoi_gian_goi=item.di_dong.thoi_gian_goi,
                    cuoc=item.di_dong.cuoc
                ),
                 CuocGoi(
                    loai_cuoc="cuoc_1900",
                    thoi_gian_goi=item.cuoc_1900.thoi_gian_goi,
                    cuoc=item.cuoc_1900.cuoc
                ),
                 CuocGoi(
                    loai_cuoc="quoc_te",
                    thoi_gian_goi=item.quoc_te.thoi_gian_goi,
                    cuoc=item.quoc_te.cuoc
                )
            ]
            # Gán vào relationship - cần đảm bảo tên relationship đúng trong model DauSoDichVu
            # Giả sử relationship được định nghĩa đúng cách để nhận list CuocGoi
            dau_so_model.co_dinh_noi_hat = cuoc_goi_models[0]
            dau_so_model.co_dinh_lien_tinh = cuoc_goi_models[1]
            dau_so_model.di_dong = cuoc_goi_models[2]
            dau_so_model.cuoc_1900 = cuoc_goi_models[3]
            dau_so_model.quoc_te = cuoc_goi_models[4]

            # Tạo và gán CuocThueBao
            cuoc_thue_bao_model = CuocThueBao(
                # dau_so_id sẽ được SQLAlchemy tự động gán
                thue_bao_thang=item.cuoc_thue_bao.thue_bao_thang,
                cam_ket_thang=item.cuoc_thue_bao.cam_ket_thang,
                tra_truoc_thang=item.cuoc_thue_bao.tra_truoc_thang
            )
            dau_so_model.cuoc_thue_bao = cuoc_thue_bao_model

            # Add đối tượng cha (DauSoDichVu model) vào session
            # SQLAlchemy sẽ tự xử lý việc add các đối tượng con đã gán qua relationship
            db.add(dau_so_model)
        
        # Lưu tổng kết
        if data.tong_ket: # Kiểm tra xem tong_ket có tồn tại không
            tong_ket_model = TongKet(
                # doi_soat_id sẽ được SQLAlchemy tự động gán
                cong_tien_dich_vu=data.tong_ket.cong_tien_dich_vu,
                tien_thue_gtgt=data.tong_ket.tien_thue_gtgt,
                tong_cong_tien=data.tong_ket.tong_cong_tien
            )
            doi_soat.tong_ket = tong_ket_model # Gán vào relationship của DoiSoatCoDinh
            # Không cần db.add(tong_ket_model) nếu relationship được cấu hình đúng (cascade)
        
        # Commit tất cả thay đổi một lần ở cuối nếu không có lỗi
        # db.commit() # Nên commit ở cuối cùng trong task Celery thay vì ở đây
        
        logger.info(f"Đã chuẩn bị DoiSoatCoDinh (ID: {doi_soat.id}) với {len(data.du_lieu)} đầu số để lưu")
        return doi_soat
        
    except Exception as e:
        logger.error(f"Lỗi khi lưu đối soát cố định: {str(e)}")
        raise

def save_doi_soat_1800_1900_to_db(db: Session, template_id: int, data: DoiSoat1800_1900Schema) -> DoiSoat1800_1900:
    """
    Lưu kết quả đối soát 1800/1900 vào database
    
    Args:
        db: Database session
        template_id: ID của template gốc
        data: Dữ liệu đối soát 1800/1900 (schema)
        
    Returns:
        Đối tượng DoiSoat1800_1900 đã lưu
    """
    try:
        # Tạo đối tượng DoiSoat1800_1900
        doi_soat = DoiSoat1800_1900(
            thang_doi_soat=data.thang_doi_soat,
            tu_mang=data.tu_mang,
            den_doi_tac=data.den_doi_tac,
            loai_dich_vu=data.loai_dich_vu,
            hop_dong_so=data.hop_dong_so,
            file_name=data.file_name,
            ghi_chu=data.ghi_chu
        )
        db.add(doi_soat)
        db.flush()  # Lấy ID mới
        
        # Thêm các nhóm dịch vụ
        for nhom in data.nhom_dich_vu:
            nhom_dich_vu = NhomDichVu1800_1900(
                doi_soat_id=doi_soat.id,
                ten_dich_vu=nhom.ten_dich_vu
            )
            db.add(nhom_dich_vu)
            db.flush()  # Lấy ID mới
            
            # Thêm chi tiết dịch vụ trong nhóm
            for ct in nhom.chi_tiet:
                chi_tiet = ChiTietDichVu1800_1900(
                    nhom_dich_vu_id=nhom_dich_vu.id,
                    stt=ct.stt,
                    so_dich_vu=ct.so_dich_vu,
                    so_lieu_ben_a=ct.so_lieu_ben_a,
                    so_lieu_ben_b=ct.so_lieu_ben_b,
                    chenh_lech=ct.chenh_lech,
                    so_lieu_tinh_dt=ct.so_lieu_tinh_dt,
                    muc_cuoc=ct.muc_cuoc,
                    cuoc_thu_khach=ct.cuoc_thu_khach,
                    doanh_thu_ben_a=ct.doanh_thu_ben_a,
                    doanh_thu_ben_b=ct.doanh_thu_ben_b
                )
                db.add(chi_tiet)
            
            # Thêm tổng kết cho nhóm
            if hasattr(nhom, "tong_ket") and nhom.tong_ket:
                tong_ket = TongKet1800_1900(
                    nhom_dich_vu_id=nhom_dich_vu.id,
                    so_luong_ben_a=nhom.tong_ket.so_luong_ben_a,
                    so_luong_ben_b=nhom.tong_ket.so_luong_ben_b,
                    chenh_lech=nhom.tong_ket.chenh_lech,
                    tong_dt_ben_a=nhom.tong_ket.tong_dt_ben_a,
                    tong_dt_ben_b=nhom.tong_ket.tong_dt_ben_b
                )
                db.add(tong_ket)
        
        # Lưu thông tin thanh toán
        if data.thanh_toan:
            thanh_toan = ThanhToan1800_1900(
                doi_soat_id=doi_soat.id,
                doanh_thu_ben_a_thanh_toan=data.thanh_toan.doanh_thu_ben_a_thanh_toan,
                doanh_thu_ben_b_thanh_toan=data.thanh_toan.doanh_thu_ben_b_thanh_toan,
                sau_bu_tru=data.thanh_toan.sau_bu_tru,
                ben_thanh_toan=data.thanh_toan.ben_thanh_toan,
                ben_nhan=data.thanh_toan.ben_nhan,
                ghi_chu=data.thanh_toan.ghi_chu
            )
            db.add(thanh_toan)
        
        logger.info(f"Đã lưu DoiSoat1800_1900 (ID: {doi_soat.id}) với {len(data.nhom_dich_vu)} nhóm dịch vụ")
        return doi_soat
        
    except Exception as e:
        logger.error(f"Lỗi khi lưu đối soát 1800/1900: {str(e)}")
        raise

def sync_template_with_dscd(db: Session, template_id: int) -> bool:
    """
    Đồng bộ hóa dữ liệu template với bảng DoiSoatCoDinh
    
    Args:
        db: Database session
        template_id: ID của template cần đồng bộ
        
    Returns:
        True nếu đồng bộ thành công, False nếu không
    """
    try:
        # Lấy thông tin template
        template = db.query(ReconciliationTemplate).filter(
            ReconciliationTemplate.id == template_id
        ).first()
        
        if not template or template.template_type != "dscd":
            logger.warning(f"Template {template_id} không tồn tại hoặc không phải là mẫu DSCD")
            return False
            
        if not template.processed_data:
            logger.warning(f"Template {template_id} chưa có dữ liệu phân tích")
            return False
        
        logger.info(f"Processed data: {template.processed_data}")
            
        # Kiểm tra nếu đã có bản ghi trong DoiSoatCoDinh
        dscd = db.query(DoiSoatCoDinh).filter(
            DoiSoatCoDinh.template_id == template_id
        ).first()
        
        # Nếu chưa có, tạo mới từ dữ liệu JSON
        if not dscd:
            logger.info(f"Tạo mới bản ghi DoiSoatCoDinh cho template {template_id}")
            
            try:
                # Tạo schema từ JSON
                from ..schemas.dscd import DoiSoatCoDinh as DoiSoatCoDinhSchema
                dscd_schema = DoiSoatCoDinhSchema(**template.processed_data)
                logger.info(f"Đã tạo schema từ dữ liệu: {dscd_schema}")
                
                # Lưu vào database
                result = save_doi_soat_co_dinh_to_db(db, template_id, dscd_schema)
                logger.info(f"Đã lưu DoiSoatCoDinh với ID: {result.id}")
                return True
            except Exception as e:
                logger.error(f"Lỗi khi tạo mới bản ghi DoiSoatCoDinh: {str(e)}")
                raise
            
        # Nếu đã có, cập nhật từ dữ liệu JSON
        else:
            logger.info(f"Cập nhật bản ghi DoiSoatCoDinh cho template {template_id}")
            
            # Cập nhật các trường cơ bản
            dscd.thang_doi_soat = template.processed_data.get("thang_doi_soat", dscd.thang_doi_soat)
            dscd.hop_dong_so = template.processed_data.get("hop_dong_so", dscd.hop_dong_so)
            dscd.tu_mang = template.processed_data.get("tu_mang", dscd.tu_mang)
            dscd.den_doi_tac = template.processed_data.get("den_doi_tac", dscd.den_doi_tac)
            
            # Cập nhật dữ liệu chi tiết là phức tạp, cần triển khai sau
            # TODO: Cập nhật dữ liệu DauSoDichVu, CuocGoi, CuocThueBao, TongKet
            
            db.commit()
            return True
            
    except Exception as e:
        logger.error(f"Lỗi khi đồng bộ template với DoiSoatCoDinh: {str(e)}")
        return False 