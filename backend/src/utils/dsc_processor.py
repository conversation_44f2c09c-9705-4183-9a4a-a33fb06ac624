import os
import re
import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime

from ..schemas.dsc import DoiSoatCuoc, DauSoDichVuCuoc, TongKetCuoc

# Cấu hình logging
logger = logging.getLogger(__name__)

# Các mẫu regex để nhận dạng file đối soát cước
DSC_PATTERNS = [
    r'Doi\s*soat\s*cuoc',
    r'DoiSoatCuoc',
    r'Đối\s*soát\s*cước',
    r'Doanh\s*thu\s*cước',
]

# Keywords để tìm cột dữ liệu
SAN_LUONG_KEYWORDS = ["SẢN LƯỢNG", "SAN LUONG", "CUỘC GỌI"]
THANH_TIEN_KEYWORDS = ["THÀNH TIỀN", "THANH TIEN", "DOANH THU"]
TELCO_KEYWORDS = {
    "vnm": ["VIETNAMOBILE", "VNM", "VIETNAM MOBILE"],
    "viettel": ["VIETTEL", "VT"],
    "vnpt": ["VNPT", "VINAPHONE"],
    "vms": ["MOBIFONE", "VMS", "MOBI"],
    "khac": ["KHÁC", "KHAC", "OTHER", "CÒN LẠI", "CON LAI"]
}

# Keywords để tìm cột số điện thoại
PHONE_COLUMN_KEYWORDS = [
    "ĐẦU SỐ", "DAU SO", "SỐ ĐIỆN THOẠI", "SO DIEN THOAI", 
    "SĐT", "SDT", "HOTLINE", "PHONE", "TÊN ĐẦU SỐ", "TEN DAU SO"
]

# Các dòng dữ liệu cần xác định
DATA_START_ROW = 8  # Dòng bắt đầu dữ liệu
HEADER_ROW_INDEX = 5  # Dòng header chính

def is_dsc_file(filename: str) -> bool:
    """
    Kiểm tra xem file có phải là file đối soát cước không
    
    Args:
        filename: Tên file cần kiểm tra
        
    Returns:
        True nếu là file đối soát cước, False nếu không phải
    """
    # Chuyển filename thành lowercase để so sánh không phân biệt chữ hoa/thường
    filename_lower = filename.lower()
    
    # Kiểm tra các mẫu regex
    for pattern in DSC_PATTERNS:
        if re.search(pattern, filename, re.IGNORECASE):
            return True
    
    return False

def extract_month_year(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Trích xuất thông tin tháng và năm từ tên file
    
    Args:
        filename: Tên file đối soát cước
        
    Returns:
        Tuple chứa (tháng, năm) hoặc (None, None) nếu không trích xuất được
    """
    filename = os.path.basename(filename)
    
    # Kiểm tra mẫu "T1_2025" hoặc "T1-2025" hoặc "T1/2025"
    pattern1 = r'[Tt](\d{1,2})[\s_\-\/](\d{4})'
    matches = re.search(pattern1, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Kiểm tra mẫu "thang 1 nam 2025" hoặc "thang1/2025"
    pattern2 = r'[Tt]h[aá]ng\s*(\d{1,2})[\s_\-\/]*(?:[Nn][aă]m\s*)?(\d{4})'
    matches = re.search(pattern2, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Kiểm tra mẫu "1-2025" hoặc "1/2025" hoặc "01_2025"
    pattern3 = r'(\d{1,2})[\-\/\_](\d{4})(?:\.|$)'
    matches = re.search(pattern3, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Kiểm tra mẫu "_01_2025" hoặc "_1_2025"
    pattern4 = r'_(\d{1,2})_(\d{4})'
    matches = re.search(pattern4, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    return None, None

def extract_partners(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Trích xuất thông tin đối tác từ tên file
    
    Args:
        filename: Tên file đối soát cước
        
    Returns:
        Tuple chứa (từ_mạng, đến_đối_tác) hoặc (None, None) nếu không trích xuất được
    """
    filename = os.path.basename(filename)
    
    # Mẫu HTC_TO_ITEL hoặc HTC TO ITEL
    pattern_to = r'([A-Za-z0-9]+)[_\-\s]+[Tt][Oo][_\-\s]+([A-Za-z0-9]+)'
    matches = re.search(pattern_to, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Mẫu cụ thể: Doi soat cuoc_HTC_ITEL_T1_2025.xlsx
    pattern_dsc = r'Doi\s*soat\s*cuoc[_\-\s]+([A-Za-z0-9]+)[_\-\s]+([A-Za-z0-9]+)'
    matches = re.search(pattern_dsc, filename, re.IGNORECASE)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Kiểm tra mẫu phổ biến: HTC_<ĐỐI TÁC>_<THÔNG TIN KHÁC>
    pattern_partner = r'([A-Za-z0-9]+)[_\-]([A-Za-z0-9]+)[_\-]'
    matches = re.search(pattern_partner, filename)
    if matches:
        partner1, partner2 = matches.groups()
        
        # Loại bỏ số và các thông tin tháng năm
        if re.match(r'\d+', partner2) or re.match(r'[tT]\d+', partner2):
            # Thử tìm partner khác trong tên file
            for word in re.findall(r'[A-Za-z]{3,}', filename):
                if word.upper() not in ['DOI', 'SOAT', 'CUOC', 'THANG', 'NAM', 'DOANH', 'THU', 'REPORT', 'FILE']:
                    if word.upper() != partner1.upper():
                        return partner1, word
        
        # Thường HTC là mạng nguồn
        if partner1.upper() == "HTC":
            return partner1, partner2
        elif partner2.upper() == "HTC":
            return partner2, partner1
        
        # Nếu không có HTC, giả định partner1 là mạng nguồn
        return partner1, partner2
    
    # Thử cho các trường hợp tên file phức tạp hơn
    words = re.findall(r'[A-Za-z0-9]+', filename)
    htc_idx = None
    
    # Tìm HTC trong danh sách từ
    for i, word in enumerate(words):
        if word.upper() == "HTC":
            htc_idx = i
            break
    
    if htc_idx is not None:
        # Ưu tiên từ phía sau HTC
        if htc_idx + 1 < len(words):
            next_word = words[htc_idx + 1]
            # Nếu từ tiếp theo dường như là một đối tác (không phải số/tháng/lệnh)
            if (not re.match(r'^\d+$', next_word) and 
                next_word.upper() not in ['TO', 'T', 'THANG', 'NAM', 'CUOC', 'SOAT']):
                return 'HTC', next_word
        
        # Nếu không tìm thấy đối tác phù hợp sau HTC, tìm các từ khác
        for word in words:
            # Tìm từ dài ít nhất 3 ký tự, không phải là từ phổ biến,
            # không phải là HTC, và không phải là số
            if (len(word) >= 3 and 
                word.upper() not in ['DOI', 'SOAT', 'CUOC', 'THANG', 'NAM', 'DOANH', 'THU', 'HTC', 'REPORT', 'FILE'] and
                not re.match(r'^\d+$', word) and
                not re.match(r'^[tT]\d+$', word)):
                return 'HTC', word
    
    # Mẫu HTC_ITEL, HTC-ITEL
    pattern_basic = r'([A-Za-z0-9]+)[_\-\s]+(?:[Tt][Oo][_\-\s]+)?([A-Za-z0-9]+)'
    matches = re.search(pattern_basic, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    return None, None

def normalize_phone_number(phone: str) -> str:
    """
    Chuẩn hóa số điện thoại
    
    Args:
        phone: Số điện thoại cần chuẩn hóa
        
    Returns:
        Số điện thoại đã chuẩn hóa
    """
    if not phone or not isinstance(phone, str):
        return ""
    
    # Loại bỏ các ký tự không phải số
    normalized = re.sub(r'[^\d]', '', phone)
    
    # Loại bỏ mã quốc gia 84 ở đầu nếu có và thêm số 0
    if normalized.startswith('84') and len(normalized) >= 10:
        normalized = '0' + normalized[2:]
    
    return normalized

def extract_phone_numbers_from_excel(file_path: str) -> List[str]:
    """
    Trích xuất số điện thoại từ file Excel
    
    Args:
        file_path: Đường dẫn đến file Excel
        
    Returns:
        Danh sách các số điện thoại đã trích xuất
    """
    try:
        # Đọc file Excel
        xls = pd.ExcelFile(file_path)
        sheet_name = xls.sheet_names[0]  # Mặc định lấy sheet đầu tiên
        df = pd.read_excel(xls, sheet_name=sheet_name, header=None)
        
        phone_numbers = []
        phone_column_idx = None
        
        # Tìm cột chứa số điện thoại
        for row_idx in range(min(10, df.shape[0])):  # Chỉ kiểm tra 10 dòng đầu
            for col_idx in range(df.shape[1]):
                cell_value = str(df.iloc[row_idx, col_idx]) if not pd.isna(df.iloc[row_idx, col_idx]) else ""
                cell_upper = cell_value.upper()
                
                # Kiểm tra từ khóa trong tên cột
                if any(keyword in cell_upper for keyword in PHONE_COLUMN_KEYWORDS):
                    phone_column_idx = col_idx
                    logger.info(f"Đã tìm thấy cột số điện thoại tại cột {col_idx+1}: '{cell_value}'")
                    break
            
            if phone_column_idx is not None:
                break
        
        # Nếu không tìm thấy cột theo từ khóa, thử tìm cột có định dạng giống số điện thoại
        if phone_column_idx is None:
            logger.info("Không tìm thấy cột số điện thoại bằng từ khóa, đang tìm theo mẫu...")
            
            # Mẫu số điện thoại Việt Nam
            phone_pattern = re.compile(r'(0|\+84|84)?[35789]\d{8}')
            
            max_matches = 0
            for col_idx in range(df.shape[1]):
                matches = 0
                for row_idx in range(10, min(30, df.shape[0])):  # Kiểm tra từ dòng 10 đến 30
                    cell_value = str(df.iloc[row_idx, col_idx]) if not pd.isna(df.iloc[row_idx, col_idx]) else ""
                    if phone_pattern.search(cell_value):
                        matches += 1
                
                if matches > max_matches:
                    max_matches = matches
                    phone_column_idx = col_idx
            
            if max_matches > 0:
                logger.info(f"Tìm thấy cột số điện thoại theo mẫu tại cột {phone_column_idx+1} với {max_matches} kết quả")
        
        # Nếu vẫn không tìm thấy, sử dụng cột thứ 2 làm mặc định (thường là cột đầu số)
        if phone_column_idx is None:
            phone_column_idx = 1
            logger.info(f"Không tìm thấy cột số điện thoại, sử dụng cột mặc định (cột {phone_column_idx+1})")
        
        # Lặp qua các dòng để trích xuất số điện thoại
        for row_idx in range(df.shape[0]):
            if pd.isna(df.iloc[row_idx, phone_column_idx]):
                continue
                
            cell_value = str(df.iloc[row_idx, phone_column_idx])
            
            # Kiểm tra nếu giá trị là số điện thoại hợp lệ
            normalized = normalize_phone_number(cell_value)
            if normalized and len(normalized) >= 4:  # Số điện thoại có ít nhất 4 chữ số
                phone_numbers.append(normalized)
        
        logger.info(f"Đã trích xuất {len(phone_numbers)} số điện thoại từ file")
        return phone_numbers
    
    except Exception as e:
        logger.error(f"Lỗi khi trích xuất số điện thoại: {str(e)}")
        return []

def filter_phone_numbers(phones: List[str], prefix: Optional[str] = None) -> List[str]:
    """
    Lọc danh sách số điện thoại theo tiền tố
    
    Args:
        phones: Danh sách số điện thoại
        prefix: Tiền tố để lọc (ví dụ: '1900', '1800')
        
    Returns:
        Danh sách số điện thoại sau khi lọc
    """
    if not prefix:
        return phones
    
    return [phone for phone in phones if phone.startswith(prefix)]

def get_phone_number_stats(phones: List[str]) -> Dict[str, int]:
    """
    Thống kê số điện thoại theo đầu số
    
    Args:
        phones: Danh sách số điện thoại
        
    Returns:
        Dictionary với key là đầu số, value là số lượng
    """
    stats = {}
    for phone in phones:
        prefix = phone[:4] if len(phone) >= 4 else phone
        stats[prefix] = stats.get(prefix, 0) + 1
    
    return dict(sorted(stats.items(), key=lambda x: x[1], reverse=True))

def save_phone_numbers_to_file(phones: List[str], output_path: str) -> str:
    """
    Lưu danh sách số điện thoại vào file
    
    Args:
        phones: Danh sách số điện thoại
        output_path: Đường dẫn lưu file
        
    Returns:
        Đường dẫn đến file đã lưu
    """
    try:
        with open(output_path, 'w') as f:
            for phone in phones:
                f.write(f"{phone}\n")
        logger.info(f"Đã lưu {len(phones)} số điện thoại vào file {output_path}")
        return output_path
    except Exception as e:
        logger.error(f"Lỗi khi lưu số điện thoại vào file: {str(e)}")
        return ""

def extract_phones_from_dsc(file_path: str, prefix: Optional[str] = None) -> List[str]:
    """
    Trích xuất số điện thoại từ file đối soát cước
    
    Args:
        file_path: Đường dẫn đến file Excel
        prefix: Tiền tố để lọc (ví dụ: '1900', '1800')
        
    Returns:
        Danh sách các số điện thoại đã trích xuất và lọc
    """
    all_phones = extract_phone_numbers_from_excel(file_path)
    filtered_phones = filter_phone_numbers(all_phones, prefix)
    
    # Xóa trùng lặp
    unique_phones = list(set(filtered_phones))
    
    logger.info(f"Đã trích xuất {len(all_phones)} số, sau khi lọc còn {len(filtered_phones)}, sau khi xóa trùng lặp còn {len(unique_phones)}")
    return unique_phones

def process_dsc_file(file_path: str, prefix: Optional[str] = None, partner_id: Optional[int] = None) -> DoiSoatCuoc:
    """
    Xử lý file đối soát cước thành dữ liệu có cấu trúc
    
    Args:
        file_path: Đường dẫn đến file đối soát cước
        prefix: Tiền tố số điện thoại để lọc (tùy chọn)
        partner_id: ID của đối tác liên quan (tùy chọn)
        
    Returns:
        Đối tượng DoiSoatCuoc chứa thông tin đối soát
    """
    try:
        # Đọc file Excel
        file_name = os.path.basename(file_path)
        xls = pd.ExcelFile(file_path)
        
        # Mặc định lấy sheet đầu tiên
        sheet_name = xls.sheet_names[0]
        # Đọc không dùng header để dễ dàng quét các dòng đầu
        df = pd.read_excel(xls, sheet_name=sheet_name, header=None) 
        
        # --- Bắt đầu thay đổi: Trích xuất metadata từ nội dung ---
        thang_doi_soat = None # Không trích xuất tháng/năm từ mẫu
        nam_doi_soat = None
        tu_mang = None
        den_doi_tac = None
        hop_dong_so = None
        
        # Quét các dòng đầu để tìm metadata
        max_header_rows_to_scan = 10 # Quét tối đa 10 dòng đầu
        for i in range(min(max_header_rows_to_scan, df.shape[0])):
            try:
                # Đảm bảo đọc giá trị ô dưới dạng string
                row_str = ' '.join(str(df.iloc[i, col]) for col in range(df.shape[1]) if not pd.isna(df.iloc[i, col])).upper()
                
                # Tìm Tháng/Năm (Ví dụ, mặc dù ta không dùng nhưng để tham khảo)
                # date_match = re.search(r'THÁNG\s+(\d{1,2})\s*[/\\-]?\s*NĂM\s+(\d{4})', row_str) or \
                #              re.search(r'THÁNG\s+(\d{1,2})\s*[/\\-]\s*(\d{4})', row_str)
                # if date_match:
                #     thang_doi_soat = date_match.group(1)
                #     nam_doi_soat = date_match.group(2)

                # Tìm Từ mạng và Đến đối tác
                # Mẫu: "TỪ MẠNG <TÊN MẠNG> ĐẾN SỐ DỊCH VỤ CỦA <TÊN ĐỐI TÁC>"
                partner_match = re.search(r'TỪ\s*MẠNG\s*(\S+.*?)\s*ĐẾN\s*(?:SỐ\s*DỊCH\s*VỤ\s*CỦA)?\s*(\S+.*)', row_str)
                if partner_match:
                    # Lấy phần tên mạng, loại bỏ dấu ':' nếu có
                    tu_mang = partner_match.group(1).replace(':', '').strip() 
                    # Lấy phần tên đối tác, loại bỏ dấu ':' nếu có
                    den_doi_tac = partner_match.group(2).replace(':', '').strip() 

                # Tìm hợp đồng số
                hop_dong_match = re.search(r'HỢP\s*ĐỒNG\s*SỐ\s*[.:…]?\s*(\S+)', row_str)
                if hop_dong_match:
                    hop_dong_so = hop_dong_match.group(1)
                    
            except Exception as scan_err:
                logger.warning(f"Lỗi khi quét dòng header {i} để tìm metadata: {scan_err}")
        
        # Gán giá trị mặc định nếu không tìm thấy trong nội dung
        tu_mang = tu_mang or "HTC" # Mặc định
        den_doi_tac = den_doi_tac or "KHÔNG XÁC ĐỊNH" # Mặc định
        
        logger.info(f"Metadata extracted from content: tu_mang='{tu_mang}', den_doi_tac='{den_doi_tac}', hop_dong_so='{hop_dong_so}'")
        # --- Kết thúc thay đổi: Trích xuất metadata từ nội dung ---

        # Tìm các cột dữ liệu quan trọng
        san_luong_cols = {}
        ty_le_cp_cols = {}
        thanh_tien_cols = {}
        
        # Xác định vị trí các cột
        for j in range(df.shape[1]):
            col_name = str(df.iloc[HEADER_ROW_INDEX, j]) if not pd.isna(df.iloc[HEADER_ROW_INDEX, j]) else ""
            col_name_upper = col_name.upper()
            
            # Xác định nhà mạng
            telco_type = None
            for telco, keywords in TELCO_KEYWORDS.items():
                if any(keyword in col_name_upper for keyword in keywords):
                    telco_type = telco
                    break
            
            if telco_type:
                # Xác định loại dữ liệu (sản lượng, tỷ lệ CP, thành tiền)
                if any(keyword in col_name_upper for keyword in SAN_LUONG_KEYWORDS):
                    san_luong_cols[telco_type] = j
                elif "TỶ LỆ" in col_name_upper or "CP" in col_name_upper:
                    ty_le_cp_cols[telco_type] = j
                elif any(keyword in col_name_upper for keyword in THANH_TIEN_KEYWORDS):
                    thanh_tien_cols[telco_type] = j
            
            # Tìm cột STT và Đầu số
            if "STT" in col_name_upper:
                stt_col = j
            elif "ĐẦU SỐ" in col_name_upper or "DAU SO" in col_name_upper:
                dau_so_col = j
            elif "TỔNG" in col_name_upper and "THANH TOÁN" in col_name_upper:
                tong_thanh_toan_col = j
        
        # Mặc định nếu không tìm thấy
        stt_col = stt_col if 'stt_col' in locals() else 0
        dau_so_col = dau_so_col if 'dau_so_col' in locals() else 1
        tong_thanh_toan_col = tong_thanh_toan_col if 'tong_thanh_toan_col' in locals() else df.shape[1] - 1
        
        # Danh sách lưu các đầu số dịch vụ
        du_lieu = []
        
        # Trích xuất số điện thoại từ file
        phone_numbers = extract_phone_numbers_from_excel(file_path)
        if prefix:
            phone_numbers = filter_phone_numbers(phone_numbers, prefix)
        
        # Loại bỏ trùng lặp
        phone_numbers = list(set(phone_numbers))
        logger.info(f"Đã trích xuất {len(phone_numbers)} số điện thoại từ file {file_name}")
        
        # Map để lưu trữ thông tin về đầu số đã được trích xuất từ bảng dữ liệu
        dau_so_map = {}
        
        # Xử lý dữ liệu từ bảng trong file Excel
        try:
            # Tìm dòng bắt đầu dữ liệu thực tế
            row_idx = DATA_START_ROW
            while row_idx < df.shape[0]:
                if not pd.isna(df.iloc[row_idx, stt_col]):
                    try:
                        stt_val = df.iloc[row_idx, stt_col]
                        if isinstance(stt_val, (int, float)) and stt_val > 0:
                            break
                    except:
                        pass
                row_idx += 1
            
            # Lặp qua các dòng dữ liệu
            stt_counter = 1  # Bộ đếm STT
            while row_idx < df.shape[0]:
                # Kiểm tra nếu đã đến phần tổng kết
                cell_value = df.iloc[row_idx, 0]
                if pd.isna(cell_value) or (isinstance(cell_value, str) and ("Cộng tiền" in cell_value or "TỔNG" in cell_value.upper())):
                    break
                
                # Trích xuất dữ liệu từ dòng hiện tại
                try:
                    # STT và đầu số
                    stt = int(float(df.iloc[row_idx, stt_col])) if not pd.isna(df.iloc[row_idx, stt_col]) else 0
                    dau_so = str(df.iloc[row_idx, dau_so_col]) if not pd.isna(df.iloc[row_idx, dau_so_col]) else ""
                    
                    # Chỉ xử lý dòng có đầu số hợp lệ
                    if dau_so and re.match(r'^\d+$', dau_so.strip()):
                        dau_so = dau_so.strip()
                        
                        # <<< Log giá trị đọc từ DataFrame >>>
                        log_values = {
                            'row': row_idx,
                            'stt': stt,
                            'dau_so': dau_so,
                            # Chỉ log cột tổng thanh toán vì các cột khác sẽ được set = 0
                            'tong_tt_idx': tong_thanh_toan_col, 
                            'tong_tt_val': df.iloc[row_idx, tong_thanh_toan_col] if tong_thanh_toan_col is not None and not pd.isna(df.iloc[row_idx, tong_thanh_toan_col]) else 'N/A'
                        }
                        logger.info(f"[process_dsc_file] Raw values read (only TongTT relevant): {log_values}")
                        # <<< Kết thúc log giá trị đọc >>>
                        
                        # Tạo đối tượng dòng dữ liệu schema
                        # <<< Force carrier-specific fields to 0, read only tong_thanh_toan >>>
                        dau_so_data = DauSoDichVuCuoc(
                            stt=stt,
                            dau_so=dau_so,
                            # VNM
                            vnm_san_luong=0,
                            vnm_ty_le_cp=0,
                            vnm_thanh_tien=0,
                            # VIETTEL
                            viettel_san_luong=0,
                            viettel_ty_le_cp=0,
                            viettel_thanh_tien=0,
                            # VNPT
                            vnpt_san_luong=0,
                            vnpt_ty_le_cp=0,
                            vnpt_thanh_tien=0,
                            # VMS
                            vms_san_luong=0,
                            vms_ty_le_cp=0,
                            vms_thanh_tien=0,
                            # Các mạng còn lại
                            khac_san_luong=0,
                            khac_ty_le_cp=0,
                            khac_thanh_tien=0,
                            # Tổng thanh toán - Force to 0
                            tong_thanh_toan=0
                        )
                        
                        # Lưu vào map đầu số để sau này có thể tìm kiếm nhanh
                        dau_so_map[dau_so] = dau_so_data
                        du_lieu.append(dau_so_data)
                except Exception as e:
                    logger.warning(f"Lỗi khi xử lý dòng {row_idx}: {str(e)}")
                
                row_idx += 1
        except Exception as e:
            logger.error(f"Lỗi khi xử lý dữ liệu chính: {str(e)}")
        
        # Xử lý phần tổng kết
        cong_tien_dich_vu = 0
        tien_thue_gtgt = 0
        tong_cong_tien = 0
        
        try:
            # Tìm dòng Cộng tiền Dịch vụ
            for i in range(row_idx, min(row_idx + 15, df.shape[0])):
                cell_value = str(df.iloc[i, 0]).strip() if not pd.isna(df.iloc[i, 0]) else ""
                
                if "Cộng tiền" in cell_value or "TỔNG CỘNG" in cell_value.upper():
                    cong_tien_dich_vu = float(df.iloc[i, tong_thanh_toan_col]) if not pd.isna(df.iloc[i, tong_thanh_toan_col]) else sum(item.tong_thanh_toan for item in du_lieu)
                    
                    # Kiểm tra dòng tiếp theo có phải là thuế GTGT
                    if i+1 < df.shape[0]:
                        thue_text = str(df.iloc[i+1, 0]).strip() if not pd.isna(df.iloc[i+1, 0]) else ""
                        if "GTGT" in thue_text or "VAT" in thue_text.upper() or "THUẾ" in thue_text.upper():
                            tien_thue_gtgt = float(df.iloc[i+1, tong_thanh_toan_col]) if not pd.isna(df.iloc[i+1, tong_thanh_toan_col]) else cong_tien_dich_vu * 0.1
                            
                            # Kiểm tra dòng tổng cộng
                            if i+2 < df.shape[0]:
                                tong_text = str(df.iloc[i+2, 0]).strip() if not pd.isna(df.iloc[i+2, 0]) else ""
                                if "Tổng cộng" in tong_text or "TỔNG CỘNG" in tong_text.upper():
                                    tong_cong_tien = float(df.iloc[i+2, tong_thanh_toan_col]) if not pd.isna(df.iloc[i+2, tong_thanh_toan_col]) else cong_tien_dich_vu + tien_thue_gtgt
                    
                    break
        except Exception as e:
            logger.error(f"Lỗi khi xử lý phần tổng kết: {str(e)}")
        
        # Nếu vẫn chưa có tổng kết, tính từ dữ liệu
        if cong_tien_dich_vu == 0:
            cong_tien_dich_vu = sum(item.tong_thanh_toan for item in du_lieu)
            tien_thue_gtgt = cong_tien_dich_vu * 0.1  # Mặc định 10%
            tong_cong_tien = cong_tien_dich_vu + tien_thue_gtgt
        
        # Thêm các số điện thoại đã trích xuất vào danh sách đầu số dịch vụ
        for idx, phone in enumerate(phone_numbers):
            # Kiểm tra xem số điện thoại này đã có trong map đầu số chưa
            if phone not in dau_so_map:
                # Nếu chưa có, tạo mới một đối tượng DauSoDichVuCuoc cho số này
                stt_counter = len(du_lieu) + 1
                dau_so_data = DauSoDichVuCuoc(
                    stt=stt_counter,
                    dau_so=phone,
                    
                    # Các giá trị mặc định là 0 vì không có thông tin chi tiết
                    vnm_san_luong=0,
                    vnm_ty_le_cp=0,
                    vnm_thanh_tien=0,
                    
                    viettel_san_luong=0,
                    viettel_ty_le_cp=0,
                    viettel_thanh_tien=0,
                    
                    vnpt_san_luong=0,
                    vnpt_ty_le_cp=0,
                    vnpt_thanh_tien=0,
                    
                    vms_san_luong=0,
                    vms_ty_le_cp=0,
                    vms_thanh_tien=0,
                    
                    khac_san_luong=0,
                    khac_ty_le_cp=0,
                    khac_thanh_tien=0,
                    
                    tong_thanh_toan=0
                )
                du_lieu.append(dau_so_data)
        
        # Tạo đối tượng tổng kết
        # <<< Force summary fields to 0 >>>
        tong_ket = TongKetCuoc(
            cong_tien_dich_vu=0,
            tien_thue_gtgt=0,
            tong_cong_tien=0
        )
        
        # Thống kê số lượng đầu số đã trích xuất
        logger.info(f"Đã trích xuất tổng cộng {len(du_lieu)} đầu số dịch vụ từ file {file_name}")
        
        # Tạo đối tượng DoiSoatCuoc cuối cùng
        doi_soat_cuoc = DoiSoatCuoc(
            thang_doi_soat=thang_doi_soat if thang_doi_soat else "",
            nam_doi_soat=nam_doi_soat if nam_doi_soat else "",
            tu_mang=tu_mang,
            den_doi_tac=den_doi_tac,
            dau_so_dich_vu=du_lieu,
            tong_ket=tong_ket,
            file_name=file_name,
            hop_dong_so=hop_dong_so,
            partner_id=partner_id
        )
        
        return doi_soat_cuoc
    except Exception as e:
        logger.error(f"Lỗi khi xử lý file đối soát cước {file_path}: {str(e)}")
        raise ValueError(f"Không thể xử lý file đối soát cước: {str(e)}") 