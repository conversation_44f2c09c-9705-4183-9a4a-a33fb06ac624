import os
import re
import logging
import pandas as pd
import unicodedata
from typing import Tuple, Optional, List, Dict, Any

from ..models.template import TemplateType
from .dst_1800_1900_processor import is_dst_1800_1900_file, DST_1800_1900_PATTERNS
from .dsc_processor import is_dsc_file
from .dscd_processor import is_dscd_file

# Cấu hình logging
logger = logging.getLogger(__name__)

class DoiSoatClassifier:
    """
    Lớp phân loại tự động các file đối soát.
    """
    
    # Pattern nhận dạng cho DoiSoatCoDinh (DSCD)
    DSCD_PATTERNS = [
        r'cố\s*định',
        r'co\s*dinh',
        r'c[ố|ó]?\s*[đd]ịnh',
        r'thuê\s*bao',
        r'thue\s*bao',
        r'TB',
        r'CDHTC',      # Thêm pattern từ mẫu CDHTC
        r'CD',         # CD có thể là viết tắt của cố định
        r'_CD_',       # CD trong tên file
        r'HTC_[0-9]+', # Các mẫu HTC_xxx thường là cố định
        r'DS\s*cố\s*định', # DS cố định 
        r'DSCD',       # Viết tắt của Đối Soát Cố Định
        r'DS CD',      # DS CD
        r'DS-CD',      # DS-CD
        r'BIDV',       # Thêm pattern cho file BIDV
        r'Doctor-check', # Thêm pattern cho Doctor-check
        r'Shield'      # Thêm pattern cho Shield
    ]
    
    # Chuỗi tìm kiếm đơn giản cho DSCD (không dùng regex)
    DSCD_SIMPLE_STRINGS = [
        "codinh", "cd_", "_cd", "_cd_", "dscd", "dscodinh", 
        "thuebao", "tbcodinh", "dscodinh",
        "doisoatcodinh", "cdinh",
        # Các mẫu đặc biệt
        "bidv", "doctorcheck", "shield" 
    ]
    
    # Pattern nhận dạng cho DoiSoatCuoc (DSC)
    DSC_PATTERNS = [
        r'cước',
        r'cuoc',
        r'giá\s*cước',
        r'gia\s*cuoc',
        r'phí',
        r'phi',
        r'chi\s*tiết\s*cuộc\s*gọi', # Thêm pattern từ mẫu chi tiết cuộc gọi
        r'DoanhThu',                # Thêm pattern cho file doanh thu
        r'ITC_',                    # Các mẫu với ITC_ thường là DSC
    ]
    
    # Chuỗi tìm kiếm đơn giản cho DSC (không dùng regex)
    DSC_SIMPLE_STRINGS = [
        "cuoc", "doanhthu", "chitietcuocgoi", "itc", "dsc", "dscuoc",
        "giacuoc", "phi", "dthu"
    ]
    
    # Pattern nhận dạng cho Cước Kết Nối (CKN)
    CKN_PATTERNS = [
        r'CKN',
        r'cước\s*kết\s*nối',
        r'cuoc\s*ket\s*noi',
        r'BBDS\s*CKN',
        r'biên\s*bản\s*CKN',
        r'bien\s*ban\s*CKN',
        r'BBLL',       # Biên bản liên lac
    ]
    
    # Chuỗi tìm kiếm đơn giản cho CKN (không dùng regex)
    CKN_SIMPLE_STRINGS = [
        "ckn", "cuocketnoi", "bbdsckn", "bbds_ckn", 
        "bienbanckn", "bbll"
    ]
    
    # Pattern nhận dạng cho 1800/1900 (DST_1800_1900)
    DST_1800_1900_PATTERNS = [
        r'1800',
        r'1900',
        r'1[89]00',
        r'HTC_1_200',      # Thêm pattern dựa trên file mẫu
        r'BBDS_QT',        # Biên bản đối soát thường thuộc loại 1800/1900
        r'B01_1819',       # Mẫu B01_1819 có vẻ liên quan đến 1800/1900
    ]
    
    # Chuỗi tìm kiếm đơn giản cho 1800/1900 (không dùng regex)
    DST_1800_1900_SIMPLE_STRINGS = [
        "1800", "1900", "1819", "bbds", "ds1800", "ds1900"
    ]
    
    # Các từ khóa đặc trưng trong nội dung cho từng loại mẫu
    CONTENT_KEYWORDS = {
        TemplateType.DSCD: [
            "thuê bao cố định", "tb cố định", "dịch vụ cố định", 
            "thuê bao điện thoại", "cước thuê bao", "cố định"
        ],
        TemplateType.DSC: [
            "chi tiết cuộc gọi", "cước cuộc gọi", "giá cước", 
            "cước phí", "doanh thu", "dịch vụ cước"
        ],
        TemplateType.DST_1800_1900: [
            "1800", "1900", "dịch vụ 1800", "dịch vụ 1900",
            "tổng đài 1800", "tổng đài 1900", "dịch vụ tổng đài 1800"
        ],
        TemplateType.CKN: [
            "cước kết nối", "cuoc ket noi", "biên bản liên lạc",
            "ckn", "biên bản đối soát cước kết nối"
        ]
    }
    
    @classmethod
    def normalize_text(cls, text: str) -> str:
        """
        Chuẩn hóa văn bản để dễ so sánh bằng cách:
        - Chuyển về chữ thường
        - Giữ nguyên hoặc thay thế các ký tự tiếng Việt
        - Loại bỏ tất cả ký tự không phải a-z, 0-9 (bao gồm khoảng trắng)
        
        Args:
            text: Chuỗi cần chuẩn hóa
            
        Returns:
            Chuỗi đã chuẩn hóa
        """
        # Chuyển về chữ thường
        text = text.lower()
        
        # Thay thế trực tiếp các ký tự tiếng Việt phổ biến
        vietnamese_chars = {
            'á': 'a', 'à': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
            'ă': 'a', 'ắ': 'a', 'ằ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
            'â': 'a', 'ấ': 'a', 'ầ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
            'é': 'e', 'è': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
            'ê': 'e', 'ế': 'e', 'ề': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
            'í': 'i', 'ì': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
            'ó': 'o', 'ò': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
            'ô': 'o', 'ố': 'o', 'ồ': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
            'ơ': 'o', 'ớ': 'o', 'ờ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
            'ú': 'u', 'ù': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
            'ư': 'u', 'ứ': 'u', 'ừ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
            'ý': 'y', 'ỳ': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
            'đ': 'd'  # Quan trọng: Chuyển đổi đ thành d
        }
        
        for vietnamese, latin in vietnamese_chars.items():
            text = text.replace(vietnamese, latin)
        
        # Loại bỏ tất cả ký tự không phải a-z và 0-9 (bao gồm khoảng trắng)
        text = re.sub(r'[^a-z0-9]', '', text)
        
        logger.debug(f"Chuẩn hóa văn bản: '{text}'")
        return text
    
    @classmethod
    def classify_from_filename(cls, filename: str) -> Optional[TemplateType]:
        """
        Phân loại dựa trên tên file.
        
        Args:
            filename: Tên file cần phân loại
            
        Returns:
            Loại mẫu đối soát (TemplateType) hoặc None nếu không xác định được
        """
        basename = os.path.basename(filename)
        filename_lower = basename.lower()
        filename_norm = cls.normalize_text(basename)
        
        logger.info(f"Phân loại file: {basename}")
        logger.debug(f"Tên đã chuẩn hóa: {filename_norm}")
        
        # Debug: In ra các chuỗi đơn giản để kiểm tra
        logger.debug(f"DSCD_SIMPLE_STRINGS: {cls.DSCD_SIMPLE_STRINGS}")
        
        # Xử lý trường hợp đặc biệt: "cước cố định" nên được phân loại là DSCD
        if "cước cố định" in filename_lower or "cuoc co dinh" in filename_norm:
            logger.info(f"Phát hiện mẫu DSCD với pattern đặc biệt: cước cố định")
            return TemplateType.DSCD
            
        # Kiểm tra DSCD trước dựa trên các pattern cụ thể
        if "cố định" in filename_lower or "co dinh" in filename_norm:
            logger.info(f"Phát hiện mẫu DSCD với pattern: cố định/co dinh")
            return TemplateType.DSCD
            
        # Kiểm tra chuỗi đặc trưng mạnh cho DSCD
        for simple_str in cls.DSCD_SIMPLE_STRINGS:
            logger.debug(f"Kiểm tra chuỗi DSCD: '{simple_str}' trong '{filename_norm}'")
            # Kiểm tra cho chuỗi "cd" - phải là chuỗi riêng biệt hoặc được phân cách
            if simple_str == "cd":
                # Tìm "cd" như một từ riêng biệt hoặc trong các ngữ cảnh cụ thể 
                # như "_cd", "cd_", "htc_cd", "cd_htc"
                patterns = [r'\bcd\b', r'_cd_', r'_cd\b', r'\bcd_']
                if any(re.search(pattern, filename_norm) for pattern in patterns):
                    logger.info(f"Phát hiện mẫu DSCD với pattern cd trong ngữ cảnh phù hợp")
                    return TemplateType.DSCD
            elif simple_str in filename_norm:
                logger.info(f"Phát hiện mẫu DSCD với chuỗi đơn giản: {simple_str}")
                return TemplateType.DSCD
        
        # Kiểm tra chuỗi đặc trưng mạnh trước
        # Kiểm tra DSC (đối soát cước) trước nếu có từ "cuoc" trong tên file
        if "cuoc" in filename_norm or "cước" in filename_lower:
            for simple_str in cls.DSC_SIMPLE_STRINGS:
                if simple_str in filename_norm:
                    logger.info(f"Phát hiện mẫu DSC với chuỗi đơn giản: {simple_str}")
                    return TemplateType.DSC
        
        # Kiểm tra với các chuỗi đơn giản trước
        # Kiểm tra 1800/1900
        for simple_str in cls.DST_1800_1900_SIMPLE_STRINGS:
            if simple_str in filename_norm:
                logger.info(f"Phát hiện mẫu DST_1800_1900 với chuỗi đơn giản: {simple_str}")
                return TemplateType.DST_1800_1900
        
        # Kiểm tra DSC 
        for simple_str in cls.DSC_SIMPLE_STRINGS:
            if simple_str in filename_norm:
                logger.info(f"Phát hiện mẫu DSC với chuỗi đơn giản: {simple_str}")
                return TemplateType.DSC
        
        # Kiểm tra CKN
        for simple_str in cls.CKN_SIMPLE_STRINGS:
            if simple_str in filename_norm:
                logger.info(f"Phát hiện mẫu CKN với chuỗi đơn giản: {simple_str}")
                return TemplateType.CKN
        
        # Nếu không tìm thấy với chuỗi đơn giản, thử với regex pattern
        # Kiểm tra loại 1800_1900 trước
        for pattern in cls.DST_1800_1900_PATTERNS:
            logger.debug(f"Kiểm tra pattern 1800/1900: {pattern}")
            if re.search(pattern, filename, re.IGNORECASE):
                logger.info(f"Phát hiện mẫu DST_1800_1900 với pattern: {pattern}")
                return TemplateType.DST_1800_1900
                
        # Kiểm tra loại DoiSoatCoDinh
        for pattern in cls.DSCD_PATTERNS:
            logger.debug(f"Kiểm tra pattern DSCD: {pattern}, filename: {basename}")
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                logger.info(f"Phát hiện mẫu DSCD với pattern: {pattern}, matched: {match.group(0)}")
                return TemplateType.DSCD
                
        # Kiểm tra loại DoiSoatCuoc
        for pattern in cls.DSC_PATTERNS:
            logger.debug(f"Kiểm tra pattern DSC: {pattern}")
            if re.search(pattern, filename, re.IGNORECASE):
                logger.info(f"Phát hiện mẫu DSC với pattern: {pattern}")
                return TemplateType.DSC
                
        # Kiểm tra loại CKN
        for pattern in cls.CKN_PATTERNS:
            logger.debug(f"Kiểm tra pattern CKN: {pattern}")
            if re.search(pattern, filename, re.IGNORECASE):
                logger.info(f"Phát hiện mẫu CKN với pattern: {pattern}")
                return TemplateType.CKN
        
        # Sử dụng các hàm is_*_file từ các module khác
        if is_dst_1800_1900_file(filename):
            logger.info(f"Phát hiện mẫu DST_1800_1900 qua hàm is_dst_1800_1900_file")
            return TemplateType.DST_1800_1900
            
        if is_dscd_file(filename):
            logger.info(f"Phát hiện mẫu DSCD qua hàm is_dscd_file")
            return TemplateType.DSCD
        
        if is_dsc_file(filename):
            logger.info(f"Phát hiện mẫu DSC qua hàm is_dsc_file")
            return TemplateType.DSC
            
        logger.info(f"Không thể phân loại file dựa trên tên: {filename}")
        return None
    
    @classmethod
    def classify_from_content(cls, file_path: str) -> Optional[TemplateType]:
        """
        Phân loại dựa trên nội dung file.
        
        Args:
            file_path: Đường dẫn đến file Excel
            
        Returns:
            Loại mẫu đối soát (TemplateType) hoặc None nếu không xác định được
        """
        try:
            # Thử mở file với nhiều sheet khác nhau
            content_text = ""
            try:
                # Đọc tất cả các sheet và lấy nhiều dòng hơn
                xls = pd.ExcelFile(file_path)
                sheets = xls.sheet_names
                
                for sheet in sheets[:3]:  # Đọc tối đa 3 sheet đầu tiên
                    df = pd.read_excel(file_path, sheet_name=sheet, header=None, nrows=50)
                    for i in range(min(50, df.shape[0])):
                        for j in range(min(15, df.shape[1])):
                            if j < df.shape[1]:
                                cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                                content_text += " " + cell_value
            except Exception as sheet_err:
                logger.warning(f"Lỗi khi đọc nhiều sheet: {str(sheet_err)}")
                # Fallback: đọc sheet mặc định
                df = pd.read_excel(file_path, header=None, nrows=50)
                for i in range(min(50, df.shape[0])):
                    for j in range(min(15, df.shape[1])):
                        if j < df.shape[1]:
                            cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                            content_text += " " + cell_value
            
            # Chuẩn hóa nội dung để dễ tìm kiếm
            content_text = cls.normalize_text(content_text)
            logger.info(f"Phân tích nội dung file: {os.path.basename(file_path)}")
            logger.debug(f"Nội dung đã chuẩn hóa: {content_text[:200]}...")  # Chỉ log 200 ký tự đầu
            
            # Tính điểm cho mỗi loại mẫu dựa trên các từ khóa trong nội dung
            scores: Dict[TemplateType, int] = {
                TemplateType.DSCD: 0,
                TemplateType.DSC: 0,
                TemplateType.DST_1800_1900: 0,
                TemplateType.CKN: 0
            }
            
            # Kiểm tra các chuỗi đơn giản DSCD
            for keyword in cls.DSCD_SIMPLE_STRINGS:
                norm_keyword = cls.normalize_text(keyword)
                if norm_keyword in content_text:
                    scores[TemplateType.DSCD] += 1
                    logger.info(f"Tìm thấy từ khóa '{norm_keyword}' cho loại DSCD")
            
            # Kiểm tra các chuỗi đơn giản DSC
            for keyword in cls.DSC_SIMPLE_STRINGS:
                norm_keyword = cls.normalize_text(keyword)
                if norm_keyword in content_text:
                    scores[TemplateType.DSC] += 1
                    logger.info(f"Tìm thấy từ khóa '{norm_keyword}' cho loại DSC")
            
            # Kiểm tra các chuỗi đơn giản DST_1800_1900
            for keyword in cls.DST_1800_1900_SIMPLE_STRINGS:
                norm_keyword = cls.normalize_text(keyword)
                if norm_keyword in content_text:
                    scores[TemplateType.DST_1800_1900] += 1
                    logger.info(f"Tìm thấy từ khóa '{norm_keyword}' cho loại DST_1800_1900")
                    
            # Kiểm tra các chuỗi đơn giản CKN
            for keyword in cls.CKN_SIMPLE_STRINGS:
                norm_keyword = cls.normalize_text(keyword)
                if norm_keyword in content_text:
                    scores[TemplateType.CKN] += 1
                    logger.info(f"Tìm thấy từ khóa '{norm_keyword}' cho loại CKN")
            
            # Kiểm tra các từ khóa từ CONTENT_KEYWORDS (nâng cao hơn)
            for template_type, keywords in cls.CONTENT_KEYWORDS.items():
                for keyword in keywords:
                    norm_keyword = cls.normalize_text(keyword)
                    if norm_keyword in content_text:
                        scores[template_type] += 1
                        logger.info(f"Tìm thấy từ khóa '{norm_keyword}' cho loại {template_type}")
            
            # Kiểm tra cụ thể cho 1800/1900
            if "1800" in content_text or "1900" in content_text:
                scores[TemplateType.DST_1800_1900] += 3  # Tăng trọng số cho 1800/1900
                logger.info(f"Tìm thấy 1800/1900 trong nội dung, tăng điểm")
            
            # Log kết quả điểm
            logger.info(f"Kết quả phân tích nội dung: DSCD={scores[TemplateType.DSCD]}, DSC={scores[TemplateType.DSC]}, DST_1800_1900={scores[TemplateType.DST_1800_1900]}, CKN={scores[TemplateType.CKN]}")
            
            # Lấy loại mẫu có điểm cao nhất
            max_score = max(scores.values())
            if max_score > 0:
                best_type = max(scores.items(), key=lambda x: x[1])[0]
                logger.info(f"Phân loại từ nội dung: {best_type} với điểm {max_score}")
                return best_type
                
            logger.info(f"Không thể phân loại từ nội dung file")
            return None
            
        except Exception as e:
            logger.error(f"Lỗi khi phân tích nội dung file: {str(e)}")
            return None
    
    @classmethod
    def extract_original_filename(cls, file_path: str) -> str:
        """
        Trích xuất tên file gốc từ đường dẫn file có thể chứa UUID.
        
        Args:
            file_path: Đường dẫn đến file, có thể có dạng UUID_filename.ext
            
        Returns:
            Tên file có thể là tên gốc hoặc tên file đã cho nếu không trích xuất được
        """
        try:
            # Lấy chỉ tên file, không bao gồm đường dẫn
            filename = os.path.basename(file_path)
            
            # Kiểm tra xem file có cấu trúc UUID_filename không
            uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}_(.+)$'
            match = re.match(uuid_pattern, filename)
            
            if match:
                # Lấy phần filename sau UUID_
                logger.info(f"Trích xuất tên file gốc từ: {filename} -> {match.group(1)}")
                return match.group(1)
            
            return filename
        except Exception as e:
            logger.warning(f"Không thể trích xuất tên file gốc: {str(e)}")
            return os.path.basename(file_path)

    @classmethod
    def classify(cls, file_path: str) -> Tuple[Optional[TemplateType], float]:
        """
        Phân loại file đối soát và trả về loại cùng độ tin cậy
        
        Args:
            file_path: Đường dẫn đến file Excel
            
        Returns:
            Tuple chứa (loại_đối_soát, độ_tin_cậy)
        """
        filename = os.path.basename(file_path)
        # Thử trích xuất tên file gốc nếu có
        original_filename = cls.extract_original_filename(file_path)
        
        logger.info(f"Bắt đầu phân loại file: {filename}")
        if original_filename != filename:
            logger.info(f"Tên file gốc: {original_filename}")
        
        # Thử phân loại từ tên file gốc trước nếu khác với filename
        if original_filename != filename:
            file_type = cls.classify_from_filename(original_filename)
            if file_type:
                logger.info(f"Phân loại thành công từ tên file gốc: {file_type}")
                return file_type, 0.95  # Độ tin cậy cao hơn vì dùng tên gốc
        
        # Nếu không thành công với tên gốc hoặc tên gốc trùng với filename,
        # thử phân loại từ tên file hiện tại
        file_type = cls.classify_from_filename(filename)
        if file_type:
            logger.info(f"Phân loại thành công từ tên file: {file_type}")
            return file_type, 0.9
            
        # Nếu không xác định được từ tên file, phân tích nội dung
        logger.info(f"Không thể phân loại từ tên file, chuyển sang phân tích nội dung")
        content_type = cls.classify_from_content(file_path)
        if content_type:
            logger.info(f"Phân loại thành công từ nội dung: {content_type}")
            return content_type, 0.7
        
        # Nếu vẫn không xác định được, thử dự đoán dựa trên đuôi file và metadata
        try:
            extension = os.path.splitext(filename)[1].lower()
            if extension in ['.pdf']:
                # PDF thường là biên bản đối soát, dự đoán là 1800/1900
                logger.info(f"File PDF, dự đoán là DST_1800_1900")
                return TemplateType.DST_1800_1900, 0.5
            
            if "doanh" in filename.lower() or "doanhtu" in filename.lower() or "doanh" in original_filename.lower():
                # File doanh thu, dự đoán là DSC
                logger.info(f"File doanh thu, dự đoán là DSC")
                return TemplateType.DSC, 0.5
            
            # Fallback: nếu tên file có chứa "HTC", ưu tiên DSCD
            if "htc" in filename.lower() or "htc" in original_filename.lower():
                logger.info(f"File có 'HTC', dự đoán là DSCD")
                return TemplateType.DSCD, 0.4
        except Exception as e:
            logger.error(f"Lỗi khi dự đoán: {str(e)}")
            
        # Không thể xác định
        logger.warning(f"Không thể xác định loại mẫu cho file: {filename}")
        return None, 0.0 