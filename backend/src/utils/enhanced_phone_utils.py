"""
<PERSON><PERSON><PERSON> cụ nâng cao cho phân loại số điện thoại.
<PERSON><PERSON> gồm các hàm để phân loại số điện thoại với thông tin chi tiết hơn.
"""
from typing import Dict, Tuple, Optional, Any
from ..models.call_log import NumberType
from ..models.enums import DauSoType, ServiceType
import re
import logging
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from ..models.call_log import CallLog

log = logging.getLogger(__name__)

# <PERSON><PERSON><PERSON> từ điển dữ liệu
MOBILE_PREFIXES = {
    # Viettel
    '032', '033', '034', '035', '036', '037', '038', '039',
    '086', '096', '097', '098',
    
    # Mobifone
    '070', '076', '077', '078', '079',
    '089', '090', '093',
    
    # Vinaphone
    '081', '082', '083', '084', '085',
    '088', '091', '094',
    
    # Vietnamobile
    '056', '058', '092',
    
    # Gmobile
    '059', '099',
    
    # iTelecom
    '087'
}

FIXED_SERVICE_PREFIXES = {'1900', '1800', '1700', '1600'}

# Mới: Các prefix số cố định tỉnh lẻ
FIXED_PROVINCIAL_PREFIXES = {
    '0522', '0523', '0528', '0525', '0559',
    '0524', '0526', '0527', '0529',
    '0543', '0548', '0551', '0552', '0553'
}

# Mới: Các prefix số toll-free
TOLL_FREE_PREFIXES = {
    '01800', '01801', '01802', '01803', '01804', '01805'
}

# Mới: Mã quốc gia
INTL_COUNTRY_CODES = {
    '1': 'North America',
    '44': 'United Kingdom',
    '61': 'Australia',
    '65': 'Singapore',
    '81': 'Japan',
    '82': 'South Korea',
    '84': 'Vietnam',
    '86': 'China',
    '236': 'Libya',
    '61': 'Australia',
    '65': 'Singapore', 
    '44': 'United Kingdom',
    '45': 'Denmark',
    '62': 'Indonesia',
    '60': 'Malaysia',
    '63': 'Philippines'
}

# Mới: Ánh xạ prefix di động -> nhà mạng
MOBILE_OPERATOR_MAP = {
    # Viettel
    '032': 'Viettel', '033': 'Viettel', '034': 'Viettel', '035': 'Viettel',
    '036': 'Viettel', '037': 'Viettel', '038': 'Viettel', '039': 'Viettel',
    '086': 'Viettel', '096': 'Viettel', '097': 'Viettel', '098': 'Viettel',
    
    # Mobifone
    '070': 'Mobifone', '076': 'Mobifone', '077': 'Mobifone', 
    '078': 'Mobifone', '079': 'Mobifone',
    '089': 'Mobifone', '090': 'Mobifone', '093': 'Mobifone',
    
    # Vinaphone
    '081': 'Vinaphone', '082': 'Vinaphone', '083': 'Vinaphone',
    '084': 'Vinaphone', '085': 'Vinaphone',
    '088': 'Vinaphone', '091': 'Vinaphone', '094': 'Vinaphone',
    
    # Vietnamobile
    '056': 'Vietnamobile', '058': 'Vietnamobile', '092': 'Vietnamobile',
    
    # Gmobile
    '059': 'Gmobile', '099': 'Gmobile',
    
    # iTelecom
    '087': 'iTelecom'
}

# AREA CODE DATA (Example, replace with your actual data source/logic)
VN_AREA_CODES = {
    # Fixed line area codes (example, add more)
    "24": "Hà Nội",
    "28": "TP. Hồ Chí Minh",
    "20": "Lào Cai", # Example 3-digit code
    "210": "Phú Thọ",
    "211": "Vĩnh Phúc",
    "218": "Hòa Bình",
    "219": "Hà Giang",
    "209": "Cao Bằng",
    "203": "Quảng Ninh",
    "204": "Bắc Giang",
    "205": "Lạng Sơn",
    "206": "Bắc Kạn",
    "207": "Tuyên Quang",
    "22": "Sơn La", # Example 2-digit code
    "23": "Hà Nam",
    "25": "Lai Châu",
    "26": "Điện Biên",
    "27": "Yên Bái", 
    "29": "Ninh Bình",
    "212": "Lai Châu",
    "213": "Lào Cai",
    "214": "Sơn La",
    "215": "Điện Biên",
    "216": "Hòa Bình",
    "220": "Hải Dương",
    "221": "Hưng Yên",
    "222": "Bắc Ninh",
    "225": "Hải Phòng",
    "226": "Hà Nam",
    "227": "Thái Bình",
    "228": "Nam Định",
    "229": "Ninh Bình",
    "232": "Quảng Bình",
    "233": "Quảng Trị",
    "234": "Thừa Thiên Huế",
    "235": "Quảng Nam",
    "236": "Đà Nẵng",
    "237": "Thanh Hóa",
    "238": "Nghệ An",
    "239": "Hà Tĩnh",
    "251": "Đồng Nai",
    "252": "Bình Thuận",
    "254": "Bà Rịa - Vũng Tàu",
    "256": "Bình Định",
    "257": "Phú Yên",
    "258": "Khánh Hòa",
    "259": "Ninh Thuận",
    "260": "Kon Tum",
    "261": "Đắk Nông",
    "262": "Đắk Lắk",
    "263": "Lâm Đồng",
    "269": "Gia Lai",
    "270": "Vĩnh Long",
    "271": "Bình Phước",
    "272": "Long An",
    "273": "Tiền Giang",
    "274": "Bình Dương",
    "275": "Bến Tre",
    "276": "Tây Ninh",
    "277": "Đồng Tháp",
    "290": "Cà Mau",
    "291": "Bạc Liêu",
    "292": "Cần Thơ",
    "293": "Hậu Giang",
    "294": "Trà Vinh",
    "296": "An Giang",
    "297": "Kiên Giang",
    "299": "Sóc Trăng",
}

def normalize_phone_number_enhanced(phone: str) -> str:
    """
    Phiên bản nâng cao của hàm chuẩn hóa số điện thoại với hỗ trợ số quốc tế
    """
    if not phone:
        return phone
    
    # Loại bỏ các ký tự không phải số nhưng giữ lại dấu +
    digits_only = ''.join(c for c in str(phone) if c.isdigit() or c == '+')
    
    # Nếu chuỗi rỗng sau khi lọc, trả về chuỗi gốc
    if not digits_only:
        return phone
    
    # Xử lý số quốc tế
    if digits_only.startswith('+'):
        return digits_only
    
    # Chuyển đổi các định dạng quốc tế thông dụng
    if digits_only.startswith('00'):
        # Format 00xx thành +xx
        return '+' + digits_only[2:]
    
    if digits_only.startswith('011') and len(digits_only) > 5:
        # Format 011xx (US) thành +xx
        return '+' + digits_only[3:]
    
    # Không thêm 0 cho số dịch vụ
    if (digits_only.startswith('1900') or 
        digits_only.startswith('1800') or 
        digits_only.startswith('1700') or 
        digits_only.startswith('1600')):
        return digits_only
    
    # Nếu không bắt đầu bằng 0, thêm 0 vào đầu
    if not digits_only.startswith('0'):
        return '0' + digits_only
    
    return digits_only


def classify_phone_number_enhanced(phone: str) -> Tuple[NumberType, Optional[str]]:
    """
    Phiên bản nâng cao của hàm phân loại số điện thoại.
    Trả về tuple gồm (loại số, ghi chú)
    """
    if not phone:
        return NumberType.UNKNOWN, None
    
    # Xử lý số quốc tế
    if phone.startswith('+'):
        country_code = None
        for length in [3, 2, 1]:
            if len(phone) > length:
                code = phone[1:length+1]
                if code in INTL_COUNTRY_CODES:
                    country_code = INTL_COUNTRY_CODES[code]
                    break
        
        note = f"International: {country_code}" if country_code else "International"
        return NumberType.INTL, note
    
    # Xử lý số toll-free
    for prefix in TOLL_FREE_PREFIXES:
        if phone.startswith(prefix):
            return NumberType.FIXED, "Toll-Free Service"
    
    # Xử lý các số cố định tỉnh lẻ
    for prefix in FIXED_PROVINCIAL_PREFIXES:
        if phone.startswith(prefix):
            return NumberType.FIXED, f"Provincial Fixed: {prefix}"
    
    # Xử lý các số dịch vụ cố định
    prefix_4 = phone[:4] if len(phone) >= 4 else ""
    if prefix_4 in FIXED_SERVICE_PREFIXES:
        return NumberType.FIXED, f"Service Number: {prefix_4}"
    
    # Xử lý số di động
    prefix_3 = phone[:3] if len(phone) >= 3 else ""
    if prefix_3 in MOBILE_PREFIXES:
        operator = MOBILE_OPERATOR_MAP.get(prefix_3, "Unknown")
        return NumberType.MOBILE, f"Mobile: {operator}"
    
    # Xử lý số cố định bắt đầu bằng 02
    if phone.startswith('02'):
        area_code = phone[:3]
        return NumberType.FIXED, f"Fixed Line: {area_code}"
    
    # Các số có prefix đặc biệt khác
    if phone.startswith('061') or phone.startswith('065'):
        return NumberType.INTL, "Special Prefix (06x)"
    
    return NumberType.UNKNOWN, None


def get_phone_details(phone: str) -> Dict:
    """
    Trả về thông tin chi tiết về số điện thoại
    """
    normalized = normalize_phone_number_enhanced(phone)
    phone_type, note = classify_phone_number_enhanced(normalized)
    
    details = {
        'original': phone,
        'normalized': normalized,
        'type': phone_type,
        'note': note
    }
    
    # Thêm thông tin về nhà mạng nếu là số di động
    if phone_type == NumberType.MOBILE and len(normalized) >= 3:
        prefix_3 = normalized[:3]
        details['operator'] = MOBILE_OPERATOR_MAP.get(prefix_3, "Unknown")
    
    # Thêm thông tin về quốc gia nếu là số quốc tế
    if phone_type == NumberType.INTL:
        if normalized.startswith('+'):
            for length in [3, 2, 1]:
                if len(normalized) > length:
                    code = normalized[1:length+1]
                    if code in INTL_COUNTRY_CODES:
                        details['country'] = INTL_COUNTRY_CODES[code]
                        break
        elif normalized.startswith('061'):
            details['country'] = 'Australia'
        elif normalized.startswith('065'):
            details['country'] = 'Singapore'
    
    return details

def extract_area_code(phone: str) -> Optional[str]:
    """
    Extracts the area code from a Vietnamese fixed-line phone number.
    Handles numbers starting with '0' or '84' or '+84'.
    Returns the area code (e.g., '24', '28') or None if not valid.
    """
    if not phone:
        return None

    # Clean the number: remove common separators and leading '+'
    cleaned = ''.join(filter(str.isdigit, phone.replace(' ', '').replace('.', '').replace('-', '')))
    
    if not cleaned:
        return None

    digits = cleaned # Use 'digits' for clarity

    # Define minimum subscriber number length (allow 6 or 7 digits)
    MIN_SUBSCRIBER_LEN = 6 

    # Case 1: Starts with '0' (National format)
    if digits.startswith('0'):
        national_number = digits[1:] # Remove leading '0'
        if not national_number.startswith('2'): # Fixed lines must start with '2' after '0'
            return None

        # Try 3-digit area code FIRST (e.g., 0210, 0203)
        potential_area_code_3 = national_number[0:3]
        if potential_area_code_3 in VN_AREA_CODES and len(national_number[3:]) >= MIN_SUBSCRIBER_LEN:
            return potential_area_code_3
            
        # Then try 2-digit area code (e.g., 024, 028)
        potential_area_code_2 = national_number[0:2]
        if potential_area_code_2 in VN_AREA_CODES and len(national_number[2:]) >= MIN_SUBSCRIBER_LEN:
             return potential_area_code_2

        return None # No valid fixed line format found

    # Case 2: Starts with '84' (International format for VN)
    elif digits.startswith('84'):
        national_number = digits[2:] # Remove leading '84'
        if not national_number.startswith('2'): # Fixed lines must start with '2' after '84'
            return None

        # Try 3-digit area code FIRST (e.g., 84210, 84203)
        potential_area_code_3 = national_number[0:3]
        if potential_area_code_3 in VN_AREA_CODES and len(national_number[3:]) >= MIN_SUBSCRIBER_LEN:
            return potential_area_code_3

        # Then try 2-digit area code (e.g., 8424, 8428)
        potential_area_code_2 = national_number[0:2]
        if potential_area_code_2 in VN_AREA_CODES and len(national_number[2:]) >= MIN_SUBSCRIBER_LEN:
             return potential_area_code_2

        return None # No valid fixed line format found
        
    # Case 3: Starts directly with '2' (Assumed local format within an area)
    # This function primarily aims to extract based on national/intl format.
    # Handling local-only formats might require context or different logic.
    # For now, if it starts with '2' but not '0' or '84', we don't extract.
    # If needed, logic could be added here to check VN_AREA_CODES directly.

    return None # Not a recognizable VN fixed line format for area code extraction

def determine_service_type(call_log: "CallLog") -> Optional[ServiceType]:
    """
    Determines the ServiceType for an outgoing call based on CallLog details.

    Assumes this is an outgoing call (call_type == OUT) and the caller is FIXED.

    Args:
        call_log: The CallLog object representing the call record.

    Returns:
        The determined ServiceType, or None if unable to determine.
    """
    if not call_log:
        log.warning("Received None for call_log in determine_service_type")
        return None

    # Sử dụng trực tiếp caller và callee vì không có caller_std/callee_std
    caller = call_log.caller
    callee = call_log.callee
    callee_type = call_log.callee_type
    caller_type = call_log.caller_type

    if not caller or not callee:
         log.warning(f"Missing caller or callee in CallLog ID: {getattr(call_log, 'id', 'N/A')}")
         return None

    # 1. Service Numbers (1800/1900) - Ưu tiên cao nhất
    # Chuẩn hóa đơn giản để kiểm tra prefix
    normalized_callee = ''.join(filter(str.isdigit, callee))
    if normalized_callee.startswith("1800"):
        # Caller is FIXED in DSCD context
        return ServiceType.SERVICE_1800_VOICE_CD 
    elif normalized_callee.startswith("1900"):
        # 1900 applies to both fixed and mobile callers according to enum name
        return ServiceType.SERVICE_1900_VOICE_VAS

    # 2. International Calls
    if callee_type == NumberType.INTL:
        return ServiceType.INTL_OUTBOUND

    # 3. Mobile Calls
    if callee_type == NumberType.MOBILE:
        return ServiceType.MOBILE_NORMAL

    # 4. Fixed Line Calls (Local vs. Long Distance)
    if caller_type == NumberType.FIXED and callee_type == NumberType.FIXED:
        caller_area = extract_area_code(caller) # Use the function defined above
        callee_area = extract_area_code(callee) # Use the function defined above

        if caller_area and callee_area:
            if caller_area == callee_area:
                return ServiceType.FIXED_NOI_HAT
            else:
                # Giả định là liên tỉnh khác mạng nếu mã vùng khác nhau
                return ServiceType.FIXED_LIEN_TINH_KHAC_MANG
        else:
            log.warning(f"Could not determine area codes for fixed call. "
                        f"CallLog ID: {getattr(call_log, 'id', 'N/A')}, "
                        f"Caller: {caller}, Callee: {callee}")
            return None # Hoặc một loại mặc định nếu cần

    # 5. Fallback / Unhandled cases
    log.warning(f"Unhandled call data for service determination. "
                f"CallLog ID: {getattr(call_log, 'id', 'N/A')}, "
                f"CallerType: {caller_type}, CalleeType: {callee_type}, "
                f"Callee: {callee}")
    return None # Hoặc một loại mặc định nếu cần

def matches_caller(caller: str, template_dau_so_info: Dict[str, Any]) -> bool:
    """
    Kiểm tra xem số 'caller' có khớp với thông tin đầu số/dải số/tiền tố
    từ template hay không.

    Args:
        caller: Số điện thoại người gọi (đã chuẩn hóa, chỉ chứa số).
        template_dau_so_info: Dict chứa thông tin từ DauSoDichVu của template,
                            bao gồm 'type', 'standardized_display',
                            'start_num_str', 'end_num_str', 'prefix'.

    Returns:
        True nếu khớp, False nếu không.
    """
    if not caller or not caller.isdigit():
        return False

    dau_so_type = template_dau_so_info.get("type")

    if dau_so_type in [DauSoType.RANGE_10, DauSoType.RANGE_100, DauSoType.RANGE_OTHER]:
        # Lấy start_num_str và end_num_str từ template
        start_str = template_dau_so_info.get("start_num_str")
        end_str = template_dau_so_info.get("end_num_str")
        
        # Nếu không có, thử lấy từ standardized_display
        if not start_str or not end_str:
            display = template_dau_so_info.get("standardized_display", "")
            if "-" in display:
                start_str, end_str = display.split("-")
                start_str = start_str.strip()
                end_str = end_str.strip()
        
        if not start_str or not end_str:
            log.warning(f"Cannot determine range for {dau_so_type}: {template_dau_so_info}")
            return False

        # Kiểm tra thêm độ dài dải số với RANGE_10 và RANGE_100
        if dau_so_type in [DauSoType.RANGE_10, DauSoType.RANGE_100]:
            try:
                start_num = int(start_str)
                end_num = int(end_str)
                expected_range = 10 if dau_so_type == DauSoType.RANGE_10 else 100
                if end_num - start_num + 1 != expected_range:
                    log.warning(f"Invalid range size for {dau_so_type}. Expected {expected_range}, got {end_num - start_num + 1}")
                    return False
            except ValueError:
                log.warning(f"Invalid number format in range: start={start_str}, end={end_str}")
                return False

        return start_str <= caller <= end_str

    elif dau_so_type == DauSoType.SINGLE:
        display_val = template_dau_so_info.get("standardized_display")
        log.debug(f"SINGLE match check: caller='{caller}' (type: {type(caller)}), display='{display_val}' (type: {type(display_val)})")
        return str(caller) == str(display_val)

    elif dau_so_type == DauSoType.PREFIX:
        prefix = template_dau_so_info.get("prefix")
        if not prefix:
            return False
        return caller.startswith(prefix)

    return False 