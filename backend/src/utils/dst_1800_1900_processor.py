import os
import re
import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from ..schemas.dst_1800_1900 import (
    DoiSoat1800_1900, 
    NhomDichVu1800_1900, 
    ChiTietDichVu1800_1900,
    TongKet1800_1900,
    ThanhToan1800_1900
)

# Cấu hình logging
logger = logging.getLogger(__name__)

# Các mẫu regex để nhận dạng file đối soát 1800/1900
DST_1800_1900_PATTERNS = [
    r'18001900',
    r'1800_1900',
    r'1800[-_\s]1900',
    r'1900[-_\s]1800',
    r'1900',
    r'1800'
]

def normalize_service_number(service_number: str) -> str:
    """Chuẩn hóa định dạng số dịch vụ"""
    service_number = service_number.strip()
    
    # Thay thế các ký tự đặc biệt bằng dấu -
    service_number = re.sub(r'->|→|~|–|:|;', '-', service_number)
    
    # <PERSON><PERSON><PERSON> các ký tự không cần thiết
    service_number = re.sub(r'[)(,"]', '', service_number)
    
    return service_number

def is_valid_service_number(text: str) -> bool:
    """
    Kiểm tra xem chuỗi có phải là số dịch vụ hợp lệ không
    
    Args:
        text: Chuỗi cần kiểm tra
    
    Returns:
        True nếu là số dịch vụ hợp lệ, False nếu không phải
    """
    if not isinstance(text, str):
        return False
        
    # Loại bỏ các chuỗi dài quá 100 ký tự (thường là văn bản, không phải số dịch vụ)
    if len(text) > 100:
        return False
    
    # Kiểm tra nếu chứa các từ khóa không mong muốn (giữ lại "trừ" và "gồm" vì có thể là phần của mô tả dịch vụ)
    unwanted_keywords = [
        "doanh thu", "thanh toán", "sau bù trừ", "tổng cộng", 
        "của", "cho", "phải", "là", "vnđ", "tiền", "gtgt", "vat",
        "được", "thuế", "cước", "cuộc"
    ]
    
    text_lower = text.lower()
    
    # Loại bỏ các string bắt đầu với keyword không mong muốn
    for keyword in unwanted_keywords:
        if keyword in text_lower:
            # Nếu keyword là sub-string hoàn toàn hoặc nằm ở đầu chuỗi
            if text_lower.startswith(keyword) or f" {keyword} " in text_lower:
                return False
    
    # Trường hợp đặc biệt: Cho phép chuỗi chỉ chứa "1800" hoặc "1900"
    if text.strip() in ["1800", "1900"]:
        return True
    
    # Đặc biệt: Nếu là dạng tiêu đề dịch vụ
    if re.search(r'dịch\s*vụ\s*1[89]00', text_lower):
        # Kiểm tra nếu là tiêu đề chính như "I. Dịch vụ 1900"
        if re.match(r'[IVX]+\.\s*dịch\s*vụ\s*1[89]00', text_lower):
            return True
        # Hoặc mẫu như "MBC sử dụng 1800 của HTC"
        if re.search(r'sử\s*dụng\s*1[89]00', text_lower):
            return True
    
    # Kiểm tra xem có chứa số dịch vụ không
    if re.search(r'1[89]00\d*', text):
        return True
    
    # Kiểm tra mẫu đặc biệt dạng khoảng 1900xxx-1900yyy
    if re.search(r'1[89]00.*-.*1[89]00', text):
        return True
    
    # Kiểm tra mẫu đặc biệt dạng "18004xxx" hoặc "19004xxx"
    if re.search(r'1[89]00\d*x+', text):
        return True
    
    # Kiểm tra dạng danh sách có dấu ";"
    if ";" in text and re.search(r'1[89]00', text):
        return True
    
    # Kiểm tra dạng loại trừ như "190032xx trừ..."
    if "trừ" in text_lower and re.search(r'1[89]00', text):
        return True
    
    # Kiểm tra dạng mô tả phạm vi như "xx gồm..."
    if re.search(r'xx\s*gồm', text_lower) and re.search(r'1[89]00', text):
        return True
    
    # Kiểm tra dạng mô tả đặc biệt trong ngoặc đơn với xx
    if re.search(r'1[89]00\d+xx\s*\(', text):
        return True
        
    return False

def has_complex_list(text: str) -> bool:
    """Kiểm tra nếu chuỗi chứa danh sách phức tạp (dạng liệt kê nhiều lớp)"""
    # Mẫu như: 190031xx (14;18;35;38;66;68;79;99)
    pattern1 = r'1[89]00\d+xx\s*\([\d;-]+\)'
    
    # Mẫu như: 190032xx (xx gồm 01-27;29-31;...)
    pattern2 = r'1[89]00\d+xx\s*\(xx\s*gồm\s*[\d;-]+\)'
    
    # Mẫu như: 190033xx (xx trừ 00-03;05-18;...)
    pattern3 = r'1[89]00\d+xx\s*\(xx\s*trừ\s*[\d;-]+\)'
    
    # Mẫu phức tạp hơn
    pattern4 = r'1[89]00\d+xx\s*\(.+\)'
    
    return bool(re.search(pattern1, text) or re.search(pattern2, text) or re.search(pattern3, text) or re.search(pattern4, text))

def has_multiple_spaces(text: str) -> bool:
    """Kiểm tra nếu chuỗi chứa nhiều dấu cách liên tiếp"""
    return bool(re.search(r'  +', text))

def has_x_pattern(text: str) -> bool:
    """Kiểm tra nếu chuỗi chứa mẫu x cho số dịch vụ"""
    return bool(re.search(r'1[89]00\d*xx', text) or re.search(r'1[89]00\d*x+', text))

def has_semicolon_list(text: str) -> bool:
    """Kiểm tra nếu chuỗi chứa danh sách phân cách bằng dấu chấm phẩy"""
    return ";" in text and re.search(r'1[89]00', text)

def has_exclusion(text: str) -> bool:
    """Kiểm tra nếu chuỗi chứa từ loại trừ"""
    return bool(re.search(r'(trừ|loại trừ)', text.lower()))

def has_multi_line_format(text: str) -> bool:
    """Kiểm tra nếu chuỗi có định dạng nhiều dòng"""
    return '\n' in text

def detect_service_sections(df):
    """
    Phát hiện các đoạn dịch vụ trong file Excel với nhiều định dạng khác nhau
    
    Args:
        df: DataFrame pandas chứa dữ liệu Excel
        
    Returns:
        Danh sách các đoạn dịch vụ tìm thấy
    """
    service_sections = []
    for i in range(df.shape[0]):
        for j in range(min(5, df.shape[1])):
            if j >= df.shape[1]:
                continue
                
            cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
            
            # Mẫu tiêu đề đặc biệt của BB_1900_1800
            if re.search(r'(I{1,3})\.\s*\w+\s*sử\s*dụng\s*(1[89]00)', cell_value, re.IGNORECASE):
                match = re.search(r'(I{1,3})\.\s*\w+\s*sử\s*dụng\s*(1[89]00)', cell_value, re.IGNORECASE)
                section = match.group(2)
                service_sections.append({"row": i, "col": j, "type": section, "format": "special"})
            # Mẫu tiêu đề chuẩn
            elif re.search(r'(I{1,3})\.\s*Dịch\s*vụ\s*(1[89]00)', cell_value, re.IGNORECASE):
                match = re.search(r'(I{1,3})\.\s*Dịch\s*vụ\s*(1[89]00)', cell_value, re.IGNORECASE)
                section = match.group(2)
                service_sections.append({"row": i, "col": j, "type": section, "format": "standard"})
            # Tìm kiếm các chuỗi như "DỊCH VỤ 19002xxx"
            elif re.search(r'DỊCH\s*VỤ\s*(1[89]00\d+)', cell_value, re.IGNORECASE):
                match = re.search(r'DỊCH\s*VỤ\s*(1[89]00\d+)', cell_value, re.IGNORECASE)
                service_num = match.group(1)
                service_type = service_num[:4]  # Lấy 1800 hoặc 1900
                service_sections.append({"row": i, "col": j, "type": service_type, "format": "specific"})
            # Tìm kiếm các chuỗi như "TỪ... ĐẾN SỐ DỊCH VỤ 1900 CỦA HTC"
            elif re.search(r'ĐẾN\s+SỐ\s+DỊCH\s+VỤ\s+(1[89]00)\s+CỦA', cell_value, re.IGNORECASE):
                match = re.search(r'ĐẾN\s+SỐ\s+DỊCH\s+VỤ\s+(1[89]00)\s+CỦA', cell_value, re.IGNORECASE)
                service_type = match.group(1)
                service_sections.append({"row": i, "col": j, "type": service_type, "format": "vnm"})
    
    return service_sections

def extract_service_groups(df, section_info):
    """
    Trích xuất các nhóm dịch vụ từ một đoạn dịch vụ
    
    Args:
        df: DataFrame pandas chứa dữ liệu Excel
        section_info: Thông tin về đoạn dịch vụ cần trích xuất
        
    Returns:
        Danh sách các nhóm dịch vụ và số dịch vụ tương ứng
    """
    service_groups = []
    start_row = section_info["row"]
    service_type = section_info["type"]
    
    # Tìm vị trí bảng dữ liệu - tìm dòng chứa "STT"
    table_start = None
    for i in range(start_row, min(start_row + 15, df.shape[0])):
        for j in range(min(5, df.shape[1])):
            cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
            if cell_value.strip().upper() == "STT":
                table_start = i + 1  # Bỏ qua header
                break
        if table_start is not None:
            break
    
    if table_start is None:
        return service_groups
    
    # Tìm cột chứa "Nhóm dịch vụ"
    group_col = None
    for j in range(min(5, df.shape[1])):
        if table_start < df.shape[0] and j < df.shape[1]:
            cell_value = str(df.iloc[table_start, j]) if not pd.isna(df.iloc[table_start, j]) else ""
            if "NHÓM" in cell_value.upper() and "DỊCH VỤ" in cell_value.upper():
                group_col = j
                break
    
    if group_col is None:
        # Thử tìm cột khác có thể chứa thông tin dịch vụ
        for j in range(min(5, df.shape[1])):
            if table_start < df.shape[0] and j < df.shape[1]:
                cell_value = str(df.iloc[table_start, j]) if not pd.isna(df.iloc[table_start, j]) else ""
                if any(keyword in cell_value.upper() for keyword in ["DỊCH VỤ", "SỐ", "NHÓM"]):
                    group_col = j
                    break
        
        if group_col is None:
            group_col = 1  # Mặc định là cột 1 nếu không tìm thấy
    
    # Đọc dữ liệu các nhóm dịch vụ
    i = table_start + 1  # Bỏ qua dòng header
    while i < df.shape[0]:
        # Kiểm tra xem có phải đã đến phần tiếp theo 
        if i < df.shape[0]:
            row_content = " ".join([str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else "" for j in range(min(5, df.shape[1]))])
            if "DOANH THU" in row_content.upper() or "TỔNG CỘNG" in row_content.upper():
                break
        
        # Lấy giá trị STT và nhóm dịch vụ
        stt_val = None
        for j in range(min(2, df.shape[1])):
            cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
            if cell_value.strip() and re.match(r'^\d+$', cell_value.strip()):
                stt_val = cell_value.strip()
                break
        
        # Lấy giá trị nhóm dịch vụ
        group_val = ""
        if i < df.shape[0] and group_col < df.shape[1]:
            group_val = str(df.iloc[i, group_col]) if not pd.isna(df.iloc[i, group_col]) else ""
        
        # Xử lý trường hợp đặc biệt của BB_1900_1800
        if stt_val is not None and section_info.get("format") == "special":
            # Tìm kiếm "Nhóm giá" trong tất cả các cột
            for j in range(min(5, df.shape[1])):
                if j < df.shape[1]:
                    cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                    if "Nhóm giá" in cell_value:
                        group_val = cell_value
                        break
        
        if stt_val is not None and group_val.strip():
            # Xử lý các trường hợp đặc biệt
            if service_type == "1900" and "Nhóm giá" in group_val:
                # Nhóm giá đặc biệt trong 1900
                group_match = re.search(r'Nhóm\s+giá\s+(\d+)', group_val)
                if group_match:
                    price_group = group_match.group(1)
                    
                    if price_group == "909":
                        service_groups.append({
                            "name": group_val,
                            "mapped_numbers": ["1900909xxx"],
                            "has_revenue_share": True
                        })
                    elif price_group == "2727":
                        service_groups.append({
                            "name": group_val,
                            "mapped_numbers": ["19002727xx"],
                            "has_revenue_share": True
                        })
                    else:
                        service_groups.append({
                            "name": group_val,
                            "mapped_numbers": [f"1900{price_group}xx"],
                            "has_revenue_share": True
                        })
            elif service_type == "1800" and "1800" in group_val:
                # Dịch vụ 1800 đơn giản
                service_groups.append({
                    "name": group_val,
                    "mapped_numbers": ["1800xxxx"],
                    "has_revenue_share": False  # Giả định 1800 không có doanh thu ăn chia
                })
            elif service_type == "1800" and section_info.get("format") == "special":
                # Xử lý đặc biệt cho 1800 trong BB_1900_1800
                service_groups.append({
                    "name": "1800",
                    "mapped_numbers": ["1800xxxx"],
                    "has_revenue_share": False
                })
            elif service_type == "1900" and section_info.get("format") == "special":
                # Tìm nhóm giá trong các cột
                price_group = None
                for j in range(min(7, df.shape[1])):
                    if j < df.shape[1]:
                        cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                        if "giá" in cell_value.lower():
                            match = re.search(r'(\d+)', cell_value)
                            if match:
                                price_group = match.group(1)
                                break
                
                if price_group:
                    service_groups.append({
                        "name": f"Nhóm giá {price_group}",
                        "mapped_numbers": [f"1900{price_group}xxx"],
                        "has_revenue_share": True
                    })
                else:
                    service_groups.append({
                        "name": group_val,
                        "original_text": group_val,
                        "has_revenue_share": True
                    })
            else:
                # Trường hợp thông thường
                service_groups.append({
                    "name": group_val,
                    "original_text": group_val,
                    "has_revenue_share": True
                })
        
        i += 1
    
    return service_groups

def analyze_file_structure(file_path):
    """
    Phân tích cấu trúc file Excel để xác định thông tin tổng quan
    
    Args:
        file_path: Đường dẫn đến file Excel
        
    Returns:
        Dictionary chứa thông tin về cấu trúc file
    """
    try:
        df = pd.read_excel(file_path, header=None)
        
        # Tìm kiếm các từ khóa quan trọng
        keywords = {
            "service_types": [],  # 1800, 1900
            "title_rows": [],     # Dòng chứa tiêu đề
            "table_headers": [],  # Dòng chứa header bảng
            "nhom_gia": False     # Có chứa "Nhóm giá" không
        }
        
        for i in range(min(30, df.shape[0])):
            for j in range(min(10, df.shape[1])):
                if j < df.shape[1]:
                    cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                    
                    # Tìm loại dịch vụ
                    service_match = re.search(r'(1[89]00)', cell_value)
                    if service_match and service_match.group(1) not in keywords["service_types"]:
                        keywords["service_types"].append(service_match.group(1))
                    
                    # Tìm tiêu đề
                    if re.search(r'(I{1,3})\.\s*', cell_value):
                        keywords["title_rows"].append(i)
                    
                    # Tìm header bảng
                    if cell_value.strip().upper() == "STT":
                        keywords["table_headers"].append(i)
                    
                    # Kiểm tra "Nhóm giá"
                    if "Nhóm giá" in cell_value:
                        keywords["nhom_gia"] = True
        
        return keywords
    except Exception as e:
        logger.warning(f"Lỗi khi phân tích cấu trúc file: {str(e)}")
        return None

def find_service_numbers_in_file(df, service_type):
    """
    Tìm kiếm các số dịch vụ trong file Excel
    
    Args:
        df: DataFrame pandas chứa dữ liệu Excel
        service_type: Loại dịch vụ cần tìm (1800 hoặc 1900)
        
    Returns:
        Danh sách các số dịch vụ tìm thấy
    """
    service_numbers = []
    
    # Mẫu số dịch vụ 1800/1900
    pattern = re.compile(rf'{service_type}\d+')
    
    # Mẫu khoảng số dịch vụ 
    range_pattern = re.compile(rf'{service_type}\d+\s*-\s*{service_type}\d+')
    
    # Mẫu cho định dạng XX
    xx_pattern = re.compile(rf'{service_type}\d+xx')
    
    for i in range(df.shape[0]):
        for j in range(df.shape[1]):
            if j < df.shape[1]:
                cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                
                # Xử lý định dạng XX
                xx_matches = xx_pattern.findall(cell_value)
                for match in xx_matches:
                    if match not in service_numbers:
                        service_numbers.append(match)
                
                # Tìm khoảng số dịch vụ
                range_matches = range_pattern.findall(cell_value)
                for match in range_matches:
                    if match not in service_numbers:
                        service_numbers.append(match)
                
                # Tìm số dịch vụ đơn lẻ
                matches = pattern.findall(cell_value)
                for match in matches:
                    # Loại bỏ các số đã xuất hiện trong khoảng hoặc định dạng XX
                    if all(match not in range_match for range_match in range_matches) and all(match not in xx_match for xx_match in xx_matches):
                        if match not in service_numbers:
                            service_numbers.append(match)
    
    return service_numbers

def extract_service_numbers_from_vnm(df):
    """
    Trích xuất số dịch vụ từ file VNM có cấu trúc cụ thể
    
    Args:
        df: DataFrame pandas chứa dữ liệu Excel
        
    Returns:
        Danh sách các số dịch vụ tìm thấy
    """
    service_numbers = []
    
    # Tìm cột chứa nhóm dịch vụ
    group_col = None
    for i in range(min(10, df.shape[0])):
        for j in range(min(5, df.shape[1])):
            if j < df.shape[1]:
                cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                if "NHÓM DỊCH VỤ" in cell_value.upper():
                    group_col = j
                    break
        if group_col is not None:
            break
    
    if group_col is None:
        return service_numbers
    
    # Đọc các giá trị từ cột dịch vụ
    for i in range(group_col + 1, df.shape[0]):
        if i < df.shape[0] and group_col < df.shape[1]:
            cell_value = str(df.iloc[i, group_col]) if not pd.isna(df.iloc[i, group_col]) else ""
            
            if cell_value.strip() and any(x in cell_value for x in ["1900", "1800"]):
                # Xử lý các mẫu XX
                if "xx" in cell_value.lower():
                    service_numbers.append(cell_value.strip())
                # Xử lý khoảng số với dấu gạch nối
                elif "-" in cell_value:
                    parts = cell_value.split(";")
                    for part in parts:
                        part = part.strip()
                        if part:
                            service_numbers.append(part)
                # Xử lý danh sách với dấu chấm phẩy
                elif ";" in cell_value:
                    parts = cell_value.split(";")
                    for part in parts:
                        part = part.strip()
                        if part:
                            service_numbers.append(part)
                else:
                    service_numbers.append(cell_value.strip())
    
    return service_numbers

def extract_service_ranges_with_exclusions(text: str) -> List[str]:
    """
    Trích xuất các khoảng số dịch vụ từ chuỗi có loại trừ
    
    Args:
        text: Chuỗi cần phân tích (ví dụ: "190032xx (xx trừ 01-27;29-31)")
        
    Returns:
        Danh sách các khoảng số dịch vụ đã được phân tích
    """
    # Chuẩn hóa chuỗi
    text = text.strip()
    
    # Mẫu cho dạng "190032xx (xx trừ 01-27;29-31)"
    pattern1 = r'(1[89]00\d+xx)\s*\(xx\s*trừ\s*([\d;-]+)\)'
    match1 = re.search(pattern1, text)
    if match1:
        base_number = match1.group(1)
        exclusions = match1.group(2)
        return [f"{base_number} trừ {exclusions}"]
    
    # Mẫu cho dạng "190032xx (xx gồm 01-27;29-31)"
    pattern2 = r'(1[89]00\d+xx)\s*\(xx\s*gồm\s*([\d;-]+)\)'
    match2 = re.search(pattern2, text)
    if match2:
        base_number = match2.group(1)
        inclusions = match2.group(2)
        return [f"{base_number} gồm {inclusions}"]
    
    # Mẫu cho dạng "190031xx (14;18;35;38)" - danh sách số cụ thể
    pattern3 = r'(1[89]00\d+xx)\s*\(([\d;-]+)\)'
    match3 = re.search(pattern3, text)
    if match3:
        base_number = match3.group(1)
        specific_numbers = match3.group(2)
        return [f"{base_number} ({specific_numbers})"]
    
    # Trả về chuỗi gốc nếu không khớp với bất kỳ mẫu nào
    return [text]

def parse_service_range(service_str: str) -> List[str]:
    """
    Phân tích dãy số dịch vụ từ các định dạng khác nhau
    
    Args:
        service_str: Chuỗi chứa số dịch vụ (ví dụ: "1900400000-1900400099", "1900400000->1900400099")
        
    Returns:
        Danh sách các số dịch vụ đã được phân tích
    """
    # Kiểm tra nếu không phải số dịch vụ hợp lệ
    if not is_valid_service_number(service_str):
        return []
    
    # Chuẩn hóa chuỗi đầu vào
    service_str = normalize_service_number(service_str)
    
    # Trường hợp đặc biệt: Chuỗi chỉ chứa "1800" hoặc "1900"
    if service_str.strip() in ["1800", "1900"]:
        return [service_str.strip()]
    
    # Thay thế dấu xuống dòng bằng dấu cách
    if '\n' in service_str:
        service_str = service_str.replace('\n', ' ')
    
    # Kiểm tra dạng mô tả phức tạp có chứa xx và các phạm vi con
    if has_complex_list(service_str):
        return extract_service_ranges_with_exclusions(service_str)
    
    # Xử lý dạng loại trừ đơn giản
    if "trừ" in service_str.lower():
        return [service_str]
    
    # Kiểm tra định dạng khoảng số (range)
    range_match = re.search(r'(\d+)\s*-\s*(\d+)', service_str)
    if range_match:
        start = range_match.group(1)
        end = range_match.group(2)
        
        # Trường hợp dạng 19004000xx-19004009xx
        if len(start) != len(end) or 'x' in start or 'x' in end:
            return [f"{start}-{end}"]
        
        # Trường hợp đơn giản như 1900400000-1900400099
        return [f"{start}-{end}"]
    
    # Kiểm tra danh sách số được phân tách bằng dấu phẩy
    if ',' in service_str and re.search(r'1[89]00', service_str):
        return [item.strip() for item in service_str.split(',') if item.strip() and is_valid_service_number(item.strip())]
    
    # Kiểm tra dạng loại trừ như "190032xx trừ..."
    if "trừ" in service_str.lower() and re.search(r'1[89]00', service_str):
        return [service_str]
    
    # Kiểm tra dạng mô tả phạm vi như "xx gồm..."
    if re.search(r'xx\s*gồm', service_str.lower()) and re.search(r'1[89]00', service_str):
        return [service_str]
    
    # Kiểm tra dạng mô tả đặc biệt trong ngoặc đơn với xx
    if re.search(r'1[89]00\d+xx\s*\(', service_str):
        return [service_str]
        
    return []

def is_dst_1800_1900_file(filename: str) -> bool:
    """
    Kiểm tra xem file có phải là file đối soát 1800/1900 không
    
    Args:
        filename: Tên file cần kiểm tra
        
    Returns:
        True nếu là file đối soát 1800/1900, False nếu không phải
    """
    filename = os.path.basename(filename).lower()
    for pattern in DST_1800_1900_PATTERNS:
        if re.search(pattern, filename, re.IGNORECASE):
            return True
    return False

def extract_month_year(filename: str) -> Tuple[Optional[int], Optional[int]]:
    """
    Trích xuất tháng và năm từ tên file
    
    Args:
        filename: Tên file đối soát 1800/1900
        
    Returns:
        Tuple chứa (tháng, năm) hoặc (None, None) nếu không trích xuất được
    """
    filename = os.path.basename(filename)
    
    # Mẫu 1: T1_2025, Tháng 1-2025, Tháng 1_2025
    pattern1 = r'(T|Th[áa]ng)\s*(\d{1,2})[-_\.\s](\d{4})'
    match = re.search(pattern1, filename, re.IGNORECASE)
    if match:
        return int(match.group(2)), int(match.group(3))
    
    # Mẫu 2: 012025, 202501 (năm tháng hoặc tháng năm)
    pattern2 = r'(\d{2})(\d{4})'
    match = re.search(pattern2, filename)
    if match:
        if int(match.group(1)) <= 12:
            return int(match.group(1)), int(match.group(2))
        else:
            # Định dạng năm trước tháng sau
            year_str = match.group(1) + match.group(2)[:2]
            month_str = match.group(2)[2:]
            if int(month_str) <= 12:
                return int(month_str), int(year_str)
    
    return None, None

def extract_partners(filename: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
    """
    Trích xuất thông tin đối tác và loại dịch vụ từ tên file
    
    Args:
        filename: Tên file đối soát 1800/1900
        
    Returns:
        Tuple chứa (từ_mạng, đến_đối_tác, loại_dịch_vụ) hoặc (None, None, None) nếu không trích xuất được
    """
    filename = os.path.basename(filename)
    
    # Xác định loại dịch vụ
    service_type = None
    if re.search(r'18001900|1800[-_\s]1900|1900[-_\s]1800', filename, re.IGNORECASE):
        service_type = "1800xxxx/1900xxxx"
    elif re.search(r'1900', filename, re.IGNORECASE):
        service_type = "1900xxxx"
    elif re.search(r'1800', filename, re.IGNORECASE):
        service_type = "1800xxxx"
    
    # Xác định từ mạng và đến đối tác
    partners = re.findall(r'([A-Za-z]{2,5})', filename.upper())
    if len(partners) >= 2:
        from_partner, to_partner = None, None
        
        # Kiểm tra mẫu HTC_PARTNER hoặc PARTNER_HTC
        htc_index = None
        for i, partner in enumerate(partners):
            if partner == "HTC":
                htc_index = i
                break
                
        if htc_index is not None:
            # Nếu HTC đứng trước, HTC là from_partner
            if htc_index == 0 and len(partners) > 1:
                from_partner = "HTC"
                to_partner = partners[1]
            # Nếu HTC đứng sau, HTC là to_partner
            elif htc_index > 0:
                from_partner = partners[htc_index - 1]
                to_partner = "HTC"
        else:
            # Nếu không có HTC, giả định 2 đối tác đầu tiên
            from_partner = partners[0]
            to_partner = partners[1] if len(partners) > 1 else None
        
        return from_partner, to_partner, service_type
    
    return None, None, service_type

def process_dst_1800_1900_file(file_path: str, partner_id: Optional[int] = None) -> DoiSoat1800_1900:
    """
    Xử lý file đối soát dịch vụ 1800/1900 và chuyển đổi thành model DoiSoat1800_1900
    
    Args:
        file_path: Đường dẫn đến file Excel đối soát
        partner_id: ID của đối tác liên quan (tùy chọn)
        
    Returns:
        Model DoiSoat1800_1900 chứa dữ liệu đã xử lý
    """
    try:
        # Đọc file Excel
        file_name = os.path.basename(file_path)
        xls = pd.ExcelFile(file_path)
        
        # Mặc định lấy sheet đầu tiên
        sheet_name = xls.sheet_names[0]
        df = pd.read_excel(xls, sheet_name=sheet_name, header=None)
        
        # Trích xuất thông tin tháng/năm và đối tác
        thang, nam = extract_month_year(file_name)
        thang_doi_soat = f"{thang}/{nam}" if thang and nam else None
        tu_mang, den_doi_tac, loai_dich_vu = extract_partners(file_name)
        
        # Mặc định nếu không trích xuất được
        tu_mang = tu_mang or "HTC"
        den_doi_tac = den_doi_tac or "ĐỐI TÁC"
        loai_dich_vu = loai_dich_vu or "1900xxxx"
        
        # Tìm hợp đồng số (nếu có)
        hop_dong_so = None
        try:
            for i in range(10):
                header_text = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                if "HỢP ĐỒNG SỐ" in header_text.upper():
                    hop_dong_match = re.search(r'HỢP ĐỒNG SỐ\s*[.:…]\s*(\S+)', header_text, re.IGNORECASE)
                    if hop_dong_match:
                        hop_dong_so = hop_dong_match.group(1)
                        break
        except Exception as e:
            logger.warning(f"Không thể trích xuất số hợp đồng: {str(e)}")
        
        # Phân tích cấu trúc file để xác định đặc điểm
        file_structure = analyze_file_structure(file_path)
        
        # Phát hiện các đoạn dịch vụ với các định dạng khác nhau
        service_sections = detect_service_sections(df)
        logger.info(f"Đã phát hiện {len(service_sections)} đoạn dịch vụ trong file {file_name}")
        
        # Danh sách nhóm dịch vụ
        nhom_dich_vu_list = []
        
        # Kiểm tra xem có phải định dạng đặc biệt không
        is_special_format = any(section.get("format") in ["special", "vnm"] for section in service_sections)
        
        # Kiểm tra xem có phải file VNM đặc biệt không
        is_vnm_file = "vnm" in file_name.lower()
        
        # Xử lý định dạng đặc biệt
        if is_special_format or is_vnm_file:
            # Xử lý theo định dạng đặc biệt
            for section in service_sections:
                service_type = section["type"]
                # Trích xuất các nhóm dịch vụ từ đoạn
                service_groups = extract_service_groups(df, section)
                
                if service_groups:
                    # Tạo nhóm dịch vụ
                    nhom_dich_vu = NhomDichVu1800_1900(
                        ma_nhom=service_type,
                        ten_nhom=f"Dịch vụ {service_type}",
                        chi_tiet=[]
                    )
                    
                    # Thêm chi tiết dịch vụ
                    for idx, group in enumerate(service_groups, 1):
                        so_dich_vu = group.get("original_text", group.get("name", ""))
                        mapped_numbers = group.get("mapped_numbers", [so_dich_vu])
                        
                        for mapped_num in mapped_numbers:
                            chi_tiet = ChiTietDichVu1800_1900(
                                stt=idx,
                                so_dich_vu=mapped_num,
                                # Giá trị mặc định cho các trường khác
                                so_lieu_ben_a=0,
                                so_lieu_ben_b=0,
                                chenh_lech=0,
                                so_lieu_tinh_dt=0,
                                muc_cuoc=0,
                                cuoc_thu_khach=0,
                                doanh_thu_ben_a=0,
                                doanh_thu_ben_b=0
                            )
                            nhom_dich_vu.chi_tiet.append(chi_tiet)
                    
                    nhom_dich_vu_list.append(nhom_dich_vu)
            
            # Nếu là file VNM đặc biệt và không tìm thấy đoạn dịch vụ, xử lý riêng
            if is_vnm_file and len(service_sections) == 0:
                vnm_numbers = extract_service_numbers_from_vnm(df)
                if vnm_numbers:
                    nhom_dich_vu = NhomDichVu1800_1900(
                        ma_nhom="1900",
                        ten_nhom="Dịch vụ 1900",
                        chi_tiet=[]
                    )
                    
                    for idx, num in enumerate(vnm_numbers, 1):
                        chi_tiet = ChiTietDichVu1800_1900(
                            stt=idx,
                            so_dich_vu=num,
                            so_lieu_ben_a=0,
                            so_lieu_ben_b=0,
                            chenh_lech=0,
                            so_lieu_tinh_dt=0,
                            muc_cuoc=0,
                            cuoc_thu_khach=0,
                            doanh_thu_ben_a=0,
                            doanh_thu_ben_b=0
                        )
                        nhom_dich_vu.chi_tiet.append(chi_tiet)
                    
                    nhom_dich_vu_list.append(nhom_dich_vu)
            
            # Nếu không tìm thấy đoạn dịch vụ nhưng biết có dịch vụ từ cấu trúc
            if len(service_sections) == 0 and file_structure and file_structure.get("service_types"):
                for service_type in file_structure.get("service_types"):
                    # Tìm kiếm dữ liệu dịch vụ trong file
                    service_numbers = find_service_numbers_in_file(df, service_type)
                    
                    if service_numbers:
                        nhom_dich_vu = NhomDichVu1800_1900(
                            ma_nhom=service_type,
                            ten_nhom=f"Dịch vụ {service_type}",
                            chi_tiet=[]
                        )
                        
                        for idx, num in enumerate(service_numbers, 1):
                            chi_tiet = ChiTietDichVu1800_1900(
                                stt=idx,
                                so_dich_vu=num,
                                so_lieu_ben_a=0,
                                so_lieu_ben_b=0,
                                chenh_lech=0,
                                so_lieu_tinh_dt=0,
                                muc_cuoc=0,
                                cuoc_thu_khach=0,
                                doanh_thu_ben_a=0,
                                doanh_thu_ben_b=0
                            )
                            nhom_dich_vu.chi_tiet.append(chi_tiet)
                        
                        nhom_dich_vu_list.append(nhom_dich_vu)
        
        # Nếu không có nhóm dịch vụ nào được phát hiện với định dạng đặc biệt, sử dụng phương pháp truyền thống
        if not nhom_dich_vu_list:
            # 1. Tìm các header của dịch vụ (I. Dịch vụ 1900xxxx, II. Dịch vụ 1800xxxx, ...)
            service_headers = []
            for i in range(df.shape[0]):
                cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                if re.search(r'(I{1,3}\.?\s*Dịch\s*[Vv]ụ|Dịch\s*[Vv]ụ)\s*(1[89]00|109)', cell_value, re.IGNORECASE):
                    service_name = ""
                    if "1900" in cell_value:
                        service_name = "1900xxxx"
                    elif "1800" in cell_value:
                        service_name = "1800xxxx"
                    elif "109" in cell_value:
                        service_name = "109x"
                    
                    service_headers.append({"row": i, "service_type": service_name, "service_name": cell_value.strip()})
                    logger.debug(f"Tìm thấy nhóm dịch vụ: {service_name} (dòng {i+1}): {cell_value.strip()}")
        
        # 2. Tìm các bảng dịch vụ
        tables = []
        for i in range(df.shape[0]):
            row_text = " ".join([str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else "" for j in range(min(5, df.shape[1]))])
            if "STT" in row_text.upper() and ("SỐ DỊCH VỤ" in row_text.upper() or "ĐẦU SỐ" in row_text.upper()):
                # Tìm cột chứa số dịch vụ
                service_col = None
                for j in range(min(10, df.shape[1])):
                    if j >= df.shape[1]:
                        continue
                        
                    header_text = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                    if "SỐ DỊCH VỤ" in header_text.upper() or "ĐẦU SỐ" in header_text.upper() or "MÃ DỊCH VỤ" in header_text.upper():
                        service_col = j
                        break
                
                if service_col is not None:
                    # Tìm dòng kết thúc bảng
                    table_end = None
                    for k in range(i + 1, min(i + 50, df.shape[0])):
                        if k >= df.shape[0]:
                            break
                            
                        cell_value = str(df.iloc[k, 0]) if not pd.isna(df.iloc[k, 0]) else ""
                        if "TỔNG CỘNG" in cell_value.upper() or "CỘNG" in cell_value.upper():
                            table_end = k
                            break
                    
                    if table_end is None:
                        table_end = min(i + 30, df.shape[0])
                    
                    # Xác định loại dịch vụ của bảng này
                    service_type = None
                    for k in range(i + 1, min(table_end, df.shape[0])):
                        if k >= df.shape[0] or service_col >= df.shape[1]:
                            continue
                            
                        cell_value = str(df.iloc[k, service_col]) if not pd.isna(df.iloc[k, service_col]) else ""
                        if "1900" in cell_value:
                            service_type = "1900xxxx"
                            break
                        elif "1800" in cell_value:
                            service_type = "1800xxxx"
                            break
                        elif "109" in cell_value:
                            service_type = "109x"
                            break
                    
                    if service_type:
                        tables.append({
                            "start_row": i,
                            "end_row": table_end,
                            "service_col": service_col,
                            "service_type": service_type
                        })
                        logger.debug(f"Tìm thấy bảng dịch vụ {service_type} từ dòng {i+1} đến dòng {table_end+1}")
        
        # 3. Kết hợp thông tin nhóm dịch vụ và bảng để trích xuất chi tiết dịch vụ
        for header in service_headers:
            # Tìm bảng tương ứng với nhóm dịch vụ này
            matched_table = None
            for table in tables:
                if (table["service_type"] == header["service_type"] and 
                    abs(table["start_row"] - header["row"]) < 15):
                    matched_table = table
                    break
            
            # Danh sách chi tiết dịch vụ
            chi_tiet_list = []
            raw_service_numbers = []
            
            if matched_table:
                # Trích xuất số dịch vụ và thông tin từ bảng
                for i in range(matched_table["start_row"] + 1, matched_table["end_row"]):
                    if i >= df.shape[0] or matched_table["service_col"] >= df.shape[1]:
                        continue
                    
                    # Lưu lại định dạng gốc của số dịch vụ
                    cell_value = str(df.iloc[i, matched_table["service_col"]]) if not pd.isna(df.iloc[i, matched_table["service_col"]]) else ""
                    cell_value = cell_value.strip()
                    
                    if cell_value and (header["service_type"][:4].lower() in cell_value.lower() or
                                      (header["service_type"] == "109x" and "109" in cell_value)):
                        raw_service_numbers.append({
                            "row": i,
                            "text": cell_value,
                            "has_tru": "trừ" in cell_value.lower()
                        })
                        
                        # Đọc dữ liệu theo cấu trúc bảng
                        try:
                            # Xác định cột dữ liệu quan trọng trong bảng (nếu cần)
                            # Đọc các trường số liệu...
                            
                            # Tạo chi tiết dịch vụ
                            chi_tiet = ChiTietDichVu1800_1900(
                                stt=int(float(df.iloc[i, 0])) if not pd.isna(df.iloc[i, 0]) else 0,
                                so_dich_vu=cell_value,
                                # Thêm các trường khác tùy vào cấu trúc bảng
                                so_lieu_ben_a=0,
                                so_lieu_ben_b=0,
                                chenh_lech=0,
                                so_lieu_tinh_dt=0,
                                muc_cuoc=0,
                                cuoc_thu_khach=0,
                                doanh_thu_ben_a=0,
                                doanh_thu_ben_b=0
                            )
                            
                            chi_tiet_list.append(chi_tiet)
                        except Exception as e:
                            logger.warning(f"Lỗi khi xử lý dòng {i}: {str(e)}")
            else:
                # Tìm số dịch vụ trong các ô văn bản
                for i in range(header["row"] + 1, min(header["row"] + 20, df.shape[0])):
                    for j in range(min(5, df.shape[1])):
                        if j >= df.shape[1]:
                            continue
                            
                        cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                        cell_value = cell_value.strip()
                        
                        if cell_value and (header["service_type"][:4].lower() in cell_value.lower() or 
                                          (header["service_type"] == "109x" and "109" in cell_value)):
                            # Kiểm tra thêm để loại bỏ các cụm từ không mong muốn
                            if "dịch vụ" not in cell_value.lower() and "thanh toán" not in cell_value.lower() and "doanh thu" not in cell_value.lower():
                                if cell_value not in [item["text"] for item in raw_service_numbers]:
                                    raw_service_numbers.append({
                                        "row": i,
                                        "text": cell_value,
                                        "has_tru": "trừ" in cell_value.lower()
                                    })
                
                # Tạo chi tiết dịch vụ từ raw_service_numbers nếu cần
                for idx, item in enumerate(raw_service_numbers, 1):
                    chi_tiet = ChiTietDichVu1800_1900(
                        stt=idx,
                        so_dich_vu=item["text"],
                        so_lieu_ben_a=0,
                        so_lieu_ben_b=0,
                        chenh_lech=0,
                        so_lieu_tinh_dt=0,
                        muc_cuoc=0,
                        cuoc_thu_khach=0,
                        doanh_thu_ben_a=0,
                        doanh_thu_ben_b=0
                    )
                    
                    chi_tiet_list.append(chi_tiet)
            
            # Tạo tổng kết cho nhóm dịch vụ
            tong_ket = TongKet1800_1900(
                tong_cong_chua_vat=0,
                thue_vat=0,
                tong_cong_co_vat=0
            )
            
            # Tạo nhóm dịch vụ với thông tin chi tiết
            if chi_tiet_list:  # Chỉ thêm nhóm nếu có số dịch vụ
                nhom_dich_vu = NhomDichVu1800_1900(
                    ten_dich_vu=header["service_name"],
                    chi_tiet=chi_tiet_list,
                    tong_ket=tong_ket
                )
                
                # Thêm thông tin bổ sung về định dạng gốc của số dịch vụ (nếu schema cho phép)
                # nhom_dich_vu.raw_service_numbers = raw_service_numbers
                
                nhom_dich_vu_list.append(nhom_dich_vu)
        
        # 3. Tìm phần thanh toán (Thanh toán, Sau bù trừ)
        thanh_toan_data = {
            "doanh_thu_ben_a_thanh_toan": 0,
            "doanh_thu_ben_b_thanh_toan": 0,
            "sau_bu_tru": 0,
            "ben_thanh_toan": tu_mang,  # Mặc định
            "ben_nhan": den_doi_tac     # Mặc định
        }
        
        try:
            # Tìm section thanh toán (thường là III. Thanh toán)
            thanh_toan_idx = None
            for i in range(df.shape[0]):
                cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                if "THANH TOÁN" in cell_value.upper() or "III" in cell_value.upper() and "THANH TOÁN" in cell_value.upper():
                    thanh_toan_idx = i
                    logger.debug(f"Tìm thấy phần thanh toán (dòng {i+1})")
                    break
            
            if thanh_toan_idx is not None:
                # Tìm bảng thanh toán (có các dòng "1. Tổng doanh thu...")
                for i in range(thanh_toan_idx, min(thanh_toan_idx + 20, df.shape[0])):
                    cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                    
                    # Dòng 1: Doanh thu A thanh toán cho B
                    if "1." in cell_value and f"{tu_mang}" in cell_value and f"{den_doi_tac}" in cell_value:
                        # Tìm giá trị Tổng cộng của dòng này (thường ở cột cuối)
                        for j in range(df.shape[1] - 1, 0, -1):
                            if not pd.isna(df.iloc[i, j]):
                                thanh_toan_data["doanh_thu_ben_a_thanh_toan"] = float(df.iloc[i, j])
                                break
                    
                    # Dòng 2: Doanh thu B thanh toán cho A
                    elif "2." in cell_value and f"{den_doi_tac}" in cell_value and f"{tu_mang}" in cell_value:
                        # Tìm giá trị Tổng cộng của dòng này (thường ở cột cuối)
                        for j in range(df.shape[1] - 1, 0, -1):
                            if not pd.isna(df.iloc[i, j]):
                                thanh_toan_data["doanh_thu_ben_b_thanh_toan"] = float(df.iloc[i, j])
                                break
                    
                    # Dòng 3: Sau bù trừ
                    elif "3." in cell_value and "SAU BÙ TRỪ" in cell_value.upper():
                        # Tìm giá trị Tổng cộng của dòng này (thường ở cột cuối)
                        for j in range(df.shape[1] - 1, 0, -1):
                            if not pd.isna(df.iloc[i, j]):
                                thanh_toan_data["sau_bu_tru"] = float(df.iloc[i, j])
                                break
                
                # Tìm dòng "Sau bù trừ X phải thanh toán cho Y"
                for i in range(thanh_toan_idx, min(thanh_toan_idx + 30, df.shape[0])):
                    cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                    if "SAU BÙ TRỪ" in cell_value.upper() and "PHẢI THANH TOÁN CHO" in cell_value.upper():
                        # Trích xuất bên thanh toán và bên nhận
                        logger.debug(f"Tìm thấy thông tin sau bù trừ (dòng {i+1})")
                        for partner in [tu_mang, den_doi_tac]:
                            if partner in cell_value:
                                pattern = rf"SAU BÙ TRỪ\s+(\w+)\s+PHẢI THANH TOÁN CHO\s+(\w+)"
                                m = re.search(pattern, cell_value, re.IGNORECASE)
                                if m:
                                    thanh_toan_data["ben_thanh_toan"] = m.group(1)
                                    thanh_toan_data["ben_nhan"] = m.group(2)
                                break
                        break
            
            # Nếu sau_bu_tru chưa được tính, tính từ doanh thu thanh toán
            if thanh_toan_data["sau_bu_tru"] == 0:
                thanh_toan_data["sau_bu_tru"] = abs(
                    thanh_toan_data["doanh_thu_ben_a_thanh_toan"] - 
                    thanh_toan_data["doanh_thu_ben_b_thanh_toan"]
                )
            
            # Xác định bên thanh toán và bên nhận dựa trên số tiền
            if not (thanh_toan_data["ben_thanh_toan"] != tu_mang or thanh_toan_data["ben_nhan"] != den_doi_tac):
                if thanh_toan_data["doanh_thu_ben_a_thanh_toan"] > thanh_toan_data["doanh_thu_ben_b_thanh_toan"]:
                    thanh_toan_data["ben_thanh_toan"] = tu_mang
                    thanh_toan_data["ben_nhan"] = den_doi_tac
                else:
                    thanh_toan_data["ben_thanh_toan"] = den_doi_tac
                    thanh_toan_data["ben_nhan"] = tu_mang
                
        except Exception as e:
            logger.warning(f"Lỗi khi xử lý phần thanh toán: {str(e)}")
        
        # Tạo đối tượng thanh toán
        thanh_toan = ThanhToan1800_1900(
            doanh_thu_ben_a_thanh_toan=thanh_toan_data["doanh_thu_ben_a_thanh_toan"],
            doanh_thu_ben_b_thanh_toan=thanh_toan_data["doanh_thu_ben_b_thanh_toan"],
            sau_bu_tru=thanh_toan_data["sau_bu_tru"],
            ben_thanh_toan=thanh_toan_data["ben_thanh_toan"],
            ben_nhan=thanh_toan_data["ben_nhan"]
        )
        
        # Tạo đối tượng DoiSoat1800_1900 cuối cùng
        doi_soat = DoiSoat1800_1900(
            thang_doi_soat=thang_doi_soat if thang_doi_soat else "",
            tu_mang=tu_mang,
            den_doi_tac=den_doi_tac,
            loai_dich_vu=loai_dich_vu,
            nhom_dich_vu=nhom_dich_vu_list,
            thanh_toan=thanh_toan,
            hop_dong_so=hop_dong_so,
            file_name=file_name,
            partner_id=partner_id  # Thêm partner_id
        )
        
        return doi_soat
        
    except Exception as e:
        logger.error(f"Lỗi khi xử lý file đối soát 1800/1900 {file_path}: {str(e)}")
        raise ValueError(f"Không thể xử lý file đối soát 1800/1900: {str(e)}")

def extract_dst_1800_1900_numbers(file_path: str, prefix: Optional[str] = None) -> Dict[str, Any]:
    """
    Trích xuất số điện thoại từ file đối soát 1800/1900, giữ nguyên định dạng gốc
    
    Args:
        file_path: Đường dẫn đến file Excel đối soát 1800/1900
        prefix: Tiền tố để lọc (ví dụ: '1900', '1800')
        
    Returns:
        Dictionary chứa danh sách số điện thoại đã trích xuất
    """
    try:
        # Đọc file Excel
        file_name = os.path.basename(file_path)
        xls = pd.ExcelFile(file_path)
        
        # Mặc định lấy sheet đầu tiên
        sheet_name = xls.sheet_names[0]
        df = pd.read_excel(xls, sheet_name=sheet_name, header=None)
        
        # Kết quả chi tiết
        result = {
            "raw_numbers": [],  # Lưu định dạng gốc
            "service_groups": [],  # Phân nhóm theo loại dịch vụ
            "special_cases": {
                "multi_line_ranges": [],  # Khoảng số trên nhiều dòng
                "multiple_ranges": [],    # Nhiều khoảng số trên một dòng
                "x_pattern_numbers": [],  # Số dịch vụ dạng xxxx
                "semicolon_lists": [],    # Danh sách số với dấu ;
                "excluded_numbers": []    # Số dịch vụ có loại trừ (trừ)
            }
        }
        
        # 1. Trích xuất thông tin tổng quát về file
        extracted_info = extract_service_details(file_path)
        if extracted_info and "service_groups" in extracted_info:
            # Đã trích xuất được chi tiết các nhóm dịch vụ
            result["service_groups"] = extracted_info["service_groups"]
            
            # Tổng hợp tất cả số dịch vụ và phát hiện các trường hợp đặc biệt
            for group in extracted_info["service_groups"]:
                for item in group.get("raw_service_numbers", []):
                    cell_value = item["text"]
                    
                    # Chỉ thêm vào kết quả nếu phù hợp với prefix
                    if prefix:
                        if prefix in cell_value:
                            result["raw_numbers"].append(cell_value)
                    else:
                        result["raw_numbers"].append(cell_value)
                    
                    # Kiểm tra các trường hợp đặc biệt
                    if ";" in cell_value:
                        result["special_cases"]["semicolon_lists"].append(cell_value)
                    
                    if re.search(r'1[89]00\d*x+', cell_value):
                        result["special_cases"]["x_pattern_numbers"].append(cell_value)
                    
                    if "-" in cell_value and ";" in cell_value:
                        result["special_cases"]["multiple_ranges"].append(cell_value)
                    
                    if "trừ" in cell_value.lower():
                        result["special_cases"]["excluded_numbers"].append(cell_value)
                    
                    if "\n" in cell_value or cell_value.endswith(";") or cell_value.endswith(","):
                        result["special_cases"]["multi_line_ranges"].append(cell_value)
        else:
            # Nếu không trích xuất được chi tiết, dùng phương pháp đơn giản hơn
            phone_numbers = []
            # Dictionary để theo dõi các ô có thể là phần tiếp theo của ô trước đó
            multi_line_cells = {}
            
            # Xử lý file và trích xuất số điện thoại
            for i in range(df.shape[0]):
                for j in range(df.shape[1]):
                    if pd.isna(df.iloc[i, j]):
                        continue
                    
                    cell_value = str(df.iloc[i, j])
                    cell_value = cell_value.strip()
                    
                    # Bỏ qua nếu ô rỗng
                    if not cell_value:
                        continue
                    
                    # Kiểm tra nếu ô có thể là phần tiếp theo của ô trước đó
                    prev_key = f"{i-1}_{j}"
                    if prev_key in multi_line_cells:
                        # Nối với ô trước đó
                        combined_value = multi_line_cells[prev_key] + " " + cell_value
                        cell_value = combined_value
                        
                        # Thêm vào trường hợp đặc biệt
                        result["special_cases"]["multi_line_ranges"].append(cell_value)
                        
                        # Xóa khỏi danh sách theo dõi
                        del multi_line_cells[prev_key]
                    
                    # Kiểm tra nếu ô kết thúc bằng dấu ; hoặc ,
                    if cell_value.endswith(';') or cell_value.endswith(','):
                        multi_line_cells[f"{i}_{j}"] = cell_value
                    
                    # Kiểm tra nếu cell chứa số 1800 hoặc 1900
                    if re.search(r'1[89]00\d{3,}', cell_value) or re.search(r'1[89]00\d*x+', cell_value):
                        # Trích xuất số điện thoại từ chuỗi
                        matches = re.findall(r'1[89]00\d{3,}', cell_value)
                        for match in matches:
                            # Nếu có prefix để lọc
                            if prefix:
                                if match.startswith(prefix):
                                    phone_numbers.append(match)
                            else:
                                phone_numbers.append(match)
                        
                        # Nếu cell hợp lệ và chưa được thêm vào kết quả
                        if is_valid_service_number(cell_value) and cell_value not in result["raw_numbers"]:
                            if prefix:
                                if prefix in cell_value:
                                    result["raw_numbers"].append(cell_value)
                                    
                                    # Kiểm tra các trường hợp đặc biệt
                                    if ";" in cell_value:
                                        result["special_cases"]["semicolon_lists"].append(cell_value)
                                    
                                    if re.search(r'1[89]00\d*x+', cell_value):
                                        result["special_cases"]["x_pattern_numbers"].append(cell_value)
                                    
                                    if "-" in cell_value and ";" in cell_value:
                                        result["special_cases"]["multiple_ranges"].append(cell_value)
                                    
                                    if "trừ" in cell_value.lower():
                                        result["special_cases"]["excluded_numbers"].append(cell_value)
                            else:
                                result["raw_numbers"].append(cell_value)
                                
                                # Kiểm tra các trường hợp đặc biệt
                                if ";" in cell_value:
                                    result["special_cases"]["semicolon_lists"].append(cell_value)
                                
                                if re.search(r'1[89]00\d*x+', cell_value):
                                    result["special_cases"]["x_pattern_numbers"].append(cell_value)
                                
                                if "-" in cell_value and ";" in cell_value:
                                    result["special_cases"]["multiple_ranges"].append(cell_value)
                                
                                if "trừ" in cell_value.lower():
                                    result["special_cases"]["excluded_numbers"].append(cell_value)
            
            # Loại bỏ trùng lặp trong phone_numbers
            result["numbers"] = list(set(phone_numbers))
        
        # Loại bỏ trùng lặp trong raw_numbers
        unique_raw_numbers = []
        for num in result["raw_numbers"]:
            if num not in unique_raw_numbers:
                unique_raw_numbers.append(num)
        
        result["raw_numbers"] = unique_raw_numbers
        
        # Tạo thống kê về các trường hợp đặc biệt
        special_stats = {
            "multi_line_ranges": len(result["special_cases"]["multi_line_ranges"]),
            "multiple_ranges": len(result["special_cases"]["multiple_ranges"]),
            "x_pattern_numbers": len(result["special_cases"]["x_pattern_numbers"]),
            "semicolon_lists": len(result["special_cases"]["semicolon_lists"]),
            "excluded_numbers": len(result["special_cases"]["excluded_numbers"])
        }
        
        # Tạo kết quả cuối cùng
        return {
            "success": True,
            "message": f"Đã trích xuất {len(result['raw_numbers'])} số dịch vụ",
            "total": len(result["raw_numbers"]),
            "file_name": file_name,
            "raw_numbers": result["raw_numbers"],
            "service_groups": result.get("service_groups", []),
            "special_stats": special_stats
        }
    
    except Exception as e:
        logger.error(f"Lỗi khi trích xuất số điện thoại từ file {file_path}: {str(e)}")
        return {
            "success": False,
            "message": f"Lỗi: {str(e)}",
            "total": 0,
            "file_name": os.path.basename(file_path)
        }

def extract_service_details(file_path: str) -> Dict[str, Any]:
    """
    Trích xuất chi tiết các nhóm dịch vụ từ file Excel đối soát 1800/1900,
    giữ nguyên định dạng gốc của các số dịch vụ
    
    Args:
        file_path: Đường dẫn đến file đối soát
        
    Returns:
        Dict chứa thông tin chi tiết các nhóm và các số dịch vụ
    """
    try:
        # Đọc file Excel
        file_name = os.path.basename(file_path)
        logger.debug(f"Phân tích file: {file_name}")
        
        xls = pd.ExcelFile(file_path)
        sheet_name = xls.sheet_names[0]  # Lấy sheet đầu tiên
        df = pd.read_excel(xls, sheet_name=sheet_name, header=None)
        
        # Kết quả trích xuất
        result = {
            "file_name": file_name,
            "hop_dong_so": None,
            "service_groups": []
        }
        
        # 1. Tìm hợp đồng số (nếu có)
        try:
            for i in range(min(10, df.shape[0])):
                cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                if "HỢP ĐỒNG" in cell_value.upper():
                    hop_dong_match = re.search(r'HỢP ĐỒNG\s*(?:SỐ)?\s*[.:…]?\s*(\S+)', cell_value, re.IGNORECASE)
                    if hop_dong_match:
                        result["hop_dong_so"] = hop_dong_match.group(1)
                        logger.debug(f"Tìm thấy hợp đồng số: {result['hop_dong_so']}")
                        break
        except Exception as e:
            logger.warning(f"Lỗi khi tìm số hợp đồng: {str(e)}")
        
        # 2. Tìm các nhóm dịch vụ từ tiêu đề
        service_headers = []
        for i in range(df.shape[0]):
            cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
            
            # Tìm các tiêu đề nhóm dịch vụ
            service_type = None
            
            # Mẫu 1: Dịch vụ 1900xxxx hoặc I. Dịch vụ 1900xxxx
            if re.search(r'(I{1,3}\.?\s*Dịch\s*[Vv]ụ|Dịch\s*[Vv]ụ)\s*(1[89]00|109)', cell_value, re.IGNORECASE):
                if "1900" in cell_value:
                    service_type = "1900xxxx"
                elif "1800" in cell_value:
                    service_type = "1800xxxx"
                elif "109" in cell_value:
                    service_type = "109x"
            
            # Mẫu 2: TỪ MẠNG ... ĐẾN SỐ DỊCH VỤ 1900 CỦA ...
            elif "DỊCH VỤ 1900" in cell_value.upper() or "DỊCH VỤ 1800" in cell_value.upper():
                if "1900" in cell_value:
                    service_type = "1900xxxx"
                elif "1800" in cell_value:
                    service_type = "1800xxxx"
            
            # Mẫu 3: I. MBC sử dụng 1800 của HTC
            elif re.search(r'(I{1,3}\.?\s*.*(sử dụng|s[uư] d[uụ]ng).*1[89]00)', cell_value):
                if "1900" in cell_value:
                    service_type = "1900xxxx"
                elif "1800" in cell_value:
                    service_type = "1800xxxx"
            
            if service_type:
                service_headers.append({
                    "row": i,
                    "service_type": service_type,
                    "service_name": cell_value.strip(),
                    "source": "header"
                })
                logger.debug(f"Tìm thấy nhóm dịch vụ: {service_type} (dòng {i+1}): {cell_value.strip()}")
        
        # 3. Tìm các bảng dịch vụ
        tables = []
        for i in range(df.shape[0]):
            row_text = " ".join([str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else "" for j in range(min(5, df.shape[1]))])
            if "STT" in row_text.upper() and ("SỐ DỊCH VỤ" in row_text.upper() or "ĐẦU SỐ" in row_text.upper()):
                # Tìm cột chứa số dịch vụ
                service_col = None
                for j in range(min(10, df.shape[1])):
                    if j >= df.shape[1]:
                        continue
                        
                    header_text = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                    if "SỐ DỊCH VỤ" in header_text.upper() or "ĐẦU SỐ" in header_text.upper() or "MÃ DỊCH VỤ" in header_text.upper():
                        service_col = j
                        break
                
                if service_col is not None:
                    # Tìm dòng kết thúc bảng
                    table_end = None
                    for k in range(i + 1, min(i + 50, df.shape[0])):
                        if k >= df.shape[0]:
                            break
                            
                        cell_value = str(df.iloc[k, 0]) if not pd.isna(df.iloc[k, 0]) else ""
                        if "TỔNG CỘNG" in cell_value.upper() or "CỘNG" in cell_value.upper():
                            table_end = k
                            break
                    
                    if table_end is None:
                        table_end = min(i + 30, df.shape[0])
                    
                    # Xác định loại dịch vụ của bảng này
                    service_type = None
                    for k in range(i + 1, min(table_end, df.shape[0])):
                        if k >= df.shape[0] or service_col >= df.shape[1]:
                            continue
                            
                        cell_value = str(df.iloc[k, service_col]) if not pd.isna(df.iloc[k, service_col]) else ""
                        if "1900" in cell_value:
                            service_type = "1900xxxx"
                            break
                        elif "1800" in cell_value:
                            service_type = "1800xxxx"
                            break
                        elif "109" in cell_value:
                            service_type = "109x"
                            break
                    
                    if service_type:
                        tables.append({
                            "start_row": i,
                            "end_row": table_end,
                            "service_col": service_col,
                            "service_type": service_type
                        })
                        logger.debug(f"Tìm thấy bảng dịch vụ {service_type} từ dòng {i+1} đến dòng {table_end+1}")
        
        # 4. Kết hợp thông tin nhóm dịch vụ và bảng
        for header in service_headers:
            group_info = {
                "service_type": header["service_type"],
                "service_name": header["service_name"],
                "service_numbers": [],
                "raw_service_numbers": []
            }
            
            # Tìm bảng tương ứng với nhóm dịch vụ này
            matched_table = None
            for table in tables:
                if (table["service_type"] == header["service_type"] and 
                    abs(table["start_row"] - header["row"]) < 15):
                    matched_table = table
                    break
            
            # Dictionary để theo dõi các ô có thể là phần tiếp theo của ô trước đó
            multi_line_cells = {}
            
            if matched_table:
                # Trích xuất số dịch vụ từ bảng
                for i in range(matched_table["start_row"] + 1, matched_table["end_row"]):
                    if i >= df.shape[0] or matched_table["service_col"] >= df.shape[1]:
                        continue
                        
                    cell_value = str(df.iloc[i, matched_table["service_col"]]) if not pd.isna(df.iloc[i, matched_table["service_col"]]) else ""
                    cell_value = cell_value.strip()
                    
                    # Kiểm tra nếu ô có thể là phần tiếp theo của ô trước đó
                    prev_key = f"{i-1}_{matched_table['service_col']}"
                    if prev_key in multi_line_cells:
                        # Nối với ô trước đó
                        combined_value = multi_line_cells[prev_key] + " " + cell_value
                        cell_value = combined_value
                        
                        # Xóa khỏi danh sách theo dõi
                        del multi_line_cells[prev_key]
                    
                    # Kiểm tra nếu ô kết thúc bằng dấu ; hoặc ,
                    if cell_value.endswith(';') or cell_value.endswith(','):
                        multi_line_cells[f"{i}_{matched_table['service_col']}"] = cell_value
                    
                    if cell_value and (header["service_type"][:4].lower() in cell_value.lower() or
                                      (header["service_type"] == "109x" and "109" in cell_value)):
                        group_info["raw_service_numbers"].append({
                            "row": i,
                            "text": cell_value,
                            "has_tru": "trừ" in cell_value.lower()
                        })
                
                group_info["extraction_method"] = "table"
                group_info["table_range"] = f"{matched_table['start_row']+1}-{matched_table['end_row']}"
            else:
                # Tìm số dịch vụ trong các ô văn bản
                for i in range(header["row"] + 1, min(header["row"] + 20, df.shape[0])):
                    for j in range(min(5, df.shape[1])):
                        if j >= df.shape[1]:
                            continue
                            
                        cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                        cell_value = cell_value.strip()
                        
                        # Kiểm tra nếu ô có thể là phần tiếp theo của ô trước đó
                        prev_key = f"{i-1}_{j}"
                        if prev_key in multi_line_cells:
                            # Nối với ô trước đó
                            combined_value = multi_line_cells[prev_key] + " " + cell_value
                            cell_value = combined_value
                            
                            # Xóa khỏi danh sách theo dõi
                            del multi_line_cells[prev_key]
                        
                        # Kiểm tra nếu ô kết thúc bằng dấu ; hoặc ,
                        if cell_value.endswith(';') or cell_value.endswith(','):
                            multi_line_cells[f"{i}_{j}"] = cell_value
                        
                        if cell_value and (header["service_type"][:4].lower() in cell_value.lower() or 
                                          (header["service_type"] == "109x" and "109" in cell_value)):
                            # Kiểm tra thêm để loại bỏ các cụm từ không mong muốn
                            if is_valid_service_number(cell_value):
                                if cell_value not in [item["text"] for item in group_info["raw_service_numbers"]]:
                                    group_info["raw_service_numbers"].append({
                                        "row": i,
                                        "text": cell_value,
                                        "has_tru": "trừ" in cell_value.lower()
                                    })
                
                group_info["extraction_method"] = "text"
            
            # Chỉ thêm nhóm nếu có số dịch vụ
            if group_info["raw_service_numbers"]:
                # Cũng lưu lại danh sách các số dịch vụ dưới dạng chuỗi thông thường
                for item in group_info["raw_service_numbers"]:
                    group_info["service_numbers"].append(item["text"])
                
                result["service_groups"].append(group_info)
        
        # 5. Nếu không tìm thấy nhóm nào, quét toàn bộ file để tìm số dịch vụ
        if not result["service_groups"]:
            raw_service_numbers = []
            multi_line_cells = {}
            
            for i in range(df.shape[0]):
                for j in range(min(5, df.shape[1])):
                    if j >= df.shape[1]:
                        continue
                        
                    cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                    cell_value = cell_value.strip()
                    
                    # Bỏ qua nếu ô rỗng
                    if not cell_value:
                        continue
                    
                    # Kiểm tra nếu ô có thể là phần tiếp theo của ô trước đó
                    prev_key = f"{i-1}_{j}"
                    if prev_key in multi_line_cells:
                        # Nối với ô trước đó
                        combined_value = multi_line_cells[prev_key] + " " + cell_value
                        cell_value = combined_value
                        
                        # Xóa khỏi danh sách theo dõi
                        del multi_line_cells[prev_key]
                    
                    # Kiểm tra nếu ô kết thúc bằng dấu ; hoặc ,
                    if cell_value.endswith(';') or cell_value.endswith(','):
                        multi_line_cells[f"{i}_{j}"] = cell_value
                    
                    # Kiểm tra các trường hợp đặc biệt
                    if is_valid_service_number(cell_value):
                        # Xác định loại dịch vụ
                        service_type = None
                        if "1900" in cell_value:
                            service_type = "1900xxxx"
                        elif "1800" in cell_value:
                            service_type = "1800xxxx"
                        elif "109" in cell_value:
                            service_type = "109x"
                        
                        if service_type:
                            # Tìm xem đã có nhóm dịch vụ này chưa
                            group_found = False
                            for group in result["service_groups"]:
                                if group["service_type"] == service_type:
                                    # Thêm vào nhóm hiện có
                                    group["raw_service_numbers"].append({
                                        "row": i,
                                        "text": cell_value,
                                        "has_tru": "trừ" in cell_value.lower()
                                    })
                                    group["service_numbers"].append(cell_value)
                                    group_found = True
                                    break
                            
                            # Nếu chưa có nhóm, tạo nhóm mới
                            if not group_found:
                                group_info = {
                                    "service_type": service_type,
                                    "service_name": f"Dịch vụ {service_type}",
                                    "service_numbers": [cell_value],
                                    "raw_service_numbers": [{
                                        "row": i,
                                        "text": cell_value,
                                        "has_tru": "trừ" in cell_value.lower()
                                    }],
                                    "extraction_method": "scan"
                                }
                                result["service_groups"].append(group_info)
        
        # 6. Tìm thông tin thanh toán
        result["thanh_toan"] = {"found": False, "has_bu_tru": False}
        
        for i in range(df.shape[0]):
            cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
            if "THANH TOÁN" in cell_value.upper() or "III" in cell_value.upper() and "THANH TOÁN" in cell_value.upper():
                result["thanh_toan"]["found"] = True
                logger.debug(f"Tìm thấy phần thanh toán (dòng {i+1})")
                break
        
        # Tìm thông tin sau bù trừ
        for i in range(df.shape[0]):
            row_text = " ".join([str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else "" for j in range(min(5, df.shape[1]))])
            if "SAU BÙ TRỪ" in row_text.upper() and "PHẢI THANH TOÁN CHO" in row_text.upper():
                result["thanh_toan"]["has_bu_tru"] = True
                logger.debug(f"Tìm thấy thông tin sau bù trừ (dòng {i+1})")
                break
        
        return result
        
    except Exception as e:
        logger.error(f"Lỗi khi phân tích file {file_path}: {str(e)}")
        return {
            "file_name": os.path.basename(file_path),
            "error": str(e),
            "service_groups": []
        } 