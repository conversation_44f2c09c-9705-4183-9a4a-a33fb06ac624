import pandas as pd
import re
import os
import csv
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import logging
from ..schemas.dscd import DoiSoatCoDinh, DauSoDichVu, <PERSON>uo<PERSON><PERSON><PERSON>, <PERSON>uocThueBao, TongKet
from ..models.enums import DauSoType
from .enhanced_phone_utils import normalize_phone_number_enhanced

# Cấu hình logging
logger = logging.getLogger(__name__)

# Các mẫu regex để nhận dạng file DSCD
DSCD_PATTERNS = [
    r'(ĐS\s*CĐ|ĐS\s*CỐ\s*ĐỊNH|DS\s*cố\s*định|Đối\s*soát\s*cước\s*cố\s*định|Doi\s*soat\s*so\s*co\s*dinh).+\.(xlsx|xls)$',
    r'(DS\s*cuoc\s*co\s*dinh).+\.(xlsx|xls)$',
    r'(BIDV|Doctor-check|Shield).+\.(xlsx|xls)$'
]

# Từ khóa tìm kiếm cột chứa số điện thoại
PHONE_COLUMN_KEYWORDS = [
    "ĐẦU SỐ", "DAU SO", "SỐ THUÊ BAO", "SO THUE BAO", "THUÊ BAO", "THUE BAO", 
    "SỐ ĐIỆN THOẠI", "SO DIEN THOAI", "ĐIỆN THOẠI", "DIEN THOAI"
]

# Ánh xạ vị trí cột trong Excel
EXCEL_COLUMN_MAPPING = {
    "stt": "A",
    "dau_so": "B",
    "co_dinh_noi_hat_thoi_gian": "C",
    "co_dinh_noi_hat_cuoc": "D",
    "co_dinh_lien_tinh_thoi_gian": "E",
    "co_dinh_lien_tinh_cuoc": "F",
    "di_dong_thoi_gian": "G",
    "di_dong_cuoc": "H",
    "cuoc_1900_thoi_gian": "I",
    "cuoc_1900_cuoc": "J",
    "quoc_te_thoi_gian": "K",
    "quoc_te_cuoc": "L",
    "thue_bao_thang": "M",
    "cam_ket_thang": "N",
    "tra_truoc_thang": "O",
    "cuoc_thu_khach": "P",
    "cuoc_tra_htc": "Q"
}

# Hàng chứa tiêu đề bảng
HEADER_ROW = 6
# Hàng bắt đầu dữ liệu
DATA_START_ROW = 8
# Các hàng tổng kết
SUMMARY_ROWS = {
    "cong_tien_dich_vu": 19,
    "tien_thue_gtgt": 20,
    "tong_cong_tien": 21
}


def is_dscd_file(filename: str) -> bool:
    """
    Kiểm tra xem file có phải là file đối soát cố định không
    
    Args:
        filename: Tên file cần kiểm tra
        
    Returns:
        True nếu là file đối soát cố định, False nếu không phải
    """
    filename = os.path.basename(filename)
    for pattern in DSCD_PATTERNS:
        if re.search(pattern, filename, re.IGNORECASE):
            return True
    return False


def extract_month_year(filename: str) -> Tuple[Optional[int], Optional[int]]:
    """
    Trích xuất tháng và năm từ tên file
    
    Args:
        filename: Tên file đối soát cố định
        
    Returns:
        Tuple chứa (tháng, năm) hoặc (None, None) nếu không trích xuất được
    """
    filename = os.path.basename(filename)
    
    # Mẫu 1: T1_2025, Tháng 1-2025, Tháng 1_2025
    pattern1 = r'(T|Th[áa]ng)\s*(\d{1,2})[-_\.\s](\d{4})'
    match = re.search(pattern1, filename, re.IGNORECASE)
    if match:
        return int(match.group(2)), int(match.group(3))
    
    # Mẫu 2: 012025, 202501 (năm tháng hoặc tháng năm)
    pattern2 = r'(\d{2})(\d{4})'
    match = re.search(pattern2, filename)
    if match:
        if int(match.group(1)) <= 12:
            return int(match.group(1)), int(match.group(2))
        else:
            # Định dạng năm trước tháng sau
            year_str = match.group(1) + match.group(2)[:2]
            month_str = match.group(2)[2:]
            if int(month_str) <= 12:
                return int(month_str), int(year_str)
    
    # Mẫu 3: thang 1_2025
    pattern3 = r'thang\s*(\d{1,2})[-_\.\s](\d{4})'
    match = re.search(pattern3, filename, re.IGNORECASE)
    if match:
        return int(match.group(1)), int(match.group(2))
    
    return None, None


def parse_and_standardize_dau_so(raw_dau_so: str) -> Dict[str, Any]:
    """Phân tích và chuẩn hóa chuỗi đầu số."""
    logger.debug(f"--- Enter parse_and_standardize_dau_so with raw_dau_so: '{raw_dau_so}' (type: {type(raw_dau_so)}) ---")
    if not isinstance(raw_dau_so, str):
        logger.debug(f"Input is not string, converting to string.")
        raw_dau_so = str(raw_dau_so)
        
    # Giá trị trả về mặc định nếu không khớp pattern nào
    default_result = {
        "standardized_display": raw_dau_so.strip(), # Start with original stripped
        "type": DauSoType.UNKNOWN,
        "count": None,
        "start_num_str": None,
        "end_num_str": None,
        "prefix": None
    }
    
    cleaned = re.sub(r'[^\d.+\-()]', '', raw_dau_so.strip()) # Keep +, -, () for patterns
    logger.debug(f"Cleaned string (keeping +, -, ()): '{cleaned}'")
    if not cleaned:
        default_result["standardized_display"] = ""
        logger.debug(f"Returning default for empty cleaned string: {default_result}")
        return default_result
        
    # 1. Kiểm tra tiền tố '...'
    # Note: Prefix normalization happens *after* matching pattern
    logger.debug(f"Checking for PREFIX ('...')")
    # Use the initial 'cleaned' string which might contain non-digits relevant to prefix
    if cleaned.endswith('...'):
        prefix_val = cleaned[:-3]
        # Ensure the part before '...' is digits before normalizing
        if re.match(r'^\d+$', prefix_val): 
            normalized_prefix = normalize_phone_number_enhanced(prefix_val)
            result = {
                "standardized_display": f"{normalized_prefix}...",
                "type": DauSoType.PREFIX,
                "count": None,
                "start_num_str": None,
                "end_num_str": None,
                "prefix": normalized_prefix # Use normalized prefix
            }
            logger.info(f"Matched PREFIX: Original prefix '{prefix_val}' -> Normalized: '{normalized_prefix}'. Returning: {result}")
            return result
        else:
             logger.debug(f"Ends with '...' but prefix part '{prefix_val}' contains non-digits.")
            
    # --- START CHANGE: Prepare cleaned string specifically for RANGE checks ---
    cleaned_for_range = re.sub(r'[^\d-]', '', cleaned) # Keep only digits and hyphen
    logger.debug(f"String cleaned for RANGE matching: '{cleaned_for_range}'")
    # --- END CHANGE ---
    
    # 2. Kiểm tra dải số có count trong ngoặc: "START-END(COUNT)"
    logger.debug(f"Checking for RANGE_WITH_COUNT pattern r'^(\d+)-(\d+)\((\d+)\)$' on '{cleaned_for_range}'") # Use cleaned_for_range
    # Need to check the original 'cleaned' string for the parentheses part
    count_part_match = re.search(r'\((\d+)\)$', cleaned) # Find count in original cleaned string
    range_part_match = re.match(r'^(\d+)-(\d+)', cleaned_for_range) # Match range part on range-cleaned string
    
    if range_part_match and count_part_match:
        start_str_val, end_str_val = range_part_match.groups()
        count_str = count_part_match.group(1)
        logger.debug(f"Pattern RANGE_WITH_COUNT components matched. Range: start='{start_str_val}', end='{end_str_val}'. Count: '{count_str}'")
        try:
            count = int(count_str)
            # Normalize start/end from matched range part
            normalized_start = normalize_phone_number_enhanced(start_str_val)
            normalized_end = normalize_phone_number_enhanced(end_str_val)
            standard_display = f"{normalized_start}-{normalized_end}"
            dau_so_type = DauSoType.RANGE_OTHER
            if count == 10:
                dau_so_type = DauSoType.RANGE_10
            elif count == 100:
                dau_so_type = DauSoType.RANGE_100
            
            result = {
                "standardized_display": standard_display, 
                "type": dau_so_type,
                "count": count,
                "start_num_str": normalized_start, 
                "end_num_str": normalized_end,     
                "prefix": None
            }
            logger.info(f"Matched {dau_so_type.name}. Returning: {result}")
            return result
        except ValueError:
            logger.warning(f"Pattern RANGE_WITH_COUNT matched, but count '{count_str}' is not a valid integer. Continuing...")
            pass 
    else:
        logger.debug(f"Pattern RANGE_WITH_COUNT did not match components.")
            
    # 3. Kiểm tra dải số không có count: "START-END"
    logger.debug(f"Checking for RANGE pattern r'^(\d+)-(\d+)$'. on '{cleaned_for_range}'") # Use cleaned_for_range
    range_match = re.match(r'^(\d+)-(\d+)$', cleaned_for_range)
    if range_match:
        start_str_val, end_str_val = range_match.groups()
        logger.debug(f"Pattern RANGE matched. Groups: start='{start_str_val}', end='{end_str_val}'")
        # Normalize start/end
        normalized_start = normalize_phone_number_enhanced(start_str_val)
        normalized_end = normalize_phone_number_enhanced(end_str_val)

        if normalized_start.isdigit() and normalized_end.isdigit():
            logger.debug(f"Normalized Start/End are digits: '{normalized_start}', '{normalized_end}'")
            try:
                start_num = int(normalized_start)
                end_num = int(normalized_end)
                if end_num >= start_num:
                    count = end_num - start_num + 1
                    dau_so_type = DauSoType.RANGE_OTHER
                    if count == 10:
                        dau_so_type = DauSoType.RANGE_10
                    elif count == 100:
                        dau_so_type = DauSoType.RANGE_100
                    
                    result = {
                        "standardized_display": f"{normalized_start}-{normalized_end}",
                        "type": dau_so_type,
                        "count": count,
                        "start_num_str": normalized_start, 
                        "end_num_str": normalized_end,     
                        "prefix": None
                    }
                    logger.info(f"Matched {dau_so_type.name}. Returning: {result}")
                    return result
                else:
                     logger.warning(f"Pattern RANGE matched, but normalized end_num {end_num} < start_num {start_num}. Invalid range. Continuing...")
            except ValueError:
                logger.warning(f"Pattern RANGE matched, but could not convert normalized start/end to int. Start: '{normalized_start}', End: '{normalized_end}'. Continuing...")
                pass 
        else:
             logger.debug(f"Pattern RANGE matched, but normalized start/end are not digits. Start: '{normalized_start}', End: '{normalized_end}'. Continuing...")
    else:
        logger.debug(f"Pattern RANGE did not match.")
                
    # --- START CHANGE: Prepare cleaned string specifically for SINGLE check ---
    digits_only = re.sub(r'[^\d]', '', cleaned) # Remove all non-digits
    logger.debug(f"String cleaned for SINGLE matching (digits_only): '{digits_only}'")
    # --- END CHANGE ---
    
    # 4. Kiểm tra số đơn lẻ
    # Use the digits_only string for the check
    logger.debug(f"Checking for SINGLE pattern (regex ^\d+$) on digits_only: '{digits_only}'")
    if digits_only: # Check if not empty after removing non-digits
        # It is a single number (or multiple numbers concatenated, which is unlikely if range didn't match)
        # We should probably use the original 'cleaned' string here if we want to preserve original non-range format?
        # Let's stick to normalizing the digits_only version for consistency for now.
        normalized_single = normalize_phone_number_enhanced(digits_only)
        result = {
            "standardized_display": normalized_single, 
            "type": DauSoType.SINGLE,
            "count": 1,
            "start_num_str": None,
            "end_num_str": None,
            "prefix": None
        }
        logger.info(f"Matched SINGLE. Original digits: '{digits_only}' -> Normalized: '{normalized_single}'. Returning: {result}")
        return result
    else:
        logger.debug(f"Did not match SINGLE pattern (digits_only is empty).")
        
    # 5. Nếu không khớp các trường hợp trên, trả về UNKNOWN
    # Apply normalization to the original stripped display as a last resort
    normalized_unknown = normalize_phone_number_enhanced(default_result["standardized_display"])
    default_result["standardized_display"] = normalized_unknown
    logger.warning(f"No specific pattern matched. Returning UNKNOWN with normalized display: {default_result}")
    return default_result


def extract_partner_name(filename: str) -> Optional[str]:
    """
    Trích xuất tên đối tác từ tên file
    
    Args:
        filename: Tên file đối soát cố định
        
    Returns:
        Tên đối tác hoặc None nếu không trích xuất được
    """
    filename = os.path.basename(filename)
    
    # Trường hợp đặc biệt
    special_cases = {
        "THÁI BÌNH DƯƠNG": "THÁI BÌNH DƯƠNG",
        "Realtime": "Realtime",
        "BIDV": "BIDV HÀ TÂY",
        "Doctor-check": "Doctor-check",
        "Shield VN": "Shield VN",
        "HITC": "HITC"
    }
    
    # Kiểm tra các trường hợp đặc biệt
    for key, value in special_cases.items():
        # --- Thay đổi: Sử dụng regex để kiểm tra key linh hoạt hơn ---
        # Tạo pattern regex từ key, thay thế khoảng trắng bằng pattern linh hoạt (khoảng trắng hoặc _)
        # Thêm \b để đảm bảo khớp toàn bộ từ (word boundary)
        # --- START FIX: Use manual split/join instead of re.sub for replacement ---
        key_parts = re.split(r'\s+', key) # Split key by whitespace
        replacement_pattern = r'[\s_]+'   # Pattern to match whitespace or underscore
        joined_key_pattern = replacement_pattern.join(key_parts) # Join parts with the pattern
        pattern_key = r'\b' + joined_key_pattern + r'\b' # Add word boundaries
        # --- END FIX ---
        # pattern_key = r'\b' + re.sub(r'\s+', r'[\s_]+', key) + r'\b' # OLD problematic line
        try:
            if re.search(pattern_key, filename, re.IGNORECASE):
                logger.info(f"Matched special case partner: {value} using regex pattern: '{pattern_key}'")
                return value
        except Exception as regex_err:
             logger.warning(f"Regex error while checking special case key '{key}' with pattern '{pattern_key}': {regex_err}")
        # --- Kết thúc thay đổi ---

    # Mẫu: ĐS CĐ_PARTNER_Tháng, DS cố định PARTNER_T
    patterns = [
        r'ĐS\s*CĐ_([A-Za-z0-9\s]+)_',
        r'DS\s*cố\s*định\s+([A-Za-z0-9\s]+)_',
        r'Đối\s*soát\s*cước\s*cố\s*định\s+HTC\s*-\s*([A-Za-z0-9\s]+)\s+',
        r'Doi\s*soat\s*so\s*co\s*dinh\s+([A-Za-z0-9\s]+)_',
        r'DS\s*cuoc\s*co\s*dinh\s+([A-Za-z0-9\s]+)\s+thang'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename, re.IGNORECASE)
        if match:
            return match.group(1).strip()
    
    return None


def normalize_phone_number(phone: str) -> str:
    """
    Chuẩn hóa số điện thoại, loại bỏ kí tự không phải số
    
    Args:
        phone: Số điện thoại cần chuẩn hóa
        
    Returns:
        Số điện thoại đã chuẩn hóa
    """
    if not phone:
        return ""
    return re.sub(r'[^\d]', '', str(phone))


def extract_phone_numbers_from_excel(df: pd.DataFrame) -> List[str]:
    """
    Trích xuất danh sách số điện thoại từ DataFrame Excel
    
    Args:
        df: DataFrame từ file Excel
        
    Returns:
        Danh sách các số điện thoại
    """
    try:
        # Tìm cột chứa số điện thoại
        column_names = df.columns.tolist()
        
        # Tìm cột chứa số điện thoại dựa trên từ khóa
        dau_so_column = None
        for col in column_names:
            col_upper = str(col).upper()
            if any(keyword in col_upper for keyword in PHONE_COLUMN_KEYWORDS):
                dau_so_column = col
                break
                
        # Nếu không tìm thấy bằng tên cột, kiểm tra dữ liệu
        if dau_so_column is None:
            
            # Kiểm tra dữ liệu mẫu từ mỗi cột
            for col in df.columns:
                # Lấy giá trị không phải NaN để kiểm tra
                sample_values = df[col].dropna().astype(str).head(20).tolist()
                
                # Kiểm tra xem các giá trị có phù hợp với mẫu số điện thoại không
                phone_pattern_matches = sum(1 for val in sample_values 
                                          if re.search(r'(\d{2,}\.|\d{3,}[-\s]|\d{8,})', str(val)))
                
                # Nếu có ít nhất 50% giá trị khớp với mẫu số điện thoại
                if phone_pattern_matches >= min(1, len(sample_values) / 2):
                    dau_so_column = col
                    break
         
        # Nếu vẫn không tìm thấy, dùng cột "dau_so" (thường là cột thứ 2)
        if dau_so_column is None and len(column_names) >= 2:
            dau_so_column = column_names[1]  # Lấy cột thứ 2 nếu không tìm thấy
        
        if dau_so_column is None:
            return []
        
        # Lấy và lọc danh sách số điện thoại
        raw_values = df[dau_so_column].astype(str).tolist()
        phone_numbers = []
        
        for val in raw_values:
            val = str(val).strip()
            # Bỏ qua giá trị nan, None, chuỗi rỗng, và giá trị không phải số điện thoại
            if val and val.lower() not in ('nan', 'none', '', 'đầu số dịch vụ') and re.search(r'\d{3,}', val):
                # Kiểm tra xem có nhiều dòng trong một ô không
                if "\n" in val:
                    lines = val.split("\n")
                    for line in lines:
                        line = line.strip()
                        if line and re.search(r'\d{3,}', line):
                            phone_numbers.append(line)
                else:
                    phone_numbers.append(val)
        
        return phone_numbers
    
    except Exception as e:
        logger.error(f"Lỗi khi trích xuất số điện thoại: {str(e)}")
        return []


def filter_phone_numbers(phones: List[str], prefix: Optional[str] = None) -> List[str]:
    """
    Lọc số điện thoại theo tiền tố
    
    Args:
        phones: Danh sách số điện thoại cần lọc
        prefix: Tiền tố để lọc (nếu có)
        
    Returns:
        Danh sách số điện thoại đã chuẩn hóa và lọc
    """
    normalized_phones = [normalize_phone_number(phone) for phone in phones]
    
    if prefix:
        return [phone for phone in normalized_phones if phone.startswith(prefix)]
    
    return normalized_phones


def analyze_phone_numbers(phones: List[str]) -> Dict[str, Any]:
    """
    Phân tích số điện thoại và tạo thống kê
    
    Args:
        phones: Danh sách số điện thoại đã chuẩn hóa
        
    Returns:
        Dictionary chứa thống kê về số điện thoại
    """
    result = {
        "total": len(phones),
        "by_prefix": {},
        "by_length": {}
    }
    
    # Thống kê theo tiền tố
    for phone in phones:
        if len(phone) >= 3:
            prefix = phone[:3]  # Lấy 3 số đầu tiên
            result["by_prefix"][prefix] = result["by_prefix"].get(prefix, 0) + 1
    
    # Thống kê theo độ dài
    for phone in phones:
        length = len(phone)
        result["by_length"][length] = result["by_length"].get(length, 0) + 1
    
    return result


def save_phone_numbers(phones: List[str], original_phones: List[str], output_dir: str, base_filename: str = "extracted_phones") -> Dict[str, str]:
    """
    Lưu số điện thoại vào file
    
    Args:
        phones: Danh sách số điện thoại đã chuẩn hóa
        original_phones: Danh sách số điện thoại gốc
        output_dir: Thư mục đầu ra
        base_filename: Tên file cơ sở
        
    Returns:
        Dictionary chứa đường dẫn đến các file đã tạo
    """
    os.makedirs(output_dir, exist_ok=True)
    
    files = {}
    
    # Lưu file text
    txt_output_file = os.path.join(output_dir, f"{base_filename}.txt")
    with open(txt_output_file, 'w', encoding='utf-8') as f:
        for phone in phones:
            f.write(f"{phone}\n")
    files["txt"] = txt_output_file
    
    # Lưu file CSV với thông tin chi tiết
    csv_output_file = os.path.join(output_dir, f"{base_filename}.csv")
    with open(csv_output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        # Viết header
        writer.writerow(['Số gốc', 'Số chuẩn hóa', 'Tiền tố', 'Độ dài'])
        
        # Viết dữ liệu
        for i, normalized in enumerate(phones):
            original = original_phones[i] if i < len(original_phones) else ''
            prefix = normalized[:3] if len(normalized) >= 3 else ''
            writer.writerow([original, normalized, prefix, len(normalized)])
    files["csv"] = csv_output_file
    
    return files


def process_dscd_file(file_path: str, partner_id: Optional[int] = None) -> DoiSoatCoDinh:
    """
    Xử lý file đối soát cố định và chuyển đổi thành model DoiSoatCoDinh
    (Chế độ xử lý mẫu: chỉ lấy metadata từ nội dung và cấu trúc, số liệu = 0)
    
    Args:
        file_path: Đường dẫn đến file Excel đối soát cố định
        partner_id: ID của đối tác liên quan (tùy chọn)
        
    Returns:
        Model DoiSoatCoDinh chứa dữ liệu đã xử lý
    """
    try:
        # Đọc file Excel
        file_name = os.path.basename(file_path)
        xls = pd.ExcelFile(file_path)
        
        # Mặc định lấy sheet đầu tiên
        sheet_name = xls.sheet_names[0]
        # Đọc không dùng header để dễ dàng quét các dòng đầu
        df = pd.read_excel(xls, sheet_name=sheet_name, header=None)
        
        # --- Bắt đầu thay đổi: Trích xuất metadata (Partner từ tên file, HĐ từ nội dung) ---
        thang_doi_soat = None # Không trích xuất tháng/năm từ mẫu
        nam_doi_soat = None
        # Lấy đối tác từ tên file bằng hàm đã có
        den_doi_tac = extract_partner_name(file_name) 
        tu_mang = "HTC" # Mặc định
        hop_dong_so = None
        
        # Quét các dòng đầu để tìm hợp đồng số
        max_header_rows_to_scan = 10 # Quét tối đa 10 dòng đầu
        for i in range(min(max_header_rows_to_scan, df.shape[0])):
            try:
                # Đảm bảo đọc giá trị ô dưới dạng string, nối các ô trên cùng 1 dòng
                row_str = ' '.join(str(df.iloc[i, col]) for col in range(df.shape[1]) if not pd.isna(df.iloc[i, col])).upper()
                
                # Tìm hợp đồng số
                hop_dong_match = re.search(r'HỢP\s*ĐỒNG\s*SỐ\s*[.:…]?\s*(\S+)', row_str)
                if hop_dong_match:
                    hop_dong_so = hop_dong_match.group(1)
                    break # Dừng khi tìm thấy hợp đồng

            except Exception as scan_err:
                logger.warning(f"Lỗi khi quét dòng header {i} để tìm hợp đồng số: {scan_err}")

        # Gán giá trị mặc định nếu không tìm thấy đối tác từ tên file
        den_doi_tac = den_doi_tac or "KHÔNG XÁC ĐỊNH" # Mặc định
        
        logger.info(f"DSCD Metadata extracted: tu_mang='{tu_mang}' (Default), den_doi_tac='{den_doi_tac}' (from Filename), hop_dong_so='{hop_dong_so}' (from Content)")
        # --- Kết thúc thay đổi: Trích xuất metadata ---

        # --- Bắt đầu thay đổi: Tìm dòng header và cột đầu số động --- 
        header_row_index_found = -1
        dau_so_column_index = 1 # Mặc định cột thứ 2 (index 1)
        column_names = [] # Danh sách tên cột (hoặc index nếu không có tên)

        # Quét các dòng đầu để tìm dòng header thực sự
        max_header_search_rows = 15
        found_header = False
        for i in range(min(max_header_search_rows, df.shape[0])):
            try:
                row_values = [str(df.iloc[i, col]).upper() for col in range(df.shape[1]) if not pd.isna(df.iloc[i, col])]
                row_str_upper = ' '.join(row_values)
                
                # Điều kiện tìm header: chứa 'STT' và một keyword của cột số điện thoại
                has_stt = "STT" in row_values or "STT" in row_str_upper 
                has_phone_keyword = any(keyword in row_str_upper for keyword in PHONE_COLUMN_KEYWORDS)
                
                if has_stt and has_phone_keyword:
                    header_row_index_found = i
                    logger.info(f"Tìm thấy dòng header chính tại index: {header_row_index_found}")
                    
                    # Lấy tên cột từ dòng header này
                    column_names = [str(df.iloc[header_row_index_found, col]) for col in range(df.shape[1])] 
                    logger.debug(f"Header columns found: {column_names}")
                    
                    # Tìm index cột đầu số dựa trên header vừa tìm được
                    for idx, col_name in enumerate(column_names):
                        col_upper = col_name.upper()
                        if any(keyword in col_upper for keyword in PHONE_COLUMN_KEYWORDS):
                            dau_so_column_index = idx
                            logger.info(f"Tìm thấy cột đầu số '{col_name}' tại index {dau_so_column_index} dựa trên header động")
                            break # Dừng khi tìm thấy cột đầu số
                    found_header = True
                    break # Dừng quét header
            except Exception as header_scan_err:
                 logger.warning(f"Lỗi khi quét dòng {i} tìm header: {header_scan_err}")

        if not found_header:
            logger.warning("Không tìm thấy dòng header chính chứa STT và keyword cột đầu số. Sẽ sử dụng index mặc định cho cột đầu số (1). Cần kiểm tra lại logic tìm header hoặc file mẫu.")
            # Vẫn dùng dau_so_column_index = 1 (mặc định)
            # Cần xác định dòng bắt đầu dữ liệu một cách khác hoặc báo lỗi
            # Tạm thời vẫn dùng giá trị cũ + 1 nếu có, nếu không thì mặc định
            header_row_index_found = 5 # Giả định header ở index 5 nếu không tìm thấy
            column_names = list(range(df.shape[1])) # Dùng index nếu không có header

        # Xác định dòng bắt đầu dữ liệu là dòng ngay sau header
        actual_data_start_row = header_row_index_found + 1
        logger.info(f"Xác định dòng bắt đầu dữ liệu tại index: {actual_data_start_row}")
        # --- Kết thúc thay đổi: Tìm dòng header và cột đầu số động ---
        
        # Bỏ phần đọc lại file Excel vì đã tìm header và cột trên df gốc
        # try:
        #     # Xác định dòng header thực sự (ví dụ: dòng thứ 6 trong file gốc là index 5)
        #     header_actual_row_index = 5 
        #     df_with_header = pd.read_excel(xls, sheet_name=sheet_name, header=header_actual_row_index)
        #     column_names = df_with_header.columns.tolist()
        #     logger.debug(f"Các cột trong file (đọc lại với header): {column_names}")
        # except Exception as e:
        #      logger.error(f"Không thể đọc lại file với header tại dòng {header_actual_row_index+1}: {e}. Sẽ sử dụng chỉ số cột.")
        #      column_names = list(range(df.shape[1])) # Dùng chỉ số nếu lỗi
        #      df_with_header = df # Dùng df gốc

        # Tìm cột chứa số điện thoại dựa trên từ khóa trên tên cột (đã làm ở trên)
        # dau_so_column = None
        # for idx, col in enumerate(column_names):
        #     col_upper = str(col).upper()
        #     if any(keyword in col_upper for keyword in PHONE_COLUMN_KEYWORDS):
        #         dau_so_column = col
        #         dau_so_column_index = idx # Lấy index của cột tìm được
        #         logger.info(f"Tìm thấy cột chứa số điện thoại theo tên: '{col}' tại index {idx}")
        #         break
        # if dau_so_column is None:
        #     logger.info(f"Không tìm thấy cột số điện thoại theo tên, sử dụng cột mặc định index {dau_so_column_index}")
        
        # Xử lý dữ liệu chính (đọc từ df gốc không có header)
        du_lieu = []
        try:
            # --- START: Change iteration method from while to for loop --- 
            logger.info(f"Entering FOR loop: Start Index={actual_data_start_row}, End Index (estimated)={df.shape[0]-1}")
            
            processed_rows_count = 0 # Đếm số dòng đã xử lý thành công
            max_rows_to_process = 500 # Giới hạn số dòng tối đa để tránh vòng lặp vô hạn
            stt_counter = 1 # Khởi tạo biến đếm STT riêng biệt
            
            # Iterate using df.iterrows() starting from the identified data row
            for row_idx_from_iter, row in df.iloc[actual_data_start_row:].iterrows():
                row_idx = row_idx_from_iter 
                # logger.debug(f"--- Processing row index: {row_idx} --- INSIDE FOR LOOP ENTRY --- ") # REMOVED DEBUG

                # Check if we exceeded the max rows limit
                if processed_rows_count >= max_rows_to_process:
                    logger.warning(f"Reached max_rows_to_process ({max_rows_to_process}), stopping data processing.")
                    break

                # --- Read first cell (STT) using the row Series --- 
                cell_value_col0 = None
                try: 
                    # logger.debug(f"Row {row_idx}: BEFORE reading cell [*, 0] from row Series") # REMOVED DEBUG
                    raw_cell_value = row[0] 
                    # logger.debug(f"Row {row_idx}: AFTER reading cell [*, 0], raw_value_type={type(raw_cell_value)}") # REMOVED DEBUG
                    
                    # logger.debug(f"Row {row_idx}: BEFORE converting cell [*, 0] to string") # REMOVED DEBUG
                    cell_value_col0 = str(raw_cell_value) if not pd.isna(raw_cell_value) else ""
                    # logger.debug(f"Row {row_idx}: AFTER converting cell [*, 0] to string. Value: '{cell_value_col0}'") # REMOVED DEBUG
                    
                except Exception as read_err_col0:
                    logger.error(f"Row {row_idx}: Error reading/converting cell [*, 0] from row Series: {read_err_col0}")
                    processed_rows_count += 1 
                    continue 
                # --- End reading first cell ---\
                    
                is_summary_row = False 
                # Logic to detect summary row based on cell_value_col0
                if pd.isna(row[0]): # Check original NaN status from row Series
                    # --- START CHANGE: Check if THE ENTIRE ROW is null ---
                    # Old logic: is_likely_empty = all(pd.isna(row[c]) for c in range(1, min(5, len(row))))
                    is_entirely_empty = row.isnull().all() 
                    logger.debug(f"Row {row_idx}: First cell is NaN. Checking if entire row is empty: {is_entirely_empty}")
                    if is_entirely_empty:
                        logger.info(f"Row {row_idx}: Determined to be entirely empty based on all cells being NaN. Stopping data processing.")
                        # --- END CHANGE ---
                        is_summary_row = True
                    else:
                        logger.debug(f"Row {row_idx}: First cell is NaN, but other cells have data. Continuing processing.")
                elif cell_value_col0 and any(keyword in cell_value_col0.upper() for keyword in ["CỘNG TIỀN", "TỔNG CỘNG", "TỔNG", "CỘNG"]):
                    logger.info(f"Phát hiện dòng tổng kết (chứa text '{cell_value_col0}') tại index {row_idx}")
                    is_summary_row = True
                
                if is_summary_row:
                    logger.debug(f"Row {row_idx}: Condition is_summary_row is True. Breaking loop.") # Added log before break
                    break 

                # --- Process data row using row Series --- 
                try:
                    stt_raw = row[0]
                    # --- START CHANGE: Handle non-numeric STT ---
                    try:
                        stt = int(stt_raw) if not pd.isna(stt_raw) else 0
                    except ValueError:
                        logger.warning(f"Row {row_idx}: Could not parse STT value '{stt_raw}' as integer. Using internal counter value {stt_counter} instead.")
                        stt = stt_counter # Use the internal counter as fallback
                    # --- END CHANGE ---
                    
                    # logger.debug(f"Row {row_idx}: Reading DauSo from cell [*, {dau_so_column_index}] using row Series") # REMOVED DEBUG
                    dau_so_raw = str(row[dau_so_column_index]) if not pd.isna(row[dau_so_column_index]) else ""
                    # logger.debug(f"Row {row_idx}: Raw STT='{stt_raw}', Raw DauSo (Col {dau_so_column_index})='{dau_so_raw}' --- READ DONE") # REMOVED DEBUG
                    
                    dau_so_clean = dau_so_raw.strip()
                    # logger.debug(f"Row {row_idx}: Cleaned DauSo='{dau_so_clean}'") # REMOVED DEBUG
                    
                    # --- START CHANGE: Remove the is_valid_phone_format check ---
                    # is_valid_phone_format = bool(re.match(r'\d{3,}', dau_so_clean))
                    # logger.debug(f"Row {row_idx}: Is valid phone format (regex \d{{3,}})? {is_valid_phone_format}") # REMOVED DEBUG

                    # if not is_valid_phone_format:
                    #     # logger.debug(f"Bỏ qua dòng {row_idx} do giá trị ở cột đầu số ('{dau_so_clean}') không khớp regex \d{{3,}}") # REMOVED DEBUG
                    #     processed_rows_count += 1 
                    #     continue
                    # --- END CHANGE ---
                    
                    phone_lines_in_cell = []
                    # Always try to split, even if dau_so_clean might not seem valid initially
                    if "\n" in dau_so_clean:
                        # Split and basic filter (non-empty after strip)
                        phone_lines_in_cell = [line.strip() for line in dau_so_clean.split("\n") if line.strip()]
                    elif dau_so_clean: # If not empty and no newline, treat as single line
                         phone_lines_in_cell.append(dau_so_clean)

                    if phone_lines_in_cell:
                        # --- START CHANGE: Add detailed logging inside the loop ---
                        logger.debug(f"Row {row_idx}: Found {len(phone_lines_in_cell)} potential phone line(s) in the cell. Processing each...")
                        for line_idx, phone_line in enumerate(phone_lines_in_cell):
                            logger.debug(f"  Processing line {line_idx + 1}/{len(phone_lines_in_cell)}: '{phone_line}'")
                            # logger.debug(f"Row {row_idx}: Calling parse_and_standardize_dau_so for '{phone_line}'") # REMOVED DEBUG
                            parsed_info = parse_and_standardize_dau_so(phone_line)
                            logger.debug(f"    Parse result: {parsed_info}")
                            
                            # --- START CHANGE: Check parse result type before appending ---
                            if parsed_info["type"] is DauSoType.UNKNOWN:
                                logger.warning(f"Row {row_idx}, Line '{phone_line}': Could not parse as a valid DauSo type (Result: UNKNOWN). Skipping this line.")
                                continue # Skip this phone_line if it's unknown
                            # --- END CHANGE ---
                            
                            # logger.debug(f"Row {row_idx}: parse_and_standardize_dau_so returned: {parsed_info}") # REMOVED DEBUG
                            
                            # Create new CuocGoi/CuocThueBao objects *for each line*
                            co_dinh_noi_hat = CuocGoi()
                            co_dinh_lien_tinh = CuocGoi()
                            di_dong = CuocGoi()
                            cuoc_1900 = CuocGoi()
                            quoc_te = CuocGoi()
                            cuoc_thue_bao = CuocThueBao()
                            
                            row_data = DauSoDichVu(
                                stt=stt_counter, # Sử dụng biến đếm riêng
                                raw_dau_so=phone_line, 
                                standardized_display=parsed_info["standardized_display"],
                                dau_so_type=parsed_info["type"],
                                number_count=parsed_info["count"],
                                start_num_str=parsed_info["start_num_str"],
                                end_num_str=parsed_info["end_num_str"],
                                prefix=parsed_info["prefix"],
                                co_dinh_noi_hat=co_dinh_noi_hat,
                                co_dinh_lien_tinh=co_dinh_lien_tinh,
                                di_dong=di_dong,
                                cuoc_1900=cuoc_1900,
                                quoc_te=quoc_te,
                                cuoc_thue_bao=cuoc_thue_bao,
                                cuoc_thu_khach=0, 
                                cuoc_tra_htc=0    
                            )
                            du_lieu.append(row_data)
                            logger.debug(f"    Appended DauSoDichVu object. Current du_lieu size: {len(du_lieu)}")
                            stt_counter += 1 # Tăng biến đếm sau mỗi lần tạo DauSoDichVu
                            # processed_rows_count incremented later
                    else:
                         if not dau_so_clean:
                              # logger.debug(f"Bỏ qua dòng {row_idx} do cột đầu số trống") # REMOVED DEBUG
                              pass

                except Exception as e:
                    logger.warning(f"Lỗi khi xử lý nội dung dòng {row_idx}: {str(e)}")
                
                # logger.debug(f"--- Processing row index: {row_idx} --- END --- ") # REMOVED DEBUG
                processed_rows_count += 1 

            # --- END: Change iteration method --- 

        except Exception as e:
            logger.error(f"Lỗi khi xử lý dữ liệu chính: {str(e)}")
        
        # Bỏ qua phần fallback dùng extract_phone_numbers_from_excel vì logic phức tạp và có thể không cần thiết cho mẫu

        # --- Bắt đầu thay đổi: Zero hóa Tổng kết ---
        logger.info("Zeroing out summary values for template processing.")
        tong_ket = TongKet(
            cong_tien_dich_vu=0.0,
            tien_thue_gtgt=0.0,
            tong_cong_tien=0.0
        )
        # --- Kết thúc thay đổi: Zero hóa Tổng kết ---
        
        # Thống kê số lượng số điện thoại đã trích xuất
        logger.info(f"Đã trích xuất tổng cộng {len(du_lieu)} đầu số/thuê bao từ file {file_name}")
        
        # Tạo đối tượng DoiSoatCoDinh cuối cùng
        # Thêm tu_mang, den_doi_tac, nam_doi_soat vào schema nếu cần
        return DoiSoatCoDinh(
            thang_doi_soat=thang_doi_soat if thang_doi_soat else "",
            nam_doi_soat=nam_doi_soat if nam_doi_soat else "",
            tu_mang=tu_mang, # Cần thêm trường này vào schema DoiSoatCoDinh
            den_doi_tac=den_doi_tac, # Cần thêm trường này vào schema DoiSoatCoDinh
            hop_dong_so=hop_dong_so,
            du_lieu=du_lieu,
            tong_ket=tong_ket,
            file_name=file_name,
            partner_id=partner_id
        )
    except Exception as e:
        logger.error(f"Lỗi khi xử lý file đối soát cố định {file_path}: {str(e)}")
        raise ValueError(f"Không thể xử lý file đối soát cố định: {str(e)}")


def extract_dscd_phone_numbers(file_path: str, output_dir: Optional[str] = None, prefix_filter: Optional[str] = None) -> Dict[str, Any]:
    """
    Trích xuất số điện thoại từ file đối soát cố định
    
    Args:
        file_path: Đường dẫn đến file Excel DSCD
        output_dir: Thư mục đầu ra (nếu None, sẽ lưu vào cùng thư mục với file gốc)
        prefix_filter: Tiền tố để lọc số điện thoại (nếu có)
        
    Returns:
        Dictionary chứa kết quả trích xuất
    """
    try:
        logger.info(f"Đang trích xuất số điện thoại từ file DSCD: {file_path}")
        
        # Thiết lập thư mục đầu ra
        if output_dir is None:
            output_dir = os.path.dirname(file_path)
        
        # Tạo tên file đầu ra từ tên file gốc
        file_name = os.path.basename(file_path)
        base_filename = os.path.splitext(file_name)[0]
        
        # Đọc file Excel
        xls = pd.ExcelFile(file_path)
        sheet_name = xls.sheet_names[0]
        df = pd.read_excel(xls, sheet_name=sheet_name)
        
        # Trích xuất số điện thoại
        original_phones = extract_phone_numbers_from_excel(df)
        
        if not original_phones:
            return {
                "success": False,
                "message": "Không tìm thấy số điện thoại nào",
                "total": 0,
                "file_name": file_name
            }
        
        # Chuẩn hóa và lọc số điện thoại
        normalized_phones = filter_phone_numbers(original_phones, prefix_filter)
        
        if prefix_filter:
            base_filename = f"{base_filename}_{prefix_filter}"
            
        # Phân tích số điện thoại
        stats = analyze_phone_numbers(normalized_phones)
        
        # Lưu số điện thoại vào file
        files = save_phone_numbers(
            normalized_phones, 
            original_phones, 
            output_dir, 
            base_filename + "_so_dien_thoai"
        )
        
        # Thông tin đối tác và tháng
        partner = extract_partner_name(file_name)
        thang, nam = extract_month_year(file_name)
        thang_doi_soat = f"{thang}/{nam}" if thang and nam else "Không xác định"
        
        return {
            "success": True,
            "message": f"Đã trích xuất {len(normalized_phones)} số điện thoại",
            "total": len(normalized_phones),
            "file_name": file_name,
            "partner": partner,
            "thang_doi_soat": thang_doi_soat,
            "stats": stats,
            "files": files
        }
    
    except Exception as e:
        logger.error(f"Lỗi khi trích xuất số điện thoại từ file DSCD: {str(e)}")
        return {
            "success": False,
            "message": f"Lỗi: {str(e)}",
            "total": 0,
            "file_name": os.path.basename(file_path)
        } 