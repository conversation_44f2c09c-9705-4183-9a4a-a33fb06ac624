from typing import Optional, <PERSON><PERSON>
from sqlalchemy.orm import Session
import logging
import math

from ..models.pricing import RevenuePricing, BillingMethod
from ..models.enums import ServiceType

log = logging.getLogger(__name__)

def get_pricing_rule(db: Session, partner_id: int, service_type: Optional[ServiceType]) -> Optional[Tuple[float, BillingMethod]]:
    """
    Fetches the active revenue pricing rule for a given partner and service type.

    Prioritizes the specific service type, then falls back to the default rule 
    for the partner if the specific type is not found or provided as None.

    Args:
        db: The database session.
        partner_id: The ID of the partner.
        service_type: The specific ServiceType to look for.

    Returns:
        A tuple containing (price, billing_method) if an active rule is found, 
        otherwise None.
    """
    pricing_rule = None

    # 1. Try to find the specific, active service type rule
    if service_type:
        log.debug(f"Attempting to find specific pricing rule for partner {partner_id}, service {service_type.value}")
        pricing_rule = db.query(RevenuePricing).filter(
            RevenuePricing.partner_id == partner_id,
            RevenuePricing.service_type == service_type.value, # Compare with enum value
            RevenuePricing.is_active == True
        ).first()

    # 2. If specific rule not found or service_type was None, try the default active rule
    if not pricing_rule:
        log.debug(f"Specific rule not found for partner {partner_id}, service {service_type.value if service_type else 'None'}. Falling back to default.")
        pricing_rule = db.query(RevenuePricing).filter(
            RevenuePricing.partner_id == partner_id,
            RevenuePricing.billing_method == BillingMethod.DEFAULT, # Look for default method
            RevenuePricing.is_active == True
            # Ensure service_type IS NULL for default rule? The check constraint might imply this.
            # Add this if necessary: RevenuePricing.service_type == None 
        ).first()

    if pricing_rule:
        log.info(f"Found pricing rule for partner {partner_id}, service {'DEFAULT' if pricing_rule.billing_method == BillingMethod.DEFAULT else service_type.value}: "
                 f"Price={pricing_rule.price}, Method={pricing_rule.billing_method.value}")
        # Ensure price is float before returning
        try:
            price_float = float(pricing_rule.price)
            return price_float, pricing_rule.billing_method
        except (ValueError, TypeError):
             log.error(f"Could not convert price '{pricing_rule.price}' to float for rule ID {pricing_rule.id}")
             return None
    else:
        log.warning(f"No active pricing rule found for partner {partner_id} (Service: {service_type.value if service_type else 'Any/Default'})")
        return None

def apply_billing_method(duration: int, price: float, billing_method: BillingMethod) -> float:
    """
    Applies the specified billing method to calculate the cost based on duration and price.

    Args:
        duration: Call duration in seconds.
        price: The price per unit (depends on the billing method, e.g., per minute for DEFAULT).
        billing_method: The billing method to apply (e.g., BillingMethod.DEFAULT).

    Returns:
        The calculated cost as a float.
    """
    cost = 0.0

    if duration < 0:
        log.warning(f"Received negative duration ({duration}). Setting cost to 0.")
        return 0.0
    if price < 0:
         log.warning(f"Received negative price ({price}). Setting cost to 0.")
         return 0.0

    # Currently, only implementing DEFAULT method, calculated per minute (rounding up)
    if billing_method == BillingMethod.DEFAULT:
        if duration == 0:
            cost = 0.0
        else:
            # Calculate minutes, rounding up to the nearest whole minute
            minutes = math.ceil(duration / 60.0)
            cost = minutes * price
            log.debug(f"Applying DEFAULT billing: Duration={duration}s => Minutes={minutes}, Price/Min={price}, Cost={cost}")
    
    # Placeholder for other billing methods - Currently logs warning and returns 0
    # elif billing_method == BillingMethod.BLOCK_6S:
    #     # TODO: Implement BLOCK_6S logic (assuming price is per minute or second?)
    #     log.warning(f"Billing method {billing_method.value} not fully implemented yet.")
    #     cost = 0.0 # Replace with actual calculation
    # elif billing_method == BillingMethod.ONE_SEC_PLUS:
    #     # TODO: Implement ONE_SEC_PLUS logic (assuming price is per second)
    #     log.warning(f"Billing method {billing_method.value} not fully implemented yet.")
    #     cost = 0.0 # Replace with actual calculation
    # elif billing_method == BillingMethod.ONE_MIN_PLUS:
    #      # TODO: Implement ONE_MIN_PLUS logic (assuming price is per minute)
    #     log.warning(f"Billing method {billing_method.value} not fully implemented yet.")
    #     cost = 0.0 # Replace with actual calculation

    else:
        # Handle unexpected or currently unsupported billing methods for DSCD context
        log.warning(f"Billing method '{billing_method.value}' is not expected or supported in the current DSCD context. Returning 0 cost.")
        cost = 0.0

    return cost

# Will add apply_billing_method function here later 