import os
import re
import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime

from ..schemas.ckn import <PERSON>uoc<PERSON>et<PERSON>oi, DauSoDichVuCKN, TongKetCKN

# Cấu hình logging
logger = logging.getLogger(__name__)

# Các mẫu regex để nhận dạng file cước kết nối
CKN_PATTERNS = [
    r'CKN',
    r'cước\s*kết\s*nối',
    r'cuoc\s*ket\s*noi',
    r'BBDS\s*CKN',
    r'biên\s*bản\s*CKN',
    r'bien\s*ban\s*CKN',
    r'BBLL',       # Biên bản liên lạc
]

def is_ckn_file(filename: str) -> bool:
    """
    Kiểm tra xem file có phải là file cước kết nối (CKN) không
    
    Args:
        filename: Tên file cần kiểm tra
        
    Returns:
        True nếu là file cước kết nối, False nếu không phải
    """
    # Chuyển filename thành lowercase để so sánh không phân biệt chữ hoa/thường
    filename_lower = filename.lower()
    
    # Kiểm tra các mẫu regex
    for pattern in CKN_PATTERNS:
        if re.search(pattern, filename, re.IGNORECASE):
            return True
    
    return False

def extract_month_year(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Trích xuất thông tin tháng và năm từ tên file
    
    Args:
        filename: Tên file cước kết nối
        
    Returns:
        Tuple chứa (tháng, năm) hoặc (None, None) nếu không trích xuất được
    """
    filename = os.path.basename(filename)
    
    # Kiểm tra mẫu "01.2025" hoặc "012025"
    pattern1 = r'(\d{1,2})[\.\-\_]?(\d{4})'
    matches = re.search(pattern1, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Kiểm tra mẫu "thang 01 nam 2025" hoặc "thang01/2025"
    pattern2 = r'[Tt]h[aá]ng\s*(\d{1,2})[\s_\-\/]*(?:[Nn][aă]m\s*)?(\d{4})'
    matches = re.search(pattern2, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    return None, None

def extract_partners(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Trích xuất thông tin đối tác từ tên file
    
    Args:
        filename: Tên file cước kết nối
        
    Returns:
        Tuple chứa (từ_mạng, đến_đối_tác) hoặc (None, None) nếu không trích xuất được
    """
    filename = os.path.basename(filename)
    
    # Mẫu HTC_TO_[ĐỐI TÁC] hoặc HTC TO [ĐỐI TÁC]
    pattern_to = r'([A-Za-z0-9]+)[_\-\s]+[Tt][Oo][_\-\s]+([A-Za-z0-9]+)'
    matches = re.search(pattern_to, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Mẫu CKN_HTC_[ĐỐI TÁC]_[THÔNG TIN KHÁC]
    pattern_ckn = r'CKN[_\-\s]+([A-Za-z0-9]+)[_\-\s]+([A-Za-z0-9]+)'
    matches = re.search(pattern_ckn, filename, re.IGNORECASE)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Mẫu BBLL_[MÃ]HTC-[ĐỐI TÁC]_[THÔNG TIN KHÁC]
    pattern_bbll = r'BBLL[_\-\s]+(?:Fix)?([A-Za-z0-9]+)-([A-Za-z0-9]+)'
    matches = re.search(pattern_bbll, filename, re.IGNORECASE)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Mẫu BBDS CKN [ĐỐI TÁC] HTC [THÔNG TIN KHÁC]
    pattern_bbds_ckn = r'BBDS\s+CKN\s+([A-Za-z0-9]+)\s+([A-Za-z0-9]+)'
    matches = re.search(pattern_bbds_ckn, filename, re.IGNORECASE)
    if matches:
        partner1, partner2 = matches.groups()
        # Thường HTC là mạng nguồn
        if partner2.upper() == "HTC":
            return partner2, partner1
        else:
            return partner1, partner2
    
    # Thử cho các trường hợp tên file phức tạp hơn
    words = re.findall(r'[A-Za-z0-9]+', filename)
    htc_idx = None
    
    # Tìm HTC trong danh sách từ
    for i, word in enumerate(words):
        if word.upper() == "HTC":
            htc_idx = i
            break
    
    if htc_idx is not None:
        # Ưu tiên từ phía sau HTC
        if htc_idx + 1 < len(words):
            next_word = words[htc_idx + 1]
            # Nếu từ tiếp theo dường như là một đối tác (không phải số/tháng/lệnh)
            if (not re.match(r'^\d+$', next_word) and 
                next_word.upper() not in ['TO', 'T', 'THANG', 'NAM', 'CKN', 'BBDS']):
                return 'HTC', next_word
    
    return None, None

def process_ckn_file(file_path: str) -> Dict[str, Any]:
    """
    Xử lý file cước kết nối
    
    Args:
        file_path: Đường dẫn đến file Excel cước kết nối
        
    Returns:
        Dictionary chứa thông tin đã xử lý
    """
    try:
        # Trích xuất thông tin từ tên file
        filename = os.path.basename(file_path)
        month, year = extract_month_year(filename)
        tu_mang, den_doi_tac = extract_partners(filename)
        
        # Đọc nội dung file
        if file_path.endswith('.pdf'):
            # Nếu là file PDF, chỉ xử lý metadata
            result = {
                "success": True,
                "file_path": file_path,
                "file_name": filename,
                "thang_doi_soat": f"{month}/{year}" if month and year else None,
                "tu_mang": tu_mang,
                "den_doi_tac": den_doi_tac,
                "tong_san_luong": None,
                "tong_thanh_tien": None,
                "chi_tiet_dau_so": []
            }
        else:
            # Nếu là file Excel, đọc và xử lý nội dung
            try:
                # Đọc file Excel, thử nhiều sheet
                xls = pd.ExcelFile(file_path)
                main_sheet = xls.sheet_names[0]  # Mặc định lấy sheet đầu tiên
                
                # Thử đọc với nhiều cách khác nhau
                df = pd.read_excel(file_path, sheet_name=main_sheet)
                
                # Xử lý dữ liệu cơ bản
                tong_san_luong = None
                tong_thanh_tien = None
                
                # Tìm các cột tổng trong file
                for col in df.columns:
                    col_str = str(col).lower()
                    if 'tổng' in col_str or 'tong' in col_str or 'total' in col_str:
                        for idx, row in df.iterrows():
                            row_val = str(row[col]).lower() if not pd.isna(row[col]) else ""
                            if 'cuộc gọi' in row_val or 'cuoc goi' in row_val or 'san luong' in row_val:
                                try:
                                    # Tìm giá trị số lượng ở cột bên cạnh
                                    for next_col in df.columns[df.columns.get_loc(col)+1:]:
                                        if not pd.isna(row[next_col]) and str(row[next_col]).strip():
                                            tong_san_luong = float(row[next_col])
                                            break
                                except:
                                    pass
                            
                            if 'thành tiền' in row_val or 'thanh tien' in row_val or 'doanh thu' in row_val:
                                try:
                                    # Tìm giá trị tiền ở cột bên cạnh
                                    for next_col in df.columns[df.columns.get_loc(col)+1:]:
                                        if not pd.isna(row[next_col]) and str(row[next_col]).strip():
                                            tong_thanh_tien = float(row[next_col])
                                            break
                                except:
                                    pass
                
                result = {
                    "success": True,
                    "file_path": file_path,
                    "file_name": filename,
                    "thang_doi_soat": f"{month}/{year}" if month and year else None,
                    "tu_mang": tu_mang,
                    "den_doi_tac": den_doi_tac,
                    "tong_san_luong": tong_san_luong,
                    "tong_thanh_tien": tong_thanh_tien,
                    "chi_tiet_dau_so": []
                }
                
            except Exception as e:
                # Nếu không thể xử lý nội dung, vẫn trả về metadata
                logger.error(f"Lỗi khi xử lý nội dung file Excel: {str(e)}")
                result = {
                    "success": True,
                    "file_path": file_path,
                    "file_name": filename,
                    "thang_doi_soat": f"{month}/{year}" if month and year else None,
                    "tu_mang": tu_mang,
                    "den_doi_tac": den_doi_tac,
                    "tong_san_luong": None,
                    "tong_thanh_tien": None,
                    "chi_tiet_dau_so": []
                }
        
        return result
        
    except Exception as e:
        logger.error(f"Lỗi khi xử lý file cước kết nối: {str(e)}")
        return {
            "success": False,
            "file_path": file_path,
            "file_name": os.path.basename(file_path),
            "error": str(e)
        } 