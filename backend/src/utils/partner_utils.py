"""
Utilities for working with partners and partner names.
"""
from typing import Optional
from sqlalchemy.orm import Session
import re

from ..models.partner import Partner, PartnerRawName
from .enhanced_phone_utils import normalize_phone_number_enhanced

def normalize_phone_number(phone: str) -> str:
    """
    Normalize a phone number - wrapper around enhanced version.
    
    Args:
        phone: Phone number to normalize
        
    Returns:
        str: Normalized phone number
    """
    return normalize_phone_number_enhanced(phone)

def get_partner_by_raw_name(db: Session, raw_name: str) -> Optional[Partner]:
    """
    Look up a partner by a raw name string.
    
    Args:
        db: Database session
        raw_name: Raw partner name to look up
        
    Returns:
        Optional[Partner]: Partner if found, None otherwise
    """
    # Standardize the raw name
    standardized_name = raw_name.strip().lower()
    
    # Try to find a direct match in partner_raw_names
    raw_name_entry = db.query(PartnerRawName).filter(
        PartnerRawName.raw_name == standardized_name
    ).first()
    
    if raw_name_entry:
        return raw_name_entry.partner
    
    # Try to find a partner with a similar name
    partners = db.query(Partner).all()
    for partner in partners:
        if partner.name.lower() in standardized_name or standardized_name in partner.name.lower():
            # Create a new raw name entry for future lookups
            new_raw_name = PartnerRawName(
                raw_name=standardized_name,
                partner_id=partner.id
            )
            db.add(new_raw_name)
            db.commit()
            return partner
    
    return None

def create_partner_from_name(db: Session, raw_name: str) -> Partner:
    """
    Create a new partner from a raw name.
    
    Args:
        db: Database session
        raw_name: Raw partner name to create a partner from
        
    Returns:
        Partner: Newly created partner
    """
    # Clean up the name
    cleaned_name = raw_name.strip()
    
    # Create a new partner
    partner = Partner(
        name=cleaned_name,
        is_active=True
    )
    db.add(partner)
    db.flush()
    
    # Create a raw name entry
    raw_name_entry = PartnerRawName(
        raw_name=raw_name.strip().lower(),
        partner_id=partner.id
    )
    db.add(raw_name_entry)
    db.commit()
    db.refresh(partner)
    
    return partner 