from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, constr

class UserBase(BaseModel):
    username: constr(min_length=3, max_length=255)
    email: EmailStr
    full_name: constr(min_length=1, max_length=255)
    is_active: bool = True
    is_admin: bool = False

class UserCreate(UserBase):
    password: constr(min_length=8, max_length=255)

class UserUpdate(BaseModel):
    username: Optional[constr(min_length=3, max_length=255)] = None
    email: Optional[EmailStr] = None
    full_name: Optional[constr(min_length=1, max_length=255)] = None
    password: Optional[constr(min_length=8, max_length=255)] = None
    is_active: Optional[bool] = None
    is_admin: Optional[bool] = None

class UserInDBBase(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None

    class Config:
        from_attributes = True

class User(UserInDBBase):
    pass

class UserInDB(UserInDBBase):
    password_hash: str

class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class TokenPayload(BaseModel):
    sub: str
    exp: datetime
    type: str

class LoginRequest(BaseModel):
    username_or_email: str
    password: str 