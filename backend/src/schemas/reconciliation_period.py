from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, ConfigDict, Field, field_validator, conint

class ReconciliationPeriodBase(BaseModel):
    month: conint(ge=1, le=12) = Field(..., description="Tháng đối soát (1-12)")
    year: conint(gt=1970) = Field(..., description="Năm đối soát (ví dụ: 2024)")
    name: str = Field(..., description="Tên kỳ đối soát")
    notes: Optional[str] = None

class ReconciliationPeriodCreate(ReconciliationPeriodBase):
    pass

class ReconciliationPeriodUpdate(BaseModel):
    name: Optional[str] = None
    status: Optional[str] = None
    notes: Optional[str] = None

class ReconciliationPeriodInDBBase(ReconciliationPeriodBase):
    id: int
    created_by: int
    status: str
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

class ReconciliationPeriod(ReconciliationPeriodInDBBase):
    pass

class ReconciliationPeriodResponse(BaseModel):
    success: bool = True
    message: str = "Success"
    data: Optional[ReconciliationPeriod] = None

class ReconciliationPeriodListResponse(BaseModel):
    success: bool = True
    message: str = "Success"
    total: int
    data: List[ReconciliationPeriod] 