from datetime import datetime, date
from typing import Optional, List
from pydantic import BaseModel, Field
from enum import Enum

class FileTypeEnum(str, Enum):
    CALL_IN = "call_in"
    CALL_OUT = "call_out"

class CallTypeEnum(str, Enum):
    IN = "in"
    OUT = "out"

class FileStatusEnum(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class CallLogFileCreate(BaseModel):
    file_type: FileTypeEnum

class CallLogFileResponse(BaseModel):
    id: int
    filename: str
    file_type: FileTypeEnum
    status: FileStatusEnum
    uploaded_at: datetime
    total_rows: Optional[int] = None
    processed_rows: int
    error_count: int
    error_message: Optional[str] = None
    processing_time: Optional[float] = None
    task_id: Optional[str] = None

    class Config:
        from_attributes = True

class CallLogResponse(BaseModel):
    id: int
    file_id: int
    call_type: CallTypeEnum
    caller: str
    callee: str
    begin_time: datetime
    end_time: datetime
    duration: int
    caller_gateway: str
    called_gateway: str
    call_date: date
    call_hour: int
    caller_type: str
    callee_type: str
    caller_note: Optional[str] = None
    callee_note: Optional[str] = None
    area_prefix: Optional[str] = None
    area_name: Optional[str] = None
    caller_prefix: Optional[str] = None
    callee_prefix: Optional[str] = None

    class Config:
        from_attributes = True

class PaginatedCallLogFileResponse(BaseModel):
    items: List[CallLogFileResponse]
    total: int
    page: int
    size: int

class PaginatedCallLogResponse(BaseModel):
    items: List[CallLogResponse]
    total: int
    page: int
    size: int

    class Config:
        from_attributes = True

class CallLog(CallLogResponse):
    id: int
    # ... other fields ...

    class Config:
        from_attributes = True

class CallLogCreate(BaseModel):
    # ... fields ...

    class Config:
        from_attributes = True

class CallLogUpdate(CallLogCreate):
    # ... fields ...

    class Config:
        from_attributes = True 