from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, ConfigDict, field_validator
from datetime import datetime
from ..models.enums import DauSoType, ReconciliationStatus, ReconciliationType
from .partner import PartnerBase

class UploadDoiSoatResponse(BaseModel):
    """Response model cho API upload đối soát"""
    success: bool = Field(description="Trạng thái thành công")
    file_id: str = Field(description="ID của file đã upload")
    filename: str = Field(description="Tên file gốc")
    detected_type: str = Field(description="Loại đối soát được phát hiện")
    confidence: float = Field(description="Độ tin cậy của phân loại (0.0-1.0)")
    partner_id: Optional[int] = Field(None, description="ID của đối tác liên quan")
    partner_name: Optional[str] = Field(None, description="Tên của đối tác liên quan")
    message: str = Field(description="Thông báo kết quả")


class ProcessDoiSoatRequest(BaseModel):
    """Request model cho API xử lý đối soát"""
    file_id: str = Field(description="ID của file cần xử lý")
    doi_soat_type: str = Field(description="Loại đối soát (CO_DINH, CUOC, 1800_1900)")
    

class ProcessDoiSoatResponse(BaseModel):
    """Response model cho API xử lý đối soát"""
    success: bool = Field(description="Trạng thái thành công")
    file_id: str = Field(description="ID của file đã xử lý")
    doi_soat_type: str = Field(description="Loại đối soát đã sử dụng")
    processing_status: str = Field(description="Trạng thái xử lý (PENDING, COMPLETED, ERROR)")
    message: str = Field(description="Thông báo kết quả")
    detail: Optional[Dict[str, Any]] = Field(None, description="Chi tiết kết quả xử lý") 

class DoiSoatCreateFromTemplateRequest(BaseModel):
    """Request model để tạo đối soát từ template."""
    template_id: str = Field(..., description="ID của template được chọn.") # Có thể đổi thành int nếu ID là số nguyên
    ky_doi_soat: str = Field(..., pattern=r"^\d{4}-\d{2}$", description="Kỳ đối soát theo định dạng YYYY-MM.")

class DoiSoatCreateFromTemplateResponse(BaseModel):
    """Response model sau khi tạo đối soát thành công từ template."""
    id: str = Field(..., description="ID của bản đối soát vừa được tạo.")
    template_id: str = Field(..., description="ID của template đã sử dụng.")
    ky_doi_soat: str = Field(..., description="Kỳ đối soát.")
    loai_doi_soat: str = Field(..., description="Loại đối soát (ví dụ: DSC, DSCD, CKN, DST). Được xác định từ template.")
    trang_thai: str = Field(..., description="Trạng thái ban đầu của đối soát (ví dụ: Draft).")
    ngay_tao: datetime = Field(..., description="Thời gian tạo đối soát.")
    task_id: Optional[str] = Field(None, description="ID của task xử lý bất đồng bộ nếu có.")

    class Config:
        from_attributes = True

# Define a minimal Partner schema if PartnerBase is not available or suitable
class MinimalPartnerInfo(BaseModel):
    id: int
    name: str

    model_config = ConfigDict(from_attributes=True)

# --- START: Define Unified Reconciliation List Schemas ---

class ReconciliationListItem(BaseModel):
    """Schema for a single item in the unified reconciliation list."""
    id: int
    reconciliation_type: ReconciliationType = Field(..., description="Loại đối soát (DSCD, DSC, etc.)")
    ky_doi_soat: str = Field(..., description="Kỳ đối soát (YYYY-MM)")
    partner: Optional[MinimalPartnerInfo] = Field(None, description="Thông tin đối tác liên quan")
    status: ReconciliationStatus = Field(..., description="Trạng thái hiện tại của bản ghi")
    created_at: datetime = Field(..., description="Thời điểm tạo bản ghi")
    updated_at: datetime = Field(..., description="Thời điểm cập nhật gần nhất")
    # Add other common fields if needed, e.g., total amount?
    # total_amount: Optional[float] = Field(None, description="Tổng số tiền (nếu có)")

    model_config = ConfigDict(from_attributes=True)

class ReconciliationListResponse(BaseModel):
    """Response schema for the unified list of reconciliations."""
    items: List[ReconciliationListItem] = Field(..., description="Danh sách các bản ghi đối soát")
    total: int = Field(..., description="Tổng số lượng bản ghi khớp với bộ lọc")
    page: int = Field(..., description="Trang hiện tại")
    size: int = Field(..., description="Số lượng bản ghi trên mỗi trang")
    pages: int = Field(..., description="Tổng số trang")

# --- END: Define Unified Reconciliation List Schemas ---