from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field, ConfigDict, field_validator

from ..models.partner import PartnerType
from ..models.partner import normalize_partner_name

class PartnerRawNameBase(BaseModel):
    raw_name: str = Field(..., min_length=1, max_length=100)
    model_config = ConfigDict(from_attributes=True)

class PartnerRawNameCreate(PartnerRawNameBase):
    pass

class PartnerRawNameUpdate(PartnerRawNameBase):
    pass

class PartnerRawName(PartnerRawNameBase):
    id: int
    partner_id: int
    created_at: datetime
    updated_at: datetime
    model_config = ConfigDict(from_attributes=True)

class PartnerBase(BaseModel):
    type: PartnerType = Field(..., description="Partner type (telco or cp)")
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    is_active: bool = True
    error_adjustment: int = Field(0, description="CP error adjustment: -1 for setting all calls to -1s, +1 for increasing 0s calls to 1s")
    contact_name: Optional[str] = Field(None, max_length=100)
    contact_email: Optional[str] = Field(None, max_length=255)
    contact_phone: Optional[str] = Field(None, max_length=20)

    model_config = ConfigDict(use_enum_values=True)

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        if v:
            return normalize_partner_name(v)
        return v
        
    @field_validator('error_adjustment')
    @classmethod
    def validate_error_adjustment(cls, v: int, info) -> int:
        values = info.data
        if 'type' in values and values['type'] == 'telco' and v != 0:
            raise ValueError("error_adjustment must be 0 for TELCO partners")
        if v not in [-1, 0, 1]:
            raise ValueError("error_adjustment must be -1, 0, or 1")
        return v

class PartnerCreate(PartnerBase):
    raw_names: List[str] = Field(..., min_items=1)

class PartnerUpdate(BaseModel):
    type: Optional[PartnerType] = None
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None
    error_adjustment: Optional[int] = Field(None, description="CP error adjustment: -1 for setting all calls to -1s, +1 for increasing 0s calls to 1s")
    contact_name: Optional[str] = Field(None, max_length=100)
    contact_email: Optional[str] = Field(None, max_length=255)
    contact_phone: Optional[str] = Field(None, max_length=20)
    raw_names: Optional[List[str]] = Field(None, min_items=1)

    model_config = ConfigDict(use_enum_values=True)

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: Optional[str]) -> Optional[str]:
        if v:
            return normalize_partner_name(v)
        return v
        
    @field_validator('error_adjustment')
    @classmethod
    def validate_error_adjustment(cls, v: Optional[int], info) -> Optional[int]:
        if v is None:
            return v
        values = info.data
        if 'type' in values and values['type'] == 'telco' and v != 0:
            raise ValueError("error_adjustment must be 0 for TELCO partners")
        if v not in [-1, 0, 1]:
            raise ValueError("error_adjustment must be -1, 0, or 1")
        return v

class PartnerInDBBase(PartnerBase):
    id: int
    created_at: datetime
    updated_at: datetime
    raw_names: List[PartnerRawName] = []

    model_config = ConfigDict(from_attributes=True, use_enum_values=True)

class Partner(PartnerInDBBase):
    pass

class PartnerInDB(PartnerInDBBase):
    pass

# Thêm schema cho response danh sách Partner với phân trang
class PartnerListResponse(BaseModel):
    items: List[Partner]
    total: int
    # Có thể thêm page, size nếu API endpoint cũng trả về chúng
    # page: int 
    # size: int 