from enum import Enum
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime

# Từ điển mapping tên trường tiếng Việt
FIELD_LABELS = {
    # Chung
    "thang_doi_soat": "Tháng đối soát",
    "tu_mang": "Từ mạng",
    "den_doi_tac": "Đến đối tác",
    "loai_dich_vu": "Loại dịch vụ",
    "hop_dong_so": "Hợp đồng số",
    "file_name": "Tên file",
    "thanh_toan": "Thanh toán",
    "nhom_dich_vu": "Nhóm dịch vụ",
    "ten_dich_vu": "Tên dịch vụ",
    "chi_tiet": "Chi tiết",
    "tong_ket": "Tổng kết",
    
    # Chi tiết dịch vụ
    "stt": "STT",
    "so_dich_vu": "Số dịch vụ",
    "so_lieu_ben_a": "<PERSON><PERSON> liệu bên A",
    "so_lieu_ben_b": "<PERSON>ố liệu bên B",
    "chenh_lech": "<PERSON><PERSON><PERSON> lệch",
    "so_lieu_tinh_dt": "Số liệu tính DT",
    "muc_cuoc": "Mức cước",
    "cuoc_thu_khach": "Cước thu khách",
    "doanh_thu_ben_a": "Doanh thu bên A",
    "doanh_thu_ben_b": "Doanh thu bên B",
    
    # Tổng kết
    "tong_cong_chua_vat": "Tổng cộng chưa VAT",
    "thue_vat": "Thuế VAT",
    "tong_cong_co_vat": "Tổng cộng có VAT",
    
    # Thanh toán
    "doanh_thu_ben_a_thanh_toan": "Doanh thu bên A thanh toán cho bên B",
    "doanh_thu_ben_b_thanh_toan": "Doanh thu bên B thanh toán cho bên A",
    "sau_bu_tru": "Sau bù trừ",
    "ben_thanh_toan": "Bên thanh toán",
    "ben_nhan": "Bên nhận",
    "ghi_chu": "Ghi chú"
}

class LoaiDichVuEnum(str, Enum):
    """Enum cho loại dịch vụ"""
    DV_1800 = "1800xxxx"
    DV_1900 = "1900xxxx"
    DV_1800_1900 = "1800xxxx/1900xxxx"
    DV_109 = "109x"


class ChiTietDichVu1800_1900(BaseModel):
    """Schema Pydantic cho chi tiết dịch vụ"""
    id: Optional[int] = None
    stt: int
    so_dich_vu: str
    so_lieu_ben_a: float = 0
    so_lieu_ben_b: float = 0
    chenh_lech: float = 0
    so_lieu_tinh_dt: float = 0
    muc_cuoc: float = 0
    cuoc_thu_khach: float = 0
    doanh_thu_ben_a: float = 0
    doanh_thu_ben_b: float = 0
    
    def get_label(self, field_name: str) -> str:
        """Lấy nhãn tiếng Việt cho trường dữ liệu"""
        return FIELD_LABELS.get(field_name, field_name)
    
    def to_labeled_dict(self) -> Dict[str, Any]:
        """Chuyển đổi model thành dictionary có nhãn tiếng Việt"""
        result = {}
        for field_name, value in self.__dict__.items():
            if not field_name.startswith("_"):
                label = self.get_label(field_name)
                result[label] = value
        return result
    
    class Config:
        from_attributes = True


class TongKet1800_1900(BaseModel):
    """Schema Pydantic cho tổng kết nhóm dịch vụ"""
    id: Optional[int] = None
    tong_cong_chua_vat: float = 0
    thue_vat: float = 0
    tong_cong_co_vat: float = 0
    
    def get_label(self, field_name: str) -> str:
        """Lấy nhãn tiếng Việt cho trường dữ liệu"""
        return FIELD_LABELS.get(field_name, field_name)
    
    def to_labeled_dict(self) -> Dict[str, Any]:
        """Chuyển đổi model thành dictionary có nhãn tiếng Việt"""
        result = {}
        for field_name, value in self.__dict__.items():
            if not field_name.startswith("_"):
                label = self.get_label(field_name)
                result[label] = value
        return result
    
    class Config:
        from_attributes = True


class ThanhToan1800_1900(BaseModel):
    """Schema Pydantic cho thanh toán"""
    id: Optional[int] = None
    doanh_thu_ben_a_thanh_toan: float = 0
    doanh_thu_ben_b_thanh_toan: float = 0
    sau_bu_tru: float = 0
    ben_thanh_toan: str
    ben_nhan: str
    ghi_chu: Optional[str] = None
    
    def get_label(self, field_name: str) -> str:
        """Lấy nhãn tiếng Việt cho trường dữ liệu"""
        return FIELD_LABELS.get(field_name, field_name)
    
    def to_labeled_dict(self) -> Dict[str, Any]:
        """Chuyển đổi model thành dictionary có nhãn tiếng Việt"""
        result = {}
        for field_name, value in self.__dict__.items():
            if not field_name.startswith("_"):
                label = self.get_label(field_name)
                result[label] = value
        return result
    
    class Config:
        from_attributes = True


class NhomDichVu1800_1900(BaseModel):
    """Schema Pydantic cho nhóm dịch vụ"""
    id: Optional[int] = None
    ten_dich_vu: str
    chi_tiet: List[ChiTietDichVu1800_1900]
    tong_ket: TongKet1800_1900
    
    def get_label(self, field_name: str) -> str:
        """Lấy nhãn tiếng Việt cho trường dữ liệu"""
        return FIELD_LABELS.get(field_name, field_name)
    
    def to_labeled_dict(self) -> Dict[str, Any]:
        """Chuyển đổi model thành dictionary có nhãn tiếng Việt"""
        result = {}
        for field_name, value in self.__dict__.items():
            if not field_name.startswith("_"):
                label = self.get_label(field_name)
                if field_name == "chi_tiet":
                    result[label] = [item.to_labeled_dict() for item in value]
                elif field_name == "tong_ket":
                    result[label] = value.to_labeled_dict()
                else:
                    result[label] = value
        return result
    
    class Config:
        from_attributes = True


class ChiTietDauSo(BaseModel):
    """Schema Pydantic cho chi tiết đầu số dịch vụ 1800/1900"""
    so_dau_so: str
    mo_ta: Optional[str] = None
    so_luong: Optional[int] = None
    thanh_tien: Optional[float] = None
    
    class Config:
        from_attributes = True


class DoiSoat1800_1900(BaseModel):
    """
    Model cho dữ liệu đối soát 1800/1900
    """
    thang_doi_soat: Optional[str] = None
    nam_doi_soat: Optional[str] = None
    tu_mang: Optional[str] = None
    den_doi_tac: Optional[str] = None
    loai_dich_vu: Optional[str] = None
    partner_id: Optional[int] = Field(None, description="ID của đối tác thật từ database")
    dau_so: List[ChiTietDauSo] = []
    thanh_toan: Optional[ThanhToan1800_1900] = None
    hop_dong_so: Optional[str] = None
    file_name: Optional[str] = None
    ghi_chu: Optional[str] = None
    nhom_dich_vu: List[NhomDichVu1800_1900] = []
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def get_label(self, field_name: str) -> str:
        """Lấy nhãn tiếng Việt cho trường dữ liệu"""
        return FIELD_LABELS.get(field_name, field_name)
    
    def to_labeled_dict(self) -> Dict[str, Any]:
        """Chuyển đổi model thành dictionary có nhãn tiếng Việt"""
        result = {}
        for field_name, value in self.__dict__.items():
            if not field_name.startswith("_"):
                label = self.get_label(field_name)
                if field_name == "nhom_dich_vu":
                    result[label] = [item.to_labeled_dict() for item in value]
                elif field_name == "thanh_toan":
                    result[label] = value.to_labeled_dict()
                else:
                    result[label] = value
        return result
    
    class Config:
        from_attributes = True


# Schema cho tạo và cập nhật đối soát
class DoiSoat1800_1900Create(BaseModel):
    """Schema Pydantic cho tạo mới đối soát 1800/1900"""
    thang_doi_soat: str
    tu_mang: str
    den_doi_tac: str
    loai_dich_vu: str
    hop_dong_so: Optional[str] = None
    file_name: str
    ghi_chu: Optional[str] = None


class DoiSoat1800_1900Update(BaseModel):
    """Schema Pydantic cho cập nhật đối soát 1800/1900"""
    thang_doi_soat: Optional[str] = None
    tu_mang: Optional[str] = None
    den_doi_tac: Optional[str] = None
    loai_dich_vu: Optional[str] = None
    hop_dong_so: Optional[str] = None
    ghi_chu: Optional[str] = None


# Schema cho phản hồi API
class DoiSoat1800_1900Response(BaseModel):
    """Schema Pydantic cho phản hồi API đối soát 1800/1900"""
    success: bool
    message: str
    data: Optional[DoiSoat1800_1900] = None
    
    class Config:
        from_attributes = True


class DoiSoat1800_1900ListResponse(BaseModel):
    """Schema Pydantic cho phản hồi API danh sách đối soát 1800/1900"""
    success: bool
    message: str
    total: int
    data: List[DoiSoat1800_1900]
    
    class Config:
        from_attributes = True 