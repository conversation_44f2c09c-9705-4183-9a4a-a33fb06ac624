from datetime import datetime
from typing import Optional, List, Dict, Any
from decimal import Decimal
from pydantic import BaseModel, Field, ConfigDict, field_validator

from ..models.volume_range import VolumeUnit
from ..models.pricing import BillingMethod
from ..models.enums import ServiceType, SERVICE_TYPE_NAMES  # Import enum mới

# Base Schemas
class VolumeRangeBase(BaseModel):
    min_value: Optional[Decimal] = None
    max_value: Optional[Decimal] = None
    unit: VolumeUnit
    description: str = Field(..., max_length=100)
    model_config = ConfigDict(from_attributes=True)

    @field_validator('max_value')
    def validate_max_value(cls, v: Optional[Decimal], info) -> Optional[Decimal]:
        if v is not None and info.data.get('min_value') is not None and v <= info.data['min_value']:
            raise ValueError('max_value must be greater than min_value')
        return v

class RevenuePricingBase(BaseModel):
    partner_id: int
    billing_method: BillingMethod
    service_type: Optional[str] = None  # Thay đổi từ service_type_id sang service_type
    volume_range_id: Optional[int] = None
    price: Decimal = Field(..., ge=0)
    is_active: bool = True
    model_config = ConfigDict(from_attributes=True)

    @field_validator('service_type', 'volume_range_id')
    def validate_fields_for_subscription(cls, v: Optional[Any], info) -> Optional[Any]:
        if 'billing_method' in info.data:
            if info.data['billing_method'] == BillingMethod.SUBSCRIPTION and v is not None:
                raise ValueError(f'{info.field_name} must be None for subscription billing method')
            elif info.data['billing_method'] != BillingMethod.SUBSCRIPTION and info.field_name == 'service_type' and v is None:
                raise ValueError('service_type is required for non-subscription billing methods')
        return v

class CostPricingBase(BaseModel):
    partner_id: int
    billing_method: BillingMethod
    service_type: Optional[str] = None  # Thay đổi từ service_type_id sang service_type
    volume_range_id: Optional[int] = None
    description: Optional[str] = None
    price: Decimal = Field(..., ge=0)
    is_active: bool = True
    model_config = ConfigDict(from_attributes=True)

    @field_validator('service_type', 'volume_range_id')
    def validate_fields_for_subscription(cls, v: Optional[Any], info) -> Optional[Any]:
        if 'billing_method' in info.data:
            if info.data['billing_method'] == BillingMethod.SUBSCRIPTION and v is not None:
                raise ValueError(f'{info.field_name} must be None for subscription billing method')
            elif info.data['billing_method'] != BillingMethod.SUBSCRIPTION and info.field_name == 'service_type' and v is None:
                raise ValueError('service_type is required for non-subscription billing methods')
        return v

# Create Schemas
class VolumeRangeCreate(VolumeRangeBase):
    pass

class VolumeRangeUpdate(VolumeRangeBase):
    pass

class VolumeRange(VolumeRangeBase):
    id: int

class RevenuePricingCreate(RevenuePricingBase):
    pass

class RevenuePricingUpdate(RevenuePricingBase):
    pass

class RevenuePricing(RevenuePricingBase):
    id: int
    service_type_name: Optional[str] = None  # Thêm trường này để lưu tên service type
    volume_range: Optional[VolumeRange] = None

class CostPricingCreate(CostPricingBase):
    pass

class CostPricingUpdate(CostPricingBase):
    pass

class CostPricing(CostPricingBase):
    id: int
    service_type_name: Optional[str] = None  # Thêm trường này để lưu tên service type
    volume_range: Optional[VolumeRange] = None

# Response Schemas
class VolumeRangeResponse(VolumeRangeBase):
    id: int

class RevenuePricingResponse(RevenuePricingBase):
    id: int
    service_type_name: Optional[str] = None  # Thêm trường này để lưu tên service type
    volume_range: Optional[VolumeRangeResponse] = None

class CostPricingResponse(CostPricingBase):
    id: int
    service_type_name: Optional[str] = None  # Thêm trường này để lưu tên service type
    volume_range: Optional[VolumeRangeResponse] = None

# List Response Schemas
class VolumeRangeList(BaseModel):
    items: List[VolumeRangeResponse]
    total: int
    model_config = ConfigDict(from_attributes=True)

class RevenuePricingList(BaseModel):
    items: List[RevenuePricingResponse]
    total: int
    model_config = ConfigDict(from_attributes=True)

class CostPricingList(BaseModel):
    items: List[CostPricingResponse]
    total: int
    model_config = ConfigDict(from_attributes=True)

# Bulk Create Pricing Schemas
class BulkPricingServiceItem(BaseModel):
    service_type: Optional[str] = None  # Thay đổi từ service_type_id sang service_type
    volume_range_id: Optional[int] = None
    price: Decimal = Field(..., ge=0)
    description: Optional[str] = None  # Only for cost pricing
    model_config = ConfigDict(from_attributes=True)

class BulkRevenuePricingCreate(BaseModel):
    partner_id: int
    billing_method: BillingMethod
    items: List[BulkPricingServiceItem]
    model_config = ConfigDict(from_attributes=True)

class BulkCostPricingCreate(BaseModel):
    partner_id: int
    billing_method: BillingMethod
    items: List[BulkPricingServiceItem]
    model_config = ConfigDict(from_attributes=True)

# Thêm schema mới cho ServiceTypeItem
class ServiceTypeItem(BaseModel):
    code: str
    name: str
    model_config = ConfigDict(from_attributes=True)

# Form Data Response schemas
class FormDataResponse(BaseModel):
    service_types: List[ServiceTypeItem]  # Thay đổi kiểu dữ liệu
    volume_ranges: List[VolumeRange]
    billing_methods: List[BillingMethod]
    model_config = ConfigDict(from_attributes=True) 