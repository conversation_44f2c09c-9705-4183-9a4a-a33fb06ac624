from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime

class DauSoDichVuCKN(BaseModel):
    """Model cho các đầu số liên quan đến cước kết nối"""
    dau_so: str
    ten_dau_so: Optional[str] = None
    nha_cung_cap: Optional[str] = None
    san_luong: Optional[float] = None
    don_gia: Optional[float] = None
    thanh_tien: Optional[float] = None
    ghi_chu: Optional[str] = None

class TongKetCKN(BaseModel):
    """Model tổng kết cước kết nối"""
    tu_mang: Optional[str] = None
    den_doi_tac: Optional[str] = None
    thang_doi_soat: Optional[str] = None
    tong_san_luong: Optional[float] = None
    tong_thanh_tien: Optional[float] = None

class CuocKetNoi(BaseModel):
    """Model cho cước kết nối (CKN)"""
    id: Optional[int] = None
    file_id: Optional[str] = None
    file_path: Optional[str] = None
    file_name: Optional[str] = None
    thang_doi_soat: Optional[str] = None
    tu_mang: Optional[str] = None
    den_doi_tac: Optional[str] = None
    hop_dong_so: Optional[str] = None
    tong_san_luong: Optional[float] = None
    tong_thanh_tien: Optional[float] = None
    chi_tiet_dau_so: List[DauSoDichVuCKN] = []
    tong_ket: Optional[TongKetCKN] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

# Models for API requests
class CuocKetNoiCreate(BaseModel):
    """Model cho API tạo mới cước kết nối"""
    thang_doi_soat: str
    tu_mang: str
    den_doi_tac: str
    hop_dong_so: Optional[str] = None
    file_path: str

class CuocKetNoiUpdate(BaseModel):
    """Model cho API cập nhật cước kết nối"""
    thang_doi_soat: Optional[str] = None
    tu_mang: Optional[str] = None
    den_doi_tac: Optional[str] = None
    hop_dong_so: Optional[str] = None
    tong_san_luong: Optional[float] = None
    tong_thanh_tien: Optional[float] = None
    chi_tiet_dau_so: Optional[List[DauSoDichVuCKN]] = None

# Models for API responses
class CuocKetNoiResponse(BaseModel):
    """Model cho API phản hồi cước kết nối"""
    id: int
    file_id: Optional[str] = None
    file_name: Optional[str] = None
    thang_doi_soat: Optional[str] = None
    tu_mang: Optional[str] = None
    den_doi_tac: Optional[str] = None
    hop_dong_so: Optional[str] = None
    tong_san_luong: Optional[float] = 0
    tong_thanh_tien: Optional[float] = 0
    chi_tiet_dau_so: List[DauSoDichVuCKN] = []
    created_at: datetime
    updated_at: Optional[datetime] = None 