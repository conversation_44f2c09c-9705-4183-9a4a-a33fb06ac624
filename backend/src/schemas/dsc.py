from pydantic import BaseModel, Field, validator, field_validator
from typing import List, Optional, Dict, Any
from datetime import date, datetime
import re
from pydantic import ConfigDict
from ..models.enums import ReconciliationStatus
from .doi_soat import MinimalPartnerInfo

# Dictionary ánh xạ tên field -> tên hiển thị tiếng Việt
FIELD_LABELS = {
    "thang_doi_soat": "Tháng đối soát",
    "tu_mang": "TỪ MẠNG",
    "den_doi_tac": "ĐẾN SỐ DỊCH VỤ CỦA",
    "stt": "STT",
    "dau_so": "ĐẦU SỐ DỊCH VỤ",
    
    # VNM
    "vnm_san_luong": "SẢN LƯỢNG VNM",
    "vnm_ty_le_cp": "Tỷ lệ CP VNM",
    "vnm_thanh_tien": "THÀNH TIỀN VNM",
    
    # VIETTEL
    "viettel_san_luong": "SẢN LƯỢNG VIETTEL",
    "viettel_ty_le_cp": "Tỷ lệ CP VIETTEL",
    "viettel_thanh_tien": "THÀNH TIỀN VIETTEL",
    
    # VNPT
    "vnpt_san_luong": "SẢN LƯỢNG VNPT",
    "vnpt_ty_le_cp": "Tỷ lệ CP VNPT",
    "vnpt_thanh_tien": "THÀNH TIỀN VNPT",
    
    # VMS
    "vms_san_luong": "SẢN LƯỢNG VMS",
    "vms_ty_le_cp": "Tỷ lệ CP VMS",
    "vms_thanh_tien": "THÀNH TIỀN VMS",
    
    # Các mạng còn lại
    "khac_san_luong": "SẢN LƯỢNG Các mạng còn lại",
    "khac_ty_le_cp": "Tỷ lệ CP Các mạng còn lại",
    "khac_thanh_tien": "THÀNH TIỀN Các mạng còn lại",
    
    "tong_thanh_toan": "TỔNG THANH TOÁN",
    "cong_tien_dich_vu": "Cộng tiền Dịch vụ",
    "tien_thue_gtgt": "Tiền thuế GTGT (10%)",
    "tong_cong_tien": "Tổng cộng tiền thanh toán",
}

class DauSoDichVuCuoc(BaseModel):
    """Model cho một đầu số dịch vụ trong đối soát cước"""
    stt: int
    dau_so: str = Field(..., description="Đầu số dịch vụ")
    vnm: Optional[int] = Field(None, description="Số lượng Vietnamobile")
    viettel: Optional[int] = Field(None, description="Số lượng Viettel")
    vnpt: Optional[int] = Field(None, description="Số lượng VNPT")
    vms: Optional[int] = Field(None, description="Số lượng Mobifone")
    khac: Optional[int] = Field(None, description="Số lượng nhà mạng khác")
    tong: Optional[int] = Field(None, description="Tổng số lượng")
    don_gia: Optional[float] = Field(None, description="Đơn giá")
    thanh_tien: Optional[float] = Field(None, description="Thành tiền")
    
    # VNM
    vnm_san_luong: float = 0
    vnm_ty_le_cp: float = 0
    vnm_thanh_tien: float = 0
    
    # VIETTEL
    viettel_san_luong: float = 0
    viettel_ty_le_cp: float = 0
    viettel_thanh_tien: float = 0
    
    # VNPT
    vnpt_san_luong: float = 0
    vnpt_ty_le_cp: float = 0
    vnpt_thanh_tien: float = 0
    
    # VMS
    vms_san_luong: float = 0
    vms_ty_le_cp: float = 0
    vms_thanh_tien: float = 0
    
    # Các mạng còn lại
    khac_san_luong: float = 0
    khac_ty_le_cp: float = 0
    khac_thanh_tien: float = 0
    
    # Tổng tiền
    tong_thanh_toan: float = 0
    
    # --- START: Thêm các trường adjusted --- 
    vnm_san_luong_adjusted: Optional[float] = None
    vnm_thanh_tien_adjusted: Optional[float] = None
    viettel_san_luong_adjusted: Optional[float] = None
    viettel_thanh_tien_adjusted: Optional[float] = None
    vnpt_san_luong_adjusted: Optional[float] = None
    vnpt_thanh_tien_adjusted: Optional[float] = None
    vms_san_luong_adjusted: Optional[float] = None
    vms_thanh_tien_adjusted: Optional[float] = None
    khac_san_luong_adjusted: Optional[float] = None
    khac_thanh_tien_adjusted: Optional[float] = None
    tong_thanh_toan_adjusted: Optional[float] = None # Thêm cho tổng thanh toán nếu cần
    # --- END: Thêm các trường adjusted --- 
    
    def get_labels(self) -> Dict[str, str]:
        """Trả về các nhãn tiếng Việt cho các trường"""
        return {k: FIELD_LABELS.get(k, k) for k in self.__dict__}

class TongKetCuoc(BaseModel):
    """Model cho phần tổng kết đối soát cước"""
    cong_tien_dich_vu: float = Field(..., description="Cộng tiền dịch vụ")
    tien_thue_gtgt: Optional[float] = Field(None, description="Tiền thuế GTGT")
    tong_cong_tien: float = Field(..., description="Tổng cộng tiền")
    
    # --- START: Thêm các trường adjusted --- 
    cong_tien_dich_vu_adjusted: Optional[float] = None
    tien_thue_gtgt_adjusted: Optional[float] = None
    tong_cong_tien_adjusted: Optional[float] = None
    # --- END: Thêm các trường adjusted --- 
    
    def get_labels(self) -> Dict[str, str]:
        """Trả về các nhãn tiếng Việt cho các trường"""
        return {k: FIELD_LABELS.get(k, k) for k in self.__dict__}

class DoiSoatCuoc(BaseModel):
    """
    Model cho dữ liệu đối soát cước
    """
    thang_doi_soat: Optional[str] = Field(None, description="Tháng đối soát")
    nam_doi_soat: Optional[str] = Field(None, description="Năm đối soát")
    tu_mang: Optional[str] = Field(None, description="Từ mạng (nhà mạng nguồn)")
    den_doi_tac: Optional[str] = Field(None, description="Đến đối tác (nhà mạng đối tác)")
    partner_id: Optional[int] = Field(None, description="ID của đối tác thật từ database")
    dau_so_dich_vu: List[DauSoDichVuCuoc] = Field(default_factory=list, description="Danh sách đầu số dịch vụ")
    tong_ket: Optional[TongKetCuoc] = Field(None, description="Tổng kết đối soát")
    file_name: Optional[str] = Field(None, description="Tên file gốc")
    hop_dong_so: Optional[str] = Field(None, description="Số hợp đồng")
    
    @field_validator('tu_mang', 'den_doi_tac', mode='before')
    @classmethod
    def normalize_partner_name(cls, v: Any):
        if v and isinstance(v, str):
            # Chuyển thành chữ hoa
            return v.strip().upper()
        return v
    
    def get_labels(self) -> Dict[str, str]:
        """Trả về các nhãn tiếng Việt cho các trường"""
        return {k: FIELD_LABELS.get(k, k) for k in self.__dict__}
    
    def to_labeled_dict(self) -> Dict[str, Any]:
        """Chuyển đổi model thành dictionary với các nhãn tiếng Việt"""
        result = {
            FIELD_LABELS["thang_doi_soat"]: self.thang_doi_soat,
            f"{FIELD_LABELS['tu_mang']} {self.tu_mang}": "",
            f"{FIELD_LABELS['den_doi_tac']} {self.den_doi_tac}": "",
            "du_lieu": []
        }
        
        for item in self.dau_so_dich_vu:
            du_lieu_item = {
                FIELD_LABELS["stt"]: item.stt,
                FIELD_LABELS["dau_so"]: item.dau_so,
                
                # VNM
                FIELD_LABELS["vnm_san_luong"]: item.vnm_san_luong,
                FIELD_LABELS["vnm_ty_le_cp"]: item.vnm_ty_le_cp,
                FIELD_LABELS["vnm_thanh_tien"]: item.vnm_thanh_tien,
                
                # VIETTEL
                FIELD_LABELS["viettel_san_luong"]: item.viettel_san_luong,
                FIELD_LABELS["viettel_ty_le_cp"]: item.viettel_ty_le_cp,
                FIELD_LABELS["viettel_thanh_tien"]: item.viettel_thanh_tien,
                
                # VNPT
                FIELD_LABELS["vnpt_san_luong"]: item.vnpt_san_luong,
                FIELD_LABELS["vnpt_ty_le_cp"]: item.vnpt_ty_le_cp,
                FIELD_LABELS["vnpt_thanh_tien"]: item.vnpt_thanh_tien,
                
                # VMS
                FIELD_LABELS["vms_san_luong"]: item.vms_san_luong,
                FIELD_LABELS["vms_ty_le_cp"]: item.vms_ty_le_cp,
                FIELD_LABELS["vms_thanh_tien"]: item.vms_thanh_tien,
                
                # Khác
                FIELD_LABELS["khac_san_luong"]: item.khac_san_luong,
                FIELD_LABELS["khac_ty_le_cp"]: item.khac_ty_le_cp,
                FIELD_LABELS["khac_thanh_tien"]: item.khac_thanh_tien,
                
                FIELD_LABELS["tong_thanh_toan"]: item.tong_thanh_toan
            }
            result["du_lieu"].append(du_lieu_item)
        
        result["tong_ket"] = {
            FIELD_LABELS["cong_tien_dich_vu"]: self.tong_ket.cong_tien_dich_vu,
            FIELD_LABELS["tien_thue_gtgt"]: self.tong_ket.tien_thue_gtgt,
            FIELD_LABELS["tong_cong_tien"]: self.tong_ket.tong_cong_tien
        }
        
        return result

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

# Models for API responses
class DoiSoatCuocCreate(BaseModel):
    """Model cho API tạo mới đối soát cước"""
    thang_doi_soat: str
    tu_mang: str
    den_doi_tac: str
    hop_dong_so: Optional[str] = None
    file_path: str

class DoiSoatCuocResponse(BaseModel):
    """Model cho API response khi trả về thông tin đối soát cước"""
    id: int
    thang_doi_soat: str
    tu_mang: str
    den_doi_tac: str
    hop_dong_so: Optional[str] = None
    file_name: Optional[str] = None
    tong_tien: float
    
    class Config:
        from_attributes = True

# --- START: Thêm Schemas cho DSC đã đối soát --- 

class DoiSoatCuocListItem(BaseModel):
    """Schema cho một item trong danh sách đối soát cước đã xử lý."""
    id: int
    thang_doi_soat: str = Field(..., description="Kỳ đối soát (YYYY-MM)")
    partner: Optional[MinimalPartnerInfo] = Field(None, description="Thông tin đối tác liên quan")
    status: ReconciliationStatus = Field(..., description="Trạng thái hiện tại của bản ghi")
    created_at: datetime = Field(..., description="Thời điểm tạo bản ghi")
    updated_at: datetime = Field(..., description="Thời điểm cập nhật gần nhất")
    # Thêm các trường tóm tắt khác nếu cần, ví dụ: tong_cong_tien?
    # tong_cong_tien: Optional[float] = Field(None, description="Tổng tiền cuối cùng (nếu có)")

    model_config = ConfigDict(from_attributes=True)

class DoiSoatCuocDetailResponse(BaseModel):
    """Schema cho response chi tiết của một bản ghi đối soát cước đã xử lý."""
    id: int
    thang_doi_soat: str
    tu_mang: str
    den_doi_tac: str
    partner_id: Optional[int] = None
    partner: Optional[MinimalPartnerInfo] = Field(None, description="Thông tin đối tác liên quan")
    file_name: Optional[str] = None
    hop_dong_so: Optional[str] = None
    template_id: Optional[int] = None
    period_id: Optional[int] = None
    is_template_data: bool = False # Luôn là False cho response này
    
    # Trường trạng thái mới
    status: ReconciliationStatus
    task_id: Optional[str] = None
    error_message: Optional[str] = None
    
    # Dữ liệu chi tiết và tổng kết đã tính toán
    du_lieu: List[DauSoDichVuCuoc] = Field(default_factory=list, description="Dữ liệu chi tiết theo đầu số dịch vụ")
    tong_ket: Optional[TongKetCuoc] = Field(None, description="Tổng kết đối soát")
    
    # Timestamps
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# --- END: Thêm Schemas cho DSC đã đối soát --- 

# --- START: Thêm Schemas cho Hiệu chỉnh DSC ---

class TongKetCuocAdjustments(BaseModel):
    """Schema cho các giá trị hiệu chỉnh của phần tổng kết."""
    # Chỉ cho phép hiệu chỉnh các giá trị này
    cong_tien_dich_vu: Optional[float] = Field(None, description="Giá trị Cộng tiền dịch vụ đã hiệu chỉnh")
    tien_thue_gtgt: Optional[float] = Field(None, description="Giá trị Tiền thuế GTGT đã hiệu chỉnh")
    tong_cong_tien: Optional[float] = Field(None, description="Giá trị Tổng cộng tiền thanh toán đã hiệu chỉnh")

class DauSoDichVuCuocAdjustments(BaseModel):
    """Schema cho các giá trị hiệu chỉnh của một đầu số dịch vụ."""
    id: int = Field(..., description="ID của bản ghi DauSoDichVuCuoc cần hiệu chỉnh")
    
    # Các trường có thể hiệu chỉnh (thêm các trường khác nếu cần)
    vnm_san_luong: Optional[float] = Field(None, description="Sản lượng VNM đã hiệu chỉnh (phút)")
    vnm_thanh_tien: Optional[float] = Field(None, description="Thành tiền VNM đã hiệu chỉnh")
    viettel_san_luong: Optional[float] = Field(None, description="Sản lượng VIETTEL đã hiệu chỉnh (phút)")
    viettel_thanh_tien: Optional[float] = Field(None, description="Thành tiền VIETTEL đã hiệu chỉnh")
    vnpt_san_luong: Optional[float] = Field(None, description="Sản lượng VNPT đã hiệu chỉnh (phút)")
    vnpt_thanh_tien: Optional[float] = Field(None, description="Thành tiền VNPT đã hiệu chỉnh")
    vms_san_luong: Optional[float] = Field(None, description="Sản lượng VMS đã hiệu chỉnh (phút)")
    vms_thanh_tien: Optional[float] = Field(None, description="Thành tiền VMS đã hiệu chỉnh")
    khac_san_luong: Optional[float] = Field(None, description="Sản lượng Khác đã hiệu chỉnh (phút)")
    khac_thanh_tien: Optional[float] = Field(None, description="Thành tiền Khác đã hiệu chỉnh")
    # tong_thanh_toan sẽ được tính toán lại, không cần hiệu chỉnh trực tiếp

class DoiSoatCuocAdjustmentsPayload(BaseModel):
    """Payload cho API hiệu chỉnh bản ghi đối soát cước (DSC)."""
    tong_ket: Optional[TongKetCuocAdjustments] = Field(None, description="Giá trị hiệu chỉnh cho tổng kết chung (tùy chọn)")
    du_lieu: Optional[List[DauSoDichVuCuocAdjustments]] = Field(None, description="Danh sách hiệu chỉnh cho các đầu số dịch vụ (tùy chọn)")
    ghi_chu_hieu_chinh: Optional[str] = Field(None, description="Ghi chú lý do hiệu chỉnh (tùy chọn)") # Thêm trường ghi chú nếu cần

# --- END: Thêm Schemas cho Hiệu chỉnh DSC --- 