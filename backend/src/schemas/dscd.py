from pydantic import BaseModel, Field, root_validator
from typing import List, Optional, Dict, Any, Union
from ..models.enums import DauSoType, ReconciliationStatus
from datetime import datetime

# Dictionary ánh xạ tên field -> tên hiển thị tiếng Việt
FIELD_LABELS = {
    "thang_doi_soat": "Tháng đối soát",
    "hop_dong_so": "<PERSON> hợp đồng số",
    "stt": "STT",
    "dau_so": "ĐẦU SỐ DỊCH VỤ",
    "co_dinh_noi_hat": "CỐ ĐỊNH NỘI HẠT",
    "thoi_gian_goi": "Thời gian gọi (duration(s))",
    "cuoc": "Cước",
    "co_dinh_lien_tinh": "CỐ ĐỊNH LIÊN TỈNH",
    "di_dong": "DI ĐỘNG",
    "cuoc_1900": "1900",
    "quoc_te": "QUỐC TẾ",
    "cuoc_thue_bao": "CƯỚC THUÊ BAO",
    "thue_bao_thang": "THÁNG",
    "cam_ket_thang": "CAM KẾT/THÁNG",
    "tra_truoc_thang": "TRẢ TRƯỚC/THÁNG",
    "cuoc_thu_khach": "CƯỚC THU KHÁCH HÀNG",
    "cuoc_tra_htc": "CƯỚC HGC TRẢ HTC",
    "cong_tien_dich_vu": "Cộng tiền Dịch vụ",
    "tien_thue_gtgt": "Tiền thuế GTGT (10%)",
    "tong_cong_tien": "Tổng cộng tiền thanh toán (giảm 20%)",
    "raw_dau_so": "Đầu số gốc",
    "standardized_display": "Đầu số chuẩn hóa",
    "dau_so_type": "Loại đầu số",
    "number_count": "Số lượng",
    "cuoc_adjusted": "Cước (Hiệu chỉnh)",
    "thoi_gian_goi_adjusted": "Thời gian gọi (Hiệu chỉnh)",
    "thue_bao_thang_adjusted": "Thuê bao tháng (Hiệu chỉnh)",
    "cam_ket_thang_adjusted": "Cam kết/tháng (Hiệu chỉnh)",
    "tra_truoc_thang_adjusted": "Trả trước/tháng (Hiệu chỉnh)",
    "cong_tien_dich_vu_adjusted": "Cộng tiền Dịch vụ (Hiệu chỉnh)",
    "tien_thue_gtgt_adjusted": "Tiền thuế GTGT (Hiệu chỉnh)",
    "tong_cong_tien_adjusted": "Tổng cộng tiền thanh toán (Hiệu chỉnh)",
    "cuoc_thu_khach_adjusted": "Cước thu khách (Hiệu chỉnh)",
    "cuoc_tra_htc_adjusted": "Cước HGC trả HTC (Hiệu chỉnh)",
    "status": "Trạng thái"
}


class CuocGoi(BaseModel):
    """Model cho cặp thời gian gọi và cước phí"""
    thoi_gian_goi: float = 0
    cuoc: float = 0
    thoi_gian_goi_adjusted: Optional[float] = None
    cuoc_adjusted: Optional[float] = None
    
    def get_labels(self) -> Dict[str, str]:
        """Trả về các nhãn tiếng Việt cho các trường"""
        return {
            "thoi_gian_goi": FIELD_LABELS["thoi_gian_goi"],
            "cuoc": FIELD_LABELS["cuoc"],
            "thoi_gian_goi_adjusted": FIELD_LABELS["thoi_gian_goi_adjusted"],
            "cuoc_adjusted": FIELD_LABELS["cuoc_adjusted"],
        }


class CuocThueBao(BaseModel):
    """Model cho các loại cước thuê bao"""
    thue_bao_thang: float = 0
    cam_ket_thang: float = 0
    tra_truoc_thang: float = 0
    thue_bao_thang_adjusted: Optional[float] = None
    cam_ket_thang_adjusted: Optional[float] = None
    tra_truoc_thang_adjusted: Optional[float] = None
    
    def get_labels(self) -> Dict[str, str]:
        """Trả về các nhãn tiếng Việt cho các trường"""
        return {
            "thue_bao_thang": FIELD_LABELS["thue_bao_thang"],
            "cam_ket_thang": FIELD_LABELS["cam_ket_thang"],
            "tra_truoc_thang": FIELD_LABELS["tra_truoc_thang"],
            "thue_bao_thang_adjusted": FIELD_LABELS["thue_bao_thang_adjusted"],
            "cam_ket_thang_adjusted": FIELD_LABELS["cam_ket_thang_adjusted"],
            "tra_truoc_thang_adjusted": FIELD_LABELS["tra_truoc_thang_adjusted"],
        }


class DauSoDichVu(BaseModel):
    """Model cho một dòng trong bảng đối soát"""
    stt: int
    raw_dau_so: str
    standardized_display: str
    dau_so_type: DauSoType
    number_count: Optional[int] = None
    start_num_str: Optional[str] = None
    end_num_str: Optional[str] = None
    prefix: Optional[str] = None
    co_dinh_noi_hat: CuocGoi = Field(default_factory=CuocGoi)
    co_dinh_lien_tinh: CuocGoi = Field(default_factory=CuocGoi)
    di_dong: CuocGoi = Field(default_factory=CuocGoi)
    cuoc_1900: CuocGoi = Field(default_factory=CuocGoi)
    quoc_te: CuocGoi = Field(default_factory=CuocGoi)
    cuoc_thue_bao: CuocThueBao = Field(default_factory=CuocThueBao)
    cuoc_thu_khach: float = 0
    cuoc_tra_htc: float = 0
    cuoc_thu_khach_adjusted: Optional[float] = None
    cuoc_tra_htc_adjusted: Optional[float] = None
    
    def get_labels(self) -> Dict[str, str]:
        """Trả về các nhãn tiếng Việt cho các trường"""
        return {
            "stt": FIELD_LABELS.get("stt"),
            "raw_dau_so": FIELD_LABELS.get("raw_dau_so"),
            "standardized_display": FIELD_LABELS.get("standardized_display"),
            "dau_so_type": FIELD_LABELS.get("dau_so_type"),
            "number_count": FIELD_LABELS.get("number_count"),
            "co_dinh_noi_hat": FIELD_LABELS.get("co_dinh_noi_hat"),
            "co_dinh_lien_tinh": FIELD_LABELS["co_dinh_lien_tinh"],
            "di_dong": FIELD_LABELS["di_dong"],
            "cuoc_1900": FIELD_LABELS["cuoc_1900"],
            "quoc_te": FIELD_LABELS["quoc_te"],
            "cuoc_thue_bao": FIELD_LABELS["cuoc_thue_bao"],
            "cuoc_thu_khach": FIELD_LABELS["cuoc_thu_khach"],
            "cuoc_tra_htc": FIELD_LABELS.get("cuoc_tra_htc"),
            "cuoc_thu_khach_adjusted": FIELD_LABELS["cuoc_thu_khach_adjusted"],
            "cuoc_tra_htc_adjusted": FIELD_LABELS["cuoc_tra_htc_adjusted"],
        }


class TongKet(BaseModel):
    """Model cho phần tổng kết ở cuối bảng đối soát"""
    cong_tien_dich_vu: float
    tien_thue_gtgt: float
    tong_cong_tien: float
    cong_tien_dich_vu_adjusted: Optional[float] = None
    tien_thue_gtgt_adjusted: Optional[float] = None
    tong_cong_tien_adjusted: Optional[float] = None
    
    def get_labels(self) -> Dict[str, str]:
        """Trả về các nhãn tiếng Việt cho các trường"""
        return {
            "cong_tien_dich_vu": FIELD_LABELS["cong_tien_dich_vu"],
            "tien_thue_gtgt": FIELD_LABELS["tien_thue_gtgt"],
            "tong_cong_tien": FIELD_LABELS["tong_cong_tien"],
            "cong_tien_dich_vu_adjusted": FIELD_LABELS["cong_tien_dich_vu_adjusted"],
            "tien_thue_gtgt_adjusted": FIELD_LABELS["tien_thue_gtgt_adjusted"],
            "tong_cong_tien_adjusted": FIELD_LABELS["tong_cong_tien_adjusted"],
        }


class DoiSoatCoDinhBase(BaseModel):
    """Base model included in other schemas, might contain common fields"""
    id: Optional[int] = None # Make ID optional to allow creation before DB assignment

    class Config:
        from_attributes = True # Enable ORM mode


class DoiSoatCoDinh(DoiSoatCoDinhBase): # Inherit from base
    """
    Model cho dữ liệu đối soát cố định dùng trong tính toán nội bộ hoặc đọc từ DB ban đầu.
    Bao gồm ID.
    """
    thang_doi_soat: Optional[str] = None
    nam_doi_soat: Optional[str] = None
    tu_mang: Optional[str] = None
    den_doi_tac: Optional[str] = None
    hop_dong_so: Optional[str] = None
    partner_id: Optional[int] = Field(None, description="ID của đối tác thật từ database")
    template_id: Optional[int] = Field(None, description="ID của template gốc nếu có")
    is_template_data: Optional[bool] = False
    status: Optional[ReconciliationStatus] = None
    du_lieu: List[DauSoDichVu] = []
    tong_ket: Optional[TongKet] = None
    file_name: Optional[str] = None # Make optional if not always present
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def get_labels(self) -> Dict[str, str]:
        """Trả về các nhãn tiếng Việt cho các trường"""
        return {
            "thang_doi_soat": FIELD_LABELS["thang_doi_soat"],
            "hop_dong_so": FIELD_LABELS["hop_dong_so"],
            "status": FIELD_LABELS["status"],
        }

    def to_labeled_dict(self) -> Dict[str, Any]:
        """Chuyển đổi model thành dictionary với các nhãn tiếng Việt"""
        result = {
            FIELD_LABELS["thang_doi_soat"]: self.thang_doi_soat,
            FIELD_LABELS["hop_dong_so"]: self.hop_dong_so,
            "du_lieu": []
        }
        
        for item in self.du_lieu:
            du_lieu_item = {
                FIELD_LABELS.get("stt"): item.stt,
                FIELD_LABELS.get("standardized_display"): item.standardized_display,
                FIELD_LABELS.get("co_dinh_noi_hat"): {
                    FIELD_LABELS["thoi_gian_goi"]: item.co_dinh_noi_hat.thoi_gian_goi,
                    FIELD_LABELS["cuoc"]: item.co_dinh_noi_hat.cuoc
                },
                FIELD_LABELS["co_dinh_lien_tinh"]: {
                    FIELD_LABELS["thoi_gian_goi"]: item.co_dinh_lien_tinh.thoi_gian_goi,
                    FIELD_LABELS["cuoc"]: item.co_dinh_lien_tinh.cuoc
                },
                FIELD_LABELS["di_dong"]: {
                    FIELD_LABELS["thoi_gian_goi"]: item.di_dong.thoi_gian_goi,
                    FIELD_LABELS["cuoc"]: item.di_dong.cuoc
                },
                FIELD_LABELS["cuoc_1900"]: {
                    FIELD_LABELS["thoi_gian_goi"]: item.cuoc_1900.thoi_gian_goi,
                    FIELD_LABELS["cuoc"]: item.cuoc_1900.cuoc
                },
                FIELD_LABELS["quoc_te"]: {
                    FIELD_LABELS["thoi_gian_goi"]: item.quoc_te.thoi_gian_goi,
                    FIELD_LABELS["cuoc"]: item.quoc_te.cuoc
                },
                FIELD_LABELS["cuoc_thue_bao"]: {
                    FIELD_LABELS["thue_bao_thang"]: item.cuoc_thue_bao.thue_bao_thang,
                    FIELD_LABELS["cam_ket_thang"]: item.cuoc_thue_bao.cam_ket_thang,
                    FIELD_LABELS["tra_truoc_thang"]: item.cuoc_thue_bao.tra_truoc_thang
                },
                FIELD_LABELS["cuoc_thu_khach"]: item.cuoc_thu_khach,
                FIELD_LABELS.get("cuoc_tra_htc"): item.cuoc_tra_htc
            }
            result["du_lieu"].append(du_lieu_item)
        
        result["tong_ket"] = {
            FIELD_LABELS["cong_tien_dich_vu"]: self.tong_ket.cong_tien_dich_vu,
            FIELD_LABELS["tien_thue_gtgt"]: self.tong_ket.tien_thue_gtgt,
            FIELD_LABELS["tong_cong_tien"]: self.tong_ket.tong_cong_tien
        }
        
        return result


# Models for API responses
class DoiSoatCoDinhCreate(BaseModel):
    """Model cho API tạo mới đối soát cố định"""
    thang_doi_soat: str
    hop_dong_so: Optional[str] = None
    file_path: str


class DoiSoatCoDinhResponse(BaseModel):
    """Model cho API response khi trả về thông tin đối soát cố định"""
    id: int
    thang_doi_soat: str
    hop_dong_so: Optional[str] = None
    file_name: Optional[str] = None
    tong_tien: float
    
    class Config:
        from_attributes = True

# Bổ sung thêm các schema mới cho list view và detail view

class DoiSoatCoDinhSummary(DoiSoatCoDinhBase): # Inherit from base
    """Model tóm tắt thông tin đối soát cố định để hiển thị trong danh sách"""
    thang_doi_soat: Optional[str] = None # Make optional
    hop_dong_so: Optional[str] = None
    tu_mang: Optional[str] = None
    den_doi_tac: Optional[str] = None
    partner_id: Optional[int] = None
    partner_name: Optional[str] = None  # Tên đối tác (nếu có)
    file_name: Optional[str] = None
    tong_tien: Optional[float] = None # Use adjusted if available, else original
    tong_dau_so: Optional[int] = None
    status: Optional[ReconciliationStatus] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_template_data: Optional[bool] = False

class DoiSoatCoDinhListResponse(BaseModel):
    """Model response cho danh sách đối soát cố định (với phân trang)"""
    total: int
    items: List[DoiSoatCoDinhSummary]
    page: int  # Trang hiện tại
    page_size: int  # Kích thước trang
    pages: int  # Tổng số trang
    
    class Config:
        from_attributes = True

class DauSoDichVuDetail(DauSoDichVu, DoiSoatCoDinhBase): # Inherit from DauSoDichVu and base
    """Chi tiết về đầu số dịch vụ, bao gồm ID và các loại cước chi tiết"""
    # Inherits all fields from DauSoDichVu
    pass # No additional fields needed here currently

class DoiSoatCoDinhDetailResponse(DoiSoatCoDinh): # Inherit from the main schema
    """Schema response cho API xem chi tiết đối soát cố định"""
    # Inherits fields from DoiSoatCoDinh
    # Override du_lieu to use the Detail schema
    du_lieu: List[DauSoDichVuDetail] = []
    # Add partner name if needed
    partner_name: Optional[str] = None

# --- START: Schemas for Update (PATCH request) ---

class CuocGoiUpdate(BaseModel):
    """Schema để cập nhật giá trị hiệu chỉnh cho CuocGoi"""
    thoi_gian_goi_adjusted: Optional[float] = None
    cuoc_adjusted: Optional[float] = None

class CuocThueBaoUpdate(BaseModel):
    """Schema để cập nhật giá trị hiệu chỉnh cho CuocThueBao"""
    thue_bao_thang_adjusted: Optional[float] = None
    cam_ket_thang_adjusted: Optional[float] = None
    tra_truoc_thang_adjusted: Optional[float] = None

class TongKetUpdate(BaseModel):
    """Schema để cập nhật giá trị hiệu chỉnh cho TongKet"""
    # Cho phép cập nhật cả giá trị gốc nếu cần, hoặc chỉ giá trị hiệu chỉnh
    # cong_tien_dich_vu: Optional[float] = None # Example if gốc can be updated
    # tien_thue_gtgt: Optional[float] = None
    # tong_cong_tien: Optional[float] = None
    cong_tien_dich_vu_adjusted: Optional[float] = None
    tien_thue_gtgt_adjusted: Optional[float] = None
    tong_cong_tien_adjusted: Optional[float] = None

class DauSoDichVuUpdate(BaseModel):
    """Schema để cập nhật một dòng DauSoDichVu"""
    id: int # Bắt buộc phải có ID để biết cập nhật dòng nào
    # Optional adjusted fields for direct DauSoDichVu attributes
    cuoc_thu_khach_adjusted: Optional[float] = None
    cuoc_tra_htc_adjusted: Optional[float] = None
    # Optional nested update schemas for related CuocGoi/CuocThueBao
    co_dinh_noi_hat: Optional[CuocGoiUpdate] = None
    co_dinh_lien_tinh: Optional[CuocGoiUpdate] = None
    di_dong: Optional[CuocGoiUpdate] = None
    cuoc_1900: Optional[CuocGoiUpdate] = None
    quoc_te: Optional[CuocGoiUpdate] = None
    cuoc_thue_bao: Optional[CuocThueBaoUpdate] = None

class DoiSoatCoDinhUpdate(BaseModel):
    """Schema cho request body của API PATCH cập nhật đối soát cố định"""
    du_lieu: Optional[List[DauSoDichVuUpdate]] = None
    tong_ket: Optional[TongKetUpdate] = None
    # Có thể thêm các trường metadata khác nếu cho phép cập nhật (ví dụ: ghi chú)
    # ghi_chu: Optional[str] = None
# --- END: Schemas for Update (PATCH request) ---

# --- START: Add Schema for Create From Template Endpoint ---
class DoiSoatCoDinhCreateFromTemplate(BaseModel):
    """Schema cho request body của API tạo mới đối soát từ template."""
    template_id: int = Field(..., description="ID của mẫu đối soát để sử dụng.")
    ky_doi_soat: str = Field(..., pattern=r"^\d{4}-\d{2}$", description="Kỳ đối soát cần tạo (định dạng YYYY-MM).")
    # Add other fields if the API expects more input
# --- END: Add Schema for Create From Template Endpoint --- 

# --- Adjustment Schemas ---

class TongKetAdjustments(BaseModel):
    cong_tien_dich_vu_adjusted: Optional[float] = Field(None, description="Tổng tiền dịch vụ đã hiệu chỉnh")
    tien_thue_gtgt_adjusted: Optional[float] = Field(None, description="Tiền thuế GTGT đã hiệu chỉnh")
    tong_cong_tien_adjusted: Optional[float] = Field(None, description="Tổng cộng tiền thanh toán đã hiệu chỉnh")

    class Config:
        from_attributes = True # Sử dụng from_attributes thay vì orm_mode trong Pydantic v2
        schema_extra = {
            "example": {
                "cong_tien_dich_vu_adjusted": 1250000.50, # Use float examples
                "tien_thue_gtgt_adjusted": 125000.05,
                "tong_cong_tien_adjusted": 1375000.55
            }
        }

# Schema con cho hiệu chỉnh CuocGoi
class CuocGoiAdjustments(BaseModel):
    thoi_gian_goi_adjusted: Optional[float] = Field(None, description="Thời gian gọi đã hiệu chỉnh")
    cuoc_adjusted: Optional[float] = Field(None, description="Cước phí đã hiệu chỉnh")
    class Config: from_attributes = True

# Schema con cho hiệu chỉnh CuocThueBao
class CuocThueBaoAdjustments(BaseModel):
    thue_bao_thang_adjusted: Optional[float] = Field(None, description="Thuê bao tháng đã hiệu chỉnh")
    cam_ket_thang_adjusted: Optional[float] = Field(None, description="Cam kết tháng đã hiệu chỉnh")
    tra_truoc_thang_adjusted: Optional[float] = Field(None, description="Trả trước tháng đã hiệu chỉnh")
    class Config: from_attributes = True

# Schema cho các trường hiệu chỉnh của một Đầu số dịch vụ (đã sửa đổi)
class DauSoDichVuAdjustments(BaseModel):
    id: int = Field(..., description="ID của bản ghi Đầu số dịch vụ cần hiệu chỉnh")
    cuoc_thu_khach_adjusted: Optional[float] = Field(None, description="Cước thu khách hàng đã hiệu chỉnh")
    cuoc_tra_htc_adjusted: Optional[float] = Field(None, description="Cước trả HTC đã hiệu chỉnh")
    # --- THÊM CÁC TRƯỜNG LỒNG NHAU --- 
    co_dinh_noi_hat: Optional[CuocGoiAdjustments] = Field(None, description="Hiệu chỉnh Cước gọi Cố định Nội hạt")
    co_dinh_lien_tinh: Optional[CuocGoiAdjustments] = Field(None, description="Hiệu chỉnh Cước gọi Cố định Liên tỉnh")
    di_dong: Optional[CuocGoiAdjustments] = Field(None, description="Hiệu chỉnh Cước gọi Di động")
    cuoc_1900: Optional[CuocGoiAdjustments] = Field(None, description="Hiệu chỉnh Cước gọi 1900")
    quoc_te: Optional[CuocGoiAdjustments] = Field(None, description="Hiệu chỉnh Cước gọi Quốc tế")
    cuoc_thue_bao: Optional[CuocThueBaoAdjustments] = Field(None, description="Hiệu chỉnh Cước Thuê bao")
    # --- KẾT THÚC THÊM --- 

    class Config:
        from_attributes = True
        schema_extra = {
            "example": {
                "id": 1,
                "cuoc_thu_khach_adjusted": 5500.75,
                "co_dinh_noi_hat": { "thoi_gian_goi_adjusted": 123.45 } 
            }
        }


# Schema chính cho toàn bộ payload hiệu chỉnh (đã sửa đổi)
class DoiSoatCoDinhAdjustmentsPayload(BaseModel):
    tong_ket: Optional[TongKetAdjustments] = Field(None, description="Dữ liệu hiệu chỉnh cho phần Tổng kết")
    du_lieu: Optional[List[DauSoDichVuAdjustments]] = Field(None, description="Danh sách các hiệu chỉnh cho từng Đầu số dịch vụ")

    # Validator và Config giữ nguyên hoặc cập nhật ví dụ nếu cần
    @root_validator(pre=True)
    def check_at_least_one_field(cls, values):
        if not values.get('tong_ket') and not values.get('du_lieu'):
            raise ValueError('At least one of tong_ket or du_lieu must be provided for adjustments')
        return values

    class Config:
        schema_extra = {
            "example": {
                "tong_ket": {
                    "cong_tien_dich_vu_adjusted": 1250000.50,
                    "tien_thue_gtgt_adjusted": 125000.05,
                    "tong_cong_tien_adjusted": 1375000.55
                },
                "du_lieu": [
                    {
                        "id": 1,
                        "cuoc_thu_khach_adjusted": 5500.75,
                        "co_dinh_noi_hat": { "thoi_gian_goi_adjusted": 123.45 },
                        "cuoc_thue_bao": { "thue_bao_thang_adjusted": 50000.00 }
                    },
                    {
                        "id": 2,
                        "cuoc_tra_htc_adjusted": 1200.00
                    }
                ]
            }
        }