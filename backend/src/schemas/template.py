from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, ConfigDict, Field

from ..models.template import TemplateType

# Thêm schema con cho thông tin Partner cơ bản
class MinimalPartnerInfo(BaseModel):
    id: int
    name: str

    model_config = ConfigDict(from_attributes=True)

class ReconciliationTemplateBase(BaseModel):
    name: str
    description: Optional[str] = None
    template_type: str
    partner_id: Optional[int] = None

class ReconciliationTemplateCreate(ReconciliationTemplateBase):
    file_path: str
    file_name: str

class ReconciliationTemplateUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    status: Optional[str] = None
    
class ReconciliationTemplateResponse(ReconciliationTemplateBase):
    id: int
    file_path: str
    file_name: str
    uploaded_by: int
    is_active: bool
    status: str  # pending, processing, completed, failed
    task_id: Optional[str] = None
    error_message: Optional[str] = None
    processing_time: Optional[float] = None
    partner: Optional[MinimalPartnerInfo] = Field(None, description="Thông tin đối tác liên kết")
    created_at: datetime
    updated_at: datetime
    detected_type: Optional[str] = None
    detection_confidence: Optional[float] = None
    
    model_config = ConfigDict(from_attributes=True)

class ReconciliationTemplateListResponse(BaseModel):
    items: List[ReconciliationTemplateResponse]
    total: int
    page: int
    size: int

class TemplateDetectionResponse(BaseModel):
    detected_type: Optional[str] = None
    confidence: float
    filename: str

class ProcessRequest(BaseModel):
    template_id: int
    
class ProcessResponse(BaseModel):
    task_id: str
    template_id: int
    status: str
    message: Optional[str] = None

# Mới thêm: Model lịch sử xử lý template
class TemplateHistoryEntry(BaseModel):
    timestamp: datetime
    status: str
    processing_time: Optional[float] = None
    message: Optional[str] = None
    error: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)

# Mới thêm: Model mapping giữa trường gốc và trường đích
class FieldMapping(BaseModel):
    source_field: str
    target_field: str
    data_type: str
    confidence: float
    
    model_config = ConfigDict(from_attributes=True)

# Mới thêm: Model response cho lịch sử 
class TemplateHistoryResponse(BaseModel):
    items: List[TemplateHistoryEntry]
    
    model_config = ConfigDict(from_attributes=True)

# Mới thêm: Model response cho field mappings
class TemplateFieldMappingsResponse(BaseModel):
    items: List[FieldMapping]
    
    model_config = ConfigDict(from_attributes=True)

# Để tương thích ngược
DoiSoatTemplateBase = ReconciliationTemplateBase
DoiSoatTemplateCreate = ReconciliationTemplateCreate
DoiSoatTemplateUpdate = ReconciliationTemplateUpdate
DoiSoatTemplateResponse = ReconciliationTemplateResponse
DoiSoatTemplateListResponse = ReconciliationTemplateListResponse 