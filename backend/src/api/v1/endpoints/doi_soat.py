from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, File, UploadFile, Form, Query, Path, Body, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import exists, orm
from typing import List, Optional, Any, Dict
from uuid import uuid4
import logging
import os
from datetime import datetime
import re
from sqlalchemy.exc import IntegrityError # Import IntegrityError

from ....core.deps import get_db
from ....core.config import settings
from ....schemas.doi_soat import (
    UploadDoiSoatResponse, ProcessDoiSoatRequest, ProcessDoiSoatResponse,
    DoiSoatCreateFromTemplateRequest, DoiSoatCreateFromTemplateResponse
)
from ....utils.doi_soat_classifier import DoiSoatClassifier
from ....utils.file_processor import save_upload_file, process_file_async
from ....models.partner import Partner
from ....models.user import User
from ....models.dscd import <PERSON>i<PERSON><PERSON><PERSON>o<PERSON><PERSON>h, DauSoDichVu
from ....models.dst_1800_1900 import DoiSoat1800_1900
from ....models.dsc import DoiSoat<PERSON>uoc
from .auth import get_current_user
from ....models.template import ReconciliationTemplate, TemplateType
from ....models.call_log import CallLog, NumberType, CallType
from sqlalchemy import extract
from ....utils.enhanced_phone_utils import matches_caller, determine_service_type
from ....utils.pricing_utils import get_pricing_rule, apply_billing_method
from ....models.enums import DauSoType
from sqlalchemy.orm import joinedload
from .... import crud, schemas # type: ignore
from ....crud import crud_dscd # Import the specific crud module
from ....crud import crud_dsc # Import the new DSC crud module
from ....crud import crud_reconciliation # Import trực tiếp crud_reconciliation
from ....core.exceptions import InvalidOperationError, NotFoundError # Import from correct location
from ....schemas.dscd import DoiSoatCoDinhDetailResponse, DoiSoatCoDinhListResponse, DoiSoatCoDinhUpdate, DoiSoatCoDinhAdjustmentsPayload # Import schema mới
from ....models.enums import ReconciliationType, ReconciliationStatus

# Cấu hình logging
logger = logging.getLogger(__name__)

router = APIRouter()

# Giới hạn kích thước file upload
MAX_UPLOAD_SIZE = settings.MAX_UPLOAD_SIZE

@router.post(
    "/create-from-template",
    response_model=DoiSoatCreateFromTemplateResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Tạo bản đối soát mới từ một template và kỳ đối soát"
)
async def create_doi_soat_from_template(
    request_data: DoiSoatCreateFromTemplateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Tạo một bản ghi đối soát mới dựa trên template và kỳ đối soát.
    
    - **template_id**: ID của mẫu đối soát.
    - **ky_doi_soat**: Kỳ đối soát (YYYY-MM).
    """
    logger.info(f"Received request to create doi soat from template {request_data.template_id} for period {request_data.ky_doi_soat}")

    template_id_int = int(request_data.template_id) # Chuyển đổi ID sang int nếu cần
    template = db.query(ReconciliationTemplate).options(orm.noload('*')).filter(
        ReconciliationTemplate.id == template_id_int
    ).first()

    if not template:
        raise NotFoundError(f"Template with ID {request_data.template_id} not found")

    partner_id = template.partner_id
    if not partner_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Template ID {request_data.template_id} does not have an associated partner."
        )

    # --- START: Logic phân nhánh theo template_type ---
    if template.template_type == TemplateType.DSCD:
        # 2. Tạo bản ghi DSCD ban đầu
        try:
            initial_record = crud.crud_dscd.create_initial_dscd_reconciliation(
                db=db,
                ky_doi_soat=request_data.ky_doi_soat,
                template_id=template_id_int,
                partner_id=partner_id
            )
            db.flush() # Đảm bảo record có ID
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating initial DSCD record: {e}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create initial reconciliation record.")

        # 3. Bắt đầu Celery task cho DSCD
        from ....worker.reconciliation_tasks import process_dscd_reconciliation
        try:
            task = process_dscd_reconciliation.delay(
                dscd_id=initial_record.id,
                template_id=template_id_int,
                ky_doi_soat=request_data.ky_doi_soat,
                partner_id=partner_id
            )
            db.commit() # Commit sau khi tạo record và gửi task thành công
        except Exception as e:
            db.rollback()
            logger.error(f"Error dispatching DSCD Celery task: {e}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to dispatch reconciliation task.")

        # 4. Trả về response
        return DoiSoatCreateFromTemplateResponse(
            id=str(initial_record.id),
            template_id=str(template.id),
            ky_doi_soat=request_data.ky_doi_soat,
            loai_doi_soat=ReconciliationType.DSCD.value,
            trang_thai=initial_record.status.value, # Lấy trạng thái từ record
            ngay_tao=initial_record.created_at,
            task_id=str(task.id)
        )

    elif template.template_type == TemplateType.DSC:
        # 2. Tạo bản ghi DSC ban đầu
        try:
            initial_record = crud.crud_dsc.create_initial_dsc_reconciliation(
                db=db,
                ky_doi_soat=request_data.ky_doi_soat,
                template_id=template_id_int,
                partner_id=partner_id
            )
            db.flush()
        except NotFoundError as nf_err:
             db.rollback()
             logger.error(f"Error during initial DSC creation (dependency not found): {nf_err}")
             raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(nf_err))
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating initial DSC record: {e}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create initial reconciliation record.")

        # 3. Bắt đầu Celery task cho DSC
        from ....worker.reconciliation_tasks import process_dsc_reconciliation
        try:
            task = process_dsc_reconciliation.delay(
                dsc_id=initial_record.id,
                template_id=template_id_int,
                ky_doi_soat=request_data.ky_doi_soat,
                partner_id=partner_id
            )
            db.commit() # Commit sau khi tạo record và gửi task thành công
        except Exception as e:
            db.rollback()
            logger.error(f"Error dispatching DSC Celery task: {e}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to dispatch reconciliation task.")
            
        # 4. Trả về response
        return DoiSoatCreateFromTemplateResponse(
            id=str(initial_record.id),
            template_id=str(template.id),
            ky_doi_soat=request_data.ky_doi_soat,
            loai_doi_soat=ReconciliationType.DSC.value, # Sửa lại loại đối soát
            trang_thai=initial_record.status.value, # Lấy trạng thái từ record
            ngay_tao=initial_record.created_at,
            task_id=str(task.id)
        )

    else:
        # Xử lý các loại template khác nếu cần, hoặc báo lỗi
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Template type '{template.template_type}' is not supported for reconciliation creation yet."
        )
    # --- END: Logic phân nhánh theo template_type ---

# --- START: Add GET Endpoint for DSCD Detail --- 
@router.get(
    "/dscd/{dscd_id}",
    response_model=DoiSoatCoDinhDetailResponse,
    status_code=status.HTTP_200_OK,
    summary="Lấy thông tin chi tiết một bản ghi đối soát cố định",
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Bản ghi đối soát cố định không tồn tại"},
    },
)
def read_dscd_reconciliation(
    dscd_id: int = Path(..., title="ID của bản ghi đối soát cố định cần xem chi tiết"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user), # Add authentication if needed
):
    """
    Lấy thông tin chi tiết của một bản ghi đối soát cố định (DSCD) theo ID.
    
    Bao gồm thông tin metadata, danh sách các đầu số dịch vụ với chi tiết cước,
    và thông tin tổng kết.
    """
    logger.info(f"Fetching DSCD reconciliation details for ID: {dscd_id}")
    
    # Call the CRUD function to get the detailed dictionary
    dscd_details = crud.crud_dscd.get_dscd_reconciliation_by_id(db=db, dscd_id=dscd_id)
    
    if dscd_details is None:
        logger.warning(f"DSCD reconciliation with ID {dscd_id} not found in database.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Bản ghi đối soát cố định với ID {dscd_id} không tồn tại.",
        )
        
    # FastAPI will automatically validate the returned dict against DoiSoatCoDinhDetailResponse
    # If the keys/types don't match, it will raise an internal server error during response validation.
    logger.info(f"Successfully retrieved DSCD details for ID: {dscd_id}")
    return dscd_details
# --- END: Add GET Endpoint for DSCD Detail --- 

# --- START: Add GET Endpoint for DSC Detail ---
@router.get(
    "/dsc/{dsc_id}",
    response_model=schemas.dsc.DoiSoatCuocDetailResponse, # Sử dụng schema chi tiết DSC
    status_code=status.HTTP_200_OK,
    summary="Lấy thông tin chi tiết một bản ghi đối soát cước (DSC)",
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Bản ghi đối soát cước không tồn tại"},
    },
)
def read_dsc_reconciliation(
    dsc_id: int = Path(..., title="ID của bản ghi đối soát cước cần xem chi tiết"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user), # Thêm xác thực nếu cần
):
    """
    Lấy thông tin chi tiết của một bản ghi đối soát cước (DSC) đã xử lý theo ID.
    Chỉ trả về các bản ghi có is_template_data = False.
    """
    logger.info(f"Fetching DSC reconciliation details for ID: {dsc_id}")
    
    # Gọi CRUD function mới cho DSC
    dsc_details = crud.crud_dsc.get_dsc_reconciliation_by_id(db=db, dsc_id=dsc_id)
    
    if dsc_details is None:
        logger.warning(f"DSC reconciliation with ID {dsc_id} not found in database.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Bản ghi đối soát cước với ID {dsc_id} không tồn tại hoặc là dữ liệu mẫu.",
        )
        
    logger.info(f"Successfully retrieved DSC details for ID: {dsc_id}")
    # FastAPI sẽ tự validate dict trả về theo DoiSoatCuocDetailResponse
    return dsc_details
# --- END: Add GET Endpoint for DSC Detail ---

# --- START: Add GET Endpoint for DSCD List --- 
@router.get(
    "/dscd",
    response_model=DoiSoatCoDinhListResponse,
    status_code=status.HTTP_200_OK,
    summary="Lấy danh sách các bản ghi đối soát cố định",
)
def list_dscd_reconciliations(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user), # Authentication
    # Filtering parameters
    thang_doi_soat: Optional[str] = Query(
        None, 
        description="Lọc theo tháng đối soát (YYYY-MM)",
        regex=r"^\d{4}-\d{2}$" # Basic regex validation
    ),
    partner_id: Optional[int] = Query(None, description="Lọc theo ID đối tác"),
    is_template_data: Optional[bool] = Query(None, description="Lọc theo loại dữ liệu (template hay thực tế)"),
    # Sorting parameters
    sort_by: str = Query("created_at", description="Trường để sắp xếp"),
    sort_order: str = Query("desc", description="Thứ tự sắp xếp (asc/desc)"),
    # Pagination parameters
    skip: int = Query(0, ge=0, description="Số bản ghi bỏ qua"),
    limit: int = Query(10, ge=1, le=100, description="Số bản ghi tối đa trả về"), # Add limits
):
    """
    Lấy danh sách các bản ghi đối soát cố định (DSCD) với tùy chọn lọc,
    sắp xếp và phân trang.
    """
    logger.info(
        f"Fetching DSCD list with filters: thang={thang_doi_soat}, partner={partner_id}, "
        f"is_template={is_template_data}, sort={sort_by}:{sort_order}, skip={skip}, limit={limit}"
    )
    
    # Validate sort_order
    if sort_order.lower() not in ['asc', 'desc']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Giá trị sort_order không hợp lệ. Chỉ chấp nhận 'asc' hoặc 'desc'."
        )
        
    # Call the CRUD function
    # The CRUD function already handles validation of sort_by field
    try:
        result = crud.crud_dscd.get_dscd_reconciliations(
            db=db,
            thang_doi_soat=thang_doi_soat,
            partner_id=partner_id,
            is_template_data=is_template_data,
            sort_by=sort_by,
            sort_order=sort_order,
            skip=skip,
            limit=limit,
        )
        logger.info(f"Retrieved {len(result.get('items', []))} DSCD records (Total: {result.get('total', 0)})")
        return result
    except Exception as e:
        logger.exception(f"Error fetching DSCD list: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Lỗi xảy ra khi lấy danh sách đối soát."
        )
# --- END: Add GET Endpoint for DSCD List --- 

# --- START: Add PATCH endpoint for updating DSCD reconciliation ---
@router.patch("/dscd/{dscd_id}", response_model=DoiSoatCoDinhDetailResponse, status_code=status.HTTP_200_OK)
def update_dscd_reconciliation_endpoint(
    *, # Make args keyword-only
    db: Session = Depends(get_db),
    dscd_id: int,
    update_data: DoiSoatCoDinhUpdate = Body(...), # Use the imported schema
    current_user: User = Depends(get_current_user) # Add authentication dependency
) -> Any:
    """
    Update an existing DSCD reconciliation record with adjusted values.
    
    Allows partial updates based on the fields provided in the request body.
    Updates the status to ADJUSTED if changes are made.
    """
    logger.info(f"Received request to update DSCD reconciliation ID: {dscd_id} by user {current_user.email}")
    try:
        # Call the CRUD function to perform the update
        updated_reconciliation_model = crud.crud_dscd.update_dscd_reconciliation(
            db=db, 
            dscd_id=dscd_id, 
            update_data=update_data
        )
        
        # CRUD returns None if not found OR if no effective change was made
        if updated_reconciliation_model is None:
            # Check if it exists at all
            exists_check = crud.crud_dscd.get_dscd_reconciliation_by_id(db=db, dscd_id=dscd_id)
            if exists_check is None:
                 logger.warning(f"Attempted to update non-existent DSCD reconciliation ID: {dscd_id}")
                 raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Reconciliation with ID {dscd_id} not found.")
            else:
                 # It exists, but no changes were applied (e.g., submitted identical adjusted data)
                 logger.info(f"No effective change applied to DSCD ID: {dscd_id}. Returning original data.")
                 # Return the existing data as nothing changed
                 return exists_check

        logger.info(f"Successfully updated DSCD reconciliation ID: {dscd_id}. Status is now {updated_reconciliation_model.status.value}")
        
        detailed_updated_data = crud.crud_dscd.get_dscd_reconciliation_by_id(db=db, dscd_id=updated_reconciliation_model.id)
        
        if detailed_updated_data is None:
            logger.error(f"Failed to retrieve details for successfully updated DSCD ID: {dscd_id}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve updated reconciliation details after successful update.")
            
        return detailed_updated_data
        
    except InvalidOperationError as ioe: # Catch the specific imported exception
        logger.warning(f"Invalid operation updating DSCD reconciliation ID {dscd_id}: {ioe}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(ioe))
    except ValueError as ve: # Catch potential validation errors raised by CRUD
        logger.warning(f"Validation error updating DSCD reconciliation ID {dscd_id}: {ve}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ve))
    except HTTPException: # Re-raise HTTP exceptions directly (like 404, 409)
        raise
    except Exception as e:
        logger.exception(f"Error updating DSCD reconciliation ID {dscd_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error updating reconciliation.")
# --- END: Add PATCH endpoint for updating DSCD reconciliation ---

# --- START: Add POST endpoint for finalizing DSCD reconciliation ---
@router.post(
    "/dscd/{dscd_id}/finalize", 
    response_model=DoiSoatCoDinhDetailResponse, # Return detail on success
    status_code=status.HTTP_200_OK,
    summary="Chốt một bản ghi đối soát cố định",
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Bản ghi đối soát không tồn tại"},
        status.HTTP_409_CONFLICT: {"description": "Không thể chốt bản ghi ở trạng thái hiện tại"}, # 409 Conflict for state issues
    }
)
def finalize_dscd_reconciliation_endpoint(
    *, # Make args keyword-only
    db: Session = Depends(get_db),
    dscd_id: int = Path(..., title="ID của bản ghi đối soát cần chốt"),
    current_user: User = Depends(get_current_user), # Add authentication
) -> Any:
    """
    Chốt một bản ghi đối soát cố định (DSCD).
    
    Hành động này cập nhật trạng thái của bản ghi thành FINALIZED và 
    ngăn chặn các chỉnh sửa tiếp theo (logic này nên được kiểm tra ở endpoint UPDATE/PATCH).
    Chỉ có thể chốt các bản ghi ở trạng thái CALCULATED hoặc ADJUSTED.
    """
    logger.info(f"Received request to finalize DSCD reconciliation ID: {dscd_id} by user {current_user.email}")
    try:
        # Call the CRUD function to finalize
        finalized_record = crud.crud_dscd.finalize_dscd_reconciliation(db=db, dscd_id=dscd_id)
        
        # CRUD returns None if not found
        if finalized_record is None:
            logger.warning(f"Attempted to finalize non-existent DSCD reconciliation ID: {dscd_id}")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Bản ghi đối soát với ID {dscd_id} không tồn tại.")

        logger.info(f"Successfully finalized DSCD reconciliation ID: {dscd_id}")
        
        # Fetch the detailed view to return the updated status and data
        detailed_finalized_data = crud.crud_dscd.get_dscd_reconciliation_by_id(db=db, dscd_id=finalized_record.id)
        
        if detailed_finalized_data is None:
            # Should not happen if finalize was successful, indicates data inconsistency
            logger.error(f"Failed to retrieve details for successfully finalized DSCD ID: {dscd_id}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Không thể lấy chi tiết bản ghi sau khi chốt.")
            
        return detailed_finalized_data

    except InvalidOperationError as ioe: # Catch the specific imported exception
        logger.warning(f"Cannot finalize DSCD ID {dscd_id}: {ioe}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(ioe))
    except HTTPException: # Re-raise HTTP exceptions (like 404, 409)
        raise
    except Exception as e:
        logger.exception(f"Error finalizing DSCD reconciliation ID {dscd_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Lỗi máy chủ nội bộ khi chốt đối soát.")
# --- END: Add POST endpoint for finalizing DSCD reconciliation ---

# --- START: Add DELETE endpoint for DSCD reconciliation ---
@router.delete(
    "/dscd/{dscd_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Xóa một bản ghi đối soát cố định",
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Bản ghi đối soát không tồn tại"},
        status.HTTP_409_CONFLICT: {"description": "Không thể xóa bản ghi (ví dụ: đã chốt)"},
        status.HTTP_403_FORBIDDEN: {"description": "Không có quyền xóa"}, # Assuming permission checks might be added
    },
    response_description="Không có nội dung trả về khi xóa thành công."
)
def delete_dscd_reconciliation_endpoint(
    *, # Make args keyword-only
    db: Session = Depends(get_db),
    dscd_id: int = Path(..., title="ID của bản ghi đối soát cần xóa"),
    current_user: User = Depends(get_current_user), # Add authentication/authorization
): # No response body for 204
    """
    Xóa một bản ghi đối soát cố định (DSCD).
    
    Hành động này sẽ xóa vĩnh viễn bản ghi và các dữ liệu liên quan (nếu cascade được cấu hình).
    Có thể không cho phép xóa các bản ghi đã được chốt (FINALIZED).
    """
    logger.info(f"Received request to delete DSCD reconciliation ID: {dscd_id} by user {current_user.email}")
    
    # Add permission check if needed (e.g., only admin or owner can delete)
    # if not crud.user.is_admin(current_user): # Example permission check
    #     raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="User does not have permission to delete.")
        
    try:
        # Call the CRUD function to delete
        deleted = crud.crud_dscd.delete_dscd_reconciliation(db=db, dscd_id=dscd_id)
        
        # CRUD returns False if not found
        if not deleted:
            logger.warning(f"Attempted to delete non-existent DSCD reconciliation ID: {dscd_id}")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Bản ghi đối soát với ID {dscd_id} không tồn tại.")

        logger.info(f"Successfully deleted DSCD reconciliation ID: {dscd_id}")
        # No return value needed for 204 No Content response
        return # Return None or nothing for 204

    except InvalidOperationError as ioe: # Catch the specific imported exception
        logger.warning(f"Cannot delete DSCD ID {dscd_id}: {ioe}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(ioe))
    except HTTPException: # Re-raise other HTTP exceptions (404, 409, 403 etc.)
        raise
    except Exception as e:
        logger.exception(f"Error deleting DSCD reconciliation ID {dscd_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Lỗi máy chủ nội bộ khi xóa đối soát.")
# --- END: Add DELETE endpoint for DSCD reconciliation ---

# --- START: Add Unified GET Endpoint for All Reconciliations --- 
@router.get(
    "/", 
    response_model=schemas.doi_soat.ReconciliationListResponse, 
    summary="Lấy danh sách thống nhất tất cả các bản ghi đối soát"
)
def list_all_reconciliations(
    db: Session = Depends(get_db),
    # Filters
    reconciliation_type: Optional[ReconciliationType] = Query(None, description="Lọc theo loại đối soát (DSCD, DSC, ...)"),
    ky_doi_soat: Optional[str] = Query(None, regex=r"^\d{4}-\d{2}$", description="Lọc theo kỳ đối soát (YYYY-MM)"), # Added regex validation
    partner_id: Optional[int] = Query(None, description="Lọc theo ID đối tác"),
    filter_status: Optional[ReconciliationStatus] = Query(None, alias="status", description="Lọc theo trạng thái đối soát"),
    # Sorting & Pagination
    sort_by: str = Query("created_at", description="Trường để sắp xếp (e.g., id, ky_doi_soat, partner_name, status, created_at)"),
    sort_order: str = Query("desc", description="Thứ tự sắp xếp (asc hoặc desc)"),
    skip: int = Query(0, ge=0, description="Số lượng bản ghi bỏ qua"),
    limit: int = Query(10, ge=1, le=100, description="Số lượng bản ghi trả về"),
    current_user: User = Depends(get_current_user) # Add auth
) -> Any:
    """
    Retrieve a unified list of all reconciliation records across different types 
    (currently only DSCD implemented) with filtering, sorting, and pagination.
    """
    logger.info(f"[LIST ALL] Received request by user {current_user.email}. Filters: type='{reconciliation_type}', month='{ky_doi_soat}', partner='{partner_id}', status='{filter_status}'. Sorting: sort='{sort_by}:{sort_order}'. Pagination: skip={skip}, limit={limit}")
    try:
        logger.debug("[LIST ALL] Calling crud_reconciliation.get_all_reconciliations...")
        # Pass the renamed variable 'filter_status' to the CRUD function
        results = crud_reconciliation.get_all_reconciliations(
            db=db,
            reconciliation_type=reconciliation_type,
            ky_doi_soat=ky_doi_soat,
            partner_id=partner_id,
            status=filter_status, # Pass the renamed variable
            sort_by=sort_by,
            sort_order=sort_order,
            skip=skip,
            limit=limit
        )
        logger.info(f"[LIST ALL] crud_reconciliation returned {results.get('total', 0)} total items.")
        logger.debug(f"[LIST ALL] Returning results: {results}")
        return results # CRUD function returns the dict structure expected by the schema
        
    except ValueError as ve: # Catch potential errors like invalid sort_by field from CRUD
        logger.warning(f"[LIST ALL] Value error during listing: {ve}", exc_info=True) 
        # Fix: Use the imported fastapi.status module correctly
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ve))
    except Exception as e:
        logger.exception(f"[LIST ALL] Unexpected error fetching unified list: {e}")
        # Fix: Use the imported fastapi.status module correctly
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Lỗi máy chủ nội bộ khi lấy danh sách đối soát.")
# --- END: Add Unified GET Endpoint for All Reconciliations --- 

@router.post("/create-from-template", response_model=schemas.dscd.DoiSoatCoDinhDetailResponse)
def create_reconciliation_from_template(
    *, # Make all args keyword-only
    db: Session = Depends(get_db),
    template_data: schemas.dscd.DoiSoatCoDinhCreateFromTemplate, # Body is the schema
    current_user: User = Depends(get_current_user), # Add authentication
) -> Any:
    """
    API endpoint to create a new DSCD reconciliation record based on processed data
    from a template.
    """
    logger.info(f"Received request to create DSCD reconciliation for period: {template_data.ky_doi_soat} by user {current_user.email}")
    try:
        # Delegate the complex creation logic to the CRUD function
        created_reconciliation = crud.dscd.create_dscd_reconciliation(
            db=db,
            ky_doi_soat=template_data.ky_doi_soat,
            template_id=template_data.template_id,
            partner_id=template_data.partner_id,
            processed_results=template_data.processed_results, # Pass the actual data
            summary_totals=template_data.summary_totals,
            is_template_data=False # Explicitly create actual data
        )
        logger.info(f"Successfully created DSCD reconciliation with ID: {created_reconciliation.id}")
        
        # Return the detailed view using the GET logic/schema
        detailed_data = crud.dscd.get_dscd_reconciliation_by_id(db=db, dscd_id=created_reconciliation.id)
        if detailed_data is None:
            logger.error(f"Failed to retrieve details for newly created reconciliation ID {created_reconciliation.id}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve created reconciliation details.")
        
        return detailed_data
        
    except ValueError as ve:
        logger.warning(f"Value error creating DSCD reconciliation: {ve}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ve))
    except IntegrityError as ie: # Catch potential FK violations or unique constraints
        logger.warning(f"Database integrity error creating DSCD reconciliation: {ie}")
        db.rollback()
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="Integrity error, possibly duplicate record or invalid reference.")
    except Exception as e:
        logger.exception(f"Error creating DSCD reconciliation from template: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error creating reconciliation.")

# Note: Renaming the old /dscd list endpoint or removing it might be necessary
# to avoid confusion with the new unified / endpoint.
# Mark as deprecated for now.
@router.get("/dscd", response_model=schemas.dscd.DoiSoatCoDinhListResponse, deprecated=True, summary="[DEPRECATED] Lấy danh sách chỉ DSCD") 
def list_dscd_reconciliations(
    db: Session = Depends(get_db),
    thang_doi_soat: Optional[str] = Query(None, description="Filter by reconciliation month (YYYY-MM)"),
    partner_id: Optional[int] = Query(None, description="Filter by partner ID"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_order: str = Query("desc", description="Sort order (asc or desc)"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of records to return"),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Retrieve a list of DSCD reconciliations with filtering, sorting, and pagination.
    
    **DEPRECATED**: Use the unified `GET /api/v1/doi-soat/` endpoint instead.
    Only retrieves actual reconciliation data (not templates).
    """
    logger.warning(f"Deprecated endpoint /api/v1/doi-soat/dscd used by user {current_user.email}.")
    logger.info(f"Fetching DSCD list [DEPRECATED]: skip={skip}, limit={limit}, sort_by='{sort_by}', sort_order='{sort_order}', month='{thang_doi_soat}', partner='{partner_id}'")
    try:
        # Call the *old* specific CRUD function
        results = crud.dscd.get_dscd_reconciliations(
            db=db,
            thang_doi_soat=thang_doi_soat,
            partner_id=partner_id,
            is_template_data=False, # Fetch only actual data, not templates
            sort_by=sort_by,
            sort_order=sort_order,
            skip=skip,
            limit=limit
        )
        return results # The CRUD function now returns the dict structure expected by the schema
        
    except ValueError as ve:
        logger.warning(f"Invalid parameters for listing DSCD reconciliations [DEPRECATED]: {ve}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ve))
    except Exception as e:
        logger.exception(f"Error fetching DSCD reconciliation list [DEPRECATED]: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error fetching list.")

@router.get("/dscd/{dscd_id}", response_model=schemas.dscd.DoiSoatCoDinhDetailResponse)
def get_dscd_reconciliation(
    *, # Make args keyword-only
    db: Session = Depends(get_db),
    dscd_id: int,
    current_user: User = Depends(get_current_user) # Add authentication
) -> Any:
    """
    Retrieve the detailed information for a specific DSCD reconciliation.
    """
    logger.info(f"Fetching details for DSCD reconciliation ID: {dscd_id} by user {current_user.email}")
    try:
        reconciliation = crud.dscd.get_dscd_reconciliation_by_id(db=db, dscd_id=dscd_id)
        if reconciliation is None:
            logger.warning(f"DSCD reconciliation with ID {dscd_id} not found.")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reconciliation not found")
        return reconciliation # CRUD function returns dict compatible with schema
    except HTTPException: # Re-raise HTTP exceptions directly
        raise
    except Exception as e:
        logger.exception(f"Error fetching DSCD reconciliation details for ID {dscd_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error fetching details.")

# --- START: Add PUT endpoint for updating DSCD adjustments ---
@router.put(
    "/dscd/{dscd_id}/adjustments",
    response_model=schemas.dscd.DoiSoatCoDinhDetailResponse, # Trả về chi tiết sau khi cập nhật
    status_code=status.HTTP_200_OK,
    summary="Cập nhật các giá trị hiệu chỉnh cho đối soát cố định",
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Bản ghi đối soát không tồn tại"},
        status.HTTP_409_CONFLICT: {"description": "Không thể hiệu chỉnh bản ghi ở trạng thái hiện tại (ví dụ: đã chốt)"},
        status.HTTP_422_UNPROCESSABLE_ENTITY: {"description": "Dữ liệu hiệu chỉnh không hợp lệ"},
    }
)
def update_dscd_adjustments(
    *,
    db: Session = Depends(get_db),
    dscd_id: int = Path(..., title="ID của bản ghi đối soát cố định cần hiệu chỉnh"),
    payload: DoiSoatCoDinhAdjustmentsPayload = Body(..., description="Dữ liệu hiệu chỉnh cho tổng kết và/hoặc các đầu số dịch vụ"),
    current_user: User = Depends(get_current_user) # Đảm bảo xác thực
) -> Any:
    """
    Cập nhật các giá trị đã hiệu chỉnh cho một bản ghi đối soát cố định (DSCD).

    Endpoint này nhận ID của bản ghi DSCD và một payload chứa:
    - `tong_ket` (tùy chọn): Chứa các giá trị hiệu chỉnh cho phần tổng kết.
    - `du_lieu` (tùy chọn): Một danh sách các đối tượng, mỗi đối tượng chứa `id` của
      `DauSoDichVu` và các giá trị hiệu chỉnh tương ứng.

    Chỉ có thể hiệu chỉnh các bản ghi chưa ở trạng thái FINALIZED.
    Sau khi hiệu chỉnh thành công, trạng thái của bản ghi sẽ được cập nhật thành ADJUSTED.
    """
    logger.info(f"Received request to update adjustments for DSCD ID: {dscd_id} by user {current_user.email} with payload: {payload.dict()}") # Log payload

    try:
        # Fix 1: Đổi tên tham số từ payload= thành adjustments=
        updated_record_model = crud.crud_dscd.update_dscd_adjustments(db=db, dscd_id=dscd_id, adjustments=payload) 

        if updated_record_model is None:
             # CRUD function should raise specific exceptions or return None if not found/error
             # Re-check existence for 404, rely on CRUD for other errors (like 409)
             exists_check = crud.crud_dscd.get_dscd_reconciliation_by_id(db=db, dscd_id=dscd_id) # Use the detailed getter
             if exists_check is None:
                 logger.warning(f"Attempted to update adjustments for non-existent DSCD ID: {dscd_id}")
                 raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Bản ghi đối soát với ID {dscd_id} không tồn tại.")
             else:
                 # This case might happen if the update didn't change anything or failed validation handled by CRUD
                 logger.error(f"CRUD operation failed to update adjustments for DSCD ID: {dscd_id}. Returning current data.")
                 # Consider returning the existing data or a more specific error from CRUD. For now, raise 500.
                 raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Lỗi không xác định khi cập nhật hiệu chỉnh.")

        logger.info(f"Successfully updated adjustments for DSCD ID: {dscd_id}. Status is now {updated_record_model.status.value if updated_record_model.status else 'UNKNOWN'}")

        # Fetch the detailed view again to return the fully populated response
        detailed_updated_data = crud.crud_dscd.get_dscd_reconciliation_by_id(db=db, dscd_id=updated_record_model.id)

        if detailed_updated_data is None:
            # This should ideally not happen if the update was successful
            logger.error(f"Failed to retrieve details after successfully updating adjustments for DSCD ID: {dscd_id}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Không thể lấy chi tiết bản ghi sau khi cập nhật hiệu chỉnh.")

        return detailed_updated_data

    except InvalidOperationError as ioe: # Catch the specific imported exception
        logger.warning(f"Invalid operation updating adjustments for DSCD ID {dscd_id}: {ioe}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(ioe))
    except ValueError as ve: # Catch potential validation errors (e.g., from CRUD logic)
        logger.warning(f"Validation error updating adjustments for DSCD ID {dscd_id}: {ve}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ve))
    except HTTPException: # Re-raise HTTP exceptions directly
        raise
    except Exception as e:
        logger.exception(f"Unexpected error updating adjustments for DSCD ID {dscd_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Lỗi máy chủ nội bộ khi cập nhật hiệu chỉnh.")
# --- END: Add PUT endpoint for updating DSCD adjustments ---

# --- START: Add PUT Endpoint for DSC Adjustments ---
@router.put(
    "/dsc/{dsc_id}/adjustments",
    response_model=schemas.dsc.DoiSoatCuocDetailResponse, # Trả về chi tiết sau khi cập nhật
    status_code=status.HTTP_200_OK,
    summary="Cập nhật các giá trị hiệu chỉnh cho đối soát cước (DSC)",
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Bản ghi đối soát không tồn tại"},
        status.HTTP_409_CONFLICT: {"description": "Không thể hiệu chỉnh bản ghi ở trạng thái hiện tại (ví dụ: đã chốt)"},
        status.HTTP_422_UNPROCESSABLE_ENTITY: {"description": "Dữ liệu hiệu chỉnh không hợp lệ (ví dụ: ID con không tồn tại)"},
    }
)
def update_dsc_adjustments_endpoint(
    *,
    db: Session = Depends(get_db),
    dsc_id: int = Path(..., title="ID của bản ghi đối soát cước cần hiệu chỉnh"),
    payload: schemas.dsc.DoiSoatCuocAdjustmentsPayload = Body(..., description="Dữ liệu hiệu chỉnh"),
    current_user: User = Depends(get_current_user) # Đảm bảo xác thực
) -> Any:
    """
    Cập nhật các giá trị đã hiệu chỉnh cho một bản ghi đối soát cước (DSC).
    
    Endpoint này nhận ID của bản ghi DSC và một payload chứa các giá trị hiệu chỉnh
    cho các đầu số dịch vụ (`du_lieu`). Tổng kết sẽ được tự động tính toán lại.
    
    Chỉ có thể hiệu chỉnh các bản ghi chưa ở trạng thái FINALIZED.
    Sau khi hiệu chỉnh thành công, trạng thái của bản ghi sẽ được cập nhật thành ADJUSTED.
    """
    logger.info(f"Received request to update adjustments for DSC ID: {dsc_id} by user {current_user.email}")

    try:
        # Gọi CRUD function để cập nhật
        updated_record = crud.crud_dsc.update_dsc_adjustments(db=db, dsc_id=dsc_id, adjustments=payload)

        # Lấy lại dữ liệu chi tiết để trả về (vì CRUD trả về model ORM)
        detailed_response = crud.crud_dsc.get_dsc_reconciliation_by_id(db=db, dsc_id=updated_record.id)
        
        if detailed_response is None:
             # Trường hợp này không nên xảy ra nếu update thành công
             logger.error(f"Failed to retrieve details for successfully adjusted DSC ID: {dsc_id}")
             raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve updated details after adjustment.")
             
        logger.info(f"Successfully updated adjustments for DSC ID: {dsc_id}")
        return detailed_response

    except NotFoundError as nf_err:
        logger.warning(f"Not found error during DSC adjustment for ID {dsc_id}: {nf_err}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(nf_err))
    except InvalidOperationError as ioe:
        logger.warning(f"Invalid operation during DSC adjustment for ID {dsc_id}: {ioe}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(ioe))
    except ValueError as ve: # Có thể xảy ra nếu ID con không hợp lệ trong CRUD
        logger.warning(f"Value error during DSC adjustment for ID {dsc_id}: {ve}")
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(ve))
    except HTTPException: # Re-raise các HTTP exception khác
        raise
    except Exception as e:
        logger.exception(f"Unexpected error updating adjustments for DSC ID {dsc_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Lỗi máy chủ nội bộ khi cập nhật hiệu chỉnh.")
# --- END: Add PUT Endpoint for DSC Adjustments ---

# --- START: Add POST Endpoint for Finalizing DSC ---
@router.post(
    "/dsc/{dsc_id}/finalize", 
    response_model=schemas.dsc.DoiSoatCuocDetailResponse, # Trả về chi tiết sau khi chốt
    status_code=status.HTTP_200_OK,
    summary="Chốt một bản ghi đối soát cước (DSC)",
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Bản ghi đối soát không tồn tại"},
        status.HTTP_409_CONFLICT: {"description": "Không thể chốt bản ghi ở trạng thái hiện tại"},
    }
)
def finalize_dsc_reconciliation_endpoint(
    *,
    db: Session = Depends(get_db),
    dsc_id: int = Path(..., title="ID của bản ghi đối soát cước cần chốt"),
    current_user: User = Depends(get_current_user), # Thêm xác thực
) -> Any:
    """
    Chốt một bản ghi đối soát cước (DSC).
    
    Hành động này cập nhật trạng thái bản ghi thành FINALIZED.
    Chỉ có thể chốt các bản ghi ở trạng thái CALCULATED hoặc ADJUSTED.
    """
    logger.info(f"Received request to finalize DSC ID: {dsc_id} by user {current_user.email}")
    try:
        # Gọi CRUD function
        finalized_record = crud.crud_dsc.finalize_dsc_reconciliation(db=db, dsc_id=dsc_id)
        
        # Lấy lại chi tiết để trả về response
        detailed_response = crud.crud_dsc.get_dsc_reconciliation_by_id(db=db, dsc_id=finalized_record.id)
        if detailed_response is None:
            logger.error(f"Failed to retrieve details for finalized DSC ID {dsc_id}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Không thể lấy chi tiết bản ghi sau khi chốt.")
            
        logger.info(f"Successfully finalized DSC ID: {dsc_id}")
        return detailed_response

    except NotFoundError as nf_err:
        logger.warning(f"Not found error during DSC finalization for ID {dsc_id}: {nf_err}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(nf_err))
    except InvalidOperationError as ioe:
        logger.warning(f"Invalid operation during DSC finalization for ID {dsc_id}: {ioe}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(ioe))
    except HTTPException: # Re-raise các HTTP exception khác
        raise
    except Exception as e:
        logger.exception(f"Unexpected error finalizing DSC ID {dsc_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Lỗi máy chủ nội bộ khi chốt đối soát.")
# --- END: Add POST Endpoint for Finalizing DSC ---

# --- START: Add DELETE Endpoint for DSC Reconciliation ---
@router.delete(
    "/dsc/{dsc_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Xóa một bản ghi đối soát cước (DSC)",
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Bản ghi đối soát không tồn tại"},
        status.HTTP_409_CONFLICT: {"description": "Không thể xóa bản ghi (ví dụ: đã chốt)"},
        # Thêm 403 nếu có kiểm tra quyền
    },
    response_description="Không có nội dung trả về khi xóa thành công."
)
def delete_dsc_reconciliation_endpoint(
    *,
    db: Session = Depends(get_db),
    dsc_id: int = Path(..., title="ID của bản ghi đối soát cước cần xóa"),
    current_user: User = Depends(get_current_user), # Thêm xác thực/ủy quyền
): # No response body for 204
    """
    Xóa một bản ghi đối soát cước (DSC).
    
    Hành động này sẽ xóa vĩnh viễn bản ghi và các dữ liệu liên quan.
    Không cho phép xóa các bản ghi đã được chốt (FINALIZED).
    """
    logger.info(f"Received request to delete DSC ID: {dsc_id} by user {current_user.email}")
        
    try:
        # Gọi CRUD function để xóa
        deleted = crud.crud_dsc.delete_dsc_reconciliation(db=db, dsc_id=dsc_id)
        
        if not deleted:
            # CRUD trả về False nếu không tìm thấy
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Bản ghi đối soát cước với ID {dsc_id} không tồn tại.")

        logger.info(f"Successfully deleted DSC ID: {dsc_id}")
        # Không cần trả về gì cho status 204
        return

    except InvalidOperationError as ioe: 
        logger.warning(f"Cannot delete DSC ID {dsc_id}: {ioe}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(ioe))
    except HTTPException: # Re-raise các HTTP exception khác (404, 409...)
        raise
    except Exception as e:
        # Bắt các lỗi khác từ DB hoặc logic
        logger.exception(f"Error deleting DSC ID {dsc_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Lỗi máy chủ nội bộ khi xóa đối soát.")
# --- END: Add DELETE Endpoint for DSC Reconciliation ---