from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ....database.session import get_db
from ....models.user import User
from ....schemas.reconciliation_period import (
    ReconciliationPeriodCreate,
    ReconciliationPeriodUpdate,
    ReconciliationPeriodResponse,
    ReconciliationPeriodListResponse,
    ReconciliationPeriod as ReconciliationPeriodSchema
)
from ....crud.reconciliation_periods import crud_reconciliation_period
from ....crud.templates import crud_template
from .auth import get_current_user

router = APIRouter()

@router.post("", response_model=ReconciliationPeriodResponse)
async def create_reconciliation_period(
    *,
    db: Session = Depends(get_db),
    data_in: ReconciliationPeriodCreate,
    current_user: User = Depends(get_current_user)
):
    """Tạo kỳ đối soát mới theo tháng và năm."""
    # <PERSON><PERSON><PERSON> tra kỳ đối soát đã tồn tại theo tháng và năm
    existing = crud_reconciliation_period.get_by_year_and_month(
        db=db, year=data_in.year, month=data_in.month
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Kỳ đối soát cho tháng {data_in.month}/{data_in.year} đã tồn tại"
        )
    
    # Tạo kỳ đối soát mới
    period = crud_reconciliation_period.create_with_owner(
        db=db, obj_in=data_in, user_id=current_user.id
    )
    
    return {
        "success": True,
        "message": "Tạo kỳ đối soát thành công",
        "data": period
    }

@router.get("", response_model=ReconciliationPeriodListResponse)
async def list_reconciliation_periods(
    *,
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    year: Optional[int] = Query(None, description="Lọc theo năm"),
    month: Optional[int] = Query(None, description="Lọc theo tháng"),
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Lấy danh sách kỳ đối soát, có thể lọc theo năm, tháng, trạng thái."""
    periods = crud_reconciliation_period.get_multi_with_filters(
        db=db,
        skip=skip,
        limit=limit,
        year=year,
        month=month,
        status=status
    )
    
    total = crud_reconciliation_period.count_with_filters(
        db=db,
        year=year,
        month=month,
        status=status
    )
    
    return {
        "success": True,
        "message": "Success",
        "total": total,
        "data": periods
    }

@router.get("/{period_id}", response_model=ReconciliationPeriodResponse)
async def get_reconciliation_period(
    *,
    db: Session = Depends(get_db),
    period_id: int,
    current_user: User = Depends(get_current_user)
):
    """Lấy thông tin chi tiết kỳ đối soát"""
    period = crud_reconciliation_period.get(db=db, id=period_id)
    if not period:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Kỳ đối soát không tồn tại"
        )
    
    return {
        "success": True,
        "message": "Success",
        "data": period
    }

@router.put("/{period_id}", response_model=ReconciliationPeriodResponse)
async def update_reconciliation_period(
    *,
    db: Session = Depends(get_db),
    period_id: int,
    data_in: ReconciliationPeriodUpdate,
    current_user: User = Depends(get_current_user)
):
    """Cập nhật kỳ đối soát"""
    period = crud_reconciliation_period.get(db=db, id=period_id)
    if not period:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Kỳ đối soát không tồn tại"
        )
    
    period = crud_reconciliation_period.update(
        db=db, db_obj=period, obj_in=data_in
    )
    
    return {
        "success": True,
        "message": "Cập nhật kỳ đối soát thành công",
        "data": period
    }

@router.delete("/{period_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_reconciliation_period(
    *,
    db: Session = Depends(get_db),
    period_id: int,
    current_user: User = Depends(get_current_user)
):
    """Xóa kỳ đối soát"""
    period = crud_reconciliation_period.get(db=db, id=period_id)
    if not period:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Kỳ đối soát không tồn tại"
        )
    
    crud_reconciliation_period.remove(db=db, id=period_id)