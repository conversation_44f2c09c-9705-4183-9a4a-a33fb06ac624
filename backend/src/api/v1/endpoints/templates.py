import os
import shutil
import uuid
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, status
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
import logging

from ....core.config import settings
from ....crud.templates import crud_template
from ....schemas.template import (
    ReconciliationTemplateCreate, 
    ReconciliationTemplateUpdate, 
    ReconciliationTemplateResponse,
    ReconciliationTemplateListResponse,
    TemplateDetectionResponse,
    ProcessResponse,
    TemplateHistoryResponse,
    TemplateHistoryEntry,
    TemplateFieldMappingsResponse,
    FieldMapping
)
from ....models.template import TemplateType
from ....database.session import get_db
from ....core.deps import get_current_user
from ....models.user import User
from ....utils.doi_soat_classifier import DoiSoatClassifier

router = APIRouter()

# G<PERSON><PERSON><PERSON> hạn kích thước file upload
MAX_UPLOAD_SIZE = settings.MAX_UPLOAD_SIZE

@router.post("/upload", response_model=ReconciliationTemplateResponse)
async def upload_template(
    name: str = Form(...),
    description: Optional[str] = Form(None),
    template_type: TemplateType = Form(...),
    auto_detect: bool = Form(False),
    partner_id: Optional[int] = Form(None),
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Upload mẫu đối soát mới
    
    - **auto_detect**: Nếu True, hệ thống sẽ tự động kiểm tra và xác minh loại mẫu đã chọn
    - **partner_id**: ID của đối tác liên quan đến mẫu đối soát
    """
    
    # Chỉ admin mới có quyền upload template
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Chỉ admin mới có quyền upload mẫu đối soát"
        )
    
    # Kiểm tra định dạng file
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Chỉ chấp nhận file Excel (.xlsx, .xls)"
        )
    
    # Kiểm tra kích thước file
    file.file.seek(0, 2)  # Di chuyển con trỏ đến cuối file
    file_size = file.file.tell()  # Lấy vị trí hiện tại (kích thước file)
    file.file.seek(0)  # Reset con trỏ về đầu file
    
    if file_size > MAX_UPLOAD_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File quá lớn. Kích thước tối đa là {MAX_UPLOAD_SIZE / (1024 * 1024)}MB"
        )
    
    # Tạo thư mục lưu trữ nếu chưa tồn tại
    templates_dir = settings.TEMPLATES_UPLOAD_DIR
    print(f"[DEBUG] Templates upload directory: {templates_dir}")
    
    try:
        os.makedirs(templates_dir, exist_ok=True)
    except Exception as e:
        print(f"[ERROR] Failed to create templates directory: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Không thể tạo thư mục lưu trữ: {str(e)}"
        )
    
    # Tạo tên file độc nhất với UUID, nhưng duy trì phần tên gốc
    file_uuid = str(uuid.uuid4())
    original_filename = file.filename
    # Tách phần tên và phần mở rộng
    original_name, file_ext = os.path.splitext(original_filename)
    
    # Kết hợp UUID với tên gốc để dễ dàng phân loại
    unique_filename = f"{file_uuid}_{original_name}{file_ext}"
    file_path = os.path.join(templates_dir, unique_filename)
    
    # Lưu file
    try:
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        print(f"[INFO] Successfully saved template file to: {file_path}")
    except Exception as e:
        print(f"[ERROR] Failed to save file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Không thể lưu file: {str(e)}"
        )
    
    # Phân loại tự động để xác minh nếu auto_detect=True
    detected_type = None
    confidence = 0.0
    
    if auto_detect:
        # Lưu lại tên file gốc để giúp phân loại
        print(f"[INFO] Auto-detecting template type for file: {original_filename}")
        
        # Phân loại trực tiếp với tên file kết hợp giúp thuật toán có thể xem xét cả tên
        detected_type, confidence = DoiSoatClassifier.classify(file_path)
        
        if detected_type:
            print(f"[INFO] Auto-detected template type: {detected_type} with confidence: {confidence}")
            
            # Nếu người dùng đã chọn một loại khác với loại được phát hiện, vẫn sử dụng loại đã chọn
            # nhưng lưu thông tin về loại được phát hiện
            if detected_type != template_type:
                print(f"[INFO] User-specified type '{template_type}' differs from detected type '{detected_type}'")
    
    # Tạo mẫu đối soát trong database
    try:
        # Thêm thông tin về tên file gốc vào mô tả nếu chưa có 
        if not description:
            description = f"Tên file gốc: {original_filename}"
        else:
            description = f"{description} | Tên file gốc: {original_filename}"
            
        template_in = ReconciliationTemplateCreate(
            name=name,
            description=description,
            template_type=template_type,  # Luôn sử dụng loại do người dùng chỉ định
            file_path=file_path,
            file_name=original_filename,  # Thêm trường file_name
            partner_id=partner_id  # Thêm partner_id
        )
        
        template = crud_template.create_with_owner(
            db=db,
            obj_in=template_in,
            owner_id=current_user.id
        )
        
        # Thêm thông tin về loại mẫu được phát hiện vào kết quả trả về
        template_dict = template.dict()
        if detected_type:
            template_dict["detected_type"] = template_type
            template_dict["detection_confidence"] = confidence
            
        return template_dict
    except Exception as e:
        # Nếu có lỗi khi lưu vào DB, xóa file đã upload
        if os.path.exists(file_path):
            os.remove(file_path)
        print(f"[ERROR] Database error when creating template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Lỗi khi lưu thông tin mẫu: {str(e)}"
        )

@router.get("/", response_model=ReconciliationTemplateListResponse)
async def list_templates(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    template_type: Optional[TemplateType] = None,
    is_active: Optional[bool] = None,
    partner_id: Optional[int] = Query(None, description="Lọc theo ID đối tác"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Lấy danh sách mẫu đối soát"""
    # # Debug log để kiểm tra thông tin người dùng
    # print(f"[DEBUG] Templates API - User: {current_user.id}, Admin: {current_user.is_admin}, Active: {current_user.is_active}")
    # print(f"[DEBUG] Templates API - Request params: skip={skip}, limit={limit}, type={template_type}, is_active={is_active}")
    
    items = crud_template.get_multi_with_filter(
        db=db,
        skip=skip,
        limit=limit,
        template_type=template_type,
        is_active=is_active,
        partner_id=partner_id
    )
    
    total = crud_template.count_with_filter(
        db=db,
        template_type=template_type,
        is_active=is_active,
        partner_id=partner_id
    )
    
    return {
        "items": items,
        "total": total,
        "page": skip // limit + 1,
        "size": limit
    }

@router.get("/{template_id}", response_model=ReconciliationTemplateResponse)
async def get_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Lấy thông tin chi tiết về một mẫu đối soát"""
    template = crud_template.get(db=db, id=template_id)
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mẫu đối soát không tồn tại"
        )
    
    return template

@router.put("/{template_id}", response_model=ReconciliationTemplateResponse)
async def update_template(
    template_id: int,
    template_in: ReconciliationTemplateUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Cập nhật thông tin mẫu đối soát"""
    # Chỉ admin mới có quyền cập nhật template
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Chỉ admin mới có quyền cập nhật mẫu đối soát"
        )
    
    template = crud_template.get(db=db, id=template_id)
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mẫu đối soát không tồn tại"
        )
    
    template = crud_template.update(
        db=db,
        db_obj=template,
        obj_in=template_in
    )
    
    return template

@router.delete("/{template_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Xóa mẫu đối soát"""
    # Chỉ admin mới có quyền xóa template
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Chỉ admin mới có quyền xóa mẫu đối soát"
        )
    
    template = crud_template.get(db=db, id=template_id)
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mẫu đối soát không tồn tại"
        )
    
    # Xóa file
    if os.path.exists(template.file_path):
        os.remove(template.file_path)
    
    crud_template.remove(db=db, id=template_id)

@router.get("/{template_id}/download")
async def download_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Download mẫu đối soát"""
    template = crud_template.get(db=db, id=template_id)
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mẫu đối soát không tồn tại"
        )
    
    # Kiểm tra xem file có tồn tại không
    if not os.path.exists(template.file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File mẫu đối soát không tồn tại"
        )
    
    # Lấy tên file gốc từ đường dẫn
    file_name = os.path.basename(template.file_path)
    
    return FileResponse(
        path=template.file_path,
        filename=f"{template.name}{os.path.splitext(file_name)[1]}",
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )

@router.post("/detect", response_model=TemplateDetectionResponse)
async def detect_template_type(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """
    Phát hiện loại mẫu từ file được tải lên mà không lưu vào hệ thống
    
    - **file**: File Excel để phân tích
    """
    # Kiểm tra định dạng file
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Chỉ chấp nhận file Excel (.xlsx, .xls)"
        )
    
    # Kiểm tra kích thước file
    file.file.seek(0, 2)  # Di chuyển con trỏ đến cuối file
    file_size = file.file.tell()  # Lấy vị trí hiện tại (kích thước file)
    file.file.seek(0)  # Reset con trỏ về đầu file
    
    if file_size > MAX_UPLOAD_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File quá lớn. Kích thước tối đa là {MAX_UPLOAD_SIZE / (1024 * 1024)}MB"
        )
    
    # Tạo thư mục tạm nếu chưa tồn tại
    temp_dir = os.path.join(settings.TEMPLATES_UPLOAD_DIR, "temp")
    os.makedirs(temp_dir, exist_ok=True)
    
    # Lưu file với tên UUID tạm thời
    file_uuid = str(uuid.uuid4())
    file_ext = os.path.splitext(file.filename)[1]
    temp_filename = f"{file_uuid}{file_ext}"
    temp_file_path = os.path.join(temp_dir, temp_filename)
    
    # Lưu tên file gốc để dùng cho phân loại
    original_filename = file.filename
    
    try:
        # Lưu file tạm
        with open(temp_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Tạo symlink hoặc copy với tên file gốc để giúp phân loại chính xác hơn
        original_temp_path = os.path.join(temp_dir, original_filename)
        try:
            # Tạo symlink (hiệu quả hơn nhưng không hoạt động trên tất cả OS)
            os.symlink(temp_file_path, original_temp_path)
            use_original_path = True
        except (OSError, AttributeError):
            # Fallback: copy file (tốn tài nguyên hơn)
            shutil.copy2(temp_file_path, original_temp_path)
            use_original_path = True
        except Exception as link_err:
            print(f"[WARNING] Không thể tạo symlink/copy với tên gốc: {str(link_err)}")
            use_original_path = False
        
        # Phân tích loại mẫu - ưu tiên sử dụng file với tên gốc
        classify_path = original_temp_path if use_original_path else temp_file_path
        
        # Ghi log tên file được sử dụng để phân loại
        print(f"[INFO] Classifying file using path: {os.path.basename(classify_path)}")
        
        # Phân tích loại mẫu
        detected_type, confidence = DoiSoatClassifier.classify(classify_path)
        
        # Trả về kết quả
        return {
            "detected_type": detected_type.value if detected_type else None,
            "confidence": confidence,
            "filename": file.filename
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Lỗi khi phân tích file: {str(e)}"
        )
    
    finally:
        # Xóa tất cả file tạm
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        if 'use_original_path' in locals() and use_original_path and os.path.exists(original_temp_path):
            os.remove(original_temp_path)

@router.post("/{template_id}/process", response_model=ProcessResponse)
async def process_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Bắt đầu quá trình phân tích mẫu đối soát
    
    - **template_id**: ID của mẫu đối soát cần phân tích
    """
    # Chỉ admin mới có quyền phân tích mẫu đối soát
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Chỉ admin mới có quyền phân tích mẫu đối soát"
        )
    
    # Kiểm tra mẫu đối soát tồn tại
    template = crud_template.get(db=db, id=template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Mẫu đối soát với ID {template_id} không tồn tại"
        )
    
    # Kiểm tra xem mẫu đã được xử lý hay đang xử lý
    if template.status == "processing":
        return ProcessResponse(
            task_id=template.task_id or "",
            template_id=template_id,
            status="processing",
            message="Mẫu đối soát đang được xử lý"
        )
    elif template.status == "completed":
        return ProcessResponse(
            task_id=template.task_id or "",
            template_id=template_id,
            status="completed",
            message="Mẫu đối soát đã được xử lý hoàn tất"
        )
    
    # Import task ở đây để tránh circular import
    from ....worker.template_tasks import process_template_file
    
    # Cập nhật trạng thái
    template.status = "pending"
    db.commit()
    
    # Gọi task Celery để xử lý
    task = process_template_file.delay(template_id)
    
    # Cập nhật task_id
    template.task_id = task.id
    db.commit()
    
    # Trả về task_id để frontend có thể theo dõi trạng thái
    return ProcessResponse(
        task_id=task.id,
        template_id=template_id,
        status="pending",
        message="Yêu cầu đã được gửi, mẫu đối soát đang được xử lý"
    )

@router.get("/{template_id}/status", response_model=ProcessResponse)
async def get_template_status(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Kiểm tra trạng thái xử lý của mẫu đối soát
    
    - **template_id**: ID của mẫu đối soát cần kiểm tra
    """
    # Kiểm tra mẫu đối soát tồn tại
    template = crud_template.get(db=db, id=template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Mẫu đối soát với ID {template_id} không tồn tại"
        )
    
    # Trả về trạng thái hiện tại
    return ProcessResponse(
        task_id=template.task_id or "",
        template_id=template_id,
        status=template.status,
        message=template.error_message if template.status == "failed" else None
    )

@router.get("/{template_id}/result")
async def get_template_result(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Lấy kết quả phân tích của mẫu đối soát
    
    - **template_id**: ID của mẫu đối soát cần lấy kết quả
    """
    # Kiểm tra mẫu đối soát tồn tại
    template = crud_template.get(db=db, id=template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Mẫu đối soát với ID {template_id} không tồn tại"
        )
    
    # Kiểm tra xem mẫu đã được xử lý chưa
    if template.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Mẫu đối soát chưa hoàn tất xử lý (trạng thái hiện tại: {template.status})"
        )
    
    # Kiểm tra xem có dữ liệu kết quả không
    if not template.processed_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Không tìm thấy dữ liệu kết quả phân tích"
        )
    
    # Trả về kết quả phân tích
    return template.processed_data

@router.get("/{template_id}/structured-data")
async def get_template_structured_data(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Lấy dữ liệu có cấu trúc của mẫu đối soát từ bảng tương ứng (doi_soat_co_dinh, doi_soat_cuoc, ...)
    
    - **template_id**: ID của mẫu đối soát cần lấy dữ liệu
    """
    # Kiểm tra mẫu đối soát tồn tại
    template = crud_template.get(db=db, id=template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Mẫu đối soát với ID {template_id} không tồn tại"
        )
    
    # Kiểm tra xem mẫu đã được xử lý chưa
    if template.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Mẫu đối soát chưa hoàn tất xử lý (trạng thái hiện tại: {template.status})"
        )
    
    # Lấy dữ liệu có cấu trúc tương ứng với loại template
    from ....models.dscd import DoiSoatCoDinh 
    from ....models.dsc import DoiSoatCuoc
    from ....models.dst_1800_1900 import DoiSoat1800_1900
    
    logger = logging.getLogger(__name__)
    logger.info(f"Lấy dữ liệu cấu trúc cho template ID {template_id}, loại {template.template_type}")
    
    structured_data = None
    if template.template_type == "dscd":
        # Lấy dữ liệu từ bảng doi_soat_co_dinh
        data = db.query(DoiSoatCoDinh).filter(
            DoiSoatCoDinh.template_id == template_id,
            DoiSoatCoDinh.is_template_data == True
        ).first()
        
        logger.info(f"Tìm thấy DoiSoatCoDinh với ID {data.id if data else None}")
        
        if data:
            logger.info(f"DoiSoatCoDinh có {len(data.du_lieu)} đầu số")
            
            # Kiểm tra các trường của dữ liệu
            for idx, ds in enumerate(data.du_lieu):
                logger.info(f"Đầu số {idx+1}: {ds.standardized_display}, Type: {ds.dau_so_type.value}, có co_dinh_noi_hat: {ds.co_dinh_noi_hat is not None}")
                
            structured_data = {
                "id": data.id,
                "thang_doi_soat": data.thang_doi_soat,
                "hop_dong_so": data.hop_dong_so,
                "tu_mang": data.tu_mang,
                "den_doi_tac": data.den_doi_tac,
                "template_id": data.template_id,
                "partner_id": data.partner_id,
                "du_lieu": [
                    {
                        "id": ds.id,
                        "stt": ds.stt,
                        "raw_dau_so": ds.raw_dau_so,
                        "standardized_display": ds.standardized_display,
                        "dau_so_type": ds.dau_so_type.value,
                        "number_count": ds.number_count,
                        "cuoc_thu_khach": ds.cuoc_thu_khach,
                        "cuoc_tra_htc": ds.cuoc_tra_htc,
                        "co_dinh_noi_hat": {
                            "thoi_gian_goi": ds.co_dinh_noi_hat.thoi_gian_goi if ds.co_dinh_noi_hat else 0,
                            "cuoc": ds.co_dinh_noi_hat.cuoc if ds.co_dinh_noi_hat else 0
                        },
                        "co_dinh_lien_tinh": {
                            "thoi_gian_goi": ds.co_dinh_lien_tinh.thoi_gian_goi if ds.co_dinh_lien_tinh else 0,
                            "cuoc": ds.co_dinh_lien_tinh.cuoc if ds.co_dinh_lien_tinh else 0
                        },
                        "di_dong": {
                            "thoi_gian_goi": ds.di_dong.thoi_gian_goi if ds.di_dong else 0,
                            "cuoc": ds.di_dong.cuoc if ds.di_dong else 0
                        },
                        "cuoc_1900": {
                            "thoi_gian_goi": ds.cuoc_1900.thoi_gian_goi if ds.cuoc_1900 else 0,
                            "cuoc": ds.cuoc_1900.cuoc if ds.cuoc_1900 else 0
                        },
                        "quoc_te": {
                            "thoi_gian_goi": ds.quoc_te.thoi_gian_goi if ds.quoc_te else 0,
                            "cuoc": ds.quoc_te.cuoc if ds.quoc_te else 0
                        },
                        "cuoc_thue_bao": {
                            "thue_bao_thang": ds.cuoc_thue_bao.thue_bao_thang if ds.cuoc_thue_bao else 0,
                            "cam_ket_thang": ds.cuoc_thue_bao.cam_ket_thang if ds.cuoc_thue_bao else 0,
                            "tra_truoc_thang": ds.cuoc_thue_bao.tra_truoc_thang if ds.cuoc_thue_bao else 0
                        } if ds.cuoc_thue_bao else None
                    } for ds in data.du_lieu
                ],
                "tong_ket": {
                    "cong_tien_dich_vu": data.tong_ket.cong_tien_dich_vu if data.tong_ket else 0,
                    "tien_thue_gtgt": data.tong_ket.tien_thue_gtgt if data.tong_ket else 0,
                    "tong_cong_tien": data.tong_ket.tong_cong_tien if data.tong_ket else 0
                } if data.tong_ket else None,
                "created_at": data.created_at,
                "updated_at": data.updated_at
            }
    elif template.template_type == "dsc":
        # Lấy dữ liệu từ bảng doi_soat_cuoc
        data = db.query(DoiSoatCuoc).filter(
            DoiSoatCuoc.template_id == template_id
        ).first()
        
        if data:
            # Định nghĩa cấu trúc dữ liệu cho DoiSoatCuoc
            
            # Parse thang/nam
            nam_doi_soat = None
            try:
                parts = data.thang_doi_soat.split('/')
                if len(parts) == 2:
                    nam_doi_soat = parts[1]
            except:
                pass # Ignore parsing errors
                
            structured_data = {
                "id": data.id,
                "thang_doi_soat": data.thang_doi_soat,
                "nam_doi_soat": nam_doi_soat,  # Add parsed year
                "tu_mang": data.tu_mang,
                "den_doi_tac": data.den_doi_tac,
                "partner_id": data.partner_id,
                "file_name": data.file_name,
                "hop_dong_so": data.hop_dong_so,
                "is_template_data": data.is_template_data,
                "created_at": data.created_at,
                "updated_at": data.updated_at,
                # Add du_lieu (related DauSoDichVuCuoc)
                "dau_so_dich_vu": [
                    {
                        "id": dsdv.id,
                        "stt": dsdv.stt,
                        "dau_so": dsdv.dau_so,
                        "vnm_san_luong": dsdv.vnm_san_luong,
                        "vnm_ty_le_cp": dsdv.vnm_ty_le_cp,
                        "vnm_thanh_tien": dsdv.vnm_thanh_tien,
                        "viettel_san_luong": dsdv.viettel_san_luong,
                        "viettel_ty_le_cp": dsdv.viettel_ty_le_cp,
                        "viettel_thanh_tien": dsdv.viettel_thanh_tien,
                        "vnpt_san_luong": dsdv.vnpt_san_luong,
                        "vnpt_ty_le_cp": dsdv.vnpt_ty_le_cp,
                        "vnpt_thanh_tien": dsdv.vnpt_thanh_tien,
                        "vms_san_luong": dsdv.vms_san_luong,
                        "vms_ty_le_cp": dsdv.vms_ty_le_cp,
                        "vms_thanh_tien": dsdv.vms_thanh_tien,
                        "khac_san_luong": dsdv.khac_san_luong,
                        "khac_ty_le_cp": dsdv.khac_ty_le_cp,
                        "khac_thanh_tien": dsdv.khac_thanh_tien,
                        "tong_thanh_toan": dsdv.tong_thanh_toan,
                        "created_at": dsdv.created_at,
                        "updated_at": dsdv.updated_at
                    } for dsdv in data.du_lieu
                ],
                # Add tong_ket (related TongKetCuoc)
                "tong_ket": {
                    "id": data.tong_ket.id,
                    "cong_tien_dich_vu": data.tong_ket.cong_tien_dich_vu,
                    "tien_thue_gtgt": data.tong_ket.tien_thue_gtgt,
                    "tong_cong_tien": data.tong_ket.tong_cong_tien,
                    "created_at": data.tong_ket.created_at,
                    "updated_at": data.tong_ket.updated_at
                } if data.tong_ket else None
            }
    elif template.template_type == "dst_1800_1900":
        # Lấy dữ liệu từ bảng doi_soat_1800_1900
        data = db.query(DoiSoat1800_1900).filter(
            DoiSoat1800_1900.template_id == template_id
        ).first()
        
        if data:
            # Định nghĩa cấu trúc dữ liệu cho DoiSoat1800_1900
            structured_data = {
                "id": data.id,
                "thang_doi_soat": data.thang_doi_soat,
                # Thêm các trường khác nếu cần
            }
    
    if not structured_data:
        # Nếu không tìm thấy dữ liệu có cấu trúc, trả về processed_data
        return template.processed_data
    
    return structured_data

@router.get("/{template_id}/history", response_model=TemplateHistoryResponse)
async def get_template_history(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Lấy lịch sử xử lý của một mẫu đối soát"""
    template = crud_template.get(db=db, id=template_id)
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mẫu đối soát không tồn tại"
        )
    
    # Tạm thời trả về một lịch sử mặc định
    # Trong môi trường thực tế, có thể lưu lịch sử xử lý trong database
    # và lấy ra dựa vào template_id
    
    history_entries = []
    
    # Thêm bản ghi cho thời điểm tạo template
    history_entries.append(
        TemplateHistoryEntry(
            timestamp=template.created_at,
            status="pending",
            message="Mẫu đối soát được tạo"
        )
    )
    
    # Nếu template đã được xử lý
    if template.status in ["completed", "failed"]:
        history_entries.append(
            TemplateHistoryEntry(
                timestamp=template.updated_at,
                status=template.status,
                processing_time=template.processing_time,
                message="Xử lý hoàn tất" if template.status == "completed" else "Xử lý thất bại",
                error=template.error_message if template.status == "failed" else None
            )
        )
    
    return {"items": history_entries}

@router.get("/{template_id}/mappings", response_model=TemplateFieldMappingsResponse)
async def get_template_field_mappings(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Lấy thông tin mapping trường dữ liệu của một mẫu đối soát"""
    template = crud_template.get(db=db, id=template_id)
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mẫu đối soát không tồn tại"
        )
    
    # Trong môi trường thực tế, các mapping có thể được lưu trữ sau khi template 
    # được xử lý, nhưng ở đây sẽ trả về dữ liệu mẫu
    
    # Tạo mapping mẫu dựa trên loại template
    sample_mappings = []
    
    if template.template_type == TemplateType.DSC:
        sample_mappings = [
            FieldMapping(
                source_field="A1",
                target_field="thang_doi_soat",
                data_type="date",
                confidence=0.95
            ),
            FieldMapping(
                source_field="B3:F3",
                target_field="dau_so",
                data_type="string",
                confidence=0.9
            ),
            FieldMapping(
                source_field="B4:F4",
                target_field="ty_le_cp",
                data_type="float",
                confidence=0.85
            ),
            FieldMapping(
                source_field="B5:F10",
                target_field="san_luong",
                data_type="integer",
                confidence=0.9
            )
        ]
    elif template.template_type == TemplateType.DSCD:
        sample_mappings = [
            FieldMapping(
                source_field="A1",
                target_field="thang_doi_soat",
                data_type="date",
                confidence=0.95
            ),
            FieldMapping(
                source_field="C4:G4",
                target_field="dau_so_dich_vu",
                data_type="string",
                confidence=0.9
            ),
            FieldMapping(
                source_field="C5:G10",
                target_field="so_luong_thue_bao",
                data_type="integer",
                confidence=0.85
            )
        ]
    elif template.template_type == TemplateType.DST_1800_1900:
        sample_mappings = [
            FieldMapping(
                source_field="A1",
                target_field="thang_doi_soat",
                data_type="date",
                confidence=0.95
            ),
            FieldMapping(
                source_field="B3:E3",
                target_field="dau_so_1800_1900",
                data_type="string",
                confidence=0.9
            ),
            FieldMapping(
                source_field="B4:E10",
                target_field="cuoc_goi",
                data_type="integer",
                confidence=0.9
            )
        ]
    
    return {"items": sample_mappings}

@router.get("/{template_id}/preview", response_model=List[Dict[str, Any]])
async def get_template_preview(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Lấy dữ liệu xem trước của một mẫu đối soát"""
    template = crud_template.get(db=db, id=template_id)
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mẫu đối soát không tồn tại"
        )
    
    # Trong môi trường thực tế, dữ liệu xem trước sẽ được đọc từ file Excel
    # Ở đây, trả về một số dữ liệu mẫu dựa trên loại template
    
    preview_data = []
    
    if template.template_type == TemplateType.DSC:
        preview_data = [
            {"dau_so": "1800xxxx", "vnm_san_luong": 1200, "vnm_ty_le_cp": 100, "vnm_thanh_tien": 120000},
            {"dau_so": "1900xxxx", "vnm_san_luong": 850, "vnm_ty_le_cp": 150, "vnm_thanh_tien": 127500},
            {"dau_so": "1800yyyy", "viettel_san_luong": 2000, "viettel_ty_le_cp": 90, "viettel_thanh_tien": 180000},
            {"dau_so": "1900yyyy", "viettel_san_luong": 1500, "viettel_ty_le_cp": 120, "viettel_thanh_tien": 180000},
        ]
    elif template.template_type == TemplateType.DSCD:
        preview_data = [
            {"dau_so": "1800xxxx", "so_thue_bao": 250, "cuoc_thue_bao": 50000, "tong_cuoc": 12500000},
            {"dau_so": "1900xxxx", "so_thue_bao": 180, "cuoc_thue_bao": 70000, "tong_cuoc": 12600000},
            {"dau_so": "1800yyyy", "so_thue_bao": 320, "cuoc_thue_bao": 45000, "tong_cuoc": 14400000},
        ]
    elif template.template_type == TemplateType.DST_1800_1900:
        preview_data = [
            {"dau_so": "1800xxxx", "tong_cuoc_goi": 3500, "thoi_luong": 210000, "don_gia": 500, "thanh_tien": 105000000},
            {"dau_so": "1900xxxx", "tong_cuoc_goi": 2800, "thoi_luong": 168000, "don_gia": 700, "thanh_tien": 117600000},
        ]
    
    return preview_data 