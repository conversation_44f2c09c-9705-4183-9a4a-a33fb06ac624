import os
import tempfile
from typing import List, Optional
from datetime import date
from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, Form, Query, status
from sqlalchemy.orm import Session

from ....core.deps import get_db, get_current_user
from ....core.config import settings
from ....crud import (
    create_call_log_file,
    get_call_log_file,
    update_call_log_file,
    get_call_log_files,
    count_call_log_files,
    delete_call_log_file,
    get_call_logs,
    count_call_logs
)
from ....models.call_log import FileType, FileStatus, CallType
from ....models.user import User
from ....schemas.call_log import CallLogFileResponse, PaginatedCallLogFileResponse, CallLogResponse, PaginatedCallLogResponse
from ....worker.call_log_tasks import process_call_log_file

router = APIRouter()

# Giới hạn kích thước file upload (200MB)
MAX_UPLOAD_SIZE = settings.MAX_UPLOAD_SIZE

@router.post("/upload", status_code=status.HTTP_202_ACCEPTED, response_model=CallLogFileResponse)
async def upload_call_log(
    file: UploadFile = File(...),
    file_type: FileType = Form(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Upload file call log (XLSX)
    """
    # Validate file extension
    if not file.filename.endswith('.xlsx'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only XLSX files are supported"
        )
    
    # Kiểm tra kích thước file
    file.file.seek(0, 2)  # Di chuyển con trỏ đến cuối file
    file_size = file.file.tell()  # Lấy vị trí hiện tại (kích thước file)
    file.file.seek(0)  # Reset con trỏ về đầu file
    
    if file_size > MAX_UPLOAD_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File too large. Maximum size is {MAX_UPLOAD_SIZE / (1024 * 1024)}MB"
        )
    
    # Tạo thư mục tạm nếu chưa tồn tại
    upload_dir = settings.CALL_LOGS_DIR
    os.makedirs(upload_dir, exist_ok=True)
    
    # Tạo file tạm
    temp_file_path = os.path.join(upload_dir, f"{current_user.id}_{file.filename}")
    
    try:
        # Lưu file tạm
        with open(temp_file_path, 'wb') as f:
            content = await file.read()
            f.write(content)
        
        # Tạo bản ghi file
        file_record = create_call_log_file(
            db=db,
            filename=file.filename,
            file_type=file_type,
            user_id=current_user.id,
            file_path=temp_file_path
        )
        
        # Khởi chạy task xử lý file
        task = process_call_log_file.delay(file_record.id)
        
        # Cập nhật task ID
        update_call_log_file(
            db=db,
            file_id=file_record.id,
            update_data={"task_id": task.id}
        )
        
        return file_record
    except Exception as e:
        # Xóa file tạm nếu có lỗi
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error uploading file: {str(e)}"
        )

@router.get("/files", response_model=PaginatedCallLogFileResponse)
async def list_call_log_files(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    file_type: Optional[FileType] = None,
    status: Optional[FileStatus] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Lấy danh sách file đã upload
    """
    # Chỉ admin mới có thể xem tất cả file
    user_id = None if current_user.is_admin else current_user.id
    
    items = get_call_log_files(
        db=db,
        user_id=user_id,
        file_type=file_type,
        status=status,
        skip=skip,
        limit=limit
    )
    
    total = count_call_log_files(
        db=db,
        user_id=user_id,
        file_type=file_type,
        status=status
    )
    
    return {
        "items": items,
        "total": total,
        "page": skip // limit + 1,
        "size": limit
    }

@router.get("/files/{file_id}", response_model=CallLogFileResponse)
async def get_call_log_file_detail(
    file_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Lấy thông tin chi tiết về file
    """
    file_record = get_call_log_file(db=db, file_id=file_id)
    
    if not file_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    # Kiểm tra quyền truy cập
    if not current_user.is_admin and file_record.uploaded_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to access this file"
        )
    
    return file_record

@router.delete("/files/{file_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_call_log_file_endpoint(
    file_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Xóa file và dữ liệu liên quan
    """
    file_record = get_call_log_file(db=db, file_id=file_id)
    
    if not file_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    # Kiểm tra quyền truy cập
    if not current_user.is_admin and file_record.uploaded_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to delete this file"
        )
    
    # Không cho phép xóa file đang xử lý
    if file_record.status == FileStatus.PROCESSING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete file while it's being processed"
        )
    
    # Xóa file tạm
    if os.path.exists(file_record.file_path):
        os.unlink(file_record.file_path)
    
    # Xóa dữ liệu
    delete_call_log_file(db=db, file_id=file_id)

@router.get("/logs", response_model=PaginatedCallLogResponse)
async def list_call_logs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),  # Tăng giới hạn tối đa lên 1000
    file_id: Optional[int] = Query(None),
    call_type: Optional[CallType] = Query(None),
    caller: Optional[str] = Query(None),
    callee: Optional[str] = Query(None),
    call_date_from: Optional[date] = Query(None),
    call_date_to: Optional[date] = Query(None),
    caller_type: Optional[str] = Query(None),
    callee_type: Optional[str] = Query(None),
    sort_field: Optional[str] = Query("begin_time", description="Trường sắp xếp: begin_time, end_time, duration, call_date, caller, callee"),
    sort_order: Optional[str] = Query("desc", description="Hướng sắp xếp: asc, desc"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Lấy danh sách các cuộc gọi với các bộ lọc
    """
    # Nếu có file_id, kiểm tra quyền truy cập
    if file_id is not None:
        file_record = get_call_log_file(db=db, file_id=file_id)
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        # Kiểm tra quyền truy cập
        if not current_user.is_admin and file_record.uploaded_by != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to access this file's data"
            )
    
    items = get_call_logs(
        db=db,
        file_id=file_id,
        call_type=call_type,
        caller=caller,
        callee=callee,
        call_date_from=call_date_from,
        call_date_to=call_date_to,
        caller_type=caller_type,
        callee_type=callee_type,
        sort_field=sort_field,
        sort_order=sort_order,
        skip=skip,
        limit=limit
    )
    
    total = count_call_logs(
        db=db,
        file_id=file_id,
        call_type=call_type,
        caller=caller,
        callee=callee,
        call_date_from=call_date_from,
        call_date_to=call_date_to,
        caller_type=caller_type,
        callee_type=callee_type
    )
    
    return {
        "items": items,
        "total": total,
        "page": skip // limit + 1,
        "size": limit
    } 