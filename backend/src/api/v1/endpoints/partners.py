from typing import Annotated, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ....database.session import get_db
from ....models.user import User
from ....models.partner import Partner, PartnerType, PartnerRawName
from ....schemas.partner import (
    PartnerCreate,
    PartnerUpdate,
    Partner as PartnerSchema,
    PartnerListResponse
)
from ...deps import get_current_admin_user
from ....crud.crud_partner import crud_partner

router = APIRouter()

@router.get("", response_model=PartnerListResponse)
async def list_partners(
    current_user: Annotated[User, Depends(get_current_admin_user)],
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="Số lượng bỏ qua"),
    limit: int = Query(10, ge=1, le=1000, description="Số lượng tối đa"),
    is_active: Optional[bool] = Query(None, description="<PERSON><PERSON><PERSON> theo trạng thái active")
):
    """
    Retrieve partners with pagination and optional filtering.
    Only accessible by admin users.
    """
    items, total = crud_partner.get_multi_paginated(
        db=db, 
        skip=skip, 
        limit=limit, 
        is_active=is_active
    )
    
    return {
        "items": items,
        "total": total
    }

@router.post("", response_model=PartnerSchema)
async def create_partner(
    partner_data: PartnerCreate,
    current_user: Annotated[User, Depends(get_current_admin_user)],
    db: Session = Depends(get_db)
):
    """
    Create new partner.
    Only accessible by admin users.
    """
    # Check if partner with same name exists
    if db.query(Partner).filter(Partner.name == partner_data.name).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Partner with this name already exists"
        )
    
    # Remove duplicates from the raw_names list
    unique_raw_names = list(dict.fromkeys(partner_data.raw_names))
    
    # Log warning if duplicates were found
    if len(unique_raw_names) < len(partner_data.raw_names):
        print(f"Warning: Removed {len(partner_data.raw_names) - len(unique_raw_names)} duplicate raw names")
    
    # Check if any of the raw names already exist
    for raw_name in unique_raw_names:
        if db.query(PartnerRawName).filter(PartnerRawName.raw_name == raw_name).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Partner with raw name '{raw_name}' already exists"
            )
    
    # Create partner without raw_names
    partner_dict = partner_data.model_dump(exclude={"raw_names"})
    db_partner = Partner(**partner_dict)
    db.add(db_partner)
    db.flush()  # Flush to get the partner ID
    
    # Create raw names
    for raw_name in unique_raw_names:
        db_raw_name = PartnerRawName(
            partner_id=db_partner.id,
            raw_name=raw_name
        )
        db.add(db_raw_name)
    
    db.commit()
    db.refresh(db_partner)
    return db_partner

@router.get("/{partner_id}", response_model=PartnerSchema)
async def get_partner(
    partner_id: int,
    current_user: Annotated[User, Depends(get_current_admin_user)],
    db: Session = Depends(get_db)
):
    """
    Get partner by ID.
    Only accessible by admin users.
    """
    partner = db.query(Partner).filter(Partner.id == partner_id).first()
    if not partner:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Partner not found"
        )
    return partner

@router.put("/{partner_id}", response_model=PartnerSchema)
async def update_partner(
    partner_id: int,
    partner_data: PartnerUpdate,
    current_user: Annotated[User, Depends(get_current_admin_user)],
    db: Session = Depends(get_db)
):
    """
    Update partner.
    Only accessible by admin users.
    """
    partner = db.query(Partner).filter(Partner.id == partner_id).first()
    if not partner:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Partner not found"
        )
    
    # Check name uniqueness if being updated
    if partner_data.name and partner_data.name != partner.name:
        if db.query(Partner).filter(Partner.name == partner_data.name).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Partner with this name already exists"
            )
    
    # Handle raw_names update if provided
    if partner_data.raw_names is not None:
        # Remove duplicates from the list
        unique_raw_names = list(dict.fromkeys(partner_data.raw_names))
        
        # Log warning if duplicates were found
        if len(unique_raw_names) < len(partner_data.raw_names):
            print(f"Warning: Removed {len(partner_data.raw_names) - len(unique_raw_names)} duplicate raw names")
        
        # Check if any of the new raw names already exist for other partners
        for raw_name in unique_raw_names:
            existing = db.query(PartnerRawName).filter(
                PartnerRawName.raw_name == raw_name,
                PartnerRawName.partner_id != partner_id
            ).first()
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Partner with raw name '{raw_name}' already exists"
                )
        
        # Delete existing raw names
        db.query(PartnerRawName).filter(PartnerRawName.partner_id == partner_id).delete()
        
        # Create new raw names
        for raw_name in unique_raw_names:
            db_raw_name = PartnerRawName(
                partner_id=partner_id,
                raw_name=raw_name
            )
            db.add(db_raw_name)
    
    # Update partner fields (excluding raw_names)
    update_data = partner_data.model_dump(exclude={"raw_names"}, exclude_unset=True)
    for field, value in update_data.items():
        setattr(partner, field, value)
    
    db.commit()
    db.refresh(partner)
    return partner

@router.delete("/{partner_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_partner(
    partner_id: int,
    current_user: Annotated[User, Depends(get_current_admin_user)],
    db: Session = Depends(get_db)
):
    """
    Delete partner.
    Only accessible by admin users.
    """
    partner = db.query(Partner).filter(Partner.id == partner_id).first()
    if not partner:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Partner not found"
        )
    
    db.delete(partner)
    db.commit()

@router.post("/{partner_id}/toggle-active", response_model=PartnerSchema)
async def toggle_partner_active_status(
    partner_id: int,
    current_user: Annotated[User, Depends(get_current_admin_user)],
    db: Session = Depends(get_db)
):
    """
    Toggle partner active status.
    Only accessible by admin users.
    """
    partner = db.query(Partner).filter(Partner.id == partner_id).first()
    if not partner:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Partner not found"
        )
    
    partner.is_active = not partner.is_active
    db.commit()
    db.refresh(partner)
    return partner 