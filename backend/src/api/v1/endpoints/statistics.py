from typing import Optional
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from sqlalchemy import func, or_, and_, Float, case, not_

from ....core.deps import get_db
from ....models.call_log import CallLog, NumberType
from ....models.partner import Partner
from ....schemas.statistics import StatisticsResponse
from ....core.logging import logger

router = APIRouter()

async def get_partner_raw_names(
    partner_name: Optional[str],
    partner_id: Optional[int],
    db: Session
) -> tuple[Partner, list[str]]:
    """Helper function to get partner raw names"""
    partner_query = db.query(Partner)
    if partner_name:
        partner_query = partner_query.filter(Partner.name == partner_name)
    if partner_id:
        partner_query = partner_query.filter(Partner.id == partner_id)
    
    partner = partner_query.first()
    if not partner:
        raise HTTPException(status_code=404, detail="Partner not found")
        
    raw_names = [raw.raw_name for raw in partner.raw_names]
    if not raw_names:
        raise HTTPException(status_code=404, detail="No raw names found for partner")
    
    return partner, raw_names

@router.get("/htc_to_viettel_cd_to_cd", response_model=StatisticsResponse)
async def get_htc_to_viettel_cd_to_cd(
    period: str = Query(..., description="Period in format YYYY-MM"),
    partner_name: Optional[str] = Query(None, description="Partner name"),
    partner_id: Optional[int] = Query(None, description="Partner ID"),
    db: Session = Depends(get_db)
):
    """Tổng số phút cuộc gọi đi từ CD HTC tới CD Viettel"""
    try:
        # Parse period
        start_date = datetime.strptime(f"{period}-01", "%Y-%m-%d")
        end_date = (start_date.replace(day=1) + timedelta(days=32)).replace(day=1)
        
        # Get partner info for metadata
        partner = None
        if partner_id:
            partner = db.query(Partner).filter(Partner.id == partner_id).first()
        elif partner_name:
            partner = db.query(Partner).filter(Partner.name == partner_name).first()

        # Build query with correct area_name filtering to match expected result
        query = (
            db.query(func.sum(func.cast(CallLog.duration / 60.0, type_=Float)))  # Convert to minutes
            .filter(
                and_(
                    CallLog.begin_time >= start_date,
                    CallLog.begin_time < end_date,
                    CallLog.call_type == "out",
                    # Filter for Viettel fixed lines, excluding special services
                    CallLog.area_name.like("VIETTEL%"),
                    ~CallLog.area_name.in_(["VIETTEL 1800", "VIETTEL 1900"]), # 1800 and 1900 are special services
                    CallLog.caller_type == NumberType.FIXED,
                    CallLog.callee_type == NumberType.FIXED,
                    # Exclude calls where caller and callee have the same first 3 digits
                    func.substr(CallLog.caller, 1, 3) != func.substr(CallLog.callee, 1, 3)
                )
            )
        )

        # Debug log the SQL query
        logger.debug(f"SQL Query: {query.statement.compile(compile_kwargs={'literal_binds': True})}")
        
        # Execute query
        total_minutes = query.scalar() or 0.0
        
        # Debug log the total
        logger.debug(f"Total minutes: {total_minutes}")

        return StatisticsResponse(
            totalMinutes=float(total_minutes),
            description="Tổng số phút cuộc gọi đi từ CD HTC tới CD Viettel",
            metadata={
                "period": period,
                "partner_id": partner.id if partner else None,
                "partner_name": partner.name if partner else None,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid period format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in get_htc_to_viettel_cd_to_cd: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/htc_to_viettel_cd_to_dd", response_model=StatisticsResponse)
async def get_htc_to_viettel_cd_to_dd(
    period: str = Query(..., description="Period in format YYYY-MM"),
    partner_name: Optional[str] = Query(None, description="Partner name"),
    partner_id: Optional[int] = Query(None, description="Partner ID"),
    db: Session = Depends(get_db)
):
    """Tổng số phút cuộc gọi đi từ CD HTC đến DD Viettel"""
    try:
        # Parse period
        start_date = datetime.strptime(f"{period}-01", "%Y-%m-%d")
        end_date = (start_date.replace(day=1) + timedelta(days=32)).replace(day=1)
        
        # Get partner raw names for filtering
        partner, raw_names = await get_partner_raw_names(partner_name, partner_id, db)

        # First, identify all CP partners with error_adjustment != 0
        error_cp_partners = db.query(Partner).filter(
            and_(
                Partner.type == "cp",
                Partner.error_adjustment != 0,
                Partner.is_active == True
            )
        ).all()
        
        logger.info(f"Found {len(error_cp_partners)} CP partners with error_adjustment != 0")
        
        # Extract raw names for all error partners
        error_partner_raw_names = []
        for error_partner in error_cp_partners:
            logger.info(f"CP partner {error_partner.name} has error_adjustment = {error_partner.error_adjustment}")
            for raw_name in error_partner.raw_names:
                error_partner_raw_names.append((raw_name.raw_name, error_partner.error_adjustment))
                logger.info(f"Added raw name: '{raw_name.raw_name}' with adjustment {error_partner.error_adjustment}")
        
        logger.info(f"Collected {len(error_partner_raw_names)} raw names from CP partners with error_adjustment")
        
        # Build the base query for ALL calls from CD HTC to DD Viettel
        # Instead of filtering by area_name, we'll make the query more specific
        base_query = db.query(CallLog).filter(
            and_(
                CallLog.begin_time >= start_date,
                CallLog.begin_time < end_date,
                CallLog.call_type == "out",
                # More specific filtering for Viettel mobile numbers
                CallLog.area_name.like("VIETTEL%"), 
                ~CallLog.area_name.in_(["VIETTEL 1800", "VIETTEL 1900"]),  # Exclude special services
                CallLog.caller_type == NumberType.FIXED,
                CallLog.callee_type == NumberType.MOBILE
            )
        )
        
        # Log the total count and minutes before adjustment
        total_count_before = base_query.count()
        total_seconds_before = db.query(func.sum(CallLog.duration)).filter(base_query.whereclause).scalar() or 0
        logger.info(f"Before adjustment: {total_count_before} calls, {total_seconds_before} seconds ({total_seconds_before/60.0:.2f} minutes)")
        
        # For partners with error_adjustment = -1, set all their calls to duration-1
        minus_one_partner_names = [name for name, adj in error_partner_raw_names if adj == -1]
        logger.info(f"Partners with -1 adjustment: {minus_one_partner_names}")
        
        # For partners with error_adjustment = 1, increase 0s calls to 1s
        plus_one_partner_names = [name for name, adj in error_partner_raw_names if adj == 1]
        logger.info(f"Partners with +1 adjustment: {plus_one_partner_names}")
        
        # Check how many calls would be affected by each adjustment type
        if minus_one_partner_names:
            minus_one_count = base_query.filter(CallLog.caller_gateway.in_(minus_one_partner_names)).count()
            logger.info(f"Calls affected by -1 adjustment: {minus_one_count}")
        else:
            minus_one_count = 0
            
        if plus_one_partner_names:
            plus_one_zero_count = base_query.filter(
                and_(
                    CallLog.caller_gateway.in_(plus_one_partner_names),
                    CallLog.duration == 0
                )
            ).count()
            logger.info(f"Calls affected by +1 adjustment (0s calls): {plus_one_zero_count}")
        else:
            plus_one_zero_count = 0
        
        # Apply error adjustments based on partner types
        if minus_one_partner_names or plus_one_partner_names:
            # Create the duration calculation based on conditions
            if minus_one_partner_names and plus_one_partner_names:
                # Both -1 and +1 adjustments are needed
                duration_case = case(
                    (CallLog.caller_gateway.in_(minus_one_partner_names), CallLog.duration - 1),
                    (and_(
                        CallLog.caller_gateway.in_(plus_one_partner_names),
                        CallLog.duration == 0
                    ), 1),
                    else_=CallLog.duration
                )
            elif minus_one_partner_names:
                # Only -1 adjustment is needed
                duration_case = case(
                    (CallLog.caller_gateway.in_(minus_one_partner_names), CallLog.duration - 1),
                    else_=CallLog.duration
                )
            elif plus_one_partner_names:
                # Only +1 adjustment is needed
                duration_case = case(
                    (and_(
                        CallLog.caller_gateway.in_(plus_one_partner_names),
                        CallLog.duration == 0
                    ), 1),
                    else_=CallLog.duration
                )
            
            # Create the query with the case expression and convert to minutes
            query = db.query(func.sum(func.cast(duration_case / 60.0, type_=Float))).filter(base_query.whereclause)
            
            # Log the partners being adjusted
            logger.info(f"Applying duration adjustments for CP partners: " + 
                      f"{', '.join([p for p in minus_one_partner_names + plus_one_partner_names])}")
                      
            # Calculate the estimated impact of adjustments
            total_adjustment = minus_one_count * (-1) + plus_one_zero_count * 1
            logger.info(f"Estimated adjustment impact: {total_adjustment} seconds")
        else:
            # No error adjustments, just use the base query
            query = db.query(func.sum(func.cast(CallLog.duration / 60.0, type_=Float))).filter(base_query.whereclause)

        # Debug log the SQL query
        try:
            sql_str = str(query.statement.compile(compile_kwargs={"literal_binds": True}))
            logger.debug(f"Generated SQL: {sql_str}")
        except:
            logger.debug("Could not compile SQL with literal_binds")
        
        # Execute query
        total_minutes = query.scalar() or 0.0
        
        # Debug log the total
        logger.info(f"After adjustment: {total_minutes:.2f} minutes")

        # Include information about CP error adjustments that were applied
        cp_error_info = ""
        if minus_one_partner_names:
            cp_error_info += f" (có {len(minus_one_partner_names)} CP lỗi -1)"
        if plus_one_partner_names:
            cp_error_info += f" (có {len(plus_one_partner_names)} CP lỗi +1)"

        return StatisticsResponse(
            totalMinutes=float(total_minutes),
            description="Tổng số phút cuộc gọi đi từ CD HTC đến DD Viettel" + cp_error_info,
            metadata={
                "period": period,
                "partner_id": partner.id if partner else None,
                "partner_name": partner.name if partner else None,
                "minus_one_partners": len(minus_one_partner_names) if minus_one_partner_names else 0,
                "plus_one_partners": len(plus_one_partner_names) if plus_one_partner_names else 0,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid period format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in get_htc_to_viettel_cd_to_dd: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/htc_to_viettel_cd_port_out", response_model=StatisticsResponse)
async def get_htc_to_viettel_cd_port_out(
    period: str = Query(..., description="Period in format YYYY-MM"),
    partner_name: Optional[str] = Query(None, description="Partner name"),
    partner_id: Optional[int] = Query(None, description="Partner ID"),
    db: Session = Depends(get_db)
):
    """Tổng số phút cuộc gọi đi từ CD HTC đến thuê bao di động Viettel đã Port_out"""
    try:
        # Parse period
        start_date = datetime.strptime(f"{period}-01", "%Y-%m-%d")
        end_date = (start_date.replace(day=1) + timedelta(days=32)).replace(day=1)
        
        # Get partner raw names
        partner, raw_names = await get_partner_raw_names(partner_name, partner_id, db)

        # Build query
        query = (
            db.query(func.sum(CallLog.duration))
            .filter(
                and_(
                    CallLog.begin_time >= start_date,
                    CallLog.begin_time < end_date,
                    CallLog.call_type == "out",
                    CallLog.called_gateway.in_(raw_names),
                    CallLog.caller_type == NumberType.FIXED,
                    CallLog.callee_type == NumberType.PORTED
                )
            )
        )

        # Debug log the SQL query
        logger.debug(f"SQL Query: {query.statement.compile(compile_kwargs={'literal_binds': True})}")
        
        # Execute query
        total_minutes = query.scalar() or 0.0
        
        # Debug log the total
        logger.debug(f"Total minutes: {total_minutes}")

        return StatisticsResponse(
            totalMinutes=float(total_minutes),
            description="Tổng số phút cuộc gọi đi từ CD HTC đến thuê bao di động Viettel đã Port_out",
            metadata={
                "period": period,
                "partner_id": partner.id if partner else None,
                "partner_name": partner.name if partner else None,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid period format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in get_htc_to_viettel_cd_port_out: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/htc_to_viettel_cd_intl", response_model=StatisticsResponse)
async def get_htc_to_viettel_cd_intl(
    period: str = Query(..., description="Period in format YYYY-MM"),
    partner_name: Optional[str] = Query(None, description="Partner name"),
    partner_id: Optional[int] = Query(None, description="Partner ID"),
    db: Session = Depends(get_db)
):
    """Tổng số phút CD Viettel sử dụng dịch vụ HTC gọi đi Quốc tế"""
    try:
        # Parse period
        start_date = datetime.strptime(f"{period}-01", "%Y-%m-%d")
        end_date = (start_date.replace(day=1) + timedelta(days=32)).replace(day=1)
        
        # Get partner raw names
        partner, raw_names = await get_partner_raw_names(partner_name, partner_id, db)

        # Build query
        query = (
            db.query(func.sum(CallLog.duration))
            .filter(
                and_(
                    CallLog.begin_time >= start_date,
                    CallLog.begin_time < end_date,
                    CallLog.call_type == "out",
                    CallLog.caller_gateway.in_(raw_names),
                    CallLog.callee_type == NumberType.INTL
                )
            )
        )

        # Debug log the SQL query
        logger.debug(f"SQL Query: {query.statement.compile(compile_kwargs={'literal_binds': True})}")
        
        # Execute query
        total_minutes = query.scalar() or 0.0
        
        # Debug log the total
        logger.debug(f"Total minutes: {total_minutes}")

        return StatisticsResponse(
            totalMinutes=float(total_minutes),
            description="Tổng số phút CD Viettel sử dụng dịch vụ HTC gọi đi Quốc tế",
            metadata={
                "period": period,
                "partner_id": partner.id if partner else None,
                "partner_name": partner.name if partner else None,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid period format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in get_htc_to_viettel_cd_intl: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/viettel_to_htc_cd_to_cd", response_model=StatisticsResponse)
async def get_viettel_to_htc_cd_to_cd(
    period: str = Query(..., description="Period in format YYYY-MM"),
    partner_name: Optional[str] = Query(None, description="Partner name"),
    partner_id: Optional[int] = Query(None, description="Partner ID"),
    db: Session = Depends(get_db)
):
    """Tổng số phút cuộc gọi từ CD Viettel đến CD HTC"""
    try:
        # Parse period
        start_date = datetime.strptime(f"{period}-01", "%Y-%m-%d")
        end_date = (start_date.replace(day=1) + timedelta(days=32)).replace(day=1)
        
        # Get partner raw names
        partner, raw_names = await get_partner_raw_names(partner_name, partner_id, db)

        # Build query theo yêu cầu mới - chuyển đổi từ giây sang phút, sẽ làm tròn thành số nguyên
        query = (
            db.query(func.sum(func.cast(CallLog.duration / 60.0, type_=Float)))  # Chuyển đổi từ giây sang phút
            .filter(
                and_(
                    CallLog.begin_time >= start_date,
                    CallLog.begin_time < end_date,
                    CallLog.call_type == "in",
                    CallLog.caller_type == NumberType.FIXED,
                    CallLog.callee_type == NumberType.FIXED,
                    CallLog.caller_gateway.ilike("%VIETTEL%"), # Thay vì dùng in_(raw_names)
                    not_(CallLog.callee_note.ilike("%1800%")),
                    not_(CallLog.callee_note.ilike("%1900%")),
                    not_(
                        or_(
                            and_(
                                CallLog.caller_note.ilike("%024%"),
                                CallLog.callee_note.ilike("%024%")
                            ),
                            and_(
                                CallLog.caller_note.ilike("%028%"),
                                CallLog.callee_note.ilike("%028%")
                            )
                        )
                    )
                )
            )
        )

        # Debug log the SQL query
        logger.debug(f"SQL Query: {query.statement.compile(compile_kwargs={'literal_binds': True})}")
        
        # Execute query
        total_minutes = query.scalar() or 0.0
        
        # Làm tròn kết quả thành số nguyên
        total_minutes = round(total_minutes)
        
        # Debug log the total
        logger.debug(f"Total minutes: {total_minutes}")

        return StatisticsResponse(
            totalMinutes=float(total_minutes),
            description="Tổng số phút cuộc gọi từ CD Viettel đến CD HTC",
            metadata={
                "period": period,
                "partner_id": partner.id if partner else None,
                "partner_name": partner.name if partner else None,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid period format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in get_viettel_to_htc_cd_to_cd: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/viettel_to_htc_dd_to_cd", response_model=StatisticsResponse)
async def get_viettel_to_htc_dd_to_cd(
    period: str = Query(..., description="Period in format YYYY-MM"),
    partner_name: Optional[str] = Query(None, description="Partner name"),
    partner_id: Optional[int] = Query(None, description="Partner ID"),
    db: Session = Depends(get_db)
):
    """Tổng số phút cuộc gọi từ DD Viettel đến CD HTC"""
    try:
        # Parse period
        start_date = datetime.strptime(f"{period}-01", "%Y-%m-%d")
        end_date = (start_date.replace(day=1) + timedelta(days=32)).replace(day=1)
        
        # Get partner raw names
        partner, raw_names = await get_partner_raw_names(partner_name, partner_id, db)

        # Build query theo điều kiện mới và chuyển đổi từ giây sang phút
        query = (
            db.query(func.sum(func.cast(CallLog.duration / 60.0, type_=Float)))  # Chuyển đổi từ giây sang phút
            .filter(
                and_(
                    CallLog.begin_time >= start_date,
                    CallLog.begin_time < end_date,
                    CallLog.call_type == "in",
                    CallLog.callee_type == NumberType.FIXED,
                    CallLog.caller_gateway.ilike("%VIETTEL%"),
                    CallLog.caller_type == NumberType.MOBILE,
                    not_(CallLog.callee_note.ilike("%1800%")),
                    not_(CallLog.callee_note.ilike("%1900%")),
                    CallLog.caller_note.ilike("%VIETTEL%")
                )
            )
        )

        # Debug log the SQL query
        logger.debug(f"SQL Query: {query.statement.compile(compile_kwargs={'literal_binds': True})}")
        
        # Execute query
        total_minutes = query.scalar() or 0.0
        
        # Làm tròn kết quả thành số nguyên
        total_minutes = round(total_minutes)
        
        # Debug log the total
        logger.debug(f"Total minutes: {total_minutes}")

        return StatisticsResponse(
            totalMinutes=float(total_minutes),
            description="Tổng số phút cuộc gọi từ DD Viettel đến CD HTC",
            metadata={
                "period": period,
                "partner_id": partner.id if partner else None,
                "partner_name": partner.name if partner else None,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid period format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in get_viettel_to_htc_dd_to_cd: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/viettel_to_htc_cd_intl", response_model=StatisticsResponse)
async def get_viettel_to_htc_cd_intl(
    period: str = Query(..., description="Period in format YYYY-MM"),
    partner_name: Optional[str] = Query(None, description="Partner name"),
    partner_id: Optional[int] = Query(None, description="Partner ID"),
    db: Session = Depends(get_db)
):
    """Tổng số phút CD HTC sử dụng dịch vụ Viettel gọi đi Quốc tế"""
    try:
        # Parse period
        start_date = datetime.strptime(f"{period}-01", "%Y-%m-%d")
        end_date = (start_date.replace(day=1) + timedelta(days=32)).replace(day=1)
        
        # Get partner raw names
        partner, raw_names = await get_partner_raw_names(partner_name, partner_id, db)

        # Build query
        query = (
            db.query(func.sum(CallLog.duration))
            .filter(
                and_(
                    CallLog.begin_time >= start_date,
                    CallLog.begin_time < end_date,
                    CallLog.call_type == "in",
                    CallLog.called_gateway.in_(raw_names),
                    CallLog.callee_type == NumberType.INTL
                )
            )
        )

        # Debug log the SQL query
        logger.debug(f"SQL Query: {query.statement.compile(compile_kwargs={'literal_binds': True})}")
        
        # Execute query
        total_minutes = query.scalar() or 0.0
        
        # Debug log the total
        logger.debug(f"Total minutes: {total_minutes}")

        return StatisticsResponse(
            totalMinutes=float(total_minutes),
            description="Tổng số phút CD HTC sử dụng dịch vụ Viettel gọi đi Quốc tế",
            metadata={
                "period": period,
                "partner_id": partner.id if partner else None,
                "partner_name": partner.name if partner else None,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid period format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in get_viettel_to_htc_cd_intl: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/htc_to_viettel_cd_to_cd_debug", response_model=dict)
async def get_htc_to_viettel_cd_to_cd_debug(
    period: str = Query(..., description="Period in format YYYY-MM"),
    db: Session = Depends(get_db)
):
    """Debug endpoint to analyze calls data distribution"""
    try:
        # Parse period
        start_date = datetime.strptime(f"{period}-01", "%Y-%m-%d")
        end_date = (start_date.replace(day=1) + timedelta(days=32)).replace(day=1)
        
        # Base filter for Viettel fixed line calls
        base_filter = and_(
            CallLog.begin_time >= start_date,
            CallLog.begin_time < end_date,
            CallLog.call_type == "out",
            CallLog.area_name.like("VIETTEL%"),
            ~CallLog.area_name.in_(["VIETTEL_1800", "VIETTEL_1900"]),
            CallLog.caller_type == NumberType.FIXED,
            CallLog.callee_type == NumberType.FIXED,
            func.substr(CallLog.caller, 1, 3) != func.substr(CallLog.callee, 1, 3)
        )
        
        # Get total count with base filter
        total_count = db.query(CallLog).filter(base_filter).count()
        
        # Get total minutes with base filter
        total_minutes = db.query(func.sum(CallLog.duration)).filter(base_filter).scalar() or 0
        
        # Get data distribution by area_name
        area_distribution = db.query(
            CallLog.area_name, 
            func.count(CallLog.id), 
            func.sum(CallLog.duration)
        ).filter(base_filter).group_by(CallLog.area_name).all()
        
        # Format area distribution results
        area_stats = [
            {
                "area_name": area[0],
                "count": area[1],
                "minutes": float(area[2]) if area[2] else 0
            }
            for area in area_distribution
        ]
        
        # Get distribution by duration ranges
        duration_ranges = [
            {"min": 0, "max": 60, "label": "0-1 min"},
            {"min": 60, "max": 300, "label": "1-5 min"},
            {"min": 300, "max": 600, "label": "5-10 min"},
            {"min": 600, "max": 1800, "label": "10-30 min"},
            {"min": 1800, "max": 3600, "label": "30-60 min"},
            {"min": 3600, "max": None, "label": "60+ min"}
        ]
        
        duration_stats = []
        for r in duration_ranges:
            if r["max"]:
                range_filter = and_(base_filter, CallLog.duration > r["min"], CallLog.duration <= r["max"])
            else:
                range_filter = and_(base_filter, CallLog.duration > r["min"])
            
            count = db.query(CallLog).filter(range_filter).count()
            minutes = db.query(func.sum(CallLog.duration)).filter(range_filter).scalar() or 0
            
            duration_stats.append({
                "range": r["label"],
                "count": count,
                "minutes": float(minutes)
            })
        
        # Return comprehensive debug information
        return {
            "total_count": total_count,
            "total_minutes": float(total_minutes),
            "area_distribution": area_stats,
            "duration_distribution": duration_stats
        }
        
    except Exception as e:
        logger.error(f"Error in debug endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/viettel_to_htc_19004/{service_group}", response_model=StatisticsResponse)
async def get_viettel_to_htc_19004(
    service_group: str,
    period: str = Query(..., description="Period in format YYYY-MM"),
    partner_name: Optional[str] = Query(None, description="Partner name"),
    partner_id: Optional[int] = Query(None, description="Partner ID"),
    db: Session = Depends(get_db)
):
    """Tổng số phút cuộc gọi từ Viettel đến dịch vụ 19004 của HTC"""
    try:
        # Parse period
        start_date = datetime.strptime(f"{period}-01", "%Y-%m-%d")
        end_date = (start_date.replace(day=1) + timedelta(days=32)).replace(day=1)
        
        # Get partner raw names
        partner, raw_names = await get_partner_raw_names(partner_name, partner_id, db)

        # Define filters based on service group - dựa trên dữ liệu trong biên bản đối soát
        service_filters = {
            "19004000xx": and_(
                CallLog.callee.like("19004000%"),
            ),
            "190046_00_25_90_99": or_(
                # Nhóm 190046(00-25;90-99)
                # Tạo danh sách các đầu số cụ thể
                *[CallLog.callee.like(f"190046{i:02d}%") for i in list(range(0, 26)) + list(range(90, 100))]
            ),
            "190045_00_39_19004567": or_(
                # Nhóm 190045(00-39), 19004567
                *[CallLog.callee.like(f"190045{i:02d}%") for i in range(0, 40)],
                CallLog.callee.like("19004567%")
            ),
            "190047_40_99_190046_26_49": or_(
                # Nhóm 190047(40-99), 190046(26-49)
                *[CallLog.callee.like(f"190047{i:02d}%") for i in range(40, 100)],
                *[CallLog.callee.like(f"190046{i:02d}%") for i in range(26, 50)]
            ),
            "19004001xx": and_(
                CallLog.callee.like("19004001%"),
            ),
            "190047_00_39": or_(
                # Nhóm 190047(00-39)
                *[CallLog.callee.like(f"190047{i:02d}%") for i in range(0, 40)]
            ),
            "19004002xx": and_(
                CallLog.callee.like("19004002%"),
            ),
            "19004003xx": and_(
                CallLog.callee.like("19004003%"),
            ),
            "19004004xx": and_(
                CallLog.callee.like("19004004%"),
            ),
            "190045_56_80": or_(
                # Nhóm 190045(56-80)
                *[CallLog.callee.like(f"190045{i:02d}%") for i in range(56, 81)]
            ),
            "190045_50_89": or_(
                # Nhóm 190045(50-89)
                *[CallLog.callee.like(f"190045{i:02d}%") for i in range(50, 90)]
            ),
            "190045_81_99": or_(
                # Nhóm 190045(81-99)
                *[CallLog.callee.like(f"190045{i:02d}%") for i in range(81, 100)]
            ),
        }
        
        # Ánh xạ service_group từ URL đến các nhóm dịch vụ trong biên bản đối soát
        service_group_mapping = {
            "group1": "190046_00_25_90_99",
            "group2": "190045_00_39_19004567",
            "group3": "190047_40_99_190046_26_49",
            "group4": "19004001xx",
            "group5": "190047_00_39",
            "group6": "19004002xx",
            "group7": "19004003xx",
            "group8": "19004004xx",
            "group9": "190045_56_80",
            "group10": "190045_50_89",
            "group11": "190045_81_99",
        }
        
        # Kiểm tra service_group có hợp lệ không
        if service_group in service_group_mapping:
            filter_key = service_group_mapping[service_group]
        elif service_group in service_filters:
            filter_key = service_group
        else:
            raise HTTPException(status_code=400, detail=f"Invalid service group: {service_group}")
        
        # Chuẩn bị nhóm dịch vụ hiển thị trong description
        display_names = {
            "19004000xx": "19004000xx",
            "190046_00_25_90_99": "190046(00-25; 90-99)",
            "190045_00_39_19004567": "190045(00-39); 19004567",
            "190047_40_99_190046_26_49": "190047(40-99);190046(26-49)",
            "19004001xx": "19004001xx",
            "190047_00_39": "190047(00-39)",
            "19004002xx": "19004002xx",
            "19004003xx": "19004003xx",
            "19004004xx": "19004004xx",
            "190045_56_80": "190045(56-80)",
            "190045_50_89": "190045(50-89)",
            "190045_81_99": "190045(81-99)",
        }
        
        service_display = display_names.get(filter_key, service_group)
        
        # Build query với điều kiện lọc theo service_group
        query = (
            db.query(func.sum(func.cast(CallLog.duration / 60.0, type_=Float)))  # Chuyển đổi từ giây sang phút
            .filter(
                and_(
                    CallLog.begin_time >= start_date,
                    CallLog.begin_time < end_date,
                    CallLog.call_type == "in",  # Cuộc gọi đến
                    service_filters[filter_key],  # Áp dụng điều kiện lọc theo nhóm dịch vụ
                    CallLog.caller_gateway.ilike("%VIETTEL%"),  # Từ Viettel
                )
            )
        )

        # Debug log the SQL query
        logger.debug(f"SQL Query for {service_display}: {query.statement.compile(compile_kwargs={'literal_binds': True})}")
        
        # Execute query
        total_minutes = query.scalar() or 0.0
        
        # Làm tròn kết quả thành số nguyên
        total_minutes = round(total_minutes)
        
        # Debug log the total
        logger.debug(f"Total minutes for {service_display}: {total_minutes}")

        return StatisticsResponse(
            totalMinutes=float(total_minutes),
            description=f"Tổng số phút cuộc gọi từ Viettel đến dịch vụ {service_display} của HTC",
            metadata={
                "period": period,
                "service_group": service_group,
                "service_display": service_display,
                "partner_id": partner.id if partner else None,
                "partner_name": partner.name if partner else None,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid period format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in get_viettel_to_htc_19004: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/viettel_to_htc_18004/{call_type}", response_model=StatisticsResponse)
async def get_viettel_to_htc_18004(
    call_type: str,
    period: str = Query(..., description="Period in format YYYY-MM"),
    partner_name: Optional[str] = Query(None, description="Partner name"),
    partner_id: Optional[int] = Query(None, description="Partner ID"),
    db: Session = Depends(get_db)
):
    """Tổng số phút cuộc gọi từ Viettel đến dịch vụ 18004 của HTC (cố định hoặc di động)"""
    try:
        # Parse period
        start_date = datetime.strptime(f"{period}-01", "%Y-%m-%d")
        end_date = (start_date.replace(day=1) + timedelta(days=32)).replace(day=1)
        
        # Get partner raw names
        partner, raw_names = await get_partner_raw_names(partner_name, partner_id, db)

        # Validate call type
        valid_call_types = ["fixed", "mobile"]
        if call_type not in valid_call_types:
            raise HTTPException(status_code=400, detail=f"Invalid call type. Must be one of: {', '.join(valid_call_types)}")
        
        # Build call type specific filters - dựa trên biên bản đối soát
        if call_type == "fixed":
            # 18004xxx - Cố định
            number_filter = CallLog.callee.like("18004%")
            caller_type_filter = CallLog.caller_type == NumberType.FIXED
            display_name = "18004xxx - Cố định"
        else:
            # 18004xxx - Di động
            number_filter = CallLog.callee.like("18004%")
            caller_type_filter = CallLog.caller_type == NumberType.MOBILE
            display_name = "18004xxx - Di động"
        
        # Build query
        query = (
            db.query(func.sum(func.cast(CallLog.duration / 60.0, type_=Float)))  # Chuyển đổi từ giây sang phút
            .filter(
                and_(
                    CallLog.begin_time >= start_date,
                    CallLog.begin_time < end_date,
                    CallLog.call_type == "in",  # Cuộc gọi đến
                    number_filter,  # Đến dịch vụ 18004xxx
                    CallLog.caller_gateway.ilike("%VIETTEL%"),  # Từ Viettel
                    caller_type_filter,  # Lọc theo loại cuộc gọi (cố định hoặc di động)
                    # Loại trừ các cuộc gọi có thể gây nhầm lẫn
                    not_(CallLog.callee_note.ilike("%1800%")),
                    not_(CallLog.callee_note.ilike("%1900%")),
                )
            )
        )

        # Debug log the SQL query
        logger.debug(f"SQL Query for {display_name}: {query.statement.compile(compile_kwargs={'literal_binds': True})}")
        
        # Execute query
        total_minutes = query.scalar() or 0.0
        
        # Làm tròn kết quả thành số nguyên
        total_minutes = round(total_minutes)
        
        # Debug log the total
        logger.debug(f"Total minutes for {display_name}: {total_minutes}")
        
        return StatisticsResponse(
            totalMinutes=float(total_minutes),
            description=f"Tổng số phút cuộc gọi từ Viettel đến dịch vụ {display_name}",
            metadata={
                "period": period,
                "call_type": call_type,
                "display_name": display_name,
                "partner_id": partner.id if partner else None,
                "partner_name": partner.name if partner else None,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid period format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in get_viettel_to_htc_18004: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))