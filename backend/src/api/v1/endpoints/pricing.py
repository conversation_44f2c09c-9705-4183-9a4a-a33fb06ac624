from typing import List, Optional, Dict
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ....core.deps import get_db
from ....crud import crud_pricing
from ....models.volume_range import VolumeRange
from ....models.pricing import BillingMethod, RevenuePricing as RevenuePricingModel, CostPricing as CostPricingModel
from ....models.enums import ServiceType, SERVICE_TYPE_NAMES
from ....schemas.pricing import (
    VolumeRangeCreate,
    VolumeRangeResponse,
    VolumeRangeList,
    RevenuePricingCreate,
    RevenuePricingResponse,
    RevenuePricingList,
    CostPricingCreate,
    CostPricingResponse,
    CostPricingList,
    BulkRevenuePricingCreate,
    BulkCostPricingCreate,
    FormDataResponse,
    ServiceTypeItem
)

router = APIRouter()

# Service Types endpoints
@router.get("/service-types", response_model=List[ServiceTypeItem])
def get_service_types() -> List[ServiceTypeItem]:
    """
    Retrieve all service types from enum.
    """
    return [
        ServiceTypeItem(code=service_type.value, name=SERVICE_TYPE_NAMES[service_type])
        for service_type in ServiceType
    ]

# Volume Ranges endpoints
@router.get("/volume-ranges", response_model=VolumeRangeList)
def get_volume_ranges(
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1)
) -> VolumeRangeList:
    """
    Retrieve volume ranges.
    """
    items = crud_pricing.crud_volume_range.get_multi(db, skip=skip, limit=limit)
    total = db.query(VolumeRange).count()
    return VolumeRangeList(items=[VolumeRangeResponse.model_validate(item) for item in items], total=total)

@router.get("/volume-ranges/{volume_range_id}", response_model=VolumeRangeResponse)
def get_volume_range(
    volume_range_id: int,
    db: Session = Depends(get_db)
) -> VolumeRangeResponse:
    """
    Get volume range by ID.
    """
    volume_range = crud_pricing.crud_volume_range.get(db=db, id=volume_range_id)
    if not volume_range:
        raise HTTPException(status_code=404, detail="Volume range not found")
    return VolumeRangeResponse.model_validate(volume_range)

@router.post("/volume-ranges", response_model=VolumeRangeResponse)
def create_volume_range(
    *,
    db: Session = Depends(get_db),
    volume_range_in: VolumeRangeCreate
) -> VolumeRangeResponse:
    """
    Create new volume range.
    """
    return crud_pricing.crud_volume_range.create(db=db, obj_in=volume_range_in)

# Revenue Pricing endpoints
@router.get("/revenue-pricing", response_model=RevenuePricingList)
def get_revenue_pricing(
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1),
    partner_id: Optional[int] = Query(None)
) -> RevenuePricingList:
    """
    Retrieve revenue pricing.
    """
    if partner_id:
        items = crud_pricing.crud_revenue_pricing.get_active_by_partner(db, partner_id=partner_id)
        total = len(items)
    else:
        items = crud_pricing.crud_revenue_pricing.get_multi(db, skip=skip, limit=limit)
        total = db.query(RevenuePricingModel).count()
    return RevenuePricingList(items=[RevenuePricingResponse.model_validate(item) for item in items], total=total)

@router.get("/revenue-pricing/by-partner/{partner_id}", response_model=List[RevenuePricingResponse])
def get_revenue_pricing_by_partner(
    partner_id: int,
    billing_method: Optional[BillingMethod] = None,
    db: Session = Depends(get_db)
) -> List[RevenuePricingResponse]:
    """
    Retrieve revenue pricing by partner ID.
    """
    items = crud_pricing.crud_revenue_pricing.get_active_by_partner(
        db, 
        partner_id=partner_id,
        billing_method=billing_method
    )
    return [RevenuePricingResponse.model_validate(item) for item in items]

@router.get("/revenue-pricing/{revenue_pricing_id}", response_model=RevenuePricingResponse)
def get_revenue_pricing_by_id(
    revenue_pricing_id: int,
    db: Session = Depends(get_db)
) -> RevenuePricingResponse:
    """
    Get revenue pricing by ID.
    """
    revenue_pricing = crud_pricing.crud_revenue_pricing.get(db=db, id=revenue_pricing_id)
    if not revenue_pricing:
        raise HTTPException(status_code=404, detail="Revenue pricing not found")
    return RevenuePricingResponse.model_validate(revenue_pricing)

@router.post("/revenue-pricing", response_model=RevenuePricingResponse)
def create_revenue_pricing(
    *,
    db: Session = Depends(get_db),
    revenue_pricing_in: RevenuePricingCreate
) -> RevenuePricingResponse:
    """
    Create new revenue pricing rule.
    """
    # Validate service_type nếu được cung cấp
    if revenue_pricing_in.service_type and revenue_pricing_in.service_type not in [st.value for st in ServiceType]:
        raise HTTPException(status_code=400, detail=f"Invalid service_type: {revenue_pricing_in.service_type}")
    
    # Tạo revenue pricing rule
    revenue_pricing = crud_pricing.crud_revenue_pricing.create(db=db, obj_in=revenue_pricing_in)
    
    # Thêm service_type_name vào response
    response = RevenuePricingResponse.model_validate(revenue_pricing)
    if revenue_pricing.service_type:
        response.service_type_name = SERVICE_TYPE_NAMES.get(ServiceType(revenue_pricing.service_type))
    
    return response

@router.post("/revenue-pricing/bulk", response_model=List[RevenuePricingResponse])
def create_bulk_revenue_pricing(
    *,
    db: Session = Depends(get_db),
    bulk_pricing_in: BulkRevenuePricingCreate
) -> List[RevenuePricingResponse]:
    """
    Create multiple revenue pricing entries.
    """
    return crud_pricing.crud_revenue_pricing.create_bulk(db=db, obj_in=bulk_pricing_in)

@router.patch("/revenue-pricing/{revenue_pricing_id}", response_model=RevenuePricingResponse)
def update_revenue_pricing(
    revenue_pricing_id: int,
    revenue_pricing_in: RevenuePricingCreate,
    db: Session = Depends(get_db)
) -> RevenuePricingResponse:
    """
    Update revenue pricing by ID.
    """
    revenue_pricing = crud_pricing.crud_revenue_pricing.get(db=db, id=revenue_pricing_id)
    if not revenue_pricing:
        raise HTTPException(status_code=404, detail="Revenue pricing not found")
    
    updated_pricing = crud_pricing.crud_revenue_pricing.update(
        db=db, 
        db_obj=revenue_pricing, 
        obj_in=revenue_pricing_in
    )
    return RevenuePricingResponse.model_validate(updated_pricing)

@router.delete("/revenue-pricing/{revenue_pricing_id}", response_model=dict)
def delete_revenue_pricing(
    revenue_pricing_id: int,
    db: Session = Depends(get_db)
) -> dict:
    """
    Delete revenue pricing by ID.
    """
    revenue_pricing = crud_pricing.crud_revenue_pricing.get(db=db, id=revenue_pricing_id)
    if not revenue_pricing:
        raise HTTPException(status_code=404, detail="Revenue pricing not found")
    
    # Lưu ID và thông tin cần thiết trước khi xóa
    result = {
        "id": revenue_pricing.id,
        "message": "Revenue pricing deleted successfully"
    }
    
    # Xóa đối tượng
    crud_pricing.crud_revenue_pricing.remove(db=db, id=revenue_pricing_id)
    
    return result

# Cost Pricing endpoints
@router.get("/cost-pricing", response_model=CostPricingList)
def get_cost_pricing(
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1),
    partner_id: Optional[int] = Query(None)
) -> CostPricingList:
    """
    Retrieve cost pricing.
    """
    if partner_id:
        items = crud_pricing.crud_cost_pricing.get_active_by_partner(db, partner_id=partner_id)
        total = len(items)
    else:
        items = crud_pricing.crud_cost_pricing.get_multi(db, skip=skip, limit=limit)
        total = db.query(CostPricingModel).count()
    return CostPricingList(items=[CostPricingResponse.model_validate(item) for item in items], total=total)

@router.get("/cost-pricing/by-partner/{partner_id}", response_model=List[CostPricingResponse])
def get_cost_pricing_by_partner(
    partner_id: int,
    billing_method: Optional[BillingMethod] = None,
    db: Session = Depends(get_db)
) -> List[CostPricingResponse]:
    """
    Retrieve cost pricing by partner ID.
    """
    items = crud_pricing.crud_cost_pricing.get_active_by_partner(
        db, 
        partner_id=partner_id,
        billing_method=billing_method
    )
    return [CostPricingResponse.model_validate(item) for item in items]

@router.get("/cost-pricing/{cost_pricing_id}", response_model=CostPricingResponse)
def get_cost_pricing_by_id(
    cost_pricing_id: int,
    db: Session = Depends(get_db)
) -> CostPricingResponse:
    """
    Get cost pricing by ID.
    """
    cost_pricing = crud_pricing.crud_cost_pricing.get(db=db, id=cost_pricing_id)
    if not cost_pricing:
        raise HTTPException(status_code=404, detail="Cost pricing not found")
    return CostPricingResponse.model_validate(cost_pricing)

@router.post("/cost-pricing", response_model=CostPricingResponse)
def create_cost_pricing(
    *,
    db: Session = Depends(get_db),
    cost_pricing_in: CostPricingCreate
) -> CostPricingResponse:
    """
    Create new cost pricing.
    """
    return crud_pricing.crud_cost_pricing.create(db=db, obj_in=cost_pricing_in)

@router.post("/cost-pricing/bulk", response_model=List[CostPricingResponse])
def create_bulk_cost_pricing(
    *,
    db: Session = Depends(get_db),
    bulk_pricing_in: BulkCostPricingCreate
) -> List[CostPricingResponse]:
    """
    Create multiple cost pricing entries.
    """
    return crud_pricing.crud_cost_pricing.create_bulk(db=db, obj_in=bulk_pricing_in)

@router.patch("/cost-pricing/{cost_pricing_id}", response_model=CostPricingResponse)
def update_cost_pricing(
    cost_pricing_id: int,
    cost_pricing_in: CostPricingCreate,
    db: Session = Depends(get_db)
) -> CostPricingResponse:
    """
    Update cost pricing by ID.
    """
    cost_pricing = crud_pricing.crud_cost_pricing.get(db=db, id=cost_pricing_id)
    if not cost_pricing:
        raise HTTPException(status_code=404, detail="Cost pricing not found")
    
    updated_pricing = crud_pricing.crud_cost_pricing.update(
        db=db, 
        db_obj=cost_pricing, 
        obj_in=cost_pricing_in
    )
    return CostPricingResponse.model_validate(updated_pricing)

@router.delete("/cost-pricing/{cost_pricing_id}", response_model=dict)
def delete_cost_pricing(
    cost_pricing_id: int,
    db: Session = Depends(get_db)
) -> dict:
    """
    Delete cost pricing by ID.
    """
    cost_pricing = crud_pricing.crud_cost_pricing.get(db=db, id=cost_pricing_id)
    if not cost_pricing:
        raise HTTPException(status_code=404, detail="Cost pricing not found")
    
    # Lưu ID và thông tin cần thiết trước khi xóa
    result = {
        "id": cost_pricing.id,
        "message": "Cost pricing deleted successfully"
    }
    
    # Xóa đối tượng
    crud_pricing.crud_cost_pricing.remove(db=db, id=cost_pricing_id)
    
    return result

# Form Data endpoint
@router.get("/form-data", response_model=FormDataResponse)
def get_form_data(
    db: Session = Depends(get_db)
) -> FormDataResponse:
    """
    Get all necessary data for initializing the pricing form.
    """
    # Lấy service types từ enum
    service_types = [
        ServiceTypeItem(code=service_type.value, name=SERVICE_TYPE_NAMES[service_type])
        for service_type in ServiceType
    ]
    
    volume_ranges = crud_pricing.crud_volume_range.get_multi(db)
    billing_methods = list(BillingMethod)
    
    return FormDataResponse(
        service_types=service_types,
        volume_ranges=volume_ranges,
        billing_methods=billing_methods
    )