from fastapi import APIRouter

from .endpoints import (
    auth,
    users,
    partners,
    pricing,
    call_logs,
    statistics,
    templates,
    doi_soat,
    reconciliation_periods
)

api_router = APIRouter()

api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(partners.router, prefix="/partners", tags=["partners"])
api_router.include_router(pricing.router, prefix="/pricing", tags=["pricing"])
api_router.include_router(call_logs.router, prefix="/call-logs", tags=["call-logs"])
api_router.include_router(statistics.router, prefix="/statistics", tags=["statistics"])
api_router.include_router(templates.router, prefix="/templates", tags=["templates"])
api_router.include_router(doi_soat.router, prefix="/doi-soat", tags=["doi-soat"])
api_router.include_router(
    reconciliation_periods.router, 
    prefix="/reconciliation-periods", 
    tags=["reconciliation-periods"]
)
