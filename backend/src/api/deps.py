from typing import Annotated
from fastapi import Depends, HTTPException, status

from .v1.endpoints.auth import get_current_active_user
from ..models.user import User

async def get_current_admin_user(
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> User:
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges"
        )
    return current_user 