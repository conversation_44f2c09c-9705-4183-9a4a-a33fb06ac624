from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import Any, List, Optional

from src import crud, schemas
from src.database import get_db
from src.core.exceptions import NotFoundError, BadRequestError
import logging

log = logging.getLogger(__name__)
router = APIRouter()

@router.put(
    "/{dscd_id}/adjustments",
    response_model=schemas.DoiSoatCoDinh,
    summary="Update Adjustments for a DSCD Reconciliation",
    description="Applies manual adjustments to the summary (tong_ket) and/or detail items (du_lieu) of a specific DSCD reconciliation record.",
    status_code=status.HTTP_200_OK,
)
async def update_dscd_reconciliation_adjustments(
    dscd_id: int,
    adjustments_payload: schemas.DoiSoatCoDinhAdjustmentsPayload,
    db: Session = Depends(get_db),
) -> Any:
    """
    Updates adjusted values for a DSCD reconciliation.

    - **dscd_id**: ID of the DSCD record.
    - **adjustments_payload**: JSON body containing the fields to adjust.
        - `tong_ket`: Object with adjusted summary values.
        { "cong_tien_dich_vu_adjusted": 1234.5, "tien_thue_gtgt_adjusted": 123.45, ... }
        - `du_lieu`: Array of objects with adjusted detail values.
        [ { "id": 1, "cuoc_thu_khach_adjusted": 10.5 }, { "id": 2, "cuoc_tra_htc_adjusted": 5.0 }, ... ]
    """
    log.info(f"Received request to update adjustments for DSCD ID: {dscd_id}")
    try:
        updated_reconciliation = await crud.crud_dscd.update_dscd_adjustments(
            db=db,
            dscd_id=dscd_id,
            adjustments=adjustments_payload,
        )
        return updated_reconciliation

    except NotFoundError as e:
         log.warning(f"NotFound error during adjustment for DSCD ID {dscd_id}: {e}")
         raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except BadRequestError as e:
        log.warning(f"BadRequest error during adjustment for DSCD ID {dscd_id}: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except ValueError as e:
        log.warning(f"Validation error during adjustment for DSCD ID {dscd_id}: {e}")
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e))
    except Exception as e:
        log.exception(f"Unexpected error during adjustment update for DSCD ID {dscd_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Lỗi hệ thống khi cập nhật hiệu chỉnh.",
        )