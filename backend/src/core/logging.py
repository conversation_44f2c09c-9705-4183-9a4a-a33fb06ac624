import logging
import sys
from typing import Any, Dict, List, Optional

# <PERSON><PERSON><PERSON> hình logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Tạo logger
logger = logging.getLogger("audit_call")

# Thiết lập level
logger.setLevel(logging.INFO)

# Tắt propagation để tránh log trùng lặp
logger.propagate = False

# Thêm handler nếu chưa c<PERSON>
if not logger.handlers:
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(
        logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    )
    logger.addHandler(console_handler)

# Hàm tiện ích để log các thông tin quan trọng
def log_info(message: str, extra: Optional[Dict[str, Any]] = None) -> None:
    """Log thông tin với level INFO"""
    logger.info(message, extra=extra)

def log_error(message: str, extra: Optional[Dict[str, Any]] = None) -> None:
    """Log lỗi với level ERROR"""
    logger.error(message, extra=extra)

def log_warning(message: str, extra: Optional[Dict[str, Any]] = None) -> None:
    """Log cảnh báo với level WARNING"""
    logger.warning(message, extra=extra)

def log_debug(message: str, extra: Optional[Dict[str, Any]] = None) -> None:
    """Log debug với level DEBUG"""
    logger.debug(message, extra=extra)

def log_exception(message: str, exc_info: bool = True) -> None:
    """Log exception với traceback đầy đủ"""
    logger.exception(message, exc_info=exc_info) 