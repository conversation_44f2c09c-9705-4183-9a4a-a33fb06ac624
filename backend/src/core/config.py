import os
from typing import Any, Dict, List, Optional, Union
from pydantic import AnyHttpUrl, PostgresDsn, field_validator, computed_field
from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    # Environment
    ENVIRONMENT: str = "development"

    # API configs
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Audit Call API"
    
    # CORS configurations
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8000"]
    
    @field_validator("BACKEND_CORS_ORIGINS", mode='before')
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Database configurations
    POSTGRES_SERVER: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DB: str
    POSTGRES_PORT: str
    DATABASE_URL: Optional[str] = None

    @computed_field
    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        if self.DATABASE_URL:
            return self.DATABASE_URL
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
    
    # JWT configurations
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Redis configuration
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Celery configuration
    CELERY_BROKER_URL: Optional[str] = None
    CELERY_RESULT_BACKEND: Optional[str] = None
    
    @computed_field
    @property
    def CELERY_BROKER(self) -> str:
        return self.CELERY_BROKER_URL or self.REDIS_URL
    
    @computed_field
    @property
    def CELERY_BACKEND(self) -> str:
        return self.CELERY_RESULT_BACKEND or self.REDIS_URL
    
    # Upload configuration
    MAX_UPLOAD_SIZE: int = 20 * 1024 * 1024  # 20MB
    UPLOAD_DIR: str = "/tmp/uploads"
    CALL_LOGS_DIR: str = "/tmp/uploads/call_logs"
    TEMPLATES_DIR: str = "/tmp/uploads/templates"
    
    @computed_field
    @property
    def TEMPLATES_UPLOAD_DIR(self) -> str:
        """Đường dẫn đầy đủ đến thư mục lưu trữ mẫu đối soát"""
        return self.TEMPLATES_DIR
    
    @computed_field
    @property
    def CALL_LOGS_UPLOAD_DIR(self) -> str:
        """Đường dẫn đầy đủ đến thư mục lưu trữ call logs"""
        return self.CALL_LOGS_DIR
    
    # Timezone
    TZ: str = "Asia/Ho_Chi_Minh"
    
    # Debug configurations
    DB_ECHO_LOG: bool = False

    # First Admin User
    FIRST_ADMIN_EMAIL: str = "<EMAIL>"
    FIRST_ADMIN_PASSWORD: str = "admin123"
    
    model_config = SettingsConfigDict(
        case_sensitive=True,
        env_file=os.getenv("ENV_FILE", ".env"),
        env_file_encoding="utf-8"
    )

# Create settings instance
settings = Settings() 
settings = Settings() 