from sqlalchemy.orm import Session
from ..models.user import User
from ..core.security import get_password_hash

def init_db(db: Session) -> None:
    """Initialize database with default admin user."""
    
    # Check if admin user exists
    admin = db.query(User).filter(User.email == "<EMAIL>").first()
    if not admin:
        admin = User(
            username="admin",
            email="<EMAIL>",
            full_name="System Administrator",
            password_hash=get_password_hash("admin123"),  # You should change this in production
            is_active=True,
            is_admin=True,
        )
        db.add(admin)
        db.commit()
        db.refresh(admin) 