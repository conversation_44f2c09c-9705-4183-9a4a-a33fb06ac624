from sqlalchemy.orm import Session
from ..models.partner import Partner, PartnerRawName
import logging
from sqlalchemy import select, func

logger = logging.getLogger(__name__)

def seed_partners(db: Session) -> None:
    """
    Seed the database with default partners and their raw names.
    This function will only add partners that don't already exist.
    """
    partners_data = [
        # TELCO PARTNERS
        {
            "type": "telco",
            "name": "VNPT",
            "raw_names": [
                "GW_125_VNPT_ORI", 
                "GW_125_VNPT_FIX_ORI", 
                "GW_125_VNPT_INTERNATIONAL_ORI", 
                "GW_125_VNPT_MOBILE_ORI", 
                "GW_46_VNPT_MOBILE_ORI", 
                "VNPT_GW46_FIX_ORI", 
                "VNPT_GW46_MOBILE_ORI"
            ],
            "description": "Vietnam Posts and Telecommunications Group"
        },
        {
            "type": "telco",
            "name": "VIETTEL",
            "raw_names": [
                "GW_VIETTEL_FIX_ORI", 
                "GW_VIETTEL_MNP_ORI", 
                "GW_VIETTEL_MOBILE_ORI"
            ],
            "description": "Military Telecommunications Group"
        },
        {
            "type": "telco",
            "name": "MOBIFONE",
            "raw_names": [
                "GW_125_MOBIFONE_ORI", 
                "GW_MBF_MNP_ORI", 
                "GW_MBF_MOBILE_ORI"
            ],
            "description": "Vietnam Mobile Telecom Services"
        },
        {
            "type": "telco",
            "name": "GTEL",
            "raw_names": ["GTEL_ORI"],
            "description": "Global Telecommunications Corporation"
        },
        {
            "type": "telco",
            "name": "FPT",
            "raw_names": ["FPT_NEW_ORI"],
            "description": "FPT Telecom"
        },
        {
            "type": "telco",
            "name": "VNM",
            "raw_names": ["VNM_NewSIP_ORI"],
            "description": "Vietnamobile"
        },
        {
            "type": "telco",
            "name": "ITEL",
            "raw_names": ["ITEL_ORI"],
            "description": "I-Telecom"
        },
        {
            "type": "telco",
            "name": "MOBICAST",
            "raw_names": ["GW_MOBICAST_ORI"],
            "description": "Mobicast/Reddi Mobile Network"
        },
        {
            "type": "telco",
            "name": "CMC",
            "raw_names": ["CMC_ORI"],
            "description": "CMC Telecom"
        },
        {
            "type": "telco",
            "name": "DIGITEL",
            "raw_names": ["DIGITEL_ORI"],
            "description": "Digital Telecommunications"
        },
        {
            "type": "telco",
            "name": "DIGINEXT",
            "raw_names": ["DIGINEXT_ORI, DGN_TER_33"],
            "description": "Diginext Telecommunications"
        },
        {
            "type": "telco",
            "name": "VIETDIGITEL",
            "raw_names": ["VIETDIGITEL_ORI", "VIETDGTEL_FIX_TER", "VIETDGTEL_TER"],
            "description": "Viet Digital Telecommunications"
        },
        
        # CP PARTNERS (Content Providers)
        {
            "type": "cp",
            "name": "CGV",
            "raw_names": [
                "CGV_ORI", 
                "CGV_BACKUP131_TER", 
                "CGV_FIX131_TER", 
                "CGV_FIX166_TER", 
                "CGV_MAIN131_TER", 
                "CGV_MAIN166_TER"
            ],
            "description": "Cinema chain"
        },
        {
            "type": "cp",
            "name": "CAPITALAND",
            "raw_names": ["CAPITALAND_TER"],
            "description": "Real estate company"
        },
        {
            "type": "cp",
            "name": "NAM CUONG",
            "raw_names": ["CTTNHH_NAMCUONG_TER"],
            "description": "Nam Cuong company"
        },
        {
            "type": "cp",
            "name": "STC",
            "raw_names": ["CTY_STC_TER"],
            "description": "STC company"
        },
        {
            "type": "cp",
            "name": "HAI LONG",
            "raw_names": ["CTY_TNHH HaiLong"],
            "description": "Hai Long company"
        },
        # {
        #     "type": "cp",
        #     "name": "DGN",
        #     "raw_names": ["DGN_TER_33"],
        #     "description": "DGN services"
        # },
        {
            "type": "cp",
            "name": "DIDWW",
            "raw_names": [
                "DIDWW_DID_ORI", 
                "DIDWW_1_FOR_INTERNATIONAL_TER", 
                "DIDWW_1_TER"
            ],
            "description": "DID provider"
        },
        {
            "type": "cp",
            "name": "DIDXL",
            "raw_names": ["DIDXL_1800_TER", "DIDXL_1800_TER_2"],
            "description": "DIDXL services"
        },
        {
            "type": "cp",
            "name": "DOCTOR CHECK",
            "raw_names": ["DOCTOR_CHECK_MAIN_FIX_TER"],
            "description": "Healthcare service"
        },
        {
            "type": "cp",
            "name": "GEAR INC VN",
            "raw_names": ["GearIncVN_TER"],
            "description": "Gear Inc Vietnam"
        },
        {
            "type": "cp",
            "name": "HAI SAN VAN DON",
            "raw_names": ["HAISANVANDON_TER"],
            "description": "Hai San Van Don service"
        },
        {
            "type": "cp",
            "name": "HALI",
            "raw_names": ["HALI_TER"],
            "description": "Hali service"
        },
        {
            "type": "cp",
            "name": "HOA UU DAM",
            "raw_names": ["HOA_UU_DAM_ORI", "HOA_UU_DAM_FIX_TER"],
            "description": "Hoa Uu Dam service"
        },
        {
            "type": "cp",
            "name": "HTC",
            "raw_names": [
                "HTC_DONGNHAT_Backup_TER", 
                "HTC_REALTIME_1_ORI", 
                "HTC_REALTIME_10_TER", 
                "HTC_REALTIME_2_ORI", 
                "HTC_REALTIME_4'_TER", 
                "HTC_REALTIME_5_ORI", 
                "HTC_REALTIME_5_TER", 
                "HTC_REALTIME_6_TER", 
                "HTC_REALTIME_7'_TER", 
                "HTC_REALTIME_7_TER", 
                "HTC_REALTIME_8_TER", 
                "HTC_REALTIME_9'_TER", 
                "HTC_REALTIME_9_TER", 
                "HTC_eTelecom_TER", 
                "HTC_ĐONGNHAT_TER"
            ],
            "description": "HiTecom services"
        },
        {
            "type": "cp",
            "name": "IPOS",
            "raw_names": ["IPOS_BACKUP_TER"],
            "description": "IPOS services"
        },
        {
            "type": "cp",
            "name": "ITC",
            "raw_names": [
                "ITC_18004819_TER", 
                "ITC_BIC GIADINH_TER", 
                "ITC_BIGTRANGAN_TER", 
                "ITC_TIENTHO_TER", 
                "ITC_Vinfast_TER"
            ],
            "description": "Multiple ITC services"
        },
        {
            "type": "cp",
            "name": "ITEL_SERVICES",
            "raw_names": ["ITEL_19004_TER"],
            "description": "ITEL service numbers"
        },
        {
            "type": "cp",
            "name": "NANOSOFT",
            "raw_names": ["NANOSOFT_TER"],
            "description": "Nanosoft services"
        },
        {
            "type": "cp",
            "name": "NMS",
            "raw_names": [
                "NMS_ORI", 
                "NMS_1800_TER", 
                "NMS_19004716_TER", 
                "NMS_1900_TER", 
                "NMS_2_TER", 
                "NMS_TER"
            ],
            "description": "NMS services"
        },
        {
            "type": "cp",
            "name": "SAWAII",
            "raw_names": ["SAWAII_TER"],
            "description": "Sawaii services"
        },
        {
            "type": "cp",
            "name": "SOUTH TELECOM",
            "raw_names": [
                "SOUTH_TELECOM_1_TER", 
                "SOUTH_TELECOM_2_19004_TER", 
                "SOUTH_TELECOM_2_TER"
            ],
            "description": "South Telecom services"
        },
        {
            "type": "cp",
            "name": "SEN BAC",
            "raw_names": ["SenBac_4_TER", "SenBac_5_TER", "SenBac_TER"],
            "description": "Sen Bac services"
        },
        {
            "type": "cp",
            "name": "TATA",
            "raw_names": [
                "TATA_1800_Alaw_TER", 
                "TATA_1800_TER", 
                "TATA_LNS1_TER", 
                "TATA_LNS_FOR_INTERNATIONAL_TER", 
                "TATA_LNS_TER"
            ],
            "description": "TATA services"
        },
        {
            "type": "cp",
            "name": "THAI SON",
            "raw_names": ["THAISON_1_TER", "THAISON_2_TER", "THAISON_vadcom_TER"],
            "description": "Thai Son services"
        },
        {
            "type": "cp",
            "name": "THIEN LOC",
            "raw_names": ["THIENLOC_4554_TER", "THIENLOC_FIX_TER", "THIENLOC_TER"],
            "description": "Thien Loc services"
        },
        {
            "type": "cp",
            "name": "TIS",
            "raw_names": ["TIS_TER"],
            "description": "TIS services"
        },
        {
            "type": "cp",
            "name": "THAI BINH DUONG",
            "raw_names": ["ThaiBinhDuong_BACKUP_TER"],
            "description": "Thai Binh Duong services"
        },
        # {
        #     "type": "cp",
        #     "name": "VIETDIGITEL_SERVICES",
        #     "raw_names": ["VIETDGTEL_FIX_TER", "VIETDGTEL_TER"],
        #     "description": "Vietdigitel services"
        # },
        {
            "type": "cp",
            "name": "VOIP24H",
            "raw_names": ["VOIP24H_FIX_TER"],
            "description": "VoIP24h services"
        },
        {
            "type": "cp",
            "name": "VPBX120",
            "raw_names": ["VPBX120"],
            "description": "VPBX120 services"
        },
        {
            "type": "cp",
            "name": "VT3G",
            "raw_names": ["VT3G_19004788_MAIN", "VT3G_TER_1"],
            "description": "VT3G services"
        },
        {
            "type": "cp",
            "name": "VTQT",
            "raw_names": ["VTQT_MAIN_1900400088"],
            "description": "VTQT services"
        },
        {
            "type": "cp",
            "name": "VVM",
            "raw_names": ["VVM_TER"],
            "description": "VVM services"
        },
        {
            "type": "cp",
            "name": "YGAME",
            "raw_names": ["YGAME"],
            "description": "YGAME services"
        },
        {
            "type": "cp",
            "name": "1SS",
            "raw_names": ["1SS_MAIN_TER"],
            "description": "1SS services"
        }
    ]
    
    # Check if ANY partner exists by selecting a name
    existing_check_stmt = select(Partner.name).limit(1)
    does_any_partner_exist = db.scalar(existing_check_stmt) is not None 
    logger.info(f"Checking for existing partners... Found any? {does_any_partner_exist}")
    
    # Skip if partners already exist
    if does_any_partner_exist:
        logger.info("Partners already exist in database, skipping seed")
        return
    
    # Add partners and their raw names
    partners_added = 0
    raw_names_added = 0
    
    try:
        for partner_data in partners_data:
            raw_names = partner_data.pop("raw_names", [])
            
            # Create partner
            partner = Partner(**partner_data)
            db.add(partner)
            db.flush()  # Flush to get the partner.id
            partners_added += 1
            
            # Create raw names
            for raw_name in raw_names:
                if raw_name and raw_name.lower() != "nan":  # Skip empty or NaN values
                    db_raw_name = PartnerRawName(
                        partner_id=partner.id,
                        raw_name=raw_name
                    )
                    db.add(db_raw_name)
                    raw_names_added += 1
        
        db.commit()
        logger.info(f"Successfully seeded {partners_added} partners with {raw_names_added} raw names")
    
    except Exception as e:
        db.rollback()
        logger.error(f"Error seeding partners: {str(e)}")
        raise 