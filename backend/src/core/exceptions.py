# backend/src/core/exceptions.py

class BaseAppException(Exception):
    """Base class for application specific exceptions."""
    status_code = 500
    detail = "An internal server error occurred."

    def __init__(self, detail: str | None = None):
        self.detail = detail or self.detail

class NotFoundError(BaseAppException):
    """Raised when a requested resource is not found."""
    status_code = 404
    detail = "Resource not found."

class InvalidOperationError(BaseAppException):
    """Raised when an operation is invalid for the current state."""
    status_code = 400 # Or 409 Conflict could also be appropriate
    detail = "Invalid operation for the current state."

class AuthenticationError(BaseAppException):
    """Raised for authentication failures."""
    status_code = 401
    detail = "Authentication required."

class AuthorizationError(BaseAppException):
    """Raised for authorization failures."""
    status_code = 403
    detail = "Permission denied."

class ValidationError(BaseAppException):
    """Raised for validation errors."""
    status_code = 422
    detail = "Validation Error."

    def __init__(self, detail: str | list | dict | None = None):
         self.detail = detail or self.detail

class BadRequestError(BaseAppException):
    """Raised for invalid request data or parameters."""
    status_code = 400
    detail = "Bad request."

    def __init__(self, detail: str | None = None):
        self.detail = detail or self.detail

# Add other custom exceptions as needed 