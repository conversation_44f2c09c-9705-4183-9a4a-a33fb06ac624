from typing import Annotated, Generator, Optional
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session

from ..database.session import SessionLocal
from ..core.config import settings
from ..crud.crud_user import crud_user
from ..models.user import User
from ..schemas.token import TokenPayload

# OAuth2 cho header Authorization tiêu chuẩn
reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login"
)

def get_db() -> Generator:
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

# Function để lấy token từ query parameter
async def get_token_from_query(request: Request) -> Optional[str]:
    token = request.query_params.get("token")
    return token

# Function để lấy current user từ token trong query parameter
async def get_current_user_from_query(
    request: Request,
    db: Session = Depends(get_db)
) -> User:
    token = await get_token_from_query(request)
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
        )
        
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except (jwt.JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )
        
    user = crud_user.get(db, id=token_data.sub)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

def get_current_user(
    db: Session = Depends(get_db),
    token: Annotated[str, Depends(reusable_oauth2)] = None
) -> User:
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except (jwt.JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )
    user = crud_user.get(db, id=token_data.sub)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

def get_current_active_user(
    current_user: Annotated[User, Depends(get_current_user)]
) -> User:
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

def get_current_admin_user(
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> User:
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges"
        )
    return current_user 