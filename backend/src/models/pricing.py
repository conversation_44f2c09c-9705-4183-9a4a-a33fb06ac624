import typing
from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, Boolean, Numeric, Text, ForeignKey, CheckConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from enum import Enum as PyEnum

# Import Base and related models directly
from ..database.base import Base 
from .enums import ServiceType
from .partner import Partner
from .user import User
from .volume_range import VolumeRange

# Remove TYPE_CHECKING block if not needed for other hints
# if typing.TYPE_CHECKING:
#     pass

class BillingMethod(str, PyEnum):
    DEFAULT = "default"
    SUBSCRIPTION = "subscription"
    BLOCK_6S = "block_6s"
    ONE_SEC_PLUS = "one_sec_plus"
    ONE_MIN_PLUS = "one_min_plus"

# Inherit directly from Base
class RevenuePricing(Base):
    __tablename__ = "revenue_pricing"

    id: Mapped[int] = mapped_column(primary_key=True)
    partner_id: Mapped[int] = mapped_column(ForeignKey("partners.id", ondelete="CASCADE"), nullable=False)
    billing_method: Mapped[BillingMethod] = mapped_column(String(20), nullable=False)
    service_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    volume_range_id: Mapped[Optional[int]] = mapped_column(ForeignKey("volume_ranges.id", ondelete="CASCADE"), nullable=True)
    price: Mapped[float] = mapped_column(Numeric(10, 2), nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)

    partner: Mapped["Partner"] = relationship("Partner", back_populates="revenue_rules")
    volume_range: Mapped[Optional["VolumeRange"]] = relationship("VolumeRange", back_populates="revenue_pricing") # Made Optional based on nullable FK

    __table_args__ = (
        CheckConstraint(
            "(billing_method != 'subscription' AND service_type IS NOT NULL) OR (billing_method = 'subscription' AND service_type IS NULL AND volume_range_id IS NULL) OR (billing_method = 'default')",
            name='valid_revenue_billing_method_constraints'
        ),
        CheckConstraint(
            "billing_method IN ('default', 'subscription', 'block_6s', 'one_sec_plus', 'one_min_plus')",
            name='revenue_pricing_billing_method_check'
        ),
        # Updated constraint check using ServiceType values directly
        CheckConstraint(
            f"service_type IS NULL OR service_type IN {tuple(st.value for st in ServiceType)}",
            name='valid_revenue_service_type'
        ),
    )

# Inherit directly from Base
class CostPricing(Base):
    __tablename__ = "cost_pricing"

    id: Mapped[int] = mapped_column(primary_key=True)
    partner_id: Mapped[int] = mapped_column(ForeignKey("partners.id", ondelete="CASCADE"), nullable=False)
    created_by_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    billing_method: Mapped[BillingMethod] = mapped_column(String(20), nullable=False)
    service_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    volume_range_id: Mapped[Optional[int]] = mapped_column(ForeignKey("volume_ranges.id", ondelete="CASCADE"), nullable=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    price: Mapped[float] = mapped_column(Numeric(10, 2), nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)

    partner: Mapped["Partner"] = relationship("Partner", back_populates="pricing_rules")
    created_by: Mapped[Optional["User"]] = relationship("User", back_populates="pricing_rules") # Made Optional based on nullable FK
    volume_range: Mapped[Optional["VolumeRange"]] = relationship("VolumeRange", back_populates="cost_pricing") # Made Optional based on nullable FK

    __table_args__ = (
        CheckConstraint(
            "(billing_method != 'subscription' AND service_type IS NOT NULL) OR (billing_method = 'subscription' AND service_type IS NULL AND volume_range_id IS NULL) OR (billing_method = 'default')",
            name='valid_cost_billing_method_constraints'
        ),
        CheckConstraint(
            "billing_method IN ('default', 'subscription', 'block_6s', 'one_sec_plus', 'one_min_plus')",
            name='cost_pricing_billing_method_check'
        ),
        # Updated constraint check using ServiceType values directly
        CheckConstraint(
            f"service_type IS NULL OR service_type IN {tuple(st.value for st in ServiceType)}",
            name='valid_cost_service_type'
        ),
    )
