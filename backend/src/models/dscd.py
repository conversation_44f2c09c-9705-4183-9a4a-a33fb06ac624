import typing
from datetime import datetime
from typing import List, Optional, Any
from sqlalchemy import String, Float, Integer, ForeignKey, Text, DateTime, Boolean, Enum as SQLAlchemyEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
import enum

# Move Base import outside of TYPE_CHECKING
from ..database.base import Base 
from .enums import DauSoType, ReconciliationStatus
# Move relationship imports outside of TYPE_CHECKING as well
from .template import ReconciliationTemplate
from .partner import Partner
from .reconciliation_period import ReconciliationPeriod

# Remove TYPE_CHECKING block if no longer needed
# if typing.TYPE_CHECKING:
#     pass 

# Inherit directly from Base
class DoiSoatCoDinh(Base):
    """Model database cho đối soát cố định"""
    __tablename__ = "doi_soat_co_dinh"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    thang_doi_soat: Mapped[str] = mapped_column(String(20), nullable=False, index=True)
    hop_dong_so: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    file_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Thêm các trường còn thiếu
    tu_mang: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    den_doi_tac: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    
    # Liên kết với Template
    template_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("reconciliation_templates.id", ondelete="SET NULL"), 
        nullable=True
    )
    
    # Trường mới cho kỳ đối soát
    period_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("reconciliation_periods.id", ondelete="SET NULL"), 
        nullable=True
    )
    is_template_data: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Liên kết với Partner trực tiếp
    partner_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("partners.id", ondelete="SET NULL"), 
        nullable=True
    )
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # Relationships
    du_lieu: Mapped[List["DauSoDichVu"]] = relationship(
        "DauSoDichVu", 
        back_populates="doi_soat", 
        cascade="all, delete-orphan"
    )
    tong_ket: Mapped[Optional["TongKet"]] = relationship(
        "TongKet", 
        back_populates="doi_soat", 
        uselist=False, 
        cascade="all, delete-orphan"
    )
    # Define relationships directly, outside TYPE_CHECKING blocks
    period: Mapped[Optional["ReconciliationPeriod"]] = relationship("ReconciliationPeriod")
    template: Mapped[Optional["ReconciliationTemplate"]] = relationship("ReconciliationTemplate")
    partner: Mapped[Optional["Partner"]] = relationship("Partner")
    
    # --- START: Add status column ---
    status: Mapped[Optional["ReconciliationStatus"]] = mapped_column(
        SQLAlchemyEnum(ReconciliationStatus, name="reconciliation_status_enum", create_type=False),
        default=ReconciliationStatus.CALCULATED,
        nullable=False,
        index=True
    )
    # --- END: Add status column ---
    
    def __repr__(self) -> str:
        return f"DoiSoatCoDinh(id={self.id}, thang={self.thang_doi_soat})"

# Inherit directly from Base
class DauSoDichVu(Base):
    """Model database cho đầu số dịch vụ"""
    __tablename__ = "dau_so_dich_vu"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    doi_soat_id: Mapped[int] = mapped_column(ForeignKey("doi_soat_co_dinh.id", ondelete="CASCADE"), nullable=False)
    stt: Mapped[int] = mapped_column(Integer, nullable=False)
    raw_dau_so: Mapped[str] = mapped_column(Text, nullable=False, name="dau_so") # Ensure correct name mapping if needed
    
    # Thêm các trường mới
    standardized_display: Mapped[str] = mapped_column(Text, nullable=False, index=True)
    dau_so_type: Mapped[DauSoType] = mapped_column(SQLAlchemyEnum(DauSoType), nullable=False, default=DauSoType.UNKNOWN)
    number_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    # --- Thêm các trường chuẩn hóa chi tiết --- 
    start_num_str: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    end_num_str: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    prefix: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, index=True)
    # --- Kết thúc thêm trường --- 
    
    cuoc_thu_khach: Mapped[float] = mapped_column(Float, default=0)
    cuoc_tra_htc: Mapped[float] = mapped_column(Float, default=0)
    
    # --- START: Add adjusted fields ---
    cuoc_thu_khach_adjusted: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    cuoc_tra_htc_adjusted: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    # --- END: Add adjusted fields ---
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # Relationships
    doi_soat: Mapped["DoiSoatCoDinh"] = relationship("DoiSoatCoDinh", back_populates="du_lieu")
    co_dinh_noi_hat: Mapped[Optional["CuocGoi"]] = relationship( # Made optional
        foreign_keys="[CuocGoi.dau_so_id, CuocGoi.loai_cuoc]",
        primaryjoin="and_(DauSoDichVu.id==CuocGoi.dau_so_id, CuocGoi.loai_cuoc=='co_dinh_noi_hat')",
        uselist=False,
        cascade="all, delete-orphan"
    )
    co_dinh_lien_tinh: Mapped[Optional["CuocGoi"]] = relationship( # Made optional
        foreign_keys="[CuocGoi.dau_so_id, CuocGoi.loai_cuoc]",
        primaryjoin="and_(DauSoDichVu.id==CuocGoi.dau_so_id, CuocGoi.loai_cuoc=='co_dinh_lien_tinh')",
        uselist=False,
        cascade="all, delete-orphan",
        overlaps="co_dinh_noi_hat"
    )
    di_dong: Mapped[Optional["CuocGoi"]] = relationship( # Made optional
        foreign_keys="[CuocGoi.dau_so_id, CuocGoi.loai_cuoc]",
        primaryjoin="and_(DauSoDichVu.id==CuocGoi.dau_so_id, CuocGoi.loai_cuoc=='di_dong')",
        uselist=False,
        cascade="all, delete-orphan",
        overlaps="co_dinh_noi_hat,co_dinh_lien_tinh"
    )
    cuoc_1900: Mapped[Optional["CuocGoi"]] = relationship( # Made optional
        foreign_keys="[CuocGoi.dau_so_id, CuocGoi.loai_cuoc]",
        primaryjoin="and_(DauSoDichVu.id==CuocGoi.dau_so_id, CuocGoi.loai_cuoc=='cuoc_1900')",
        uselist=False,
        cascade="all, delete-orphan",
        overlaps="co_dinh_noi_hat,co_dinh_lien_tinh,di_dong"
    )
    quoc_te: Mapped[Optional["CuocGoi"]] = relationship( # Made optional
        foreign_keys="[CuocGoi.dau_so_id, CuocGoi.loai_cuoc]", 
        primaryjoin="and_(DauSoDichVu.id==CuocGoi.dau_so_id, CuocGoi.loai_cuoc=='quoc_te')",
        uselist=False,
        cascade="all, delete-orphan",
        overlaps="co_dinh_noi_hat,co_dinh_lien_tinh,di_dong,cuoc_1900"
    )
    cuoc_thue_bao: Mapped[Optional["CuocThueBao"]] = relationship( # Made optional
        back_populates="dau_so", 
        uselist=False, 
        cascade="all, delete-orphan"
    )

# Inherit directly from Base
class CuocGoi(Base):
    """Model database cho cặp thời gian gọi và cước phí"""
    __tablename__ = "cuoc_goi"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    dau_so_id: Mapped[int] = mapped_column(ForeignKey("dau_so_dich_vu.id", ondelete="CASCADE"), nullable=False)
    loai_cuoc: Mapped[str] = mapped_column(String(50), nullable=False)
    thoi_gian_goi: Mapped[float] = mapped_column(Float, default=0)
    cuoc: Mapped[float] = mapped_column(Float, default=0)
    
    # --- START: Add adjusted fields ---
    thoi_gian_goi_adjusted: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    cuoc_adjusted: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    # --- END: Add adjusted fields ---
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # Relationship back to DauSoDichVu (added for clarity, though primaryjoins handle it)
    # dau_so: Mapped["DauSoDichVu"] = relationship(foreign_keys=[dau_so_id])

# Inherit directly from Base
class CuocThueBao(Base):
    """Model database cho cước thuê bao"""
    __tablename__ = "cuoc_thue_bao"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    dau_so_id: Mapped[int] = mapped_column(ForeignKey("dau_so_dich_vu.id", ondelete="CASCADE"), nullable=False, unique=True)
    thue_bao_thang: Mapped[float] = mapped_column(Float, default=0)
    cam_ket_thang: Mapped[float] = mapped_column(Float, default=0)
    tra_truoc_thang: Mapped[float] = mapped_column(Float, default=0)
    
    # --- START: Add adjusted fields ---
    thue_bao_thang_adjusted: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    cam_ket_thang_adjusted: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    tra_truoc_thang_adjusted: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    # --- END: Add adjusted fields ---
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # Relationships
    dau_so: Mapped["DauSoDichVu"] = relationship("DauSoDichVu", back_populates="cuoc_thue_bao")

# Inherit directly from Base
class TongKet(Base):
    """Model database cho phần tổng kết"""
    __tablename__ = "tong_ket"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    doi_soat_id: Mapped[int] = mapped_column(ForeignKey("doi_soat_co_dinh.id", ondelete="CASCADE"), nullable=False, unique=True)
    cong_tien_dich_vu: Mapped[float] = mapped_column(Float, nullable=False)
    tien_thue_gtgt: Mapped[float] = mapped_column(Float, nullable=False)
    tong_cong_tien: Mapped[float] = mapped_column(Float, nullable=False)
    
    # --- START: Add adjusted fields ---
    cong_tien_dich_vu_adjusted: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    tien_thue_gtgt_adjusted: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    tong_cong_tien_adjusted: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    # --- END: Add adjusted fields ---
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # Relationships
    doi_soat: Mapped["DoiSoatCoDinh"] = relationship("DoiSoatCoDinh", back_populates="tong_ket") 