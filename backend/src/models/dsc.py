import typing
from datetime import datetime
from typing import List, Optional
from sqlalchemy import <PERSON>umn, Integer, String, Float, ForeignKey, DateTime, Boolean, Text, Enum as SQLAlchemyEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship

# Import Base trực tiếp vì nó cần cho runtime inheritance
from ..database.base import Base 
from .enums import ReconciliationStatus

if typing.TYPE_CHECKING:
    # Chỉ import các model liên quan cho type hinting trong khối này
    from .reconciliation_period import ReconciliationPeriod
    from .template import ReconciliationTemplate
    from .partner import Partner

class DoiSoatCuoc(Base): # <PERSON><PERSON> thừa trực tiếp từ Base
    """Model database cho đối soát cước"""
    __tablename__ = "doi_soat_cuoc"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    thang_doi_soat: Mapped[str] = mapped_column(String(20), nullable=False, index=True)
    tu_mang: Mapped[str] = mapped_column(String(20), nullable=False)
    den_doi_tac: Mapped[str] = mapped_column(String(100), nullable=False)
    file_name: Mapped[Optional[str]] = mapped_column(String(255))
    hop_dong_so: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Liên kết với Template
    template_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("reconciliation_templates.id", ondelete="SET NULL"), 
        nullable=True
    )
    
    # Trường mới cho kỳ đối soát
    period_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("reconciliation_periods.id", ondelete="SET NULL"), 
        nullable=True
    )
    is_template_data: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Liên kết với Partner trực tiếp
    partner_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("partners.id", ondelete="SET NULL"), 
        nullable=True
    )
    
    # --- START: Thêm các trường mới cho quy trình đối soát ---
    status: Mapped[ReconciliationStatus] = mapped_column(
        SQLAlchemyEnum(ReconciliationStatus, name="reconciliation_status_enum", create_type=False),
        default=ReconciliationStatus.TEMPLATE,
        nullable=False,
        index=True
    )
    task_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True) # Lưu Celery task ID
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True) # Lưu thông báo lỗi
    # --- END: Thêm các trường mới cho quy trình đối soát ---
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    du_lieu: Mapped[List["DauSoDichVuCuoc"]] = relationship(
        "DauSoDichVuCuoc", 
        back_populates="doi_soat", 
        cascade="all, delete-orphan"
    )
    tong_ket: Mapped["TongKetCuoc"] = relationship(
        "TongKetCuoc", 
        back_populates="doi_soat", 
        uselist=False, 
        cascade="all, delete-orphan"
    )
    period: Mapped[Optional["ReconciliationPeriod"]] = relationship("ReconciliationPeriod")
    # Thêm relationship với Template và Partner
    template: Mapped[Optional["ReconciliationTemplate"]] = relationship("ReconciliationTemplate")
    partner: Mapped[Optional["Partner"]] = relationship("Partner")
    
    def __repr__(self) -> str:
        return f"DoiSoatCuoc(id={self.id}, thang={self.thang_doi_soat}, tu_mang={self.tu_mang}, den_doi_tac={self.den_doi_tac})"


class DauSoDichVuCuoc(Base): # Kế thừa trực tiếp từ Base
    """Model database cho đầu số dịch vụ trong đối soát cước"""
    __tablename__ = "dau_so_dich_vu_cuoc"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    doi_soat_id: Mapped[int] = mapped_column(ForeignKey("doi_soat_cuoc.id", ondelete="CASCADE"), nullable=False)
    stt: Mapped[int] = mapped_column(Integer, nullable=False)
    dau_so: Mapped[str] = mapped_column(String(20), nullable=False, index=True)
    
    # VNM
    vnm_san_luong: Mapped[float] = mapped_column(Float, default=0)
    vnm_ty_le_cp: Mapped[float] = mapped_column(Float, default=0)
    vnm_thanh_tien: Mapped[float] = mapped_column(Float, default=0)
    
    # VIETTEL
    viettel_san_luong: Mapped[float] = mapped_column(Float, default=0)
    viettel_ty_le_cp: Mapped[float] = mapped_column(Float, default=0)
    viettel_thanh_tien: Mapped[float] = mapped_column(Float, default=0)
    
    # VNPT
    vnpt_san_luong: Mapped[float] = mapped_column(Float, default=0)
    vnpt_ty_le_cp: Mapped[float] = mapped_column(Float, default=0)
    vnpt_thanh_tien: Mapped[float] = mapped_column(Float, default=0)
    
    # VMS
    vms_san_luong: Mapped[float] = mapped_column(Float, default=0)
    vms_ty_le_cp: Mapped[float] = mapped_column(Float, default=0)
    vms_thanh_tien: Mapped[float] = mapped_column(Float, default=0)
    
    # Các mạng còn lại
    khac_san_luong: Mapped[float] = mapped_column(Float, default=0)
    khac_ty_le_cp: Mapped[float] = mapped_column(Float, default=0)
    khac_thanh_tien: Mapped[float] = mapped_column(Float, default=0)
    
    # Tổng tiền
    tong_thanh_toan: Mapped[float] = mapped_column(Float, default=0)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    doi_soat: Mapped["DoiSoatCuoc"] = relationship("DoiSoatCuoc", back_populates="du_lieu")
    
    def __repr__(self) -> str:
        return f"DauSoDichVuCuoc(id={self.id}, dau_so={self.dau_so}, tong_thanh_toan={self.tong_thanh_toan})"


class TongKetCuoc(Base): # Kế thừa trực tiếp từ Base
    """Model database cho phần tổng kết trong đối soát cước"""
    __tablename__ = "tong_ket_cuoc"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    doi_soat_id: Mapped[int] = mapped_column(ForeignKey("doi_soat_cuoc.id", ondelete="CASCADE"), nullable=False, unique=True)
    cong_tien_dich_vu: Mapped[float] = mapped_column(Float, nullable=False)
    tien_thue_gtgt: Mapped[float] = mapped_column(Float, nullable=False)
    tong_cong_tien: Mapped[float] = mapped_column(Float, nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    doi_soat: Mapped["DoiSoatCuoc"] = relationship("DoiSoatCuoc", back_populates="tong_ket")
    
    def __repr__(self) -> str:
        return f"TongKetCuoc(id={self.id}, tong_cong_tien={self.tong_cong_tien})" 