import typing
from datetime import datetime
from typing import Optional, List, Any, Dict
from sqlalchemy import String, Text, ForeignKey, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..database.base import Base # Import Base directly
from enum import Enum as PyEnum

if typing.TYPE_CHECKING:
    # Remove Base import from here
    from .user import User # Add missing import for relationship
    from .partner import Partner # Add missing import for relationship

class TemplateType(str, PyEnum):
    DSC = "dsc"  # Đối soát cước
    DSCD = "dscd"  # Đối soát cố định
    DST_1800_1900 = "dst_1800_1900"  # Đối soát 1800/1900
    CKN = "ckn"  # Cước kết nối

class ReconciliationTemplate(Base):
    """Model lưu trữ mẫu đối soát"""
    __tablename__ = "reconciliation_templates"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    template_type: Mapped[str] = mapped_column(String(20), nullable=False)
    file_path: Mapped[str] = mapped_column(String(255), nullable=False)
    file_name: Mapped[str] = mapped_column(String(255), nullable=False)
    uploaded_by: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    is_active: Mapped[bool] = mapped_column(default=True)
    partner_id: Mapped[Optional[int]] = mapped_column(ForeignKey("partners.id"), nullable=True)
    
    # Trường mới cho xử lý và phân tích
    status: Mapped[str] = mapped_column(String(20), default="pending", nullable=False)  # pending, processing, completed, failed
    task_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)  # Celery task ID
    processed_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)  # Kết quả phân tích
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # Thông báo lỗi nếu có
    processing_time: Mapped[Optional[float]] = mapped_column(nullable=True)  # Thời gian xử lý (giây)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(
        default=datetime.utcnow, onupdate=datetime.utcnow
    )
    
    # Relationships
    user: Mapped["User"] = relationship(back_populates="reconciliation_templates", foreign_keys=[uploaded_by])
    partner: Mapped[Optional["Partner"]] = relationship("Partner", foreign_keys=[partner_id])

# For backward compatibility
DoiSoatTemplate = ReconciliationTemplate 