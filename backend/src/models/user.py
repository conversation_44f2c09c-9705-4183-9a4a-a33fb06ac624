import typing
from datetime import datetime
from typing import Optional, List, TYPE_CHECKING # Import TYPE_CHECKING
from sqlalchemy import String, DateTime, Boolean
from sqlalchemy.orm import Mapped, mapped_column, relationship
from ..database.base import Base # Import Base directly
# Import non-circular dependencies directly
# from .pricing import CostPricing # Keep this commented or remove if moved to TYPE_CHECKING
from .call_log import CallLogFile # Move this import out
# Remove direct import of ReconciliationTemplate
# from .template import ReconciliationTemplate 
# from .reconciliation_period import ReconciliationPeriod # Keep this commented or remove if moved to TYPE_CHECKING

# Keep TYPE_CHECKING block for CIRCULAR imports needed for hints
if TYPE_CHECKING:
    # Import models that cause circular dependency if imported directly
    from .reconciliation_period import ReconciliationPeriod 
    from .pricing import CostPricing # Keep CostPricing here if it causes circularity elsewhere
    # Add ReconciliationTemplate import here
    from .template import ReconciliationTemplate 

# Inherit directly from Base
class User(Base):
    __tablename__ = "users"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    email: Mapped[str] = mapped_column(String(255), unique=True, index=True, nullable=False)
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)
    full_name: Mapped[Optional[str]] = mapped_column(String(100))
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_admin: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships (using string references for potentially circular ones)
    pricing_rules: Mapped[List["CostPricing"]] = relationship(back_populates="created_by") 
    # Use direct import name for non-circular CallLogFile
    call_log_files: Mapped[List["CallLogFile"]] = relationship(back_populates="uploader")
    reconciliation_templates: Mapped[List["ReconciliationTemplate"]] = relationship(back_populates="user")
    reconciliation_periods: Mapped[List["ReconciliationPeriod"]] = relationship(back_populates="user") 