from enum import Enum

class ServiceType(str, Enum):
    # FIXED LINE Services
    FIXED_NOI_HAT = "FIXED_NOI_HAT"
    FIXED_LIEN_TINH_NOI_MANG = "FIXED_LIEN_TINH_NOI_MANG"
    FIXED_LIEN_TINH_KHAC_MANG = "FIXED_LIEN_TINH_KHAC_MANG"
    FIXED_QUOC_TE = "FIXED_QUOC_TE"
    
    # Mobile Services
    MOBILE_NORMAL = "MOBILE_NORMAL"
    MOBILE_MNP = "MOBILE_MNP"
    
    # 1800 Services
    SERVICE_1800_VOICE_CD = "SERVICE_1800_VOICE_CD"
    SERVICE_1800_VOICE_MOBILE = "SERVICE_1800_VOICE_MOBILE"
    
    # 1900 Services
    SERVICE_1900_VOICE_VAS = "SERVICE_1900_VOICE_VAS"
    SERVICE_1900_SMS = "SERVICE_1900_SMS"
    
    # International Services
    INTL_OUTBOUND = "INTL_OUTBOUND"
    INTL_INBOUND_VND = "INTL_INBOUND_VND"
    INTL_INBOUND_USD = "INTL_INBOUND_USD"

class DauSoType(str, Enum):
    SINGLE = "SINGLE"       # Số đơn lẻ
    RANGE_10 = "RANGE_10"   # Dải 10 số
    RANGE_100 = "RANGE_100" # Dải 100 số
    RANGE_OTHER = "RANGE_OTHER" # Dải số lượng khác
    PREFIX = "PREFIX"       # Chỉ có tiền tố (ví dụ: 24...)
    UNKNOWN = "UNKNOWN"     # Không xác định được

# Dictionary để map code sang tên hiển thị
SERVICE_TYPE_NAMES = {
    ServiceType.FIXED_NOI_HAT: "Gọi cố định nội hạt",
    ServiceType.FIXED_LIEN_TINH_NOI_MANG: "Gọi cố định liên tỉnh nội mạng",
    ServiceType.FIXED_LIEN_TINH_KHAC_MANG: "Gọi cố định liên tỉnh mạng khác",
    ServiceType.FIXED_QUOC_TE: "Gọi quốc tế",
    ServiceType.MOBILE_NORMAL: "Gọi di động tại VN",
    ServiceType.MOBILE_MNP: "Gọi di động chuyển mạng giữ số",
    ServiceType.SERVICE_1800_VOICE_CD: "1800 - Thoại CD",
    ServiceType.SERVICE_1800_VOICE_MOBILE: "1800 - Thoại Di động",
    ServiceType.SERVICE_1900_VOICE_VAS: "1900 - Thoại/GTGT",
    ServiceType.SERVICE_1900_SMS: "1900 - SMS",
    ServiceType.INTL_OUTBOUND: "Quốc tế - Chiều đi",
    ServiceType.INTL_INBOUND_VND: "Quốc tế - Chiều về (VNĐ)",
    ServiceType.INTL_INBOUND_USD: "Quốc tế - Chiều về (USD)",
}

# --- START: Add ReconciliationStatus Enum ---
class ReconciliationStatus(str, Enum):
    TEMPLATE = "template"     # Trạng thái dữ liệu gốc từ template
    PROCESSING = "processing" # Đang xử lý
    CALCULATED = "calculated" # Đã tính toán từ call logs
    ADJUSTED = "adjusted"     # Đã hiệu chỉnh
    FINALIZED = "finalized"   # Đã chốt
    ERROR = "error"          # Xử lý thất bại
# --- END: Add ReconciliationStatus Enum ---

# --- START: Add ReconciliationType Enum ---
class ReconciliationType(str, Enum):
    """Loại bản ghi đối soát."""
    DSCD = "DSCD" # Đối soát cố định
    DSC = "DSC"   # Đối soát di động (sẽ thêm sau)
    TELCO_1800_1900 = "TELCO_1800_1900" # Đối soát Telco 1800/1900 (sẽ thêm sau)
    # Thêm các loại khác nếu cần
# --- END: Add ReconciliationType Enum ---

# SERVICE_CATEGORIES được comment lại vì hiện tại không cần
"""
SERVICE_CATEGORIES = {
    "FIXED_LINE": [ServiceType.FIXED_NOI_HAT, ServiceType.FIXED_LIEN_TINH_NOI_MANG, 
                  ServiceType.FIXED_LIEN_TINH_KHAC_MANG, ServiceType.FIXED_QUOC_TE],
    "MOBILE": [ServiceType.MOBILE_NORMAL, ServiceType.MOBILE_MNP],
    "SERVICE_1800": [ServiceType.SERVICE_1800_VOICE_CD, ServiceType.SERVICE_1800_VOICE_MOBILE],
    "SERVICE_1900": [ServiceType.SERVICE_1900_VOICE_VAS, ServiceType.SERVICE_1900_SMS],
    "INTERNATIONAL": [ServiceType.INTL_OUTBOUND, ServiceType.INTL_INBOUND_VND, ServiceType.INTL_INBOUND_USD]
}
""" 