from datetime import datetime
from typing import List, Optional
import enum

from sqlalchemy import (
    String, Float, Integer, ForeignKey, DateTime, Enum, Text, Boolean
)
from sqlalchemy.orm import (
    Mapped, mapped_column, relationship
)

from ..database.base import Base

class DoiSoat1800_1900(Base):
    """M<PERSON> hình lưu trữ thông tin đối soát 1800/1900"""
    __tablename__ = "doi_soat_1800_1900"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    thang_doi_soat: Mapped[str] = mapped_column(String(20), index=True, nullable=False)
    tu_mang: Mapped[str] = mapped_column(String(50), index=True, nullable=False)
    den_doi_tac: Mapped[str] = mapped_column(String(50), index=True, nullable=False)
    loai_dich_vu: Mapped[str] = mapped_column(String(20), index=True, nullable=False)
    hop_dong_so: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    file_name: Mapped[str] = mapped_column(String(255), nullable=False)
    ghi_chu: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Trường mới cho kỳ đối soát
    period_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("reconciliation_periods.id", ondelete="SET NULL"), 
        nullable=True
    )
    is_template_data: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Quan hệ với các model khác
    nhom_dich_vu: Mapped[List["NhomDichVu1800_1900"]] = relationship(
        back_populates="doi_soat", cascade="all, delete-orphan"
    )
    thanh_toan: Mapped["ThanhToan1800_1900"] = relationship(
        back_populates="doi_soat", cascade="all, delete-orphan", uselist=False
    )
    period: Mapped[Optional["ReconciliationPeriod"]] = relationship("ReconciliationPeriod")
    
    # Thông tin thời gian
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    
    def __repr__(self) -> str:
        return f"DoiSoat1800_1900(id={self.id}, thang_doi_soat='{self.thang_doi_soat}', tu_mang='{self.tu_mang}', den_doi_tac='{self.den_doi_tac}')"


class NhomDichVu1800_1900(Base):
    """Mô hình lưu trữ thông tin nhóm dịch vụ trong đối soát 1800/1900"""
    __tablename__ = "nhom_dich_vu_1800_1900"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    doi_soat_id: Mapped[int] = mapped_column(ForeignKey("doi_soat_1800_1900.id"), nullable=False)
    ten_dich_vu: Mapped[str] = mapped_column(String(50), nullable=False)
    
    # Quan hệ với các model khác
    doi_soat: Mapped["DoiSoat1800_1900"] = relationship(back_populates="nhom_dich_vu")
    chi_tiet: Mapped[List["ChiTietDichVu1800_1900"]] = relationship(
        back_populates="nhom_dich_vu", cascade="all, delete-orphan"
    )
    tong_ket: Mapped["TongKet1800_1900"] = relationship(
        back_populates="nhom_dich_vu", cascade="all, delete-orphan", uselist=False
    )
    
    # Thông tin thời gian
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    
    def __repr__(self) -> str:
        return f"NhomDichVu1800_1900(id={self.id}, ten_dich_vu='{self.ten_dich_vu}')"


class ChiTietDichVu1800_1900(Base):
    """Mô hình lưu trữ thông tin chi tiết dịch vụ trong đối soát 1800/1900"""
    __tablename__ = "chi_tiet_dich_vu_1800_1900"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    nhom_dich_vu_id: Mapped[int] = mapped_column(ForeignKey("nhom_dich_vu_1800_1900.id"), nullable=False)
    stt: Mapped[int] = mapped_column(Integer, nullable=False)
    so_dich_vu: Mapped[str] = mapped_column(String(20), nullable=False)
    so_lieu_ben_a: Mapped[float] = mapped_column(Float, default=0)
    so_lieu_ben_b: Mapped[float] = mapped_column(Float, default=0)
    chenh_lech: Mapped[float] = mapped_column(Float, default=0)
    so_lieu_tinh_dt: Mapped[float] = mapped_column(Float, default=0)
    muc_cuoc: Mapped[float] = mapped_column(Float, default=0)
    cuoc_thu_khach: Mapped[float] = mapped_column(Float, default=0)
    doanh_thu_ben_a: Mapped[float] = mapped_column(Float, default=0)
    doanh_thu_ben_b: Mapped[float] = mapped_column(Float, default=0)
    
    # Quan hệ với các model khác
    nhom_dich_vu: Mapped["NhomDichVu1800_1900"] = relationship(back_populates="chi_tiet")
    
    # Thông tin thời gian
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    
    def __repr__(self) -> str:
        return f"ChiTietDichVu1800_1900(id={self.id}, so_dich_vu='{self.so_dich_vu}')"


class TongKet1800_1900(Base):
    """Mô hình lưu trữ thông tin tổng kết nhóm dịch vụ trong đối soát 1800/1900"""
    __tablename__ = "tong_ket_1800_1900"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    nhom_dich_vu_id: Mapped[int] = mapped_column(ForeignKey("nhom_dich_vu_1800_1900.id"), nullable=False)
    tong_cong_chua_vat: Mapped[float] = mapped_column(Float, default=0)
    thue_vat: Mapped[float] = mapped_column(Float, default=0)
    tong_cong_co_vat: Mapped[float] = mapped_column(Float, default=0)
    
    # Quan hệ với các model khác
    nhom_dich_vu: Mapped["NhomDichVu1800_1900"] = relationship(back_populates="tong_ket")
    
    # Thông tin thời gian
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    
    def __repr__(self) -> str:
        return f"TongKet1800_1900(id={self.id}, tong_cong_chua_vat={self.tong_cong_chua_vat}, thue_vat={self.thue_vat})"


class ThanhToan1800_1900(Base):
    """Mô hình lưu trữ thông tin thanh toán trong đối soát 1800/1900"""
    __tablename__ = "thanh_toan_1800_1900"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    doi_soat_id: Mapped[int] = mapped_column(ForeignKey("doi_soat_1800_1900.id"), nullable=False)
    doanh_thu_ben_a_thanh_toan: Mapped[float] = mapped_column(Float, default=0)
    doanh_thu_ben_b_thanh_toan: Mapped[float] = mapped_column(Float, default=0)
    sau_bu_tru: Mapped[float] = mapped_column(Float, default=0)
    ben_thanh_toan: Mapped[str] = mapped_column(String(50), nullable=False)
    ben_nhan: Mapped[str] = mapped_column(String(50), nullable=False)
    ghi_chu: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Quan hệ với các model khác
    doi_soat: Mapped["DoiSoat1800_1900"] = relationship(back_populates="thanh_toan")
    
    # Thông tin thời gian
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    
    def __repr__(self) -> str:
        return f"ThanhToan1800_1900(id={self.id}, sau_bu_tru={self.sau_bu_tru})" 