import typing
from typing import Optional
from sqlalchemy import String, Numeric, CheckConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from enum import Enum as PyEnum

# Import Base directly
from ..database.base import Base

if typing.TYPE_CHECKING:
    # Remove Base import from here
    from .pricing import RevenuePricing, CostPricing # Needed for relationships

class VolumeUnit(str, PyEnum):
    VND = "VND"
    MINUTE = "minute"
    MINUTE_PER_MONTH = "minute_per_month"

# Inherit directly from Base
class VolumeRange(Base):
    __tablename__ = "volume_ranges"

    id: Mapped[int] = mapped_column(primary_key=True)
    min_value: Mapped[Optional[float]] = mapped_column(Numeric(20, 2), nullable=True)
    max_value: Mapped[Optional[float]] = mapped_column(Numeric(20, 2), nullable=True)
    unit: Mapped[VolumeUnit] = mapped_column(String(20), nullable=False)
    description: Mapped[str] = mapped_column(String(100), nullable=False)

    revenue_pricing: Mapped[list["RevenuePricing"]] = relationship(back_populates="volume_range")
    cost_pricing: Mapped[list["CostPricing"]] = relationship(back_populates="volume_range")

    __table_args__ = (
        CheckConstraint('max_value IS NULL OR max_value > min_value', name='volume_range_check'),
        CheckConstraint(
            "unit IN ('VND', 'minute', 'minute_per_month')",
            name='volume_ranges_unit_check'
        ),
    ) 