from datetime import datetime
from typing import Optional, List, TYPE_CHECKING
from sqlalchemy import String, DateTime, ForeignKey, Text, Boolean, Integer, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..database.base import Base
# Remove direct User import
# from .user import User 

# Place User import inside TYPE_CHECKING
if TYPE_CHECKING:
    from .user import User

class ReconciliationPeriod(Base):
    """Model lưu trữ kỳ đối soát (chỉ theo tháng/năm)"""
    __tablename__ = "reconciliation_periods"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    month: Mapped[int] = mapped_column(Integer, index=True, nullable=False)
    year: Mapped[int] = mapped_column(Integer, index=True, nullable=False)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    status: Mapped[str] = mapped_column(String(20), default="open", nullable=False)
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_by: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(
        default=datetime.utcnow, onupdate=datetime.utcnow
    )
    
    # Relationships (Using string reference "User")
    user: Mapped["User"] = relationship("User", back_populates="reconciliation_periods")

    # Add a unique constraint for month and year combination
    __table_args__ = (
        UniqueConstraint('year', 'month', name='_year_month_uc'),
    )

    # TODO: Add relationship to Reconciliation instances once that model is defined/confirmed
    # reconciliations: Mapped[List["Reconciliation"]] = relationship("Reconciliation", back_populates="period") 