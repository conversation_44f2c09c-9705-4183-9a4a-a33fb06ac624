import typing
from datetime import datetime, date
from typing import Optional, List
from sqlalchemy import String, DateTime, Integer, Date, Float, Text, ForeignKey, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship
from enum import Enum as PyEnum

# Import Base directly
from ..database.base import Base

# Keep User import in TYPE_CHECKING for circular dependency
if typing.TYPE_CHECKING:
    # Remove Base import from here
    # from ..database.base import Base
    from .user import User # For relationship in CallLogFile

class FileType(str, PyEnum):
    CALL_IN = "call_in"
    CALL_OUT = "call_out"

class CallType(str, PyEnum):
    IN = "in"
    OUT = "out"

class FileStatus(str, PyEnum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class NumberType(str, PyEnum):
    MOBILE = "mobile"    # Số di động 
    FIXED = "fixed"      # Số cố định
    INTL = "intl"        # Số quốc tế 
    PORTED = "ported"    # <PERSON><PERSON> đã chuyển mạng
    UNKNOWN = "unknown"  # Không xác định

# Inherit directly from Base
class CallLogFile(Base):
    __tablename__ = "call_log_files"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    filename: Mapped[str] = mapped_column(String(255))
    file_type: Mapped[FileType] = mapped_column(String(20))
    uploaded_by: Mapped[int] = mapped_column(ForeignKey("users.id"))
    uploaded_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    status: Mapped[FileStatus] = mapped_column(String(20), default=FileStatus.PENDING)
    total_rows: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    processed_rows: Mapped[int] = mapped_column(Integer, default=0)
    error_count: Mapped[int] = mapped_column(Integer, default=0)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    processing_time: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    file_path: Mapped[str] = mapped_column(String(512))
    task_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Relationships (Use string ref for User)
    uploader: Mapped["User"] = relationship("User", back_populates="call_log_files")
    call_logs: Mapped[List["CallLog"]] = relationship("CallLog", back_populates="file") # Added List type hint

# Inherit directly from Base
class CallLog(Base):
    __tablename__ = "call_logs"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    file_id: Mapped[int] = mapped_column(ForeignKey("call_log_files.id", ondelete="CASCADE"))
    call_type: Mapped[CallType] = mapped_column(String(20))
    
    # Các trường dữ liệu call log thống nhất
    caller: Mapped[str] = mapped_column(String(50))
    callee: Mapped[str] = mapped_column(String(50))
    begin_time: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    end_time: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    duration: Mapped[int] = mapped_column(Integer)
    caller_gateway: Mapped[str] = mapped_column(String(100))
    called_gateway: Mapped[str] = mapped_column(String(100))
    
    # Thêm phân loại số điện thoại
    caller_type: Mapped[NumberType] = mapped_column(String(20), default=NumberType.FIXED)
    callee_type: Mapped[NumberType] = mapped_column(String(20), default=NumberType.FIXED)
    
    # Thêm ghi chú cho số điện thoại
    caller_note: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    callee_note: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    # Thêm prefix cho caller và callee
    caller_prefix: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)
    callee_prefix: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)
    
    # Thêm thông tin area cho call out
    area_prefix: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    area_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    # Các trường phân tích
    call_date: Mapped[date] = mapped_column(Date)
    call_hour: Mapped[int] = mapped_column(Integer)
    
    # Relationships (Use string ref for CallLogFile)
    file: Mapped["CallLogFile"] = relationship("CallLogFile", back_populates="call_logs")
    
    # Indexes để tối ưu truy vấn
    __table_args__ = (
        Index("ix_call_logs_call_date", "call_date"),
        Index("ix_call_logs_caller", "caller"),
        Index("ix_call_logs_callee", "callee"),
        Index("ix_call_logs_file_id", "file_id"),
        Index("ix_call_logs_caller_type", "caller_type"),
        Index("ix_call_logs_callee_type", "callee_type"),
        Index("ix_call_logs_area_prefix", "area_prefix"),  # Thêm index cho area_prefix
        Index("ix_call_logs_caller_prefix", "caller_prefix"), # Index cho caller_prefix
        Index("ix_call_logs_callee_prefix", "callee_prefix"), # Index cho callee_prefix
    ) 