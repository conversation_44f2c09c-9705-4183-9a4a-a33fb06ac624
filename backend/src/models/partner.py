import typing
from datetime import datetime
from typing import Optional, List
from sqlalchemy import String, DateTime, Enum as S<PERSON><PERSON><PERSON>, ForeignKey, event, Integer
from sqlalchemy.orm import Mapped, mapped_column, relationship
from enum import Enum as PyEnum
import re

# Import Base directly
from ..database.base import Base 

if typing.TYPE_CHECKING:
    # Remove Base import from here
    from .pricing import CostPricing, RevenuePricing # Needed for relationships

class PartnerType(str, PyEnum):
    TELCO = "telco"
    CP = "cp"

    def _generate_next_value_(name, start, count, last_values):
        return name.lower()

# Inherit directly from Base
class PartnerRawName(Base):
    __tablename__ = "partner_raw_names"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    partner_id: Mapped[int] = mapped_column(ForeignKey("partners.id", ondelete="CASCADE"), nullable=False)
    raw_name: Mapped[str] = mapped_column(String(100), nullable=False, index=True, unique=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationship back to partner
    partner: Mapped["Partner"] = relationship("Partner", back_populates="raw_names")

# Inherit directly from Base
class Partner(Base):
    __tablename__ = "partners"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    type: Mapped[PartnerType] = mapped_column(SQLEnum(PartnerType, values_callable=lambda x: [e.value for e in x]), nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(String(500))
    is_active: Mapped[bool] = mapped_column(default=True, nullable=False)
    # CP error adjustment: -1 for setting all calls to -1s duration, +1 for increasing 0s calls to 1s
    error_adjustment: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )

    # Contact information
    contact_name: Mapped[Optional[str]] = mapped_column(String(100))
    contact_email: Mapped[Optional[str]] = mapped_column(String(255))
    contact_phone: Mapped[Optional[str]] = mapped_column(String(20))

    # Relationships
    raw_names: Mapped[List["PartnerRawName"]] = relationship(
        "PartnerRawName",
        back_populates="partner",
        cascade="all, delete-orphan"
    )
    pricing_rules: Mapped[List["CostPricing"]] = relationship(
        "CostPricing",
        back_populates="partner",
        cascade="all, delete-orphan"
    )
    revenue_rules: Mapped[List["RevenuePricing"]] = relationship(
        "RevenuePricing",
        back_populates="partner",
        cascade="all, delete-orphan"
    )

def normalize_partner_name(name: str) -> str:
    """
    Chuẩn hóa tên partner:
    1. Chuyển thành chữ hoa
    2. Loại bỏ khoảng trắng thừa ở đầu và cuối
    3. Thay thế nhiều khoảng trắng liên tiếp bằng một khoảng trắng
    """
    if not name:
        return name
    # Loại bỏ khoảng trắng thừa ở đầu và cuối
    name = name.strip()
    # Thay thế nhiều khoảng trắng liên tiếp bằng một khoảng trắng
    name = re.sub(r'\s+', ' ', name)
    # Chuyển thành chữ hoa
    return name.upper()

# Event listener để tự động chuẩn hóa tên partner
@event.listens_for(Partner, 'before_insert')
@event.listens_for(Partner, 'before_update')
def normalize_partner_name_listener(mapper, connection, target):
    if target.name:
        target.name = normalize_partner_name(target.name) 