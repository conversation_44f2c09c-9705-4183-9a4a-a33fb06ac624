from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import DateTime, ForeignKey, Integer, Numeric, String, Text, func
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship


class Base(DeclarativeBase):
    pass


class TotalReportCalculated(Base):
    __tablename__ = "total_report_calculated"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    year: Mapped[int] = mapped_column(Integer, index=True)
    month: Mapped[int] = mapped_column(Integer, index=True)
    raw_format: Mapped[str] = mapped_column(String, index=True)

    # Foreign Keys
    partner_id: Mapped[Optional[int]] = mapped_column(ForeignKey("partners.id"), index=True, nullable=True)

    # Template Info
    template_type: Mapped[Optional[str]] = mapped_column(String, index=True, nullable=True)
    template_id: Mapped[Optional[str]] = mapped_column(String, index=True, nullable=True)

    # Service Info
    pic: Mapped[str] = mapped_column(String, index=True)
    service_category: Mapped[str] = mapped_column(String, index=True)
    service_group: Mapped[str] = mapped_column(String, index=True)
    service_type: Mapped[str] = mapped_column(String, index=True)
    service_name: Mapped[str] = mapped_column(String, index=True)
    service_name_2: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    service_detail: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    partner_name: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    vat: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), nullable=True)
    revenue_type_24: Mapped[Optional[str]] = mapped_column(String, nullable=True)

    # Calculated Values
    quantity: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    unit_price: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), nullable=True)
    amount_receivable: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), nullable=True)
    revenue: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2), nullable=True)

    # Other Info
    name: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    sql_pair: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    # Relationships (Optional, uncomment if needed and Partner model is defined)
    # partner: Mapped[Optional["Partner"]] = relationship()

    def __repr__(self):
        return f"<TotalReportCalculated(id={self.id}, year={self.year}, month={self.month}, partner_id={self.partner_id}, template_type='{self.template_type}', service_name='{self.service_name}')>" 