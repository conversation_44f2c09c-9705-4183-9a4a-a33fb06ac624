from celery import shared_task
from sqlalchemy import create_engine, text
from datetime import datetime, timedelta
import logging
from ..core.config import settings

logger = logging.getLogger(__name__)

@shared_task
def cleanup_old_call_logs():
    """
    Task định kỳ xóa dữ liệu nhật ký cuộc gọi cũ hơn 3 tháng
    Phương pháp xóa theo batch để tránh khóa bảng quá lâu
    """
    try:
        logger.info("Bắt đầu quá trình xóa dữ liệu cũ")
        
        # Tính thời điểm cách 3 tháng so với hiện tại
        three_months_ago = (datetime.now() - timedelta(days=90)).date()
        logger.info(f"Xóa dữ liệu trước ngày: {three_months_ago}")
        
        # Kết nối trực tiếp đến database để tránh khóa transaction của SQLAlchemy session
        engine = create_engine(settings.DATABASE_URI)
        
        with engine.connect() as conn:
            # Bắt đầu transaction
            trans = conn.begin()
            try:
                # <PERSON><PERSON><PERSON> danh sách các file_id cần xóa (files cũ hơn 3 tháng)
                file_ids_query = text("""
                    SELECT id FROM call_log_files 
                    WHERE uploaded_at < :cutoff_date
                    ORDER BY uploaded_at
                    LIMIT 100
                """)
                
                result = conn.execute(file_ids_query, {"cutoff_date": three_months_ago})
                file_ids = [row[0] for row in result]
                
                if not file_ids:
                    logger.info("Không có dữ liệu cần xóa")
                    return "No data to clean up"
                
                logger.info(f"Tìm thấy {len(file_ids)} file cần xóa")
                
                # Xóa theo batch
                batch_size = 1000
                for file_id in file_ids:
                    # Đếm số bản ghi cần xóa
                    count_query = text("SELECT COUNT(*) FROM call_logs WHERE file_id = :file_id")
                    total_rows = conn.execute(count_query, {"file_id": file_id}).scalar()
                    
                    logger.info(f"Xóa {total_rows} bản ghi từ file_id {file_id}")
                    
                    # Xử lý xóa theo batch nếu có quá nhiều bản ghi
                    if total_rows > batch_size:
                        offset = 0
                        while offset < total_rows:
                            # Lấy các id cần xóa theo batch
                            batch_ids_query = text(f"""
                                SELECT id FROM call_logs
                                WHERE file_id = :file_id
                                ORDER BY id
                                LIMIT {batch_size}
                            """)
                            batch_ids = [row[0] for row in conn.execute(batch_ids_query, {"file_id": file_id})]
                            
                            if not batch_ids:
                                break
                                
                            # Xóa batch
                            ids_str = ",".join(str(id) for id in batch_ids)
                            delete_batch = text(f"DELETE FROM call_logs WHERE id IN ({ids_str})")
                            conn.execute(delete_batch)
                            
                            logger.info(f"Đã xóa batch {offset} đến {offset + len(batch_ids)}")
                            offset += len(batch_ids)
                    else:
                        # Xóa tất cả nếu số lượng nhỏ
                        delete_logs = text("DELETE FROM call_logs WHERE file_id = :file_id")
                        conn.execute(delete_logs, {"file_id": file_id})
                    
                    # Xóa bản ghi file
                    delete_file = text("DELETE FROM call_log_files WHERE id = :file_id")
                    conn.execute(delete_file, {"file_id": file_id})
                
                # Commit transaction
                trans.commit()
                logger.info(f"Xóa thành công {len(file_ids)} file và dữ liệu liên quan")
                
                return f"Successfully cleaned up {len(file_ids)} files"
                
            except Exception as e:
                trans.rollback()
                logger.error(f"Lỗi khi xóa dữ liệu cũ: {str(e)}")
                raise
    
    except Exception as e:
        logger.error(f"Lỗi: {str(e)}")
        return f"Error: {str(e)}" 