import os
import time
import logging
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
import copy

from .celery_app import celery_app
from ..database.session import SessionLocal
from ..models.template import TemplateType, ReconciliationTemplate
from ..utils.file_processor import process_file_async
from ..utils.dsc_processor import process_dsc_file
from ..utils.dscd_processor import process_dscd_file
from ..utils.dst_1800_1900_processor import process_dst_1800_1900_file
from ..utils.db_helpers import (
    save_doi_soat_cuoc_to_db,
    save_doi_soat_co_dinh_to_db,
    save_doi_soat_1800_1900_to_db,
    sync_template_with_dscd
)
from ..services.call_log_processor import CallLogProcessor

# Cấu hình logging
logger = logging.getLogger(__name__)

def optimize_db_connection(db: Session) -> Session:
    """T<PERSON><PERSON> <PERSON><PERSON> kết nối database"""
    # Set timeout ngắn hơn cho query
    db.execute(text("SET statement_timeout = '3600s'"))
    # Tối ưu cho việc insert nhiều dữ liệu
    db.execute(text("SET synchronous_commit = off"))
    return db

@celery_app.task(bind=True, time_limit=300, soft_time_limit=240)
def process_template_file(self, template_id: int):
    """
    Xử lý file mẫu đối soát dựa vào template_id
    Task Celery này sẽ được gọi từ API khi người dùng yêu cầu phân tích mẫu
    
    time_limit: giới hạn thời gian tối đa (5 phút)
    soft_time_limit: giới hạn mềm, sẽ gửi signal SoftTimeLimitExceeded trước khi kill task (4 phút)
    """
    start_time = time.time()
    logger.info(f"Bắt đầu xử lý mẫu đối soát ID: {template_id}")
    
    # Lấy thông tin template
    db = SessionLocal()
    try:
        # Tối ưu kết nối DB
        db = optimize_db_connection(db)
        
        # Lấy thông tin về template
        template = db.query(ReconciliationTemplate).filter(ReconciliationTemplate.id == template_id).first()
        if not template:
            logger.error(f"Không tìm thấy mẫu đối soát với ID: {template_id}")
            return {"status": "error", "message": f"Mẫu đối soát với ID {template_id} không tồn tại"}
        
        # Cập nhật trạng thái "đang xử lý"
        template.status = "processing"
        template.task_id = self.request.id
        db.commit()
        
        logger.info(f"Đang xử lý mẫu: {template.name} (ID: {template_id}, Loại: {template.template_type})")
        
        # Kiểm tra file tồn tại
        if not os.path.exists(template.file_path):
            logger.error(f"File không tồn tại: {template.file_path}")
            template.status = "failed"
            template.error_message = "File không tồn tại"
            db.commit()
            return {"status": "error", "message": "File không tồn tại"}
        
        # Xử lý theo loại template
        result = None
        error_message = None
        
        try:
            if template.template_type == TemplateType.DSC:
                # Xử lý file đối soát cước
                logger.info(f"Xử lý file đối soát cước: {template.name}")
                result = process_dsc_file(template.file_path)
                
            elif template.template_type == TemplateType.DSCD:
                # Xử lý file đối soát cố định
                logger.info(f"Xử lý file đối soát cố định: {template.name}")
                result = process_dscd_file(template.file_path)
                
            elif template.template_type == TemplateType.DST_1800_1900:
                # Xử lý file đối soát 1800/1900
                logger.info(f"Xử lý file đối soát 1800/1900: {template.name}")
                result = process_dst_1800_1900_file(template.file_path)
                
            else:
                # Loại không được hỗ trợ
                error_message = f"Loại mẫu không được hỗ trợ: {template.template_type}"
                logger.error(error_message)
                
            if result:
                # >>> Create deep copy BEFORE calling .dict() <<< 
                schema_data_copy = copy.deepcopy(result)
                
                # Update template status and basic info
                template.status = "completed"
                template.processed_data = result.dict() if hasattr(result, "dict") else None
                template.processing_time = time.time() - start_time
                
                # Save detailed data using the deep copy
                try:
                    if template.template_type == TemplateType.DSC:
                        # Pass the untouched copy to the save function
                        save_doi_soat_cuoc_to_db(db, template.id, schema_data_copy)
                        logger.info(f"Đã lưu kết quả đối soát cước vào database")
                    elif template.template_type == TemplateType.DSCD:
                        # Pass the copy (already created above)
                        save_doi_soat_co_dinh_to_db(db, template.id, schema_data_copy)
                        # Đồng bộ hóa dữ liệu với bảng DSCD
                        sync_result = sync_template_with_dscd(db, template.id)
                        if sync_result:
                            logger.info(f"Đã đồng bộ hóa dữ liệu đối soát cố định với DSCD")
                        else:
                            logger.warning(f"Đồng bộ hóa dữ liệu DSCD không thành công")
                        logger.info(f"Đã lưu kết quả đối soát cố định vào database")
                    elif template.template_type == TemplateType.DST_1800_1900:
                        # Pass the copy (already created above)
                        save_doi_soat_1800_1900_to_db(db, template.id, schema_data_copy)
                        logger.info(f"Đã lưu kết quả đối soát 1800/1900 vào database")
                    
                    # Chỉ commit nếu lưu thành công
                    logger.info(f"Hoàn thành xử lý mẫu {template.name} trong {template.processing_time:.2f}s")
                    db.commit()
                    
                except Exception as e:
                    save_error_message = f"Lỗi khi lưu dữ liệu chi tiết: {str(e)}"
                    logger.error(save_error_message)
                    # Set status to failed and store the error message
                    template.status = "failed"
                    template.error_message = save_error_message
                    db.commit() # Commit the failure status
                    # Return error status
                    return {
                        "status": "error",
                        "template_id": template_id,
                        "message": save_error_message
                    }
            else:
                template.status = "failed"
                template.error_message = error_message or "Không thể xử lý file"
                logger.error(f"Lỗi xử lý mẫu: {template.error_message}")
                db.commit()
            
        except Exception as e:
            error_message = str(e)
            logger.exception(f"Lỗi khi xử lý file {template.name}: {error_message}")
            template.status = "failed"
            template.error_message = error_message
            db.commit()
        
        # Đóng session cũ
        db.close()
        
        # Trả về kết quả
        return {
            "status": "success" if result else "error",
            "template_id": template_id,
            "processing_time": time.time() - start_time,
            "message": error_message
        }
            
    except Exception as e:
        logger.exception(f"Lỗi ngoại lệ khi xử lý mẫu {template_id}: {str(e)}")
        
        try:
            # Cập nhật trạng thái lỗi
            template = db.query(ReconciliationTemplate).filter(ReconciliationTemplate.id == template_id).first()
            if template:
                template.status = "failed"
                template.error_message = str(e)
                db.commit()
        except:
            pass
        
        if db.is_active:
            db.close()
            
        # Re-raise error để Celery ghi lại
        raise
    
    finally:
        # Đảm bảo đóng session
        if 'db' in locals() and db.is_active:
            db.close()

@celery_app.task(bind=True)
def process_period_reconciliation(self, period_id: int):
    """
    Xử lý kỳ đối soát dựa vào period_id
    Task Celery này sẽ được gọi từ API khi yêu cầu xử lý kỳ đối soát
    """
    import time
    from ..models.reconciliation_period import ReconciliationPeriod
    
    start_time = time.time()
    logger.info(f"Bắt đầu xử lý kỳ đối soát ID: {period_id}")
    
    # Lấy thông tin kỳ đối soát
    db = SessionLocal()
    try:
        # Tối ưu kết nối DB
        db = optimize_db_connection(db)
        
        # Lấy thông tin về kỳ đối soát
        period = db.query(ReconciliationPeriod).filter(ReconciliationPeriod.id == period_id).first()
        if not period:
            logger.error(f"Không tìm thấy kỳ đối soát với ID: {period_id}")
            return {"status": "error", "message": f"Kỳ đối soát với ID {period_id} không tồn tại"}
        
        # Cập nhật trạng thái "đang xử lý"
        period.status = "processing"
        period.task_id = self.request.id
        db.commit()
        
        # Thực hiện xử lý call logs
        processor = CallLogProcessor(db)
        result = processor.process_call_logs_for_period(period_id)
        
        if result["success"]:
            # Cập nhật trạng thái "đã hoàn thành"
            period.status = "completed"
            period.processing_time = time.time() - start_time
            db.commit()
            logger.info(f"Xử lý kỳ đối soát {period.period_code} (ID: {period_id}) thành công trong {period.processing_time:.2f}s")
        else:
            # Cập nhật trạng thái "thất bại"
            period.status = "failed"
            period.error_message = result.get("message", "Không thể xử lý kỳ đối soát")
            db.commit()
            logger.error(f"Xử lý kỳ đối soát thất bại: {result.get('message')}")
        
        # Trả về kết quả
        return {
            "status": "success" if result["success"] else "error",
            "period_id": period_id,
            "processing_time": time.time() - start_time,
            "message": result.get("message", "")
        }
    
    except Exception as e:
        logger.exception(f"Lỗi ngoại lệ khi xử lý kỳ đối soát {period_id}: {str(e)}")
        
        try:
            # Cập nhật trạng thái lỗi
            period = db.query(ReconciliationPeriod).filter(ReconciliationPeriod.id == period_id).first()
            if period:
                period.status = "failed"
                period.error_message = str(e)
                db.commit()
        except:
            pass
        
        # Re-raise error để Celery ghi lại
        raise
    
    finally:
        # Đảm bảo đóng session
        if 'db' in locals() and db.is_active:
            db.close() 