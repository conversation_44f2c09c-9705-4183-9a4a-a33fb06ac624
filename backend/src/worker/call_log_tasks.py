import os
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import gc
import multiprocessing as mp
from functools import partial
import psutil
import sys

from sqlalchemy.orm import Session
from sqlalchemy import text

from ..database.session import SessionLocal
from ..models.call_log import CallLogFile, CallLog, FileType, CallType, FileStatus, NumberType
from ..core.config import settings
from ..core.logging import logger
from .celery_app import celery_app
from ..utils.enhanced_phone_utils import normalize_phone_number_enhanced, classify_phone_number_enhanced

# Tối ưu hàm với lookup tables
MOBILE_PREFIXES = {
    '032', '033', '034', '035', '036', '037', '038', '039',  # Viettel
    '086', '096', '097', '098',  # Viettel
    '070', '076', '077', '078', '079',  # Mobifone
    '089', '090', '093',  # Mobifone
    '081', '082', '083', '084', '085',  # Vinaphone
    '088', '091', '094',  # Vinaphone
    '052', '056', '058', '092',  # Vietnamobile (Thêm 052)
    '059', '099',  # Gmobile
    '087',  # iTelecom
    '055',  # Wintel (Thêm 055)
}

FIXED_SERVICE_PREFIXES = {'1900', '1800'} # Xóa 1700, 1600

# Các prefix số cố định tỉnh lẻ - Bỏ đi vì không chính xác và đã có logic check 02x
# FIXED_PROVINCIAL_PREFIXES = {
#     '0522', '0523', '0528', '0525', '0559',
#     '0524', '0526', '0527', '0529',
#     '0543', '0548', '0551', '0552', '0553'
# }

# Các prefix số toll-free - Bỏ đi vì không chính xác và đã xử lý 1800 trong FIXED_SERVICE_PREFIXES
# TOLL_FREE_PREFIXES = {
#     '01800', '01801', '01802', '01803', '01804', '01805'
# }

def normalize_phone_number_fast(phone: str) -> str:
    """Chuẩn hóa số điện thoại cho quá trình tìm kiếm và phân loại"""
    if not phone:
        return phone
    
    # Loại bỏ các ký tự không phải số
    digits_only = ''.join(c for c in str(phone) if c.isdigit() or c == '+')
    
    # Nếu chuỗi rỗng sau khi lọc, trả về chuỗi gốc
    if not digits_only:
        return phone
    
    # Xử lý số quốc tế bắt đầu bằng +
    if digits_only.startswith('+'):
        # Kiểm tra cụ thể +84
        if digits_only.startswith('+84') and len(digits_only) > 3: 
             # Bỏ '+84' và thêm '0' vào đầu
             return '0' + digits_only[3:]
        else:
             # Giữ nguyên các số quốc tế khác (vd: +1, +44)
             return digits_only
    
    # Xử lý số dịch vụ đặc biệt
    if (digits_only.startswith('1900') or 
        digits_only.startswith('1800') or 
        digits_only.startswith('1700') or 
        digits_only.startswith('1600')):
        return digits_only
    
    # Nếu không bắt đầu bằng 0, thêm 0 vào đầu
    if not digits_only.startswith('0'):
        return '0' + digits_only
    
    return digits_only

def classify_phone_number_fast(phone: str) -> NumberType:
    """Phân loại số điện thoại dựa vào prefix"""
    if not phone:
        return NumberType.UNKNOWN
    
    # Xử lý số quốc tế
    if phone.startswith('+'):
        return NumberType.INTL
    
    # Xử lý số dịch vụ cố định (bao gồm 1800, 1900)
    prefix_4 = phone[:4] if len(phone) >= 4 else ""
    if prefix_4 in FIXED_SERVICE_PREFIXES:
        return NumberType.FIXED
    
    # Xử lý số di động
    prefix_3 = phone[:3] if len(phone) >= 3 else ""
    if prefix_3 in MOBILE_PREFIXES:
        return NumberType.MOBILE
    
    # Xử lý số cố định bắt đầu bằng 02
    if phone.startswith('02'):
        return NumberType.FIXED
    
    return NumberType.UNKNOWN

def extract_prefix(normalized_phone: str) -> Optional[str]:
    """Trích xuất prefix từ số điện thoại đã chuẩn hóa."""
    if not normalized_phone:
        return None
    
    if normalized_phone.startswith('1800') or normalized_phone.startswith('1900'):
        return normalized_phone[:4]
    elif normalized_phone.startswith('0') and len(normalized_phone) >= 3:
        return normalized_phone[:3]
    elif normalized_phone.startswith('+') and len(normalized_phone) >= 3:
        return normalized_phone[:3]
    # Có thể thêm các trường hợp khác nếu cần
    
    return None # Trả về None nếu không xác định được prefix

def optimize_db_connection(db: Session) -> Session:
    """
    Tối ưu kết nối database cho việc xử lý batch lớn trên server cấu hình thấp
    """
    try:
        # Giảm work_mem để phù hợp với server 2GB RAM
        db.execute(text("SET SESSION work_mem = '16MB'"))
        
        # Giảm maintenance_work_mem để phù hợp với server 2GB RAM
        db.execute(text("SET SESSION maintenance_work_mem = '32MB'"))
        
        # Thêm tối ưu cho server cấu hình thấp
        db.execute(text("SET SESSION temp_buffers = '8MB'"))
        
        # Tối ưu shared_buffers (chỉ có hiệu lực nếu được cấu hình trong postgresql.conf)
        try:
            # Tối ưu các tham số hiệu suất khác
            db.execute(text("SET SESSION effective_cache_size = '512MB'"))
            db.execute(text("SET SESSION random_page_cost = 2.0"))
            db.execute(text("SET SESSION constraint_exclusion = on"))
            
            # Giới hạn các resource có thể sử dụng
            db.execute(text("SET SESSION statement_timeout = '3600s'"))  # 1 giờ
            db.execute(text("SET SESSION idle_in_transaction_session_timeout = '3600s'"))  # 1 giờ
        except Exception as e:
            # Bỏ qua nếu tham số không được hỗ trợ
            logger.warning(f"Một số tham số tối ưu không được hỗ trợ: {str(e)}")
    except Exception as e:
        # Log lỗi nhưng vẫn tiếp tục
        logger.warning(f"Không thể tối ưu kết nối database: {str(e)}")
    
    return db

def get_memory_usage():
    """
    Lấy thông tin sử dụng bộ nhớ hiện tại của process
    Trả về dữ liệu theo MB
    """
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    memory_usage_mb = memory_info.rss / (1024 * 1024)  # Convert to MB
    return memory_usage_mb

def log_memory_usage(task_name, step, file_id=None):
    """
    Ghi log thông tin sử dụng bộ nhớ
    """
    memory_mb = get_memory_usage()
    file_info = f" - File ID: {file_id}" if file_id else ""
    logger.info(f"Memory Usage [{task_name} - {step}{file_info}]: {memory_mb:.2f} MB")
    
    # Cảnh báo nếu sử dụng bộ nhớ quá cao
    if memory_mb > 1000:  # Cảnh báo khi sử dụng hơn 1GB
        logger.warning(f"High memory usage detected [{task_name} - {step}]: {memory_mb:.2f} MB")
    
    return memory_mb

@celery_app.task(bind=True)
def process_call_log_file(self, file_id: int):
    """
    Xử lý file call log dựa vào file_id
    Celery task này sẽ được gọi bởi API khi upload file thành công
    """
    start_time = time.time()
    log_memory_usage("process_call_log_file", "start", file_id)
    
    # Lấy thông tin file
    db = SessionLocal()
    try:
        # Optimize DB connection
        db = optimize_db_connection(db)
        
        file_record = db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
        if not file_record:
            logger.error(f"Không tìm thấy file với ID: {file_id}")
            return {"status": "error", "message": f"File với ID {file_id} không tồn tại"}
        
        # Cập nhật trạng thái processing
        file_record.status = FileStatus.PROCESSING
        file_record.task_id = self.request.id  
        db.commit()
        
        logger.info(f"Bắt đầu xử lý file: {file_record.filename} (ID: {file_id})")
        
        # Lấy loại file (call in hay call out)
        file_type = file_record.file_type
        
        # Xử lý theo loại file
        if file_type == FileType.CALL_IN:
            logger.info(f"Xử lý file Call In: {file_record.filename}")
            result = process_call_in_file(file_record.file_path, file_id, self, db)
        else:
            logger.info(f"Xử lý file Call Out: {file_record.filename}")
            result = process_call_out_file(file_record.file_path, file_id, self, db)
        
        # Đóng session cũ
        db.close()
        
        # Tạo session mới để cập nhật trạng thái
        new_db = SessionLocal()
        try:
            # Lấy lại file record với session mới
            file_record = new_db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
            if file_record:
                # Cập nhật kết quả thành công
                file_record.status = FileStatus.COMPLETED
                file_record.processed_rows = result.get("processed_rows", 0)
                file_record.error_count = result.get("error_count", 0)
                
                # Cập nhật thời gian xử lý
                if "processing_time" in result:
                    file_record.processing_time = result["processing_time"]
                else:
                    file_record.processing_time = time.time() - start_time
                
                if result.get("error_count", 0) > 0:
                    file_record.error_message = f"Completed with {result['error_count']} errors"
                
                new_db.commit()
                
                logger.info(f"Hoàn thành xử lý file {file_record.filename} (ID: {file_id}). "
                           f"Đã xử lý {file_record.processed_rows} dòng trong {file_record.processing_time:.2f}s. "
                           f"Lỗi: {file_record.error_count}")
        finally:
            if new_db.is_active:
                new_db.close()
        
        # Ghi log bộ nhớ sau khi hoàn thành
        log_memory_usage("process_call_log_file", "complete", file_id)
        
        return {
            "status": "success", 
            "file_id": file_id,
            "processed_rows": result.get("processed_rows", 0),
            "error_count": result.get("error_count", 0),
            "processing_time": result.get("processing_time") or time.time() - start_time
        }
        
    except Exception as e:
        logger.exception(f"Lỗi khi xử lý file {file_id}: {str(e)}")
        
        try:
            # Cập nhật trạng thái failed
            file_record = db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
            if file_record:
                file_record.status = FileStatus.FAILED
                file_record.error_message = str(e)
                db.commit()
        except:
            pass
            
        if db.is_active:
            db.close()
            
        # Re-raise error để Celery ghi lại
        raise
        
    finally:
        # Đảm bảo đóng session
        if 'db' in locals() and db.is_active:
            db.close()


def process_call_in_file(file_path: str, file_id: int, task=None, db: Session = None):
    """
    Xử lý file Call In - Đã tối ưu cho server 2 cores 2GB RAM
    
    Args:
        file_path: Đường dẫn đến file excel
        file_id: ID của file trong database
        task: Celery task nếu được gọi từ task
        db: Session database nếu đã có sẵn
    
    Returns:
        Dict chứa kết quả xử lý
    """
    start_time = time.time()
    error_count = 0
    processed_rows = 0
    
    # Log memory usage khi bắt đầu
    log_memory_usage("process_call_in_file", "start", file_id)
    
    # Tạo database session nếu chưa có
    if db is None:
        db = SessionLocal()
        db = optimize_db_connection(db)
    
    should_close_db = db is None
    
    try:
        logger.info(f"Đọc file Call In: {file_path}")
        
        # Thiết lập thông số tối ưu - giảm đáng kể cho server 2cores 2GB RAM
        batch_size = 5000      # Số dòng xử lý mỗi lần (giảm xuống để tiết kiệm bộ nhớ)
        
        # Xác định kiểu file và engine đọc phù hợp
        excel_engine = 'openpyxl'
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext == '.xls':
            excel_engine = 'xlrd'
        elif file_ext == '.xlsb':
            excel_engine = 'pyxlsb'
        
        # Đọc file excel - phương pháp tối ưu bộ nhớ
        try:
            # Đọc toàn bộ file Excel không chỉ định usecols để tránh lỗi không tìm thấy cột
            logger.info(f"Đọc file Excel {file_path}")
            df = pd.read_excel(
                file_path, 
                engine=excel_engine,
                dtype=str  # Đọc tất cả dưới dạng string trước để tránh lỗi kiểu dữ liệu
            )
            
            # Làm sạch tên cột
            df.columns = [col.lower().strip() for col in df.columns]
            logger.info(f"Các cột có trong file: {list(df.columns)}")
            
            # Xác định tên cột thời gian và map với tên cột chuẩn
            time_columns = {
                'begin_time': ['begin_time', 'begin time', 'begintime', 'start_time', 'start time', 'starttime', 'start_date', 'startdate'],
                'end_time': ['end_time', 'end time', 'endtime', 'finish_time', 'finish time', 'finishtime', 'end_date', 'enddate']
            }
            
            duration_columns = ['duration', 'conversation_time', 'conversation time', 'talk_time', 'talktime']
            
            # Ánh xạ các cột thực tế sang tên cột chuẩn
            column_mapping = {}
            
            # Tìm cột begin_time
            begin_time_col = None
            for col in df.columns:
                if col in time_columns['begin_time']:
                    begin_time_col = col
                    column_mapping[col] = 'begin_time'
                    break
            
            # Tìm cột end_time
            end_time_col = None
            for col in df.columns:
                if col in time_columns['end_time']:
                    end_time_col = col
                    column_mapping[col] = 'end_time'
                    break
            
            # Tìm cột duration
            duration_col = None
            for col in df.columns:
                if col in duration_columns:
                    duration_col = col
                    column_mapping[col] = 'conversation_time'
                    break
            
            # Kiểm tra và ánh xạ các cột còn lại
            if 'caller' not in df.columns:
                for possible_name in ['ani', 'source', 'source_number', 'from']:
                    if possible_name in df.columns:
                        column_mapping[possible_name] = 'caller'
                        break
            
            if 'callee' not in df.columns:
                for possible_name in ['destination', 'dest', 'destination_number', 'to']:
                    if possible_name in df.columns:
                        column_mapping[possible_name] = 'callee'
                        break
            
            if 'caller_gateway' not in df.columns:
                for possible_name in ['caller gateway', 'source_gateway', 'from_gateway']:
                    if possible_name in df.columns:
                        column_mapping[possible_name] = 'caller_gateway'
                        break
            
            if 'called_gateway' not in df.columns:
                for possible_name in ['called gateway', 'dest_gateway', 'to_gateway', 'tername']:
                    if possible_name in df.columns:
                        column_mapping[possible_name] = 'called_gateway'
                        break
            
            # Đổi tên các cột theo ánh xạ
            if column_mapping:
                logger.info(f"Ánh xạ cột: {column_mapping}")
                df = df.rename(columns=column_mapping)
            
            # Đảm bảo các cột bắt buộc tồn tại
            required_cols = ['caller', 'callee']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                # Nếu thiếu cột bắt buộc, thử tìm các cột tương tự
                logger.warning(f"Thiếu các cột bắt buộc: {', '.join(missing_cols)}")
                
                # In tất cả các cột hiện có để debug
                logger.info(f"Các cột hiện có: {df.columns.tolist()}")
                
                # Lấy cột đầu tiên và thứ hai làm caller và callee nếu có
                if len(df.columns) >= 2 and 'caller' not in df.columns:
                    logger.warning(f"Sử dụng cột {df.columns[0]} làm caller")
                    df = df.rename(columns={df.columns[0]: 'caller'})
                
                if len(df.columns) >= 2 and 'callee' not in df.columns:
                    logger.warning(f"Sử dụng cột {df.columns[1]} làm callee")
                    df = df.rename(columns={df.columns[1]: 'callee'})
                
                # Kiểm tra lại sau khi rename
                missing_cols = [col for col in required_cols if col not in df.columns]
                if missing_cols:
                    raise ValueError(f"Không thể xác định cột bắt buộc: {', '.join(missing_cols)}")
            
            # Log memory usage sau khi đọc file
            log_memory_usage("process_call_in_file", "after_read_file", file_id)
            
            # Xử lý từng batch dữ liệu để tối ưu bộ nhớ
            total_rows = len(df)
            logger.info(f"Bắt đầu xử lý {total_rows} dòng dữ liệu trong file Call In")
            
            for start_idx in range(0, total_rows, batch_size):
                end_idx = min(start_idx + batch_size, total_rows)
                chunk_df = df.iloc[start_idx:end_idx].copy()
                
                # Chuyển đổi cột thời gian sang định dạng datetime
                if 'begin_time' in chunk_df.columns:
                    try:
                        chunk_df['begin_time'] = pd.to_datetime(chunk_df['begin_time'], errors='coerce')
                    except Exception as e:
                        logger.warning(f"Không thể chuyển đổi cột begin_time sang datetime: {str(e)}")
                else:
                    chunk_df['begin_time'] = datetime.now()
                
                if 'end_time' in chunk_df.columns:
                    try:
                        chunk_df['end_time'] = pd.to_datetime(chunk_df['end_time'], errors='coerce')
                    except Exception as e:
                        logger.warning(f"Không thể chuyển đổi cột end_time sang datetime: {str(e)}")
                else:
                    chunk_df['end_time'] = chunk_df['begin_time'] if 'begin_time' in chunk_df.columns else datetime.now()
                
                # Đảm bảo kiểu dữ liệu của conversation_time là số
                if 'conversation_time' in chunk_df.columns:
                    chunk_df['conversation_time'] = pd.to_numeric(chunk_df['conversation_time'], errors='coerce').fillna(0).astype(int)
                else:
                    # Nếu không có cột duration, tính từ begin_time và end_time
                    if 'begin_time' in chunk_df.columns and 'end_time' in chunk_df.columns:
                        try:
                            chunk_df['conversation_time'] = (chunk_df['end_time'] - chunk_df['begin_time']).dt.total_seconds().fillna(0).astype(int)
                        except Exception as e:
                            logger.warning(f"Không thể tính duration từ begin_time và end_time: {str(e)}")
                            chunk_df['conversation_time'] = 0
                    else:
                        chunk_df['conversation_time'] = 0
                
                # Đảm bảo các cột cần thiết tồn tại
                if 'caller_gateway' not in chunk_df.columns:
                    chunk_df['caller_gateway'] = 'Unknown'
                if 'called_gateway' not in chunk_df.columns:
                    chunk_df['called_gateway'] = 'Unknown'
                
                # Tạo các trường phân tích
                if 'begin_time' in chunk_df.columns and not chunk_df['begin_time'].isna().all():
                    chunk_df['call_date'] = chunk_df['begin_time'].dt.date
                    chunk_df['call_hour'] = chunk_df['begin_time'].dt.hour
                else:
                    today = datetime.now().date()
                    current_hour = datetime.now().hour
                    chunk_df['call_date'] = today
                    chunk_df['call_hour'] = current_hour
                
                # Chuẩn hóa số điện thoại với phiên bản nâng cao
                chunk_df['caller'] = chunk_df['caller'].apply(normalize_phone_number_enhanced)
                chunk_df['callee'] = chunk_df['callee'].apply(normalize_phone_number_enhanced)
                
                # Phân loại số điện thoại với ghi chú
                caller_results = chunk_df['caller'].apply(lambda x: classify_phone_number_enhanced(x))
                chunk_df['caller_type'] = [result[0] for result in caller_results]
                chunk_df['caller_note'] = [result[1] for result in caller_results]
                
                callee_results = chunk_df['callee'].apply(lambda x: classify_phone_number_enhanced(x))
                chunk_df['callee_type'] = [result[0] for result in callee_results]
                chunk_df['callee_note'] = [result[1] for result in callee_results]
                
                # Xử lý và lưu chunk dữ liệu
                batch_result = process_chunk(chunk_df, file_id, db)
                processed_rows += batch_result["processed_rows"]
                error_count += batch_result["error_count"]
                
                # Commit sau mỗi chunk để giảm memory footprint
                db.commit()
                
                # Cập nhật tiến độ nếu được gọi từ celery task
                if task:
                    task.update_state(
                        state='PROGRESS',
                        meta={
                            'current': processed_rows,
                            'total': total_rows,  # Đã biết tổng số dòng
                            'file_id': file_id,
                            'status': 'Processing'
                        }
                    )
                
                # Log memory usage sau mỗi commit
                log_memory_usage("process_call_in_file", f"progress_{processed_rows}/{total_rows}", file_id)
                
                # Thu hồi bộ nhớ thường xuyên
                del chunk_df
                del caller_results
                del callee_results
                gc.collect()
            
            # Giải phóng bộ nhớ của dataframe chính
            del df
            gc.collect()
                
        except Exception as e:
            logger.error(f"Lỗi khi đọc file: {str(e)}")
            raise ValueError(f"Không thể đọc file Excel: {str(e)}")
        
        processing_time = time.time() - start_time
        logger.info(f"Hoàn thành xử lý file Call In. "
                   f"Đã xử lý {processed_rows} dòng trong {processing_time:.2f} giây "
                   f"({processed_rows / processing_time:.2f} dòng/giây). "
                   f"Số lỗi: {error_count}")
        
        # Log memory usage khi hoàn thành
        log_memory_usage("process_call_in_file", "complete", file_id)
        
        return {
            "processed_rows": processed_rows,
            "error_count": error_count,
            "processing_time": processing_time
        }
            
    except Exception as e:
        logger.exception(f"Lỗi khi xử lý file Call In: {str(e)}")
        raise
    
    finally:
        # Đóng session nếu tự tạo
        if should_close_db and 'db' in locals() and db and db.is_active:
            db.close()


def process_chunk(chunk_df, file_id, db):
    """Xử lý và lưu một batch dữ liệu"""
    processed_rows = 0
    error_count = 0
    
    try:
        # Sử dụng bulk_insert_mappings để tối ưu insert
        mappings = []
        
        for _, row in chunk_df.iterrows():
            try:
                # Kiểm tra và đảm bảo các trường quan trọng không null
                begin_time = row.begin_time if hasattr(row, 'begin_time') and pd.notna(row.begin_time) else datetime.now()
                end_time = row.end_time if hasattr(row, 'end_time') and pd.notna(row.end_time) else begin_time
                duration = int(row.conversation_time) if hasattr(row, 'conversation_time') and pd.notna(row.conversation_time) else 0
                
                # Đảm bảo call_date và call_hour có giá trị
                call_date = row.call_date if hasattr(row, 'call_date') and pd.notna(row.call_date) else begin_time.date()
                call_hour = row.call_hour if hasattr(row, 'call_hour') and pd.notna(row.call_hour) else begin_time.hour
                
                caller_norm = row.caller if hasattr(row, 'caller') and pd.notna(row.caller) else "Unknown"
                callee_norm = row.callee if hasattr(row, 'callee') and pd.notna(row.callee) else "Unknown"
                
                # Trích xuất prefix
                caller_prefix = extract_prefix(caller_norm)
                callee_prefix = extract_prefix(callee_norm)
                
                # Tạo mapping cho mỗi dòng
                mappings.append({
                    'file_id': file_id,
                    'call_type': CallType.IN,
                    'caller': caller_norm,
                    'callee': callee_norm,
                    'begin_time': begin_time,
                    'end_time': end_time,
                    'duration': duration,
                    'caller_gateway': row.caller_gateway if hasattr(row, 'caller_gateway') and pd.notna(row.caller_gateway) else "Unknown",
                    'called_gateway': row.called_gateway if hasattr(row, 'called_gateway') and pd.notna(row.called_gateway) else "Unknown",
                    'call_date': call_date,
                    'call_hour': call_hour,
                    'caller_type': row.caller_type if hasattr(row, 'caller_type') and pd.notna(row.caller_type) else NumberType.UNKNOWN,
                    'callee_type': row.callee_type if hasattr(row, 'callee_type') and pd.notna(row.callee_type) else NumberType.UNKNOWN,
                    'caller_note': row.caller_note if hasattr(row, 'caller_note') and pd.notna(row.caller_note) else "",
                    'callee_note': row.callee_note if hasattr(row, 'callee_note') and pd.notna(row.callee_note) else "",
                    'caller_prefix': caller_prefix,
                    'callee_prefix': callee_prefix
                })
                
                processed_rows += 1
                
            except Exception as e:
                error_count += 1
                logger.error(f"Lỗi khi xử lý dòng: {str(e)}")
                continue
        
        # Bulk insert
        if mappings:
            db.bulk_insert_mappings(CallLog, mappings)
    
    except Exception as e:
        logger.exception(f"Lỗi khi xử lý chunk: {str(e)}")
        error_count += len(chunk_df) - processed_rows
    
    return {
        "processed_rows": processed_rows,
        "error_count": error_count
    }


def process_call_out_file(file_path: str, file_id: int, task=None, db: Session = None):
    """
    Xử lý file Call Out - Đã tối ưu cho server 2 cores 2GB RAM
    
    Args:
        file_path: Đường dẫn đến file excel
        file_id: ID của file trong database
        task: Celery task nếu được gọi từ task
        db: Session database nếu đã có sẵn
    
    Returns:
        Dict chứa kết quả xử lý
    """
    start_time = time.time()
    error_count = 0
    processed_rows = 0
    
    # Log memory usage khi bắt đầu
    log_memory_usage("process_call_out_file", "start", file_id)
    
    # Tạo database session nếu chưa có
    if db is None:
        db = SessionLocal()
        db = optimize_db_connection(db)
    
    should_close_db = db is None
    
    try:
        logger.info(f"Đọc file Call Out: {file_path}")
        
        # Thiết lập thông số tối ưu - giảm đáng kể cho server 2cores 2GB RAM
        batch_size = 5000      # Số dòng xử lý mỗi lần (giảm xuống để tiết kiệm bộ nhớ)
        
        # Xác định kiểu file và engine đọc phù hợp
        excel_engine = 'openpyxl'
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext == '.xls':
            excel_engine = 'xlrd'
        elif file_ext == '.xlsb':
            excel_engine = 'pyxlsb'
        
        try:
            # Đọc toàn bộ file Excel không chỉ định usecols để tránh lỗi không tìm thấy cột
            logger.info(f"Đọc file Excel {file_path}")
            df = pd.read_excel(
                file_path,
                engine=excel_engine,
                dtype=str,  # Đọc tất cả dưới dạng string trước
                na_values=['', 'NULL', 'null', 'na', 'NA', 'n/a', 'N/A']
            )
            
            # Làm sạch tên cột
            df.columns = [col.lower().strip() for col in df.columns]
            logger.info(f"Các cột có trong file: {list(df.columns)}")
            
            # Map các cột Call Out
            column_mapping = {}
            
            # Tìm cột ANI
            if 'ani' not in df.columns:
                for possible_name in ['source', 'source_number', 'from', 'caller']:
                    if possible_name in df.columns:
                        column_mapping[possible_name] = 'ani'
                        break
            
            # Tìm cột DESTINATION
            if 'destination' not in df.columns:
                for possible_name in ['dest', 'destination_number', 'to', 'callee']:
                    if possible_name in df.columns:
                        column_mapping[possible_name] = 'destination'
                        break
            
            # Tìm cột DURATION
            if 'duration' not in df.columns:
                for possible_name in ['conversation_time', 'conversation time', 'talk_time', 'talktime']:
                    if possible_name in df.columns:
                        column_mapping[possible_name] = 'duration'
                        break
            
            # Đổi tên các cột theo ánh xạ
            if column_mapping:
                logger.info(f"Ánh xạ cột: {column_mapping}")
                df = df.rename(columns=column_mapping)
            
            # Log memory usage sau khi đọc file
            log_memory_usage("process_call_out_file", "after_read_file", file_id)
            
            # Xử lý từng batch dữ liệu để tối ưu bộ nhớ
            total_rows = len(df)
            logger.info(f"Bắt đầu xử lý {total_rows} dòng dữ liệu trong file Call Out")
            
            for start_idx in range(0, total_rows, batch_size):
                end_idx = min(start_idx + batch_size, total_rows)
                chunk_df = df.iloc[start_idx:end_idx].copy()
                
                # Xử lý chunk dữ liệu
                mappings = []
                
                # Xử lý các dòng trong chunk
                for _, row in chunk_df.iterrows():
                    try:
                        # Lấy dữ liệu từ các cột
                        ani = str(row.get('ani', '')).strip() if pd.notna(row.get('ani', '')) else None
                        destination = str(row.get('destination', '')).strip() if pd.notna(row.get('destination', '')) else None
                        
                        # Nếu không có ANI hoặc DESTINATION, kiểm tra các cột đầu tiên
                        if not ani and len(row) > 0:
                            ani = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else None
                        
                        if not destination and len(row) > 1:
                            destination = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else None
                        
                        # Nếu vẫn không có, ghi log và bỏ qua dòng này
                        if not ani or not destination:
                            logger.warning(f"Không thể xác định số gọi đi/đến trong dòng, bỏ qua: {row}")
                            continue
                        
                        # Chuyển duration thành số
                        try:
                            duration = int(float(row.get('duration', 0))) if pd.notna(row.get('duration', 0)) else 0
                        except (ValueError, TypeError):
                            duration = 0
                        
                        # Ngày, giờ gọi - tìm kiếm từ nhiều tên cột có thể có
                        begin_time = None
                        for column in ['startdate', 'start date', 'start_date', 'date', 'begin_time']:
                            if column in row.index and pd.notna(row.get(column)):
                                begin_time = row[column]
                                break
                        
                        # Nếu không tìm thấy thời gian bắt đầu, sử dụng thời gian hiện tại
                        if begin_time is None or pd.isna(begin_time):
                            begin_time = datetime.now()
                        else:
                            # Chuyển đổi begin_time thành datetime nếu nó không phải
                            if not isinstance(begin_time, datetime):
                                try:
                                    begin_time = pd.to_datetime(begin_time)
                                except Exception as e:
                                    begin_time = datetime.now()
                        
                        # Thời gian kết thúc - tìm kiếm từ nhiều tên cột có thể có
                        end_time = None
                        for column in ['enddate', 'end date', 'end_date', 'finish_time']:
                            if column in row.index and pd.notna(row.get(column)):
                                end_time = row[column]
                                break
                        
                        # Đảm bảo end_time là datetime
                        if end_time is not None and not pd.isna(end_time):
                            if not isinstance(end_time, datetime):
                                try:
                                    end_time = pd.to_datetime(end_time)
                                except Exception as e:
                                    end_time = None
                        
                        # Nếu không có end_time nhưng có begin_time và duration
                        if (end_time is None or pd.isna(end_time)) and begin_time is not None and duration > 0:
                            end_time = begin_time + timedelta(seconds=duration)
                        elif end_time is None or pd.isna(end_time):
                            end_time = begin_time  # Fallback
                        
                        # Trích xuất ngày và giờ gọi
                        try:
                            call_date = begin_time.date() if isinstance(begin_time, datetime) else datetime.now().date()
                            call_hour = begin_time.hour if isinstance(begin_time, datetime) else datetime.now().hour
                        except Exception as e:
                            call_date = datetime.now().date()
                            call_hour = datetime.now().hour
                        
                        # Chuẩn hóa số điện thoại
                        caller = normalize_phone_number_enhanced(ani) if ani else "Unknown"
                        callee = normalize_phone_number_enhanced(destination) if destination else "Unknown"
                        
                        # Phân loại số điện thoại
                        caller_type_result = classify_phone_number_enhanced(caller)
                        callee_type_result = classify_phone_number_enhanced(callee)
                        
                        caller_type = caller_type_result[0]
                        caller_note = caller_type_result[1]
                        callee_type = callee_type_result[0]
                        callee_note = callee_type_result[1]
                        
                        # Gateway
                        caller_gateway = str(row.get('caller gateway', '')).strip() if pd.notna(row.get('caller gateway', '')) else "Unknown"
                        called_gateway = str(row.get('tername', '')).strip() if pd.notna(row.get('tername', '')) else "Unknown"
                        
                        # Các trường khác
                        area_code = str(row.get('area prefix', '')).strip() if pd.notna(row.get('area prefix', '')) else ""
                        area_name = str(row.get('area name', '')).strip() if pd.notna(row.get('area name', '')) else ""
                        
                        # Thêm vào mappings để bulk insert
                        mappings.append({
                            'call_type': CallType.OUT,
                            'caller': caller,
                            'callee': callee,
                            'caller_type': caller_type,
                            'callee_type': callee_type,
                            'begin_time': begin_time,
                            'end_time': end_time,
                            'duration': duration,
                            'caller_gateway': caller_gateway,
                            'called_gateway': called_gateway,
                            'file_id': file_id,
                            'call_date': call_date,
                            'call_hour': call_hour,
                            'area_code': area_code if area_code else None,
                            'area_name': area_name if area_name else None,
                            'caller_note': caller_note,
                            'callee_note': callee_note,
                            'caller_prefix': extract_prefix(caller),
                            'callee_prefix': extract_prefix(callee)
                        })
                        
                        processed_rows += 1
                    
                    except Exception as e:
                        error_count += 1
                        logger.error(f"Lỗi khi xử lý dòng: {str(e)}")
                        continue
                
                # Bulk insert
                if mappings:
                    db.bulk_insert_mappings(CallLog, mappings)
                    db.commit()
                    
                    # Log memory usage sau mỗi commit
                    log_memory_usage("process_call_out_file", f"progress_{processed_rows}/{total_rows}", file_id)
                
                # Cập nhật tiến độ nếu được gọi từ celery task
                if task:
                    task.update_state(
                        state='PROGRESS',
                        meta={
                            'current': processed_rows,
                            'total': total_rows,  # Đã biết tổng số dòng
                            'file_id': file_id,
                            'status': 'Processing'
                        }
                    )
                
                # Thu hồi bộ nhớ
                del chunk_df
                del mappings
                gc.collect()
            
            # Giải phóng bộ nhớ của dataframe chính
            del df
            gc.collect()
            
        except Exception as e:
            logger.exception(f"Lỗi khi đọc file: {str(e)}")
            raise
        
        processing_time = time.time() - start_time
        logger.info(f"Hoàn thành xử lý file Call Out. "
                   f"Đã xử lý {processed_rows} dòng trong {processing_time:.2f} giây "
                   f"({processed_rows / processing_time:.2f} dòng/giây). "
                   f"Số lỗi: {error_count}")
        
        # Log memory usage khi hoàn thành
        log_memory_usage("process_call_out_file", "complete", file_id)
        
        return {
            "processed_rows": processed_rows,
            "error_count": error_count,
            "processing_time": processing_time
        }
            
    except Exception as e:
        logger.exception(f"Lỗi khi xử lý file Call Out: {str(e)}")
        raise
    
    finally:
        # Đóng session nếu tự tạo
        if should_close_db and 'db' in locals() and db and db.is_active:
            db.close() 