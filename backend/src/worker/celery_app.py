from celery import Celery
from ..core.config import settings
from celery.schedules import crontab

# Khởi tạo Celery app
celery_app = Celery(
    "worker",
    broker=settings.CELERY_BROKER,
    backend=settings.CELERY_BACKEND,
    broker_connection_retry_on_startup=True,
    include=[
        'src.worker.call_log_tasks',
        'src.worker.cleanup_tasks',
        'src.worker.template_tasks',
        'src.worker.reconciliation_tasks'  # Thêm reconciliation_tasks
    ]
)

# Cấu hình task routes
celery_app.conf.task_routes = {
    "src.worker.call_log_tasks.*": {"queue": "call_logs"},
    "src.worker.cleanup_tasks.*": {"queue": "maintenance"},
    "src.worker.template_tasks.*": {"queue": "templates"},
    "src.worker.reconciliation_tasks.*": {"queue": "templates"}  # Thêm route cho reconciliation tasks
}

# <PERSON>ê<PERSON> lịch cho các task định kỳ
celery_app.conf.beat_schedule = {
    'cleanup-old-call-logs': {
        'task': 'src.worker.cleanup_tasks.cleanup_old_call_logs',
        'schedule': crontab(hour=1, minute=0, day_of_month='1,10,20'),  # Chạy vào 1:00 AM vào ngày 1, 10, 20 hàng tháng
        'options': {'queue': 'maintenance'}
    },
}

# Cấu hình để tối ưu bộ nhớ và hiệu suất cho server 2 cores 4GB RAM
celery_app.conf.worker_prefetch_multiplier = 1  # Giảm số lượng task prefetch để không tải quá nhiều tasks
celery_app.conf.worker_max_tasks_per_child = 5  # Restart worker sau 5 tasks để tránh memory leak (giảm từ 10)
celery_app.conf.task_acks_late = True  # Chỉ xác nhận task sau khi hoàn thành (giảm rủi ro tasks bị mất khi worker crash)
celery_app.conf.task_reject_on_worker_lost = True  # Từ chối task nếu worker bị mất
celery_app.conf.task_time_limit = 3600  # Giới hạn thời gian thực hiện task (1 giờ)
celery_app.conf.task_soft_time_limit = 3000  # Soft limit (50 phút)
celery_app.conf.worker_concurrency = 2  # Số lượng worker processes phù hợp với 2 cores

# Cấu hình Redis broker và connection recovery
celery_app.conf.broker_transport_options = {
    'visibility_timeout': 43200,       # 12 giờ (Consider reducing if tasks are shorter)
    'socket_timeout': 15,              # Giảm timeout cho socket operations (seconds) từ 30 xuống 15
    'socket_connect_timeout': 15,      # Giảm timeout cho initial connection (seconds) từ 30 xuống 15
    'socket_keepalive': True,          # Sử dụng TCP keepalive
    'retry_on_timeout': True,          # Cố gắng retry nếu timeout
    'max_retries': 5,                  # Số lần retry tối đa cho một operation
    'retry_policy': {
        'max_retries': 10,             # Số lần retry tối đa khi connection fails
        'interval_start': 0.5,         # Bắt đầu với 0.5s delay
        'interval_step': 0.5,          # Tăng delay thêm 0.5s sau mỗi lần retry
        'interval_max': 10,            # Giới hạn delay tối đa là 10s
    }
}

# Cấu hình Redis result backend
celery_app.conf.result_backend_transport_options = {
    'retry_policy': {
        'max_retries': 10,
        'interval_start': 0.5,
        'interval_step': 0.5,
        'interval_max': 10,
    }
}
celery_app.conf.worker_send_task_events = True    # Gửi events để theo dõi trạng thái
celery_app.conf.worker_pool_restarts = True       # Cho phép khởi động lại pool khi cần
celery_app.conf.broker_connection_retry = True    # Tự động retry khi kết nối broker bị mất
celery_app.conf.broker_connection_max_retries = 10 # Số lần retry tối đa khi kết nối broker bị mất
celery_app.conf.broker_connection_timeout = 15.0  # Timeout cho kết nối broker (seconds)

# Cấu hình task serialization
celery_app.conf.task_serializer = 'json'
celery_app.conf.result_serializer = 'json'
celery_app.conf.accept_content = ['json']
