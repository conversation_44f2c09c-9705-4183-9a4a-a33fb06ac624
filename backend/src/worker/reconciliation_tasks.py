from celery import shared_task
import logging
from typing import List, Dict, Any, Optional, Union
from sqlalchemy import extract
import re
from datetime import datetime
from sqlalchemy.orm import selectinload, Session, joinedload

from ..database.session import SessionLocal
from ..models.template import ReconciliationTemplate, TemplateType
from ..models.dscd import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DauSoDichVu, <PERSON><PERSON><PERSON><PERSON><PERSON>
from ..models.dsc import DoiSoatCuoc, DauSoDichVuCuoc, TongKetCuoc
from ..models.call_log import CallLog, NumberType, CallType
from ..models.enums import ReconciliationStatus, ServiceType
from ..utils.enhanced_phone_utils import matches_caller, determine_service_type
from ..utils.pricing_utils import get_pricing_rule, apply_billing_method
from ..crud import crud_dscd
from ..models.partner import Partner

logger = logging.getLogger(__name__)

BATCH_SIZE = 100000  # Process call logs in batches of 10000

@shared_task(
    bind=True,
    name="src.worker.reconciliation_tasks.process_dscd_reconciliation",
    max_retries=3,
    default_retry_delay=300,  # 5 minutes between retries
    time_limit=3600,  # 1 hour timeout
    soft_time_limit=3300  # 55 minutes soft timeout
)
def process_dscd_reconciliation(
    self,
    dscd_id: int,
    template_id: int,
    ky_doi_soat: str,
    partner_id: int
) -> bool:
    """Process DSCD reconciliation asynchronously"""
    # 1. Log thông tin đầu vào task
    logger.info(f"Starting DSCD reconciliation task with params: "
                f"dscd_id={dscd_id}, template_id={template_id}, "
                f"ky_doi_soat={ky_doi_soat}, partner_id={partner_id}")
    
    try:
        db = SessionLocal()
        
        # Get the newly created reconciliation record (which now includes copied data)
        reconciliation_record = db.query(DoiSoatCoDinh).options(
            # Load necessary relationships for processing if needed
            selectinload(DoiSoatCoDinh.du_lieu).selectinload(DauSoDichVu.co_dinh_noi_hat), 
            selectinload(DoiSoatCoDinh.du_lieu).selectinload(DauSoDichVu.co_dinh_lien_tinh),
            selectinload(DoiSoatCoDinh.du_lieu).selectinload(DauSoDichVu.di_dong),
            selectinload(DoiSoatCoDinh.du_lieu).selectinload(DauSoDichVu.cuoc_1900),
            selectinload(DoiSoatCoDinh.du_lieu).selectinload(DauSoDichVu.quoc_te),
            selectinload(DoiSoatCoDinh.du_lieu).selectinload(DauSoDichVu.cuoc_thue_bao),
            selectinload(DoiSoatCoDinh.tong_ket)
        ).filter(DoiSoatCoDinh.id == dscd_id).first()
        
        if not reconciliation_record:
            raise ValueError(f"Reconciliation record ID {dscd_id} not found after creation.")
            
        # Get the list of DauSoDichVu directly associated with this record
        dau_so_dich_vu_list = reconciliation_record.du_lieu 
        
        # 2. Log thông tin về dữ liệu đầu số
        logger.info(f"DauSoDichVu details for reconciliation ID {dscd_id}:")
        for dau_so in dau_so_dich_vu_list:
            logger.info(f"  - ID: {dau_so.id}, Display: {dau_so.standardized_display}, "
                       f"Type: {dau_so.dau_so_type}")
        
        # 2. Parse period (already have ky_doi_soat)
        year, month = map(int, ky_doi_soat.split('-'))
        logger.info(f"Processing period: {month}/{year}")
        
        # 3. Process call logs in batches
        offset = 0
        # --- Key Change: Aggregator maps dau_so_id -> service_type -> aggregated data ---
        results_aggregator: Dict[int, Dict[ServiceType, Dict[str, Any]]] = {}
        # --- START: Initialize pricing cache ---
        pricing_cache: Dict[tuple, Optional[tuple]] = {}
        # --- END: Initialize pricing cache ---
        stats = {
            'processed': 0,
            'skipped_no_match': 0,
            'skipped_no_service': 0,
            'error_calculating_cost': 0
        }
        
        batch_count = 0
        while True:
            batch_count += 1
            
            # Get batch of call logs for the correct period and partner
            call_logs = db.query(CallLog).filter(
                CallLog.begin_time >= f"{year}-{month:02d}-01 00:00:00+07",
                CallLog.begin_time < f"{year}-{month+1 if month < 12 else 1:02d}-01 00:00:00+07" if month < 12 else f"{year+1}-01-01 00:00:00+07",
                CallLog.caller_type == 'fixed',
                CallLog.call_type == 'out'
            ).offset(offset).limit(BATCH_SIZE).all()
            
            if not call_logs:
                logger.info(f"No more call logs to process after {batch_count} batches for period {ky_doi_soat}")
                break
            
            # 3. Log thông tin về call logs mỗi batch
            logger.info(f"Batch {batch_count} call logs summary: "
                       f"First call time={call_logs[0].begin_time if call_logs else None}, "
                       f"Last call time={call_logs[-1].begin_time if call_logs else None}, "
                       f"Count={len(call_logs)}")
            
            # Process batch
            for call_log in call_logs:
                result = process_single_call_log(
                    call_log=call_log,
                    dau_so_list=dau_so_dich_vu_list,
                    partner_id=partner_id,
                    db=db,
                    pricing_cache=pricing_cache
                )
                
                if isinstance(result, tuple):
                    matched_copied_dau_so_id, service_type, data = result
                    if matched_copied_dau_so_id not in results_aggregator:
                        results_aggregator[matched_copied_dau_so_id] = {}
                    if service_type not in results_aggregator[matched_copied_dau_so_id]:
                        results_aggregator[matched_copied_dau_so_id][service_type] = data
                    else:
                        agg_entry = results_aggregator[matched_copied_dau_so_id][service_type]
                        agg_entry['total_duration_seconds'] += data['total_duration_seconds']
                        agg_entry['total_cost_before_vat'] += data['total_cost_before_vat']
                        agg_entry['call_count'] += 1
                    stats['processed'] += 1
                elif result == 0:
                    stats['error_calculating_cost'] += 1
                elif result is False:
                    stats['skipped_no_service'] += 1
                else:
                    stats['skipped_no_match'] += 1
            
            # 5. Log thông tin aggregation mỗi batch
            logger.info(f"Batch {batch_count} aggregation stats: "
                       f"Total processed={stats['processed']}, "
                       f"Skipped no match={stats['skipped_no_match']}, "
                       f"Skipped no service={stats['skipped_no_service']}, "
                       f"Error calculating={stats['error_calculating_cost']}")
            
            offset += BATCH_SIZE
        
        # 4. Update DauSoDichVu records with calculated values
        logger.info("Updating DauSoDichVu records with aggregated results")
        total_calculated_cost = 0.0

        # --- START: Map ServiceType to DauSoDichVu relationship attribute and loai_cuoc string ---
        service_type_mapping = {
            ServiceType.FIXED_NOI_HAT: ("co_dinh_noi_hat", "co_dinh_noi_hat"),
            ServiceType.FIXED_LIEN_TINH_NOI_MANG: ("co_dinh_lien_tinh", "co_dinh_lien_tinh"), # Map cả nội mạng và khác mạng vào liên tỉnh chung
            ServiceType.FIXED_LIEN_TINH_KHAC_MANG: ("co_dinh_lien_tinh", "co_dinh_lien_tinh"),
            ServiceType.MOBILE_NORMAL: ("di_dong", "di_dong"),
            ServiceType.MOBILE_MNP: ("di_dong", "di_dong"), # Map MNP vào di động chung
            ServiceType.SERVICE_1900_VOICE_VAS: ("cuoc_1900", "cuoc_1900"),
            # Lưu ý: Hiện tại DSCD task chỉ xử lý FIXED OUT, nên các loại khác như QUOC_TE có thể không có trong aggregation
            # Nhưng vẫn nên map để tương thích nếu logic filter thay đổi
            ServiceType.FIXED_QUOC_TE: ("quoc_te", "quoc_te"),
            ServiceType.INTL_OUTBOUND: ("quoc_te", "quoc_te"),
            # Các loại khác có thể không áp dụng trực tiếp cho các cột CuocGoi hiện tại
        }
        # --- END: Map ServiceType ---

        for copied_dau_so_id, service_type_dict in results_aggregator.items():
            dau_so_to_update = db.get(DauSoDichVu, copied_dau_so_id)
            if not dau_so_to_update:
                logger.warning(f"Could not find copied DauSoDichVu with ID {copied_dau_so_id} in session to update calculated values.")
                continue

            # 6. Log chi tiết về cập nhật DauSoDichVu
            logger.info(f"Updating DauSoDichVu ID {copied_dau_so_id}:")
            for service_type, aggregated_data in service_type_dict.items():
                logger.info(f"  - Service: {service_type.value}")
                logger.info(f"    Duration: {aggregated_data['total_duration_seconds']:.2f}s")
                logger.info(f"    Cost: {aggregated_data['total_cost_before_vat']:.2f}")
                logger.info(f"    Calls: {aggregated_data['call_count']}")

                if service_type not in service_type_mapping:
                    logger.warning(f"No mapping defined for ServiceType {service_type.value}. Skipping.")
                    continue

                attribute_name, loai_cuoc_str = service_type_mapping[service_type]
                cuoc_goi_obj = getattr(dau_so_to_update, attribute_name, None)

                if cuoc_goi_obj is None:
                    cuoc_goi_obj = CuocGoi(
                        dau_so_id=copied_dau_so_id,
                        loai_cuoc=loai_cuoc_str,
                        thoi_gian_goi=0.0,
                        cuoc=0.0
                    )
                    setattr(dau_so_to_update, attribute_name, cuoc_goi_obj)

                current_duration = getattr(cuoc_goi_obj, 'thoi_gian_goi', 0.0) or 0.0
                current_cost = getattr(cuoc_goi_obj, 'cuoc', 0.0) or 0.0

                duration_to_add = aggregated_data.get('total_duration_seconds', 0.0)
                cost_to_add = aggregated_data.get('total_cost_before_vat', 0.0)

                setattr(cuoc_goi_obj, 'thoi_gian_goi', current_duration + duration_to_add)
                setattr(cuoc_goi_obj, 'cuoc', current_cost + cost_to_add)

                total_calculated_cost += cost_to_add

            db.add(dau_so_to_update)

        # 5. Calculate VAT and update TongKet
        logger.info("Calculating VAT and updating TongKet")
        VAT_RATE = 0.10 # Consider making this configurable
        total_vat = total_calculated_cost * VAT_RATE
        total_cost_after_vat = total_calculated_cost + total_vat
        
        if reconciliation_record.tong_ket:
            reconciliation_record.tong_ket.cong_tien_dich_vu = total_calculated_cost
            reconciliation_record.tong_ket.tien_thue_gtgt = total_vat
            reconciliation_record.tong_ket.tong_cong_tien = total_cost_after_vat
            db.add(reconciliation_record.tong_ket)
        else:
            logger.error(f"TongKet record not found for reconciliation ID {dscd_id} during final update.")
            # Optionally create it here if it should always exist after deep copy

        # 6. Update main record status to CALCULATED
        reconciliation_record.status = ReconciliationStatus.CALCULATED
        reconciliation_record.updated_at = datetime.utcnow() # Manually update timestamp
        db.add(reconciliation_record)
        
        # 7. Commit all changes
        db.commit()
        logger.info(f"Successfully completed and committed calculated DSCD reconciliation ID: {dscd_id}")
        logger.info(f"Final stats: {stats}")
        return True
        
    except Exception as e:
        logger.exception(f"Error processing DSCD reconciliation {dscd_id}: {e}")
        # Update status to ERROR
        try:
            # Use a new session for error handling to avoid issues with the failed session
            error_db = SessionLocal()
            try:
                error_record = error_db.query(DoiSoatCoDinh).filter(DoiSoatCoDinh.id == dscd_id).first()
                if error_record:
                    error_record.status = ReconciliationStatus.ERROR
                    error_record.error_message = str(e)[:1000] # Limit error message length
                    error_db.commit()
            finally:
                 error_db.close()
        except Exception as db_err:
            logger.exception(f"Failed to update error status for DSCD ID {dscd_id}: {db_err}")
            
        # Re-raise the original exception for Celery retry mechanism
        raise
        
    finally:
        # Ensure the main session is closed
        if 'db' in locals() and db.is_active:
            db.close()

def process_single_call_log(
    call_log: CallLog,
    dau_so_list: List[DauSoDichVu],
    partner_id: int,
    db: Session,
    pricing_cache: Dict[tuple, Optional[tuple]]
) -> Optional[Union[tuple, int, bool]]:
    
    caller_normalized = re.sub(r'[^\d]', '', call_log.caller)
    
    # 4. Log chi tiết về matching và pricing
    logger.debug(f"[MATCHING] Processing call_log ID {call_log.id}:")
    logger.debug(f"  - Caller: {call_log.caller}")
    logger.debug(f"  - Normalized: {caller_normalized}")
    logger.debug(f"  - Callee: {call_log.callee}")
    logger.debug(f"  - Duration: {call_log.duration}s")
    logger.debug(f"  - Begin time: {call_log.begin_time}")
    
    if not caller_normalized:
        logger.debug(f"[MATCHING] Call log ID {call_log.id}: Skipped - empty normalized caller")
        return None
        
    matched_dau_so: Optional[DauSoDichVu] = None
    for dau_so in dau_so_list:
        template_info = {
            "type": dau_so.dau_so_type, 
            "standardized_display": dau_so.standardized_display,
            "start_num_str": dau_so.start_num_str,
            "end_num_str": dau_so.end_num_str,
            "prefix": dau_so.prefix
        }
        logger.debug(f"[MATCHING] Call log ID {call_log.id}: Trying to match against DauSo ID {dau_so.id} - {template_info}")
        
        if matches_caller(caller_normalized, template_info):
            matched_dau_so = dau_so
            logger.debug(f"[MATCHING] Call log ID {call_log.id}: Successfully matched with DauSo ID {dau_so.id}")
            break
            
    if not matched_dau_so:
        logger.debug(f"[MATCHING] Call log ID {call_log.id}: No matching DauSoDichVu found for caller {caller_normalized}")
        return None
        
    service_type = determine_service_type(call_log)
    if not service_type:
        logger.warning(f"[MATCHING] Call log ID {call_log.id}: Could not determine service type (caller: {call_log.caller}, callee: {call_log.callee})")
        return False
        
    # --- START: Trừ 1 giây khỏi duration ---
    # Lấy duration gốc
    original_duration = call_log.duration
    # Trừ đi 1 giây, đảm bảo không nhỏ hơn 0
    adjusted_duration = max(0.0, original_duration - 1.0) 
    
    # Log lại sự thay đổi nếu cần
    if adjusted_duration != original_duration:
        logger.debug(f"[DURATION ADJUST] Call log ID {call_log.id}: Original duration={original_duration}s, Adjusted duration={adjusted_duration}s")
    else:
        logger.debug(f"[DURATION ADJUST] Call log ID {call_log.id}: Duration={original_duration}s (no adjustment needed or already 0)")
    # --- END: Trừ 1 giây khỏi duration ---
        
    pricing_key = (partner_id, service_type)
    cost = 0.0
    
    if pricing_key in pricing_cache:
        pricing_info = pricing_cache[pricing_key]
        logger.debug(f"[PRICING] Call log ID {call_log.id}: Cache hit for pricing key {pricing_key}")
    else:
        logger.debug(f"[PRICING] Call log ID {call_log.id}: Cache miss for pricing key {pricing_key}. Querying DB.")
        pricing_info = get_pricing_rule(db, partner_id, service_type)
        pricing_cache[pricing_key] = pricing_info
    
    if not pricing_info:
        logger.warning(f"[PRICING] Call log ID {call_log.id}: No active pricing rule found for partner {partner_id} and service type {service_type.value}. Using 0 cost but duration will still be recorded.")
        # Vẫn sử dụng adjusted_duration cho việc ghi nhận thời gian nếu không có giá
        cost = 0.0
    else:
        price, billing_method = pricing_info
        try:
            # --- Sử dụng adjusted_duration để tính cước ---
            cost = apply_billing_method(adjusted_duration, price, billing_method) 
            logger.debug(f"[PRICING] Call log ID {call_log.id}: Applied billing method - duration={adjusted_duration}, price={price}, method={billing_method}, cost={cost}")
            # --- Kết thúc sử dụng adjusted_duration ---
        except Exception as calc_err:
            logger.error(f"[PRICING] Call log ID {call_log.id}: Error calculating cost: {calc_err}")
            return 0
    
    data = {
        # --- Sử dụng adjusted_duration để tổng hợp ---
        'total_duration_seconds': adjusted_duration, 
        'total_cost_before_vat': cost,
        'call_count': 1,
        'dau_so_display': matched_dau_so.standardized_display,
        'service_type': service_type.value,
        'dau_so_type': matched_dau_so.dau_so_type.value if matched_dau_so.dau_so_type else None,
    }
    
    # --- Sử dụng adjusted_duration trong log cuối cùng ---
    logger.debug(f"[AGGREGATION] Call log ID {call_log.id}: Matched DauSo ID {matched_dau_so.id}, ServiceType: {service_type.value}, Duration: {adjusted_duration}s will be recorded, Calculated Cost: {cost}")
    # --- Kết thúc sử dụng adjusted_duration ---
    
    return matched_dau_so.id, service_type, data


# --- START: Add Celery Task for DSC Reconciliation ---

@shared_task(
    bind=True,
    name="src.worker.reconciliation_tasks.process_dsc_reconciliation",
    max_retries=3,
    default_retry_delay=300,  # 5 minutes
    time_limit=3600,  # 1 hour timeout
    soft_time_limit=3300  # 55 minutes soft timeout
)
def process_dsc_reconciliation(
    self,
    dsc_id: int,
    template_id: int, # Vẫn cần template_id để tham chiếu pricing rule nếu cần
    ky_doi_soat: str,
    partner_id: int # ID của partner chủ đầu số (từ bản ghi reconciliation)
) -> bool:
    """Process DSC reconciliation asynchronously."""
    logger.info(f"Starting DSC reconciliation task with params: "
                f"dsc_id={dsc_id}, template_id={template_id}, "
                f"ky_doi_soat={ky_doi_soat}, partner_id={partner_id}")

    db: Optional[Session] = None
    try:
        db = SessionLocal()

        # 1. Lấy bản ghi DSC mới và dữ liệu con đã được sao chép
        reconciliation_record = db.query(DoiSoatCuoc).options(
            selectinload(DoiSoatCuoc.du_lieu), # Eager load các DauSoDichVuCuoc con
            joinedload(DoiSoatCuoc.tong_ket)   # Eager load TongKetCuoc con
        ).filter(
            DoiSoatCuoc.id == dsc_id,
            DoiSoatCuoc.is_template_data == False
        ).first()
        
        if not reconciliation_record:
            raise ValueError(f"DSC record ID {dsc_id} not found or is not actual data.")
        
        if not reconciliation_record.du_lieu:
             logger.warning(f"DSC record ID {dsc_id} has no DauSoDichVuCuoc children. Nothing to match against. Finishing task.")
             # Cập nhật trạng thái thành CALCULATED vì không có gì để tính
             reconciliation_record.status = ReconciliationStatus.CALCULATED
             reconciliation_record.updated_at = datetime.utcnow()
             reconciliation_record.task_id = self.request.id
             db.add(reconciliation_record)
             db.commit()
             return True
        
        # Tạo dict tra cứu nhanh các bản ghi con theo dau_so (string)
        target_dau_so_map: Dict[str, DauSoDichVuCuoc] = {str(ds.dau_so): ds for ds in reconciliation_record.du_lieu}
        logger.info(f"Loaded {len(target_dau_so_map)} target dau_so children for DSC ID {dsc_id}")
        
        # Tạo mapping caller_note -> Tên Partner chuẩn
        partner_name_map = {
            "mobile: Vietnamobile": "VNM",
            "mobile: Viettel": "VIETTEL",
            "mobile: Vinaphone": "VNPT",
            "Mobile: Mobifone": "MOBIFONE" # Lưu ý case M
        }
        
        # --- Query partner IDs for calling partners (giữ nguyên) ---
        calling_partner_names = list(partner_name_map.values())
        calling_partners = db.query(Partner).filter(Partner.name.in_(calling_partner_names)).all()
        calling_partner_ids = {p.name: p.id for p in calling_partners}
        for name in calling_partner_names:
            if name not in calling_partner_ids:
                logger.warning(f"Calling partner with standard name '{name}' not found in the database. Calls from this partner might be skipped during cost calculation if ID is needed elsewhere.")

        # 2. Parse kỳ đối soát (giữ nguyên)
        try:
            year, month = map(int, ky_doi_soat.split('-'))
            logger.info(f"Processing period: {month}/{year}")
            start_time_str = f"{year}-{month:02d}-01 00:00:00+07"
            if month == 12:
                end_time_str = f"{year + 1}-01-01 00:00:00+07"
            else:
                end_time_str = f"{year}-{month + 1:02d}-01 00:00:00+07"
        except ValueError:
             raise ValueError(f"Invalid ky_doi_soat format: {ky_doi_soat}. Expected YYYY-MM.")

        # 3. Xử lý Call Logs theo lô
        offset = 0
        # --- Thay đổi Aggregator: Key là ID của DauSoDichVuCuoc con --- 
        results_aggregator: Dict[int, Dict[str, Dict[str, float]]] = {} # {child_id: {partner_name: {duration: X, cost: Y}}}
        stats = {
            'processed': 0,
            'skipped_no_match': 0,
            'error_calculating_cost': 0,
            'skipped_no_caller_partner': 0
        }
        batch_count = 0

        while True:
            batch_count += 1
            logger.info(f"Processing CallLog batch {batch_count} (offset: {offset})")
            
            # Lấy lô CallLog (giữ nguyên)
            call_logs_batch = db.query(CallLog).filter(
                CallLog.begin_time >= start_time_str,
                CallLog.begin_time < end_time_str,
                CallLog.call_type == 'in', # Lọc cuộc gọi đến
                CallLog.caller_note.like('mobile: V%'), # Tối ưu truy vấn ban đầu
            ).offset(offset).limit(BATCH_SIZE).all()

            if not call_logs_batch:
                logger.info(f"No more call logs to process after {batch_count-1} batches for period {ky_doi_soat}")
                break

            # Xử lý từng call_log trong lô
            for call_log in call_logs_batch:
                # Xác định nhà mạng gọi đến (giữ nguyên)
                calling_partner_name = None
                for note_prefix, partner_name in partner_name_map.items():
                    if call_log.caller_note and call_log.caller_note.startswith(note_prefix):
                        calling_partner_name = partner_name
                        break
                
                if not calling_partner_name:
                    stats['skipped_no_caller_partner'] += 1
                    continue

                # --- Thay đổi Matching: Khớp callee với target_dau_so_map --- 
                callee_str = str(call_log.callee)
                matched_child_record = target_dau_so_map.get(callee_str)
                
                if matched_child_record: # Nếu khớp với một bản ghi con đã có
                    matched_child_id = matched_child_record.id # Lấy ID của bản ghi con
                    
                    # Điều chỉnh và làm tròn phút (giữ nguyên)
                    duration_sec = call_log.duration or 0.0
                    adjusted_duration_seconds = max(0.0, duration_sec - 1.0)
                    duration_minutes_float = adjusted_duration_seconds / 60.0
                    rounded_duration_minutes = round(duration_minutes_float)

                    # Lấy giá cước (giữ nguyên logic, dùng partner_id của record chính)
                    service_type_for_pricing = ServiceType.MOBILE_NORMAL
                    pricing_info = get_pricing_rule(db, partner_id, service_type_for_pricing)
                    
                    call_cost = 0.0
                    if pricing_info:
                        price_per_minute, billing_method = pricing_info
                        try:
                            call_cost = apply_billing_method(rounded_duration_minutes, price_per_minute, billing_method)
                        except Exception as calc_err:
                            logger.error(f"Error calculating cost for CallLog ID {call_log.id}: {calc_err}")
                            stats['error_calculating_cost'] += 1
                    else:
                        logger.warning(f"No pricing rule found for partner {partner_id}, service {service_type_for_pricing.value}. Using 0 cost for CallLog ID {call_log.id}")

                    # --- Thay đổi Aggregation: Dùng matched_child_id làm key --- 
                    if matched_child_id not in results_aggregator:
                        results_aggregator[matched_child_id] = {}
                    if calling_partner_name not in results_aggregator[matched_child_id]:
                        results_aggregator[matched_child_id][calling_partner_name] = {'duration_minutes': 0.0, 'cost': 0.0}
                    
                    results_aggregator[matched_child_id][calling_partner_name]['duration_minutes'] += rounded_duration_minutes
                    results_aggregator[matched_child_id][calling_partner_name]['cost'] += call_cost
                    stats['processed'] += 1
                    
                else:
                    stats['skipped_no_match'] += 1 # Không khớp đầu số

            offset += BATCH_SIZE
            logger.info(f"Batch {batch_count} stats: {stats}")

        # 4. Cập nhật kết quả vào bản ghi DoiSoatCuoc mới và các con của nó
        logger.info("Updating DSC record and its children with aggregated results...")
        total_cost_all = 0.0
        # --- Thay đổi: Lấy map các con đã load từ record chính --- 
        existing_children_map: Dict[int, DauSoDichVuCuoc] = {child.id: child for child in reconciliation_record.du_lieu}

        # --- Thay đổi: Duyệt qua aggregator theo child_id --- 
        for child_id, partner_data in results_aggregator.items():
            # --- Thay đổi: Tìm bản ghi con đã load theo ID --- 
            child_record = existing_children_map.get(child_id)
            if not child_record:
                # Trường hợp này không nên xảy ra nếu logic khớp đúng
                logger.error(f"Could not find loaded child record with ID {child_id} for DSC ID {dsc_id}. Skipping update for this child.")
                continue 

            # Cập nhật dữ liệu từ aggregation (giữ nguyên logic gán vào các cột partner)
            child_total_cost = 0.0
            for partner_name, values in partner_data.items():
                duration = values.get('duration_minutes', 0.0)
                cost = values.get('cost', 0.0)
                child_total_cost += cost
                
                if partner_name == "VNM":
                    child_record.vnm_san_luong = duration
                    child_record.vnm_thanh_tien = cost
                elif partner_name == "VIETTEL":
                    child_record.viettel_san_luong = duration
                    child_record.viettel_thanh_tien = cost
                elif partner_name == "VNPT":
                    child_record.vnpt_san_luong = duration
                    child_record.vnpt_thanh_tien = cost
                elif partner_name == "MOBIFONE":
                    child_record.vms_san_luong = duration
                    child_record.vms_thanh_tien = cost
                # Bỏ qua 'khac' 
            
            child_record.tong_thanh_toan = child_total_cost # Cập nhật tổng tiền cho đầu số này
            child_record.updated_at = datetime.utcnow() # Cập nhật timestamp
            db.add(child_record) # Đánh dấu bản ghi con là đã thay đổi
            total_cost_all += child_total_cost

        # 5. Cập nhật TongKetCuoc (lấy từ record chính)
        tong_ket_record = reconciliation_record.tong_ket
        if not tong_ket_record:
            # Trường hợp này không nên xảy ra vì đã copy từ template
            logger.error(f"TongKetCuoc record not found for DSC ID {dsc_id} despite being copied. Creating a new one.")
            tong_ket_record = TongKetCuoc(doi_soat_id=dsc_id)
            db.add(tong_ket_record)
            reconciliation_record.tong_ket = tong_ket_record # Gán lại vào relationship
            
        VAT_RATE = 0.10 # Nên lấy từ config
        tong_ket_record.cong_tien_dich_vu = total_cost_all
        tong_ket_record.tien_thue_gtgt = total_cost_all * VAT_RATE
        tong_ket_record.tong_cong_tien = total_cost_all * (1 + VAT_RATE)
        tong_ket_record.updated_at = datetime.utcnow() # Cập nhật timestamp
        db.add(tong_ket_record) # Đánh dấu bản ghi tổng kết là đã thay đổi

        # 6. Cập nhật trạng thái bản ghi chính
        reconciliation_record.status = ReconciliationStatus.CALCULATED
        reconciliation_record.updated_at = datetime.utcnow()
        reconciliation_record.task_id = self.request.id # Lưu task ID hiện tại
        db.add(reconciliation_record)

        # 7. Commit tất cả thay đổi
        db.commit()
        logger.info(f"Successfully completed and committed calculated DSC reconciliation ID: {dsc_id}")
        logger.info(f"Final stats: {stats}")
        return True

    except Exception as e:
        logger.exception(f"Error processing DSC reconciliation {dsc_id}: {e}")
        if db:
            db.rollback()
            # Cập nhật trạng thái lỗi (giữ nguyên)
            try:
                error_record = db.query(DoiSoatCuoc).filter(DoiSoatCuoc.id == dsc_id).first()
                if error_record:
                    error_record.status = ReconciliationStatus.ERROR
                    error_record.error_message = str(e)[:1000]
                    error_record.task_id = self.request.id
                    db.commit()
            except Exception as db_err:
                logger.exception(f"Failed to update error status for DSC ID {dsc_id}: {db_err}")
        raise

    finally:
        if db:
            db.close()

# --- END: Add Celery Task for DSC Reconciliation --- 