import os
import time
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Any, List, Optional
import gc
import multiprocessing as mp
from functools import partial

from sqlalchemy.orm import Session

from ..database.session import SessionLocal
from ..models.call_log import CallLogFile, CallLog, FileType, CallType, FileStatus, NumberType
from ..core.config import settings
from ..core.logging import logger
from .celery_app import celery_app

# Tối ưu hàm với lookup tables
MOBILE_PREFIXES = {
    '032', '033', '034', '035', '036', '037', '038', '039',  # Viettel
    '070', '076', '077', '078', '079',  # Mobifone
    '081', '082', '083', '084', '085',  # Vinaphone
    '086',  # Viettel
    '087',  # iTelecom
    '088',  # Vinaphone
    '089',  # Mobifone
    '090',  # Mobifone
    '091',  # <PERSON><PERSON>one
    '092',  # Vietnammobile
    '093',  # Mobifone
    '094',  # Vinaphone
    '096', '097', '098',  # Viettel
    '099',  # Gmobile
    '056', '058',  # Vietnammobile
    '059',  # Gmobile
}

FIXED_SERVICE_PREFIXES = {'1900', '1800', '1700', '1600'}

def normalize_phone_number_fast(phone: str) -> str:
    """
    Phiên bản tối ưu của hàm chuẩn hóa số điện thoại
    """
    if not phone:
        return phone
    
    # Loại bỏ các ký tự không phải số - nhanh hơn filter
    digits_only = ''.join(c for c in str(phone) if c.isdigit())
    
    # Nếu chuỗi rỗng sau khi lọc, trả về chuỗi gốc
    if not digits_only:
        return phone
    
    # Nếu số bắt đầu bằng 1900, giữ nguyên
    if digits_only.startswith('1900'):
        return digits_only
    
    # Nếu không bắt đầu bằng 0, thêm 0 vào đầu
    if not digits_only.startswith('0'):
        return '0' + digits_only
    
    return digits_only

def classify_phone_number_fast(phone: str) -> NumberType:
    """Phiên bản tối ưu của classify_phone_number sử dụng lookups"""
    if not phone:
        return NumberType.UNKNOWN
    
    # Kiểm tra nhanh với sử dụng set lookups
    if phone.startswith('+'):
        return NumberType.INTL
    
    # Check các prefix trong sets (O(1) lookups)
    prefix_4 = phone[:4] if len(phone) >= 4 else ""
    prefix_3 = phone[:3] if len(phone) >= 3 else ""
    prefix_2 = phone[:2] if len(phone) >= 2 else ""
    
    if prefix_4 in FIXED_SERVICE_PREFIXES:
        return NumberType.FIXED
    
    if prefix_3 in MOBILE_PREFIXES:
        return NumberType.MOBILE
    
    if prefix_2 == '02':
        return NumberType.FIXED
    
    return NumberType.UNKNOWN

def optimize_db_connection(db: Session) -> Session:
    """Tối ưu cấu hình database connection"""
    try:
        # Tối ưu cho SQLite (nếu sử dụng)
        db.execute("PRAGMA journal_mode = WAL")
        db.execute("PRAGMA synchronous = NORMAL")
        db.execute("PRAGMA cache_size = 100000")
        db.execute("PRAGMA temp_store = MEMORY")
    except Exception:
        pass
    
    return db

@celery_app.task(bind=True)
def process_call_log_file(self, file_id: int):
    """
    Task xử lý file call log
    """
    db = SessionLocal()
    start_time = time.time()
    
    try:
        # Lấy thông tin file
        file_record = db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
        if not file_record:
            logger.error(f"File không tồn tại: {file_id}")
            return {"status": "error", "message": "File not found"}
        
        logger.info(f"Bắt đầu xử lý file {file_record.filename} (ID: {file_id}, Type: {file_record.file_type})")
        
        # Cập nhật trạng thái
        file_record.status = FileStatus.PROCESSING
        db.commit()
        
        # Tối ưu hóa database connection
        db = optimize_db_connection(db)
        
        # Xác định loại file và gọi hàm xử lý tương ứng
        try:
            if file_record.file_type == FileType.CALL_IN:
                logger.info(f"Xử lý file Call In: {file_record.filename}")
                result = process_call_in_file(file_record.file_path, file_id, self, db)
            else:  # call_out
                logger.info(f"Xử lý file Call Out: {file_record.filename}")
                result = process_call_out_file(file_record.file_path, file_id, self, db)
            
            # Đóng session cũ
            db.close()
            
            # Tạo session mới để cập nhật trạng thái
            new_db = SessionLocal()
            try:
                # Lấy lại file record với session mới
                file_record = new_db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
                if file_record:
                    # Cập nhật kết quả thành công
                    file_record.status = FileStatus.COMPLETED
                    file_record.processed_rows = result.get("processed_rows", 0)
                    file_record.error_count = result.get("error_count", 0)
                    
                    # Cập nhật thời gian xử lý
                    if "processing_time" in result:
                        file_record.processing_time = result["processing_time"]
                    else:
                        file_record.processing_time = time.time() - start_time
                    
                    if result.get("error_count", 0) > 0:
                        file_record.error_message = f"Completed with {result['error_count']} errors"
                    
                    new_db.commit()
                    
                    logger.info(f"Hoàn thành xử lý file {file_record.filename} (ID: {file_id}). "
                               f"Đã xử lý {file_record.processed_rows} dòng trong {file_record.processing_time:.2f}s. "
                               f"Lỗi: {file_record.error_count}")
            finally:
                if new_db.is_active:
                    new_db.close()
            
            return {
                "status": "success", 
                "file_id": file_id,
                "processed_rows": result.get("processed_rows", 0),
                "error_count": result.get("error_count", 0),
                "processing_time": result.get("processing_time", time.time() - start_time)
            }
            
        except Exception as e:
            # Tạo session mới để cập nhật lỗi
            db.close()
            error_db = SessionLocal()
            try:
                # Lấy lại file record với session mới
                file_record = error_db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
                if file_record:
                    # Xử lý lỗi
                    file_record.status = FileStatus.FAILED
                    file_record.error_message = str(e)
                    file_record.processing_time = time.time() - start_time
                    error_db.commit()
                    
                    logger.error(f"Lỗi xử lý file {file_record.filename} (ID: {file_id}): {str(e)}")
            finally:
                if error_db.is_active:
                    error_db.close()
            
            # Log lỗi
            logger.error(f"Error processing file {file_id}: {str(e)}")
            raise
            
    except Exception as e:
        logger.error(f"Error in process_call_log_file: {str(e)}")
        raise
    finally:
        if db.is_active:
            db.close()

def process_call_in_file(file_path: str, file_id: int, task=None, db: Session = None):
    """
    Xử lý file call in (gọi vào) - phiên bản tối ưu
    """
    if db is None:
        db = SessionLocal()
        db = optimize_db_connection(db)
    
    processed_rows = 0
    error_count = 0
    start_time = time.time()
    
    try:
        # Tối ưu đọc file Excel - chỉ đọc các cột cần thiết
        logger.info(f"Bắt đầu đọc file {file_path}")
        
        # Sử dụng kiểu dữ liệu hiệu quả hơn
        df = pd.read_excel(
            file_path,
            engine='openpyxl',
            dtype={
                'caller': str,
                'callee': str,
                'conversation_time': 'int32',  # Sử dụng int32 thay vì int
                'caller_gateway': str,
                'called_gateway': str
            }
        )
        logger.info(f"Đọc xong file {file_path} với {len(df)} dòng")
        
        # Cập nhật total_rows trước khi xử lý
        total_rows = len(df)
        with SessionLocal() as progress_db:
            file_record = progress_db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
            if file_record:
                file_record.total_rows = total_rows
                progress_db.commit()
        
        # Chuẩn hóa tên cột
        df.columns = [
            'caller', 'callee', 'begin_time', 'end_time', 
            'conversation_time', 'caller_gateway', 'called_gateway'
        ]
        
        # Xử lý dữ liệu với tối ưu hóa
        logger.info("Bắt đầu xử lý dữ liệu")
        
        # Chuyển đổi kiểu dữ liệu trước khi xử lý
        df['begin_time'] = pd.to_datetime(df['begin_time'])
        df['end_time'] = pd.to_datetime(df['end_time'])
        
        # Sử dụng phương pháp vectorized để trích xuất thông tin
        df['call_date'] = df['begin_time'].dt.date
        df['call_hour'] = df['begin_time'].dt.hour
        
        # Xử lý các giá trị NaN và chuyển đổi chuỗi để tránh lỗi
        df['caller'] = df['caller'].fillna('').astype(str)
        df['callee'] = df['callee'].fillna('').astype(str)
        df['caller_gateway'] = df['caller_gateway'].fillna('').astype(str)
        df['called_gateway'] = df['called_gateway'].fillna('').astype(str)
        
        # Chuẩn hóa số điện thoại với vectorized operations hoặc apply
        logger.info("Chuẩn hóa số điện thoại")
        
        # Sử dụng bản tối ưu của hàm normalize_phone_number
        df['caller'] = df['caller'].apply(normalize_phone_number_fast)
        df['callee'] = df['callee'].apply(normalize_phone_number_fast)
        
        # Phân loại số điện thoại
        logger.info("Phân loại số điện thoại")
        df['caller_type'] = df['caller'].apply(classify_phone_number_fast)
        df['callee_type'] = df['callee'].apply(classify_phone_number_fast)
        
        # Tăng kích thước batch và giảm tần suất commit để tối ưu hiệu suất
        batch_size = 100000  # Tăng từ 50000 lên 100000
        
        # Giảm tần suất log và commit
        log_interval = 200000
        commit_interval = batch_size
        
        for start_idx in range(0, len(df), batch_size):
            batch_start_time = time.time()
            end_idx = min(start_idx + batch_size, len(df))
            batch_df = df.iloc[start_idx:end_idx]
            
            # Chỉ log khi cần thiết
            if start_idx % log_interval == 0 or start_idx == 0:
                logger.info(f"Xử lý batch {start_idx}-{end_idx} / {total_rows}")
            
            try:
                # Sử dụng bulk_insert_mappings thay vì bulk_save_objects
                mappings = []
                
                # Chuyển đổi từ DataFrame thành danh sách mappings trực tiếp
                for row in batch_df.itertuples(index=False):
                    try:
                        mappings.append({
                            'file_id': file_id,
                            'call_type': CallType.IN,
                            'caller': row.caller,
                            'callee': row.callee,
                            'begin_time': row.begin_time,
                            'end_time': row.end_time,
                            'duration': int(row.conversation_time),
                            'caller_gateway': row.caller_gateway,
                            'called_gateway': row.called_gateway,
                            'call_date': row.call_date,
                            'call_hour': row.call_hour,
                            'caller_type': row.caller_type,
                            'callee_type': row.callee_type
                        })
                    except Exception as e:
                        logger.error(f"Error processing row in call_in file: {str(e)}")
                        error_count += 1
                        continue
                
                # Bulk insert hiệu quả hơn với mappings
                if mappings:
                    db.bulk_insert_mappings(CallLog, mappings)
                    # Chỉ commit khi đạt đến khoảng commit
                    if (start_idx + batch_size) % commit_interval == 0 or end_idx == total_rows:
                        db.commit()
                
                # Cập nhật tiến trình
                processed_rows = end_idx
                
                # Chỉ log metrics và cập nhật tiến độ định kỳ
                if start_idx % log_interval == 0 or end_idx == total_rows:
                    elapsed_time = time.time() - start_time
                    batch_time = time.time() - batch_start_time
                    processing_rate = processed_rows / elapsed_time if elapsed_time > 0 else 0
                    batch_rate = len(batch_df) / batch_time if batch_time > 0 else 0
                    remaining_rows = total_rows - processed_rows
                    estimated_remaining_time = remaining_rows / processing_rate if processing_rate > 0 else 0
                    
                    logger.info(f"Đã xử lý {processed_rows}/{total_rows} dòng. "
                               f"Tốc độ: {int(batch_rate)} dòng/giây. "
                               f"Thời gian còn lại: {int(estimated_remaining_time)}s")
                    
                    # Cập nhật trạng thái cho Celery task
                    if task:
                        task.update_state(
                            state='PROGRESS',
                            meta={
                                'processed_rows': processed_rows,
                                'total_rows': total_rows,
                                'estimated_remaining_time': int(estimated_remaining_time)
                            }
                        )
                    
                    # Cập nhật database với session mới
                    with SessionLocal() as progress_db:
                        file_record = progress_db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
                        if file_record:
                            file_record.processed_rows = processed_rows
                            file_record.error_count = error_count
                            progress_db.commit()
                
                # Giải phóng bộ nhớ
                if start_idx % (batch_size * 4) == 0 and start_idx > 0:
                    gc.collect()
                        
            except Exception as e:
                logger.error(f"Error processing batch {start_idx}-{end_idx}: {str(e)}")
                db.rollback()  # Rollback nếu có lỗi
                raise
        
        # Đảm bảo commit cuối cùng
        db.commit()
        
        # Cập nhật thời gian xử lý
        total_time = time.time() - start_time
        logger.info(f"Hoàn thành xử lý file {file_path}. "
                   f"Tổng thời gian: {total_time:.2f}s. "
                   f"Tốc độ trung bình: {total_rows/total_time:.2f} dòng/giây")
        
        # Giải phóng bộ nhớ
        del df
        gc.collect()
        
        # Cập nhật thời gian xử lý vào database
        with SessionLocal() as final_db:
            file_record = final_db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
            if file_record:
                file_record.processing_time = total_time
                final_db.commit()
        
        return {
            "processed_rows": processed_rows,
            "error_count": error_count,
            "processing_time": total_time
        }
    finally:
        if db and db.is_active:
            db.close() 

def process_chunk(chunk_df, file_id, db):
    """
    Xử lý một chunk dữ liệu và thêm vào database
    """
    mappings = []
    error_count = 0
    
    # Xử lý dữ liệu
    for row in chunk_df.itertuples(index=False):
        try:
            # Phân loại số điện thoại
            caller_type = classify_phone_number_fast(row.ani)
            callee_type = classify_phone_number_fast(row.destination)
            
            # Tạo mapping
            mapping = {
                'file_id': file_id,
                'call_type': CallType.OUT,
                'caller': row.ani,
                'callee': row.destination,
                'begin_time': row.startdate,
                'end_time': row.enddate,
                'duration': int(row.duration),
                'caller_gateway': row.caller_gateway,
                'called_gateway': row.tername,
                'call_date': row.call_date,
                'call_hour': row.call_hour,
                'caller_type': caller_type,
                'callee_type': callee_type
            }
            
            # Thêm trường area_prefix nếu có
            if hasattr(row, 'area_prefix'):
                mapping['area_prefix'] = row.area_prefix
            
            # Thêm trường area_name nếu có
            if hasattr(row, 'area_name'):
                mapping['area_name'] = row.area_name
            
            mappings.append(mapping)
        except Exception as e:
            error_count += 1
            continue
    
    # Bulk insert
    if mappings:
        db.bulk_insert_mappings(CallLog, mappings)
    
    return len(mappings), error_count

def process_call_out_file(file_path: str, file_id: int, task=None, db: Session = None):
    """
    Xử lý file call out (gọi ra) - phiên bản tối ưu với hỗ trợ area_prefix và area_name
    """
    if db is None:
        db = SessionLocal()
        db = optimize_db_connection(db)
    
    processed_rows = 0
    error_count = 0
    start_time = time.time()
    
    try:
        # Kiểm tra kích thước file để quyết định chiến lược đọc
        file_size = os.path.getsize(file_path)
        use_parallel = file_size > 100 * 1024 * 1024 and mp.cpu_count() > 1
        
        logger.info(f"Bắt đầu đọc file {file_path} (Kích thước: {file_size / (1024*1024):.1f}MB)")
        
        # Xác định engine đọc file dựa trên đuôi file
        file_ext = os.path.splitext(file_path)[1].lower()
        engine = 'pyxlsb' if file_ext == '.xlsb' else 'openpyxl'
        
        # Đọc file dựa trên chiến lược đã chọn
        if use_parallel:
            logger.info(f"Sử dụng parallel reading với {min(mp.cpu_count(), 2)} processes")
            
            # Đọc header trước để xác định cột
            header_df = pd.read_excel(file_path, engine=engine, nrows=1)
            headers = list(header_df.columns)
            logger.info(f"Headers trong file: {headers}")
            
            # Kiểm tra nếu có cột mới
            has_area_prefix = 'Area prefix' in headers
            has_area_name = 'Area name' in headers
            
            # Đọc tổng số dòng
            row_count_df = pd.read_excel(file_path, engine=engine, usecols=[0])
            total_rows = len(row_count_df)
            del row_count_df  # Giải phóng bộ nhớ
            
            # Chuẩn bị các tham số dtype cho việc đọc file
            dtype_params = {
                'ANI': str,
                'DESTINATION': str,
                'DURATION': 'int32',
                'Caller gateway': str,
                'TERNAME': str
            }
            
            # Thêm các cột mới vào dtype nếu có
            if has_area_prefix:
                dtype_params['Area prefix'] = str
            if has_area_name:
                dtype_params['Area name'] = str
            
            # Chuẩn bị danh sách cột cần đọc
            usecols = ['ANI', 'DESTINATION', 'STARTDATE', 'ENDDATE', 
                      'DURATION', 'Caller gateway', 'TERNAME']
            
            # Thêm các cột mới vào usecols nếu có
            if has_area_prefix:
                usecols.append('Area prefix')
            if has_area_name:
                usecols.append('Area name')
            
            # Đọc file theo chunks song song với multiprocessing
            chunk_size = 50000
            num_chunks = (total_rows + chunk_size - 1) // chunk_size
            
            # Sử dụng multiprocessing để đọc các chunks song song
            num_processes = min(mp.cpu_count(), 2)  # Giới hạn theo số cores
            
            with mp.Pool(processes=num_processes) as pool:
                results = pool.map(
                    partial(
                        pd.read_excel,
                        io=file_path,
                        engine=engine,
                        dtype=dtype_params,
                        usecols=usecols
                    ),
                    [
                        {'skiprows': i * chunk_size + 1 if i > 0 else 1, 'nrows': chunk_size}
                        for i in range(num_chunks)
                    ]
                )
                
                # Kết hợp kết quả
                df = pd.concat(results, ignore_index=True)
                
                # Giải phóng bộ nhớ
                del results
                gc.collect()
        else:
            # Đọc file thông thường nếu không song song
            df = pd.read_excel(
                file_path,
                engine=engine,
                dtype={
                    'ANI': str,
                    'DESTINATION': str,
                    'DURATION': 'int32',
                    'Caller gateway': str,
                    'TERNAME': str,
                    'Area prefix': str,
                    'Area name': str
                }
            )
        
        total_rows = len(df)
        logger.info(f"Đọc xong file {file_path} với {total_rows} dòng")
        
        # Kiểm tra các cột trong df
        logger.info(f"Các cột trong DataFrame: {list(df.columns)}")
        
        # Kiểm tra và log số lượng giá trị null trong mỗi cột
        null_counts = df.isnull().sum()
        logger.info(f"Số lượng giá trị null trong mỗi cột:\n{null_counts}")
        
        # Cập nhật total_rows vào database
        with SessionLocal() as progress_db:
            file_record = progress_db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
            if file_record:
                file_record.total_rows = total_rows
                progress_db.commit()
        
        # Chuẩn hóa tên cột
        columns_mapping = {
            'ANI': 'ani',
            'DESTINATION': 'destination',
            'STARTDATE': 'startdate',
            'ENDDATE': 'enddate',
            'DURATION': 'duration',
            'Caller gateway': 'caller_gateway',
            'TERNAME': 'tername',
            'Area prefix': 'area_prefix',
            'Area name': 'area_name'
        }
        
        # Áp dụng mapping chỉ cho các cột có trong DataFrame
        rename_dict = {col: columns_mapping[col] for col in df.columns if col in columns_mapping}
        df = df.rename(columns=rename_dict)
        
        # Xử lý dữ liệu cơ bản
        logger.info("Xử lý dữ liệu")
        
        # Kiểm tra và chuyển đổi kiểu dữ liệu datetime
        df['startdate'] = pd.to_datetime(df['startdate'])
        df['enddate'] = pd.to_datetime(df['enddate'])
        
        # Trích xuất ngày và giờ
        df['call_date'] = df['startdate'].dt.date
        df['call_hour'] = df['startdate'].dt.hour
        
        # Xử lý các giá trị NaN và chuyển đổi string
        df['ani'] = df['ani'].fillna('').astype(str)
        df['destination'] = df['destination'].fillna('').astype(str)
        df['caller_gateway'] = df['caller_gateway'].fillna('').astype(str)
        df['tername'] = df['tername'].fillna('').astype(str)
        
        # Xử lý các trường area nếu có
        if 'area_prefix' in df.columns:
            df['area_prefix'] = df['area_prefix'].fillna('').astype(str)
        if 'area_name' in df.columns:
            df['area_name'] = df['area_name'].fillna('').astype(str)
        
        # Chuẩn hóa số điện thoại
        logger.info("Chuẩn hóa số điện thoại")
        df['ani'] = df['ani'].apply(normalize_phone_number_fast)
        df['destination'] = df['destination'].apply(normalize_phone_number_fast)
        
        # Tối ưu hóa bulk insert với batch lớn
        batch_size = 100000
        
        # Giảm tần suất log và commit
        log_interval = 200000
        commit_interval = batch_size
        
        for start_idx in range(0, total_rows, batch_size):
            batch_start_time = time.time()
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = df.iloc[start_idx:end_idx]
            
            # Chỉ log khi cần thiết
            if start_idx % log_interval == 0 or start_idx == 0:
                logger.info(f"Xử lý batch {start_idx}-{end_idx} / {total_rows}")
            
            try:
                # Xử lý chunk và insert vào database
                rows_processed, chunk_errors = process_chunk(batch_df, file_id, db)
                
                # Cập nhật số liệu
                processed_rows += rows_processed
                error_count += chunk_errors
                
                # Chỉ commit khi đạt đến khoảng commit
                if (start_idx + batch_size) % commit_interval == 0 or end_idx == total_rows:
                    db.commit()
                
                # Chỉ log và cập nhật tiến độ định kỳ
                if start_idx % log_interval == 0 or end_idx == total_rows:
                    elapsed_time = time.time() - start_time
                    batch_time = time.time() - batch_start_time
                    processing_rate = processed_rows / elapsed_time if elapsed_time > 0 else 0
                    batch_rate = len(batch_df) / batch_time if batch_time > 0 else 0
                    remaining_rows = total_rows - end_idx
                    estimated_remaining_time = remaining_rows / processing_rate if processing_rate > 0 else 0
                    
                    logger.info(f"Đã xử lý {end_idx}/{total_rows} dòng. "
                               f"Tốc độ: {int(batch_rate)} dòng/giây. "
                               f"Thời gian còn lại: {int(estimated_remaining_time)}s")
                    
                    # Cập nhật trạng thái cho Celery task
                    if task:
                        task.update_state(
                            state='PROGRESS',
                            meta={
                                'processed_rows': processed_rows,
                                'total_rows': total_rows,
                                'estimated_remaining_time': int(estimated_remaining_time)
                            }
                        )
                    
                    # Cập nhật database với session mới
                    with SessionLocal() as progress_db:
                        file_record = progress_db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
                        if file_record:
                            file_record.processed_rows = processed_rows
                            file_record.error_count = error_count
                            progress_db.commit()
                
                # Giải phóng bộ nhớ
                if start_idx % (batch_size * 4) == 0 and start_idx > 0:
                    gc.collect()
                    
            except Exception as e:
                logger.error(f"Error processing batch {start_idx}-{end_idx}: {str(e)}")
                db.rollback()
                raise
        
        # Đảm bảo commit cuối cùng
        db.commit()
        
        # Cập nhật thời gian xử lý
        total_time = time.time() - start_time
        logger.info(f"Hoàn thành xử lý file {file_path}. "
                   f"Tổng thời gian: {total_time:.2f}s. "
                   f"Tốc độ trung bình: {processed_rows/total_time:.2f} dòng/giây.")
        
        # Giải phóng bộ nhớ
        del df
        gc.collect()
        
        # Cập nhật thời gian xử lý vào database
        with SessionLocal() as final_db:
            file_record = final_db.query(CallLogFile).filter(CallLogFile.id == file_id).first()
            if file_record:
                file_record.processing_time = total_time
                final_db.commit()
        
        return {
            "processed_rows": processed_rows,
            "error_count": error_count,
            "processing_time": total_time
        }
    finally:
        if db and db.is_active:
            db.close() 