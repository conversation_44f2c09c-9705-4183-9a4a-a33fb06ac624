"""
Migration để thêm bảng reconciliation_templates và thêm các trường cần thiết
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSON

# revision identifiers, used by Alembic.
revision = 'add_reconciliation_templates'
down_revision = None  # Đặt thành giá trị phù hợp
branch_labels = None
depends_on = None

def upgrade():
    # Kiểm tra xem bảng cũ đã tồn tại chưa
    if op.get_bind().execute("SELECT to_regclass('public.doi_soat_template')").scalar():
        print("Renaming existing table doi_soat_template to reconciliation_templates")
        
        # Đổi tên bảng
        op.rename_table('doi_soat_template', 'reconciliation_templates')
        
        # Thêm các cột mới
        op.add_column('reconciliation_templates', 
                     sa.Column('file_name', sa.String(255), nullable=True))
        op.add_column('reconciliation_templates', 
                     sa.Column('status', sa.String(20), nullable=True, server_default='pending'))
        op.add_column('reconciliation_templates', 
                     sa.Column('task_id', sa.String(255), nullable=True))
        op.add_column('reconciliation_templates', 
                     sa.Column('processed_data', JSON, nullable=True))
        op.add_column('reconciliation_templates', 
                     sa.Column('error_message', sa.Text(), nullable=True))
        op.add_column('reconciliation_templates', 
                     sa.Column('processing_time', sa.Float(), nullable=True))
                     
        # Cập nhật dữ liệu cho cột file_name
        op.execute("""
            UPDATE reconciliation_templates 
            SET file_name = SUBSTRING(file_path FROM '[^/]*$')
            WHERE file_name IS NULL
        """)
        
        # Đặt NOT NULL constraint cho cột file_name
        op.alter_column('reconciliation_templates', 'file_name', nullable=False)
        op.alter_column('reconciliation_templates', 'status', nullable=False)
                     
    else:
        # Tạo bảng mới
        print("Creating new table reconciliation_templates")
        op.create_table(
            'reconciliation_templates',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('name', sa.String(255), nullable=False),
            sa.Column('description', sa.Text(), nullable=True),
            sa.Column('template_type', sa.String(20), nullable=False),
            sa.Column('file_path', sa.String(255), nullable=False),
            sa.Column('file_name', sa.String(255), nullable=False),
            sa.Column('uploaded_by', sa.Integer(), nullable=False),
            sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
            sa.Column('status', sa.String(20), nullable=False, server_default='pending'),
            sa.Column('task_id', sa.String(255), nullable=True),
            sa.Column('processed_data', JSON, nullable=True),
            sa.Column('error_message', sa.Text(), nullable=True),
            sa.Column('processing_time', sa.Float(), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.func.now()),
            sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.func.now(), 
                      onupdate=sa.func.now()),
            sa.ForeignKeyConstraint(['uploaded_by'], ['users.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
        op.create_index(op.f('ix_reconciliation_templates_id'), 'reconciliation_templates', ['id'], unique=False)

def downgrade():
    # Nếu nâng cấp từ bảng cũ, quay lại bảng cũ
    if op.get_bind().execute("SELECT to_regclass('public.reconciliation_templates')").scalar():
        # Xóa các cột mới
        op.drop_column('reconciliation_templates', 'file_name')
        op.drop_column('reconciliation_templates', 'status')
        op.drop_column('reconciliation_templates', 'task_id')
        op.drop_column('reconciliation_templates', 'processed_data')
        op.drop_column('reconciliation_templates', 'error_message')
        op.drop_column('reconciliation_templates', 'processing_time')
        
        # Đổi tên bảng lại
        op.rename_table('reconciliation_templates', 'doi_soat_template')
    else:
        # Xóa bảng nếu tạo mới
        op.drop_index(op.f('ix_reconciliation_templates_id'), table_name='reconciliation_templates')
        op.drop_table('reconciliation_templates') 