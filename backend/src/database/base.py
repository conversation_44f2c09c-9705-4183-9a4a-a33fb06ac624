from typing import Any
from sqlalchemy.orm import DeclarativeBase, declared_attr

class Base(DeclarativeBase):
    """Base class for all database models"""
    
    @declared_attr.directive
    def __tablename__(cls) -> str:
        """Generate __tablename__ automatically from class name"""
        return cls.__name__.lower()
        
    def dict(self) -> dict[str, Any]:
        """Convert model instance to dictionary"""
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}

# Import all models here for Alembic to discover them
from ..models.user import User  # noqa
from ..models.partner import Partner, PartnerType  # noqa
from ..models.enums import ServiceType  # noqa
from ..models.volume_range import VolumeRange, VolumeUnit  # noqa
from ..models.pricing import RevenuePricing, CostPricing, BillingMethod  # noqa 
from ..models.call_log import CallLogFile, CallLog  # noqa
from ..models.dsc import <PERSON>i<PERSON><PERSON><PERSON>uoc, DauSoDichVuCuoc, TongKetCuoc  # noqa
from ..models.dscd import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>u<PERSON>o<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TongKet  # noqa
from ..models.dst_1800_1900 import (
    DoiSoat1800_1900, NhomDichVu1800_1900, ChiTietDichVu1800_1900,  # noqa
    TongKet1800_1900, ThanhToan1800_1900  # noqa
)
from ..models.template import DoiSoatTemplate  # noqa