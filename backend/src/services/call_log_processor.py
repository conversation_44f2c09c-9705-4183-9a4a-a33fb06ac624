from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from ..models.reconciliation_period import ReconciliationPeriod
from ..models.template import ReconciliationTemplate, TemplateType
from ..models.dscd import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Dau<PERSON>oDich<PERSON>u, <PERSON>uoc<PERSON><PERSON>, TongKet
from ..models.dst_1800_1900 import DoiSoat1800_1900, NhomDichVu1800_1900, ChiTietDichVu1800_1900, TongKet1800_1900, ThanhToan1800_1900
from ..models.dsc import DoiSoatCuoc, DauSoDichVuCuoc, TongKetCuoc
from ..utils.dscd_processor import process_dscd_file
from ..utils.dsc_processor import process_dsc_file
from ..utils.dst_1800_1900_processor import process_dst_1800_1900_file

class CallLogProcessor:
    """
    Service xử lý call logs và tạo đối soát thực tế từ mẫu đối soát
    """
    def __init__(self, db: Session):
        self.db = db
    
    def process_call_logs_for_period(self, period_id: int) -> Dict[str, Any]:
        """
        Xử lý call logs cho một kỳ đối soát
        
        Args:
            period_id: ID của kỳ đối soát
            
        Returns:
            Dictionary chứa thông tin kết quả xử lý
        """
        # Lấy thông tin kỳ đối soát
        period = self.db.query(ReconciliationPeriod).filter(
            ReconciliationPeriod.id == period_id
        ).first()
        
        if not period:
            return {"success": False, "message": "Kỳ đối soát không tồn tại"}
        
        # Lấy thông tin template
        template = self.db.query(ReconciliationTemplate).filter(
            ReconciliationTemplate.id == period.template_id
        ).first()
        
        if not template:
            return {"success": False, "message": "Mẫu đối soát không tồn tại"}
        
        # Cập nhật trạng thái
        period.status = "processing"
        self.db.commit()
        
        try:
            # Xử lý theo loại mẫu
            if template.template_type == TemplateType.DSC:
                result = self._process_dsc_call_logs(period, template)
            elif template.template_type == TemplateType.DSCD:
                result = self._process_dscd_call_logs(period, template)
            elif template.template_type == TemplateType.DST_1800_1900:
                result = self._process_dst_call_logs(period, template)
            else:
                return {"success": False, "message": f"Loại mẫu không được hỗ trợ: {template.template_type}"}
            
            if not result["success"]:
                # Cập nhật trạng thái lỗi
                period.status = "failed"
                self.db.commit()
                return result
            
            # Cập nhật trạng thái thành công
            period.status = "completed"
            self.db.commit()
            
            return {
                "success": True,
                "message": f"Xử lý call logs cho kỳ {period.period_code} thành công",
                "data": result.get("data")
            }
            
        except Exception as e:
            # Cập nhật trạng thái lỗi
            period.status = "failed"
            self.db.commit()
            
            return {
                "success": False,
                "message": f"Lỗi khi xử lý call logs: {str(e)}"
            }
    
    def _process_dsc_call_logs(self, period: ReconciliationPeriod, template: ReconciliationTemplate) -> Dict[str, Any]:
        """
        Xử lý call logs cho đối soát cước
        
        Args:
            period: Kỳ đối soát
            template: Mẫu đối soát
            
        Returns:
            Dictionary chứa thông tin kết quả xử lý
        """
        try:
            # 1. Lấy mẫu đối soát cước từ template
            template_data = template.processed_data
            if not template_data:
                return {"success": False, "message": "Mẫu đối soát chưa được xử lý"}
            
            # 2. Tạo đối soát cước mới
            doi_soat = DoiSoatCuoc(
                thang_doi_soat=period.period_code,
                tu_mang="HTC",  # Có thể lấy từ template
                den_doi_tac=template_data.get("den_doi_tac", "PARTNER"),
                period_id=period.id,
                is_template_data=False,
                file_name=f"Generated_from_template_{template.id}_{period.period_code}"
            )
            self.db.add(doi_soat)
            self.db.flush()
            
            # 3. Lấy dữ liệu call logs từ CSDL (demo)
            # TODO: Thay thế bằng dữ liệu thực tế
            call_log_data = self._get_demo_call_log_data(period, template_data)
            
            # 4. Tạo DauSoDichVuCuoc từ dữ liệu mẫu và call logs
            template_dau_so = template_data.get("du_lieu", [])
            for idx, dau_so_template in enumerate(template_dau_so):
                # Tìm dữ liệu call logs tương ứng cho đầu số này
                call_data = call_log_data.get(dau_so_template.get("dau_so"), {})
                
                # Tạo dữ liệu đầu số mới
                dau_so = DauSoDichVuCuoc(
                    doi_soat_id=doi_soat.id,
                    stt=idx + 1,
                    dau_so=dau_so_template.get("dau_so", f"Unknown-{idx}"),
                    vnm_san_luong=call_data.get("vnm_san_luong", 0),
                    vnm_ty_le_cp=dau_so_template.get("vnm_ty_le_cp", 0),
                    vnm_thanh_tien=call_data.get("vnm_san_luong", 0) * dau_so_template.get("vnm_ty_le_cp", 0),
                    viettel_san_luong=call_data.get("viettel_san_luong", 0),
                    viettel_ty_le_cp=dau_so_template.get("viettel_ty_le_cp", 0),
                    viettel_thanh_tien=call_data.get("viettel_san_luong", 0) * dau_so_template.get("viettel_ty_le_cp", 0),
                    vnpt_san_luong=call_data.get("vnpt_san_luong", 0),
                    vnpt_ty_le_cp=dau_so_template.get("vnpt_ty_le_cp", 0),
                    vnpt_thanh_tien=call_data.get("vnpt_san_luong", 0) * dau_so_template.get("vnpt_ty_le_cp", 0),
                    vms_san_luong=call_data.get("vms_san_luong", 0),
                    vms_ty_le_cp=dau_so_template.get("vms_ty_le_cp", 0),
                    vms_thanh_tien=call_data.get("vms_san_luong", 0) * dau_so_template.get("vms_ty_le_cp", 0),
                    khac_san_luong=call_data.get("khac_san_luong", 0),
                    khac_ty_le_cp=dau_so_template.get("khac_ty_le_cp", 0),
                    khac_thanh_tien=call_data.get("khac_san_luong", 0) * dau_so_template.get("khac_ty_le_cp", 0),
                )
                # Tính tổng thanh toán
                dau_so.tong_thanh_toan = (
                    dau_so.vnm_thanh_tien + 
                    dau_so.viettel_thanh_tien + 
                    dau_so.vnpt_thanh_tien + 
                    dau_so.vms_thanh_tien + 
                    dau_so.khac_thanh_tien
                )
                self.db.add(dau_so)
            
            # 5. Tạo TongKetCuoc
            tong_ket_template = template_data.get("tong_ket", {})
            
            # Tính tổng tiền dịch vụ từ các đầu số
            cong_tien_dich_vu = sum(dau_so.tong_thanh_toan for dau_so in doi_soat.du_lieu)
            tien_thue_gtgt = cong_tien_dich_vu * 0.1  # 10% VAT
            tong_cong_tien = cong_tien_dich_vu + tien_thue_gtgt
            
            tong_ket = TongKetCuoc(
                doi_soat_id=doi_soat.id,
                cong_tien_dich_vu=cong_tien_dich_vu,
                tien_thue_gtgt=tien_thue_gtgt,
                tong_cong_tien=tong_cong_tien
            )
            self.db.add(tong_ket)
            
            # 6. Commit các thay đổi
            self.db.commit()
            
            return {
                "success": True,
                "message": "Xử lý đối soát cước thành công",
                "data": {
                    "doi_soat_id": doi_soat.id,
                    "tong_cong_tien": tong_cong_tien
                }
            }
            
        except Exception as e:
            self.db.rollback()
            return {"success": False, "message": f"Lỗi khi xử lý đối soát cước: {str(e)}"}
    
    def _process_dscd_call_logs(self, period: ReconciliationPeriod, template: ReconciliationTemplate) -> Dict[str, Any]:
        """
        Xử lý call logs cho đối soát cố định
        
        Args:
            period: Kỳ đối soát
            template: Mẫu đối soát
            
        Returns:
            Dictionary chứa thông tin kết quả xử lý
        """
        try:
            # 1. Lấy mẫu đối soát cố định từ template
            template_data = template.processed_data
            if not template_data:
                return {"success": False, "message": "Mẫu đối soát chưa được xử lý"}
            
            # 2. Tạo đối soát cố định mới
            doi_soat = DoiSoatCoDinh(
                thang_doi_soat=period.period_code,
                hop_dong_so=template_data.get("hop_dong_so", None),
                tu_mang=template_data.get("tu_mang", "HTC"),
                den_doi_tac=template_data.get("den_doi_tac", "Partner"),
                period_id=period.id,
                template_id=template.id,
                partner_id=template.partner_id,
                is_template_data=False,
                file_name=f"Generated_from_template_{template.id}_{period.period_code}"
            )
            self.db.add(doi_soat)
            self.db.flush()
            
            # 3. Lấy dữ liệu call logs (hiện tại dùng dữ liệu demo)
            # TODO: Thay thế bằng dữ liệu thực tế
            call_log_data = self._get_demo_call_log_for_dscd(period, template_data)
            
            # 4. Tạo DauSoDichVu cho từng đầu số
            template_dau_so = template_data.get("du_lieu", [])
            for idx, dau_so_template in enumerate(template_dau_so):
                # Tạo đối tượng DauSoDichVu
                dau_so = DauSoDichVu(
                    doi_soat_id=doi_soat.id,
                    stt=idx + 1,
                    dau_so=dau_so_template.get("dau_so", f"Unknown-{idx}"),
                    cuoc_thu_khach=dau_so_template.get("cuoc_thu_khach", 0),
                    cuoc_tra_htc=dau_so_template.get("cuoc_tra_htc", 0)
                )
                self.db.add(dau_so)
                self.db.flush()
                
                # Lấy dữ liệu call logs cho đầu số này
                dau_so_logs = call_log_data.get(dau_so_template.get("dau_so", ""), {})
                
                # Tạo các đối tượng CuocGoi
                # Co dinh noi hat
                self.db.add(CuocGoi(
                    dau_so_id=dau_so.id,
                    loai_cuoc="co_dinh_noi_hat",
                    thoi_gian_goi=dau_so_logs.get("co_dinh_noi_hat_thoi_gian", 0),
                    cuoc=dau_so_logs.get("co_dinh_noi_hat_cuoc", 0)
                ))
                
                # Co dinh lien tinh
                self.db.add(CuocGoi(
                    dau_so_id=dau_so.id,
                    loai_cuoc="co_dinh_lien_tinh",
                    thoi_gian_goi=dau_so_logs.get("co_dinh_lien_tinh_thoi_gian", 0),
                    cuoc=dau_so_logs.get("co_dinh_lien_tinh_cuoc", 0)
                ))
                
                # Di dong
                self.db.add(CuocGoi(
                    dau_so_id=dau_so.id,
                    loai_cuoc="di_dong",
                    thoi_gian_goi=dau_so_logs.get("di_dong_thoi_gian", 0),
                    cuoc=dau_so_logs.get("di_dong_cuoc", 0)
                ))
                
                # Cuoc 1900
                self.db.add(CuocGoi(
                    dau_so_id=dau_so.id,
                    loai_cuoc="cuoc_1900",
                    thoi_gian_goi=dau_so_logs.get("cuoc_1900_thoi_gian", 0),
                    cuoc=dau_so_logs.get("cuoc_1900_cuoc", 0)
                ))
                
                # Quoc te
                self.db.add(CuocGoi(
                    dau_so_id=dau_so.id,
                    loai_cuoc="quoc_te",
                    thoi_gian_goi=dau_so_logs.get("quoc_te_thoi_gian", 0),
                    cuoc=dau_so_logs.get("quoc_te_cuoc", 0)
                ))
                
                # Cuoc thue bao
                self.db.add(CuocThueBao(
                    dau_so_id=dau_so.id,
                    thue_bao_thang=dau_so_logs.get("thue_bao_thang", 0),
                    cam_ket_thang=dau_so_logs.get("cam_ket_thang", 0),
                    tra_truoc_thang=dau_so_logs.get("tra_truoc_thang", 0)
                ))
            
            # 5. Tạo tổng kết
            # Tính tổng tiền từ dữ liệu call logs
            tong_tien_dich_vu = sum(dau_so_log.get("tong_cuoc", 0) for dau_so_log in call_log_data.values())
            tien_thue_gtgt = tong_tien_dich_vu * 0.1  # Giả sử thuế 10%
            tong_cong_tien = tong_tien_dich_vu + tien_thue_gtgt
            
            tong_ket = TongKet(
                doi_soat_id=doi_soat.id,
                cong_tien_dich_vu=tong_tien_dich_vu,
                tien_thue_gtgt=tien_thue_gtgt,
                tong_cong_tien=tong_cong_tien
            )
            self.db.add(tong_ket)
            
            # Lưu vào DB
            self.db.commit()
            
            return {
                "success": True, 
                "message": f"Đã xử lý đối soát cố định cho kỳ {period.period_code}",
                "data": {
                    "doi_soat_id": doi_soat.id,
                    "tong_cong_tien": tong_cong_tien
                }
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Lỗi khi xử lý đối soát cố định: {str(e)}")
            return {"success": False, "message": f"Lỗi khi xử lý đối soát cố định: {str(e)}"}
    
    def _process_dst_call_logs(self, period: ReconciliationPeriod, template: ReconciliationTemplate) -> Dict[str, Any]:
        """
        Xử lý call logs cho đối soát 1800/1900
        
        Args:
            period: Kỳ đối soát
            template: Mẫu đối soát
            
        Returns:
            Dictionary chứa thông tin kết quả xử lý
        """
        # TODO: Implement xử lý call logs cho DST
        return {"success": True, "message": "Đang phát triển"}
    
    def _get_demo_call_log_data(self, period: ReconciliationPeriod, template_data: Dict) -> Dict[str, Any]:
        """
        Tạo dữ liệu call logs demo cho mục đích phát triển
        
        Args:
            period: Kỳ đối soát
            template_data: Dữ liệu mẫu đối soát
            
        Returns:
            Dictionary chứa dữ liệu call logs
        """
        import random
        
        call_logs = {}
        template_dau_so = template_data.get("du_lieu", [])
        
        for dau_so_item in template_dau_so:
            dau_so = dau_so_item.get("dau_so", "")
            if not dau_so:
                continue
                
            # Tạo dữ liệu demo ngẫu nhiên cho mỗi đầu số
            call_logs[dau_so] = {
                "vnm_san_luong": random.randint(1000, 5000),
                "viettel_san_luong": random.randint(5000, 20000),
                "vnpt_san_luong": random.randint(3000, 10000),
                "vms_san_luong": random.randint(2000, 8000),
                "khac_san_luong": random.randint(500, 2000)
            }
        
        return call_logs 

    def _get_demo_call_log_for_dscd(self, period: ReconciliationPeriod, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Tạo dữ liệu demo cho xử lý call logs DSCD.
        Trong môi trường production, dữ liệu này sẽ được lấy từ database call logs.
        
        Args:
            period: Kỳ đối soát
            template_data: Dữ liệu mẫu đối soát
            
        Returns:
            Dictionary chứa dữ liệu call logs theo từng đầu số
        """
        import random
        
        result = {}
        
        # Lấy danh sách đầu số từ template
        dau_so_list = [item.get("dau_so") for item in template_data.get("du_lieu", []) if item.get("dau_so")]
        
        # Tạo dữ liệu ngẫu nhiên cho mỗi đầu số
        for dau_so in dau_so_list:
            # Tạo số phút gọi ngẫu nhiên
            co_dinh_noi_hat_time = random.randint(100, 1000)
            co_dinh_lien_tinh_time = random.randint(50, 500)
            di_dong_time = random.randint(200, 2000)
            cuoc_1900_time = random.randint(10, 100)
            quoc_te_time = random.randint(5, 50)
            
            # Tính tiền cuộc gọi (đơn giản hóa, nhân với hệ số giá)
            co_dinh_noi_hat_cuoc = co_dinh_noi_hat_time * 200
            co_dinh_lien_tinh_cuoc = co_dinh_lien_tinh_time * 500
            di_dong_cuoc = di_dong_time * 1000
            cuoc_1900_cuoc = cuoc_1900_time * 2000
            quoc_te_cuoc = quoc_te_time * 5000
            
            # Tiền thuê bao
            thue_bao_thang = random.randint(10000, 50000)
            cam_ket_thang = random.randint(5000, 20000)
            tra_truoc_thang = random.randint(0, 10000)
            
            # Tổng tiền cuộc gọi
            tong_cuoc = (
                co_dinh_noi_hat_cuoc + 
                co_dinh_lien_tinh_cuoc + 
                di_dong_cuoc + 
                cuoc_1900_cuoc + 
                quoc_te_cuoc + 
                thue_bao_thang + 
                cam_ket_thang + 
                tra_truoc_thang
            )
            
            # Thêm vào kết quả
            result[dau_so] = {
                "co_dinh_noi_hat_thoi_gian": co_dinh_noi_hat_time,
                "co_dinh_noi_hat_cuoc": co_dinh_noi_hat_cuoc,
                "co_dinh_lien_tinh_thoi_gian": co_dinh_lien_tinh_time,
                "co_dinh_lien_tinh_cuoc": co_dinh_lien_tinh_cuoc,
                "di_dong_thoi_gian": di_dong_time,
                "di_dong_cuoc": di_dong_cuoc,
                "cuoc_1900_thoi_gian": cuoc_1900_time,
                "cuoc_1900_cuoc": cuoc_1900_cuoc,
                "quoc_te_thoi_gian": quoc_te_time,
                "quoc_te_cuoc": quoc_te_cuoc,
                "thue_bao_thang": thue_bao_thang,
                "cam_ket_thang": cam_ket_thang,
                "tra_truoc_thang": tra_truoc_thang,
                "tong_cuoc": tong_cuoc
            }
            
        return result 