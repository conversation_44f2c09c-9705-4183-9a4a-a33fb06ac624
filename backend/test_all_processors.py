import os
import glob
from test_dsc_processor import test_dsc_processor
from test_dscd_processor import test_dscd_processor
from test_dst_1800_1900_processor import test_dst_1800_1900_processor

def test_all_files_in_directory(directory):
    """Kiểm tra tất cả các file trong thư mục với các processor tương <PERSON>ng"""
    excel_files = glob.glob(os.path.join(directory, "*.xlsx"))
    excel_files.extend(glob.glob(os.path.join(directory, "*.xls")))
    
    print(f"\n{'='*80}")
    print(f"Kiểm tra {len(excel_files)} file trong thư mục: {directory}")
    print(f"{'='*80}\n")
    
    results = {
        "dsc": {"success": 0, "fail": 0, "skip": 0},
        "dscd": {"success": 0, "fail": 0, "skip": 0},
        "dst_1800_1900": {"success": 0, "fail": 0, "skip": 0},
    }
    
    for file_path in excel_files:
        file_name = os.path.basename(file_path)
        print(f"\n{'-'*80}")
        print(f"Đang kiểm tra file: {file_name}")
        print(f"{'-'*80}")
        
        # Kiểm tra với DSC processor
        print("\n1. DSC processor:")
        from src.utils.dsc_processor import is_dsc_file
        if is_dsc_file(file_name):
            try:
                result = test_dsc_processor(file_path)
                if result:
                    results["dsc"]["success"] += 1
                else:
                    results["dsc"]["fail"] += 1
            except Exception as e:
                print(f"❌ Lỗi ngoại lệ: {str(e)}")
                results["dsc"]["fail"] += 1
        else:
            results["dsc"]["skip"] += 1
            print("⏩ Bỏ qua: Không phải file đối soát cước")
        
        # Kiểm tra với DSCD processor
        print("\n2. DSCD processor:")
        from src.utils.dscd_processor import is_dscd_file
        if is_dscd_file(file_name):
            try:
                result = test_dscd_processor(file_path)
                if result:
                    results["dscd"]["success"] += 1
                else:
                    results["dscd"]["fail"] += 1
            except Exception as e:
                print(f"❌ Lỗi ngoại lệ: {str(e)}")
                results["dscd"]["fail"] += 1
        else:
            results["dscd"]["skip"] += 1
            print("⏩ Bỏ qua: Không phải file đối soát cố định")
        
        # Kiểm tra với DST 1800/1900 processor
        print("\n3. DST 1800/1900 processor:")
        from src.utils.dst_1800_1900_processor import is_dst_1800_1900_file
        if is_dst_1800_1900_file(file_name):
            try:
                result = test_dst_1800_1900_processor(file_path)
                if result:
                    results["dst_1800_1900"]["success"] += 1
                else:
                    results["dst_1800_1900"]["fail"] += 1
            except Exception as e:
                print(f"❌ Lỗi ngoại lệ: {str(e)}")
                results["dst_1800_1900"]["fail"] += 1
        else:
            results["dst_1800_1900"]["skip"] += 1
            print("⏩ Bỏ qua: Không phải file đối soát 1800/1900")
    
    # Báo cáo tổng hợp
    print(f"\n{'='*80}")
    print(f"KẾT QUẢ TỔNG HỢP")
    print(f"{'='*80}")
    print(f"Tổng số file kiểm tra: {len(excel_files)}")
    
    print("\nDSC processor:")
    print(f"- Thành công: {results['dsc']['success']}")
    print(f"- Thất bại: {results['dsc']['fail']}")
    print(f"- Bỏ qua: {results['dsc']['skip']}")
    
    print("\nDSCD processor:")
    print(f"- Thành công: {results['dscd']['success']}")
    print(f"- Thất bại: {results['dscd']['fail']}")
    print(f"- Bỏ qua: {results['dscd']['skip']}")
    
    print("\nDST 1800/1900 processor:")
    print(f"- Thành công: {results['dst_1800_1900']['success']}")
    print(f"- Thất bại: {results['dst_1800_1900']['fail']}")
    print(f"- Bỏ qua: {results['dst_1800_1900']['skip']}")

if __name__ == "__main__":
    # Kiểm tra thư mục gốc
    root_dir = "/home/<USER>/Documents/Job/AuditCall/backend/BBDS"
    test_all_files_in_directory(root_dir)
    
    # Kiểm tra các thư mục con
    subdirs = [
        os.path.join(root_dir, "DSC"),
        os.path.join(root_dir, "DSCD"),
        os.path.join(root_dir, "1800_1900"),
        os.path.join(root_dir, "CKN")
    ]
    
    for subdir in subdirs:
        if os.path.exists(subdir):
            test_all_files_in_directory(subdir) 