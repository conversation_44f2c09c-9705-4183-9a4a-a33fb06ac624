[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "audit-call"
version = "1.0.0"
authors = [
    { name = "Your Name", email = "<EMAIL>" },
]
description = "Audit Call System"
requires-python = ">=3.9"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]
namespaces = false 