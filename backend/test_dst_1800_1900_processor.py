import os
import sys
import logging
from src.utils.dst_1800_1900_processor import process_dst_1800_1900_file, is_dst_1800_1900_file
from pprint import pprint

# Cấu hình logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dst_1800_1900_processor(file_path):
    """Test dst_1800_1900 processor với file mẫu"""
    logger.info(f"Kiểm tra file: {file_path}")
    
    # Kiểm tra file có phải là dst_1800_1900 không
    if not is_dst_1800_1900_file(file_path):
        logger.warning(f"File không được nhận dạng là file đối soát 1800/1900: {file_path}")
        print(f"❌ {os.path.basename(file_path)} - KHÔNG phải file đối soát 1800/1900")
        return
    
    try:
        # Xử lý file
        logger.info("Đang xử lý file đối soát 1800/1900...")
        result = process_dst_1800_1900_file(file_path)
        
        # In kết quả
        print(f"✅ {os.path.basename(file_path)} - Xử lý thành công")
        print(f"- Tháng đối soát: {result.thang_doi_soat}")
        print(f"- Từ mạng: {result.tu_mang}")
        print(f"- Đến đối tác: {result.den_doi_tac}")
        print(f"- Loại dịch vụ: {result.loai_dich_vu}")
        print(f"- Hợp đồng số: {result.hop_dong_so}")
        print(f"- Số nhóm dịch vụ: {len(result.nhom_dich_vu)}")
        
        # Thông tin thanh toán
        if result.thanh_toan:
            print(f"- Doanh thu bên A thanh toán: {result.thanh_toan.doanh_thu_ben_a_thanh_toan:,.2f}")
            print(f"- Doanh thu bên B thanh toán: {result.thanh_toan.doanh_thu_ben_b_thanh_toan:,.2f}")
            print(f"- Sau bù trừ: {result.thanh_toan.sau_bu_tru:,.2f}")
            print(f"- Bên thanh toán: {result.thanh_toan.ben_thanh_toan}")
            print(f"- Bên nhận: {result.thanh_toan.ben_nhan}")
        
        # Thông tin về nhóm dịch vụ
        if result.nhom_dich_vu:
            print("\nNhóm dịch vụ:")
            for i, nhom in enumerate(result.nhom_dich_vu[:3]):  # Giới hạn 3 nhóm
                print(f"  {i+1}. {nhom.ten_dich_vu}")
                print(f"     - Số dịch vụ: {len(nhom.chi_tiet)}")
                if nhom.tong_ket:
                    print(f"     - Tổng cộng có VAT: {nhom.tong_ket.tong_cong_co_vat:,.2f}")
                
                # Hiển thị một số chi tiết dịch vụ
                if nhom.chi_tiet:
                    print("     - Chi tiết dịch vụ (3 đầu tiên):")
                    for j, ct in enumerate(nhom.chi_tiet[:3]):
                        print(f"       {j+1}. {ct.so_dich_vu} - Doanh thu A: {ct.doanh_thu_ben_a:,.2f}, Doanh thu B: {ct.doanh_thu_ben_b:,.2f}")
        
        return result
    
    except Exception as e:
        logger.error(f"Lỗi khi xử lý file: {str(e)}")
        print(f"❌ {os.path.basename(file_path)} - LỖI: {str(e)}")
        return None

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        test_dst_1800_1900_processor(file_path)
    else:
        print("Vui lòng cung cấp đường dẫn đến file cần kiểm tra") 