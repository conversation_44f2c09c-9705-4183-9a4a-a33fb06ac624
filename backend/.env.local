# Environment
ENVIRONMENT=development

# API Settings
PROJECT_NAME=AuditCall
API_V1_STR=/api/v1

# Database Settings
POSTGRES_SERVER=db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=hieuda87
POSTGRES_DB=audit_call
POSTGRES_PORT=5432
DATABASE_URL=**************************************/audit_call

# Security Settings
SECRET_KEY=your_development_secret_key
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Settings
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000","http://localhost","http://127.0.0.1:3000","http://127.0.0.1:8000","http://127.0.0.1","http://************"]

# Debug Settings
DB_ECHO_LOG=False

# Redis
REDIS_URL=redis://redis:6379/0

# Celery
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Upload Settings
UPLOAD_DIR=/app/uploads
CALL_LOGS_DIR=/app/uploads/call_logs
TEMPLATES_DIR=/app/uploads/templates
MAX_UPLOAD_SIZE=83886080

# Timezone
TZ=Asia/Ho_Chi_Minh

# First Admin User
FIRST_ADMIN_EMAIL=<EMAIL>
FIRST_ADMIN_PASSWORD=admin123 