import os
import sys
import re
import pandas as pd
import logging
from typing import Dict, List, Any, Optional, Tuple

# Cấu hình logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_service_groups(file_path: str) -> Dict[str, Any]:
    """
    Trích xuất các nhóm dịch vụ từ file Excel đối soát 1800/1900
    
    Args:
        file_path: Đường dẫn đến file đối soát
        
    Returns:
        Dict chứa thông tin các nhóm và chi tiết dịch vụ
    """
    try:
        # Đọc file Excel
        file_name = os.path.basename(file_path)
        logger.info(f"Phân tích file: {file_name}")
        
        xls = pd.ExcelFile(file_path)
        sheet_name = xls.sheet_names[0]  # Lấy sheet đầu tiên
        df = pd.read_excel(xls, sheet_name=sheet_name, header=None)
        
        # <PERSON><PERSON><PERSON> quả trích xuất
        result = {
            "file_name": file_name,
            "hop_dong_so": None,
            "service_groups": [],
            "thanh_toan": None
        }
        
        # 1. Tìm hợp đồng số (nếu có)
        try:
            for i in range(min(10, df.shape[0])):
                cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                if "HỢP ĐỒNG SỐ" in cell_value.upper():
                    hop_dong_match = re.search(r'HỢP ĐỒNG SỐ\s*[.:…]\s*(\S+)', cell_value, re.IGNORECASE)
                    if hop_dong_match:
                        result["hop_dong_so"] = hop_dong_match.group(1)
                        logger.info(f"Tìm thấy hợp đồng số: {result['hop_dong_so']}")
                        break
        except Exception as e:
            logger.warning(f"Lỗi khi tìm số hợp đồng: {str(e)}")
        
        # 2. Tìm các nhóm dịch vụ (I. Dịch vụ 1900xxxx, II. Dịch vụ 1800xxxx, ...)
        service_headers = []
        for i in range(df.shape[0]):
            cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
            if re.search(r'(I{1,3}\.?\s*Dịch\s*[Vv]ụ|Dịch\s*[Vv]ụ)\s*(1[89]00|109)', cell_value, re.IGNORECASE):
                service_name = ""
                if "1900" in cell_value:
                    service_name = "1900xxxx"
                elif "1800" in cell_value:
                    service_name = "1800xxxx"
                elif "109" in cell_value:
                    service_name = "109x"
                
                service_headers.append((i, service_name))
                logger.info(f"Tìm thấy nhóm dịch vụ: {service_name} (dòng {i+1})")
        
        # Nếu không tìm thấy header, thử tìm trong các bảng
        if not service_headers:
            for i in range(df.shape[0]):
                cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                
                # Kiểm tra các mẫu bảng phổ biến
                if "STT" in cell_value.upper() and i+1 < df.shape[0]:
                    next_row = df.iloc[i+1, :]
                    if not pd.isna(next_row[0]) and re.match(r'^\d+$', str(next_row[0])):
                        # Tìm trong các cột có chứa "số dịch vụ"
                        headers_row = df.iloc[i, :]
                        service_name = "Không xác định"  # Mặc định
                        
                        # Kiểm tra một vài giá trị trong cột 1 hoặc 2 để xác định loại dịch vụ
                        has_service_numbers = False
                        for j in range(1, min(5, len(headers_row))):
                            if not pd.isna(headers_row[j]):
                                col_name = str(headers_row[j]).upper()
                                if "SỐ DỊCH VỤ" in col_name or "ĐẦU SỐ" in col_name:
                                    has_service_numbers = True
                                    # Kiểm tra một vài giá trị để xác định loại dịch vụ
                                    for k in range(i+1, min(i+10, df.shape[0])):
                                        if not pd.isna(df.iloc[k, j]):
                                            service_val = str(df.iloc[k, j])
                                            if "1900" in service_val:
                                                service_name = "1900xxxx"
                                                break
                                            elif "1800" in service_val:
                                                service_name = "1800xxxx"
                                                break
                                            elif "109" in service_val:
                                                service_name = "109x"
                                                break
                        
                        if has_service_numbers:
                            service_headers.append((i, service_name))
                            logger.info(f"Tìm thấy bảng dịch vụ: {service_name} (dòng {i+1})")
        
        # Xử lý từng nhóm dịch vụ
        for header_idx, service_name in service_headers:
            try:
                # Tìm bắt đầu của bảng dữ liệu (dòng có STT)
                table_start = None
                for i in range(header_idx, min(header_idx + 10, df.shape[0])):
                    cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                    if "STT" in cell_value.upper():
                        table_start = i
                        break
                
                if table_start is None:
                    logger.warning(f"Không tìm thấy bảng chi tiết cho nhóm dịch vụ {service_name}")
                    continue
                
                # Xác định cột dữ liệu quan trọng trong bảng
                cols = {}
                headers = df.iloc[table_start, :]
                
                for j in range(df.shape[1]):
                    if pd.isna(headers[j]):
                        continue
                        
                    col_name = str(headers[j]).upper()
                    
                    if "STT" in col_name:
                        cols["stt"] = j
                    elif "SỐ DỊCH VỤ" in col_name or "ĐẦU SỐ" in col_name:
                        cols["so_dich_vu"] = j
                
                # Đếm số lượng chi tiết dịch vụ
                row_idx = table_start + 1
                service_numbers = []
                
                while row_idx < df.shape[0]:
                    if "stt" not in cols or pd.isna(df.iloc[row_idx, cols["stt"]]):
                        cell_value = str(df.iloc[row_idx, 0]) if not pd.isna(df.iloc[row_idx, 0]) else ""
                        if "TỔNG CỘNG" in cell_value.upper() or "CỘNG" in cell_value.upper():
                            break
                        row_idx += 1
                        continue
                    
                    try:
                        stt = int(float(df.iloc[row_idx, cols["stt"]])) if not pd.isna(df.iloc[row_idx, cols["stt"]]) else 0
                        if "so_dich_vu" in cols:
                            so_dich_vu = str(df.iloc[row_idx, cols["so_dich_vu"]]) if not pd.isna(df.iloc[row_idx, cols["so_dich_vu"]]) else ""
                            if so_dich_vu:
                                service_numbers.append(so_dich_vu)
                    except Exception:
                        pass
                    
                    row_idx += 1
                
                # Tìm dòng tổng cộng và giá trị VAT
                has_vat = False
                has_total = False
                
                # Tìm trong khoảng 10 dòng sau khi kết thúc bảng
                for i in range(row_idx, min(row_idx + 10, df.shape[0])):
                    cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                    if "TỔNG CỘNG" in cell_value.upper() or "CỘNG" in cell_value.upper():
                        has_total = True
                    elif "VAT" in cell_value.upper() or "THUẾ" in cell_value.upper():
                        has_vat = True
                
                # Thêm vào kết quả
                result["service_groups"].append({
                    "ten_dich_vu": service_name,
                    "so_luong_chi_tiet": len(service_numbers),
                    "service_numbers": service_numbers[:10] if len(service_numbers) > 10 else service_numbers,  # Chỉ lấy tối đa 10 số
                    "has_total": has_total,
                    "has_vat": has_vat
                })
            
            except Exception as e:
                logger.error(f"Lỗi khi xử lý nhóm dịch vụ {service_name}: {str(e)}")
        
        # 3. Tìm phần thanh toán
        thanh_toan_found = False
        for i in range(df.shape[0]):
            cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
            if "THANH TOÁN" in cell_value.upper() or "III" in cell_value.upper():
                thanh_toan_found = True
                logger.info(f"Tìm thấy phần thanh toán (dòng {i+1})")
                break
        
        # Tìm thông tin sau bù trừ
        has_bu_tru = False
        for i in range(df.shape[0]):
            cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
            if "SAU BÙ TRỪ" in cell_value.upper() and "PHẢI THANH TOÁN CHO" in cell_value.upper():
                has_bu_tru = True
                logger.info(f"Tìm thấy thông tin sau bù trừ (dòng {i+1})")
                break
        
        result["thanh_toan"] = {
            "found": thanh_toan_found,
            "has_bu_tru": has_bu_tru
        }
        
        return result
        
    except Exception as e:
        logger.error(f"Lỗi khi phân tích file {file_path}: {str(e)}")
        return {
            "file_name": os.path.basename(file_path),
            "error": str(e),
            "service_groups": [],
            "thanh_toan": None
        }

def test_file_content(directory_path: str) -> None:
    """
    Kiểm tra nội dung các file đối soát 1800/1900 trong thư mục
    
    Args:
        directory_path: Đường dẫn đến thư mục chứa các file cần kiểm tra
    """
    if not os.path.exists(directory_path):
        logger.error(f"Thư mục {directory_path} không tồn tại!")
        return
    
    # Danh sách các file Excel
    excel_files = []
    
    # Duyệt qua tất cả các file trong thư mục
    for root, _, files in os.walk(directory_path):
        for filename in files:
            if filename.endswith('.xlsx') or filename.endswith('.xls'):
                # Bỏ qua các file lock
                if filename.startswith('.~lock.'):
                    continue
                    
                file_path = os.path.join(root, filename)
                excel_files.append(file_path)
    
    logger.info(f"Tìm thấy {len(excel_files)} file Excel")
    
    # Kiểm tra từng file
    results = []
    
    for file_path in excel_files:
        # Trích xuất thông tin từ file
        result = extract_service_groups(file_path)
        results.append(result)
    
    # Tóm tắt kết quả
    logger.info("\n====== TỔNG KẾT PHÂN TÍCH ======")
    logger.info(f"Tổng số file đã phân tích: {len(results)}")
    
    valid_files = 0
    for result in results:
        # Kiểm tra nếu file có cấu trúc hợp lệ của đối soát 1800/1900
        is_valid = (
            len(result.get("service_groups", [])) > 0 and
            result.get("thanh_toan", {}).get("found", False)
        )
        
        if is_valid:
            valid_files += 1
            logger.info(f"\n* File: {result['file_name']} - HỢP LỆ")
        else:
            logger.info(f"\n* File: {result['file_name']} - KHÔNG HỢP LỆ")
        
        # In thông tin các nhóm dịch vụ
        for idx, group in enumerate(result.get("service_groups", []), 1):
            logger.info(f"  - Nhóm {idx}: {group['ten_dich_vu']}")
            logger.info(f"    + Số lượng chi tiết: {group['so_luong_chi_tiet']}")
            if group['service_numbers']:
                logger.info(f"    + Các số dịch vụ: {', '.join(group['service_numbers'])}")
            logger.info(f"    + Có tổng cộng: {'Có' if group['has_total'] else 'Không'}")
            logger.info(f"    + Có VAT: {'Có' if group['has_vat'] else 'Không'}")
        
        # In thông tin thanh toán
        thanh_toan = result.get("thanh_toan", {})
        if thanh_toan:
            logger.info(f"  - Phần thanh toán: {'Có' if thanh_toan.get('found', False) else 'Không'}")
            logger.info(f"  - Thông tin sau bù trừ: {'Có' if thanh_toan.get('has_bu_tru', False) else 'Không'}")
    
    logger.info(f"\nSố file có cấu trúc hợp lệ: {valid_files}/{len(results)}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        directory_path = sys.argv[1]
    else:
        directory_path = "BBDS/1800_1900"  # Mặc định thư mục 1800_1900
    
    logger.info(f"Bắt đầu kiểm tra nội dung các file trong thư mục: {directory_path}")
    test_file_content(directory_path) 