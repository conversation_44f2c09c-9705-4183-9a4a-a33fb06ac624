# backend/tests/crud/test_crud_dscd.py

import pytest
from unittest.mock import MagicMock, call, patch # Import patch for mocking logger
from typing import List, Dict, Any
from datetime import datetime
from decimal import Decimal # Import Decimal for checking values if needed
import logging # Import logging

# G<PERSON><PERSON> sử vị trí tương đối đúng - SỬA LẠI THÀNH IMPORT TUYỆT ĐỐI
from src.crud.crud_dscd import create_dscd_reconciliation
from src.models.dscd import <PERSON>i<PERSON><PERSON><PERSON>o<PERSON><PERSON>h, DauSoDichVu, <PERSON><PERSON><PERSON><PERSON><PERSON>, TongKet
from src.models.enums import ServiceType, DauSoType
from src.models.pricing import BillingMethod # Needed if BillingMethod logic becomes complex
# Import Session if needed for type hinting mock_db
from sqlalchemy.orm import Session

# === Tests for create_dscd_reconciliation ===

# --- Mock Data --- 

# Ví dụ dữ liệu mock
MOCK_KY_DOI_SOAT = "2024-07"
MOCK_TEMPLATE_ID = 1
MOCK_PARTNER_ID = 5

MOCK_SUMMARY_TOTALS = {
    "total_cost_before_vat": 3500.50,
    "total_vat": 350.05,
    "total_cost_after_vat": 3850.55
}

MOCK_PROCESSED_RESULTS = [
    { # Nhóm 1: Cố định nội hạt cho đầu số '0241234xxx'
        'dau_so_display': '0241234xxx',
        'dau_so_id_from_template_data': 101,
        'service_type': ServiceType.FIXED_NOI_HAT.value,
        'total_duration_seconds': 125,
        'total_cost_before_vat': 1500.50,
        'call_count': 5,
        'dau_so_type': DauSoType.PREFIX.value,
        'dau_so_count_template': None,
        'start_num_str': None,
        'end_num_str': None,
        'prefix': '0241234',
        'raw_dau_so_from_template': '0241234...'
    },
    { # Nhóm 2: Di động cho đầu số '0241234xxx'
      # Lưu ý: Cùng dau_so_display nhưng khác service_type
        'dau_so_display': '0241234xxx',
        'dau_so_id_from_template_data': 101, # Cùng ID gốc
        'service_type': ServiceType.MOBILE_NORMAL.value,
        'total_duration_seconds': 240,
        'total_cost_before_vat': 2000.00,
        'call_count': 10,
        'dau_so_type': DauSoType.PREFIX.value,
        'dau_so_count_template': None,
        'start_num_str': None,
        'end_num_str': None,
        'prefix': '0241234',
        'raw_dau_so_from_template': '0241234...'
    },
     { # Nhóm 3: 1800 cho đầu số '18009999' (SINGLE)
        'dau_so_display': '18009999',
        'dau_so_id_from_template_data': 105,
        'service_type': ServiceType.SERVICE_1800_VOICE_CD.value,
        'total_duration_seconds': 60,
        'total_cost_before_vat': 0.0, # Giả sử cước 1800 = 0
        'call_count': 2,
        'dau_so_type': DauSoType.SINGLE.value,
        'dau_so_count_template': 1,
        'start_num_str': None,
        'end_num_str': None,
        'prefix': None,
        'raw_dau_so_from_template': '18009999'
    }
]

# --- Test Cases --- 

def test_create_dscd_reconciliation_success():
    """Test successful creation of DoiSoatCoDinh and related records."""
    mock_db = MagicMock(spec=Session)
    
    # --- Mock the behavior of db.flush() to assign a dummy ID --- 
    added_objects = []
    def capture_add(obj):
        added_objects.append(obj)
        if isinstance(obj, DoiSoatCoDinh):
            obj.id = 999 # Assign a dummy ID
            
    mock_db.add.side_effect = capture_add
    # --- End Mocking flush behavior ---
    
    # Call the function with mock data
    new_doi_soat = create_dscd_reconciliation(
        db=mock_db,
        ky_doi_soat=MOCK_KY_DOI_SOAT,
        template_id=MOCK_TEMPLATE_ID,
        partner_id=MOCK_PARTNER_ID,
        processed_results=MOCK_PROCESSED_RESULTS,
        summary_totals=MOCK_SUMMARY_TOTALS
    )

    # --- Assertions --- 
    
    # 1. Check return value
    assert isinstance(new_doi_soat, DoiSoatCoDinh)
    assert new_doi_soat.id == 999 
    assert new_doi_soat.thang_doi_soat == MOCK_KY_DOI_SOAT
    assert new_doi_soat.template_id == MOCK_TEMPLATE_ID
    assert new_doi_soat.partner_id == MOCK_PARTNER_ID
    assert new_doi_soat.is_template_data == False

    # 2. Check DB method calls
    assert mock_db.add.call_count >= 2 
    added_types = [type(obj) for obj in added_objects if type(obj) in [DoiSoatCoDinh, TongKet]]
    assert DoiSoatCoDinh in added_types
    assert TongKet in added_types
    
    assert mock_db.add_all.call_count == 2
    dau_so_list_added = mock_db.add_all.call_args_list[0][0][0] 
    cuoc_goi_list_added = mock_db.add_all.call_args_list[1][0][0] 
    
    assert len(dau_so_list_added) == len(MOCK_PROCESSED_RESULTS)
    assert all(isinstance(obj, DauSoDichVu) for obj in dau_so_list_added)
    
    assert len(cuoc_goi_list_added) == len(MOCK_PROCESSED_RESULTS)
    assert all(isinstance(obj, CuocGoi) for obj in cuoc_goi_list_added)

    # Check specific values (example for first group)
    first_dau_so: DauSoDichVu = dau_so_list_added[0]
    assert first_dau_so.doi_soat_id == 999 
    assert first_dau_so.stt == 1
    assert first_dau_so.raw_dau_so == MOCK_PROCESSED_RESULTS[0]['raw_dau_so_from_template']
    assert first_dau_so.standardized_display == MOCK_PROCESSED_RESULTS[0]['dau_so_display']
    assert first_dau_so.dau_so_type.value == MOCK_PROCESSED_RESULTS[0]['dau_so_type']
    assert first_dau_so.prefix == MOCK_PROCESSED_RESULTS[0]['prefix']

    first_cuoc_goi: CuocGoi = cuoc_goi_list_added[0]
    assert first_cuoc_goi.dau_so == first_dau_so 
    assert first_cuoc_goi.loai_cuoc == 'co_dinh_noi_hat' 
    assert first_cuoc_goi.thoi_gian_goi == MOCK_PROCESSED_RESULTS[0]['total_duration_seconds']
    assert first_cuoc_goi.cuoc == pytest.approx(MOCK_PROCESSED_RESULTS[0]['total_cost_before_vat'])
    
    # Check 1800 mapping (Nhóm 3 -> CuocGoi thứ 3)
    third_cuoc_goi: CuocGoi = cuoc_goi_list_added[2]
    assert third_cuoc_goi.loai_cuoc == 'cuoc_1900' # Mapped SERVICE_1800_VOICE_CD to 'cuoc_1900'
    assert third_cuoc_goi.cuoc == pytest.approx(MOCK_PROCESSED_RESULTS[2]['total_cost_before_vat'])

    # Check TongKet values
    tong_ket_added = next((obj for obj in added_objects if isinstance(obj, TongKet)), None)
    assert tong_ket_added is not None
    assert tong_ket_added.doi_soat_id == 999
    assert tong_ket_added.cong_tien_dich_vu == pytest.approx(MOCK_SUMMARY_TOTALS["total_cost_before_vat"])
    assert tong_ket_added.tien_thue_gtgt == pytest.approx(MOCK_SUMMARY_TOTALS["total_vat"])
    assert tong_ket_added.tong_cong_tien == pytest.approx(MOCK_SUMMARY_TOTALS["total_cost_after_vat"])

    # 3. Check commit was called
    mock_db.commit.assert_called_once()
    mock_db.rollback.assert_not_called() 
    mock_db.refresh.assert_called_once_with(new_doi_soat) 

# TODO: Add test case for database error and rollback
def test_create_dscd_reconciliation_db_error():
    """Test that rollback is called when a database error occurs."""
    mock_db = MagicMock(spec=Session)
    # Simulate a database error during commit
    mock_db.commit.side_effect = Exception("Database commit failed")

    # Use pytest.raises to check if the expected exception is raised
    with pytest.raises(Exception, match="Database commit failed"):
        create_dscd_reconciliation(
            db=mock_db,
            ky_doi_soat=MOCK_KY_DOI_SOAT,
            template_id=MOCK_TEMPLATE_ID,
            partner_id=MOCK_PARTNER_ID,
            processed_results=MOCK_PROCESSED_RESULTS,
            summary_totals=MOCK_SUMMARY_TOTALS
        )

    # Assertions
    mock_db.add.assert_called() # Check that we tried to add data
    mock_db.add_all.assert_called() # Check that we tried to add bulk data
    mock_db.commit.assert_called_once() # Ensure commit was attempted
    mock_db.rollback.assert_called_once() # Ensure rollback was called after error
    mock_db.refresh.assert_not_called() # Ensure refresh was NOT called

@patch('src.crud.crud_dscd.log') # Patch the logger instance directly
def test_create_dscd_reconciliation_invalid_mapping(mock_logger: MagicMock): # Inject the patched logger
    """Test skipping and logging when an invalid service type mapping occurs."""
    mock_db = MagicMock(spec=Session)

    # --- Mock the behavior of db.flush() ---
    added_objects = []
    def capture_add(obj):
        added_objects.append(obj)
        if isinstance(obj, DoiSoatCoDinh):
            obj.id = 998 # Assign a dummy ID

    mock_db.add.side_effect = capture_add
    # --- End Mocking flush behavior ---

    # Create modified processed results with an invalid service type
    mock_processed_invalid = MOCK_PROCESSED_RESULTS[:1] + [
        { # Invalid entry
            'dau_so_display': 'INVALID',
            'dau_so_id_from_template_data': 999,
            'service_type': 'INVALID_SERVICE_TYPE', # This won't map
            'total_duration_seconds': 10,
            'total_cost_before_vat': 100.00,
            'call_count': 1,
            'dau_so_type': DauSoType.SINGLE.value,
            'dau_so_count_template': 1,
            'start_num_str': None,
            'end_num_str': None,
            'prefix': None,
            'raw_dau_so_from_template': 'INVALID'
        }
    ] + MOCK_PROCESSED_RESULTS[2:]

    # Call the function
    new_doi_soat = create_dscd_reconciliation(
        db=mock_db,
        ky_doi_soat=MOCK_KY_DOI_SOAT,
        template_id=MOCK_TEMPLATE_ID,
        partner_id=MOCK_PARTNER_ID,
        processed_results=mock_processed_invalid, # Use invalid data
        summary_totals=MOCK_SUMMARY_TOTALS
    )

    # --- Assertions ---

    # 1. Check return value (still created)
    assert isinstance(new_doi_soat, DoiSoatCoDinh)
    assert new_doi_soat.id == 998

    # 2. Check logger.error was called for the invalid type and the skipping message
    # Check that error was called at least twice (ValueError and Skipping message)
    assert mock_logger.error.call_count >= 2 
    
    # Check the specific messages were logged
    mock_logger.error.assert_any_call(
        "Invalid service_type string 'INVALID_SERVICE_TYPE' found in processed data."
    )
    mock_logger.error.assert_any_call(
        "Could not map service type 'INVALID_SERVICE_TYPE' to loai_cuoc. Skipping creation for this item."
    )

    # 3. Check db.add was called for DoiSoatCoDinh, TongKet, and the *valid* DauSo/CuocGoi pairs
    # Original has 3 entries, we replaced 1 with invalid, so 2 valid pairs remain
    # Calls expected: DoiSoatCoDinh(1), TongKet(1)
    initial_adds = [obj for obj in added_objects if isinstance(obj, (DoiSoatCoDinh, TongKet))]
    assert len(initial_adds) == 2
    assert any(isinstance(obj, DoiSoatCoDinh) for obj in initial_adds)
    assert any(isinstance(obj, TongKet) for obj in initial_adds)

    # 4. Check db.add_all was called twice (once for DauSo, once for CuocGoi)
    assert mock_db.add_all.call_count == 2

    # Verify the content of db.add_all calls
    dau_so_list_call = mock_db.add_all.call_args_list[0][0][0] # First call, first arg
    cuoc_goi_list_call = mock_db.add_all.call_args_list[1][0][0] # Second call, first arg
    
    # Should have added only the DauSoDichVu records corresponding to the valid processed_results
    assert len(dau_so_list_call) == len(MOCK_PROCESSED_RESULTS) - 1 # Excludes the invalid one
    assert all(isinstance(item, DauSoDichVu) for item in dau_so_list_call)
    
    # Should have added only the CuocGoi records corresponding to the valid processed_results
    assert len(cuoc_goi_list_call) == len(MOCK_PROCESSED_RESULTS) - 1 # Excludes the invalid one
    assert all(isinstance(item, CuocGoi) for item in cuoc_goi_list_call)

    # 5. Check db.commit was called
    mock_db.commit.assert_called_once()

    # 6. Check db.rollback was NOT called
    mock_db.rollback.assert_not_called()

    # 7. Check that the invalid item was not added to the database
    added_displays = {ds.standardized_display for ds in dau_so_list_call}
    assert 'INVALID' not in added_displays
    assert MOCK_PROCESSED_RESULTS[0]['dau_so_display'] in added_displays
    assert MOCK_PROCESSED_RESULTS[2]['dau_so_display'] in added_displays 