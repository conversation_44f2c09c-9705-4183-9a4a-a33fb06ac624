# backend/tests/utils/test_pricing_utils.py

import pytest
from unittest.mock import MagicMock, patch # For mocking db session and query results
from typing import Optional, <PERSON><PERSON>
from decimal import Decimal
from sqlalchemy.orm import Session # Import Session

# Import các module cần test và các model liên quan
# Sử dụng import tuy<PERSON>t đối từ gốc project (src)
from src.utils.pricing_utils import apply_billing_method, get_pricing_rule
from src.models.pricing import CostPricing, RevenuePricing, BillingMethod
from src.models.volume_range import VolumeRange, VolumeUnit
from src.models.enums import ServiceType

# === Tests for apply_billing_method ===

@pytest.mark.parametrize(
    "duration, price, billing_method, expected_cost",
    [
        # DEFAULT Method (Price per minute, round up minutes)
        (0, 1000.0, BillingMethod.DEFAULT, 0.0),      # 0 seconds -> 0 cost
        (1, 1000.0, BillingMethod.DEFAULT, 1000.0),     # 1 second -> 1 min cost
        (59, 1000.0, BillingMethod.DEFAULT, 1000.0),     # 59 seconds -> 1 min cost
        (60, 1000.0, BillingMethod.DEFAULT, 1000.0),     # 60 seconds -> 1 min cost
        (61, 1000.0, BillingMethod.DEFAULT, 2000.0),     # 61 seconds -> 2 min cost
        (119, 1000.0, BillingMethod.DEFAULT, 2000.0),    # 119 seconds -> 2 min cost
        (120, 1000.0, BillingMethod.DEFAULT, 2000.0),    # 120 seconds -> 2 min cost
        (121, 1000.0, BillingMethod.DEFAULT, 3000.0),    # 121 seconds -> 3 min cost
        (90, 1500.5, BillingMethod.DEFAULT, 3001.0),    # 90 seconds -> 2 mins * 1500.5
        (60, 0.0, BillingMethod.DEFAULT, 0.0),         # 0 price
        
        # Other Methods (Currently return 0 in DSCD context)
        (65, 1000.0, BillingMethod.BLOCK_6S, 0.0),
        (65, 10.0, BillingMethod.ONE_SEC_PLUS, 0.0),
        (90, 1000.0, BillingMethod.ONE_MIN_PLUS, 0.0),
        (60, 1000.0, BillingMethod.SUBSCRIPTION, 0.0), # Assuming subscription not used here
        
        # Edge cases
        (-10, 1000.0, BillingMethod.DEFAULT, 0.0),     # Negative duration
        (60, -1000.0, BillingMethod.DEFAULT, 0.0),     # Negative price
    ]
)
def test_apply_billing_method(duration: int, price: float, billing_method: BillingMethod, expected_cost: float):
    """Test the apply_billing_method function with various scenarios."""
    calculated_cost = apply_billing_method(duration, price, billing_method)
    # Use pytest.approx for float comparison
    assert calculated_cost == pytest.approx(expected_cost)

# === Tests for get_pricing_rule ===

# Helper to create a mock RevenuePricing object
def create_mock_pricing_rule(
    price: float, 
    billing_method: BillingMethod,
    service_type: Optional[str] = None, # Stored as string in DB
    is_active: bool = True,
    partner_id: int = 1 
) -> MagicMock: # Use MagicMock to simulate SQLAlchemy model instance
    mock_rule = MagicMock(spec=RevenuePricing)
    mock_rule.price = Decimal(str(price)) # Simulate Numeric/Decimal from DB
    mock_rule.billing_method = billing_method
    mock_rule.service_type = service_type
    mock_rule.is_active = is_active
    mock_rule.partner_id = partner_id
    # Add other attributes if needed by the function being tested
    return mock_rule

def test_get_pricing_rule_found_specific_active():
    """Test finding a specific, active pricing rule."""
    mock_db = MagicMock(spec=Session)
    specific_rule = create_mock_pricing_rule(1500.0, BillingMethod.DEFAULT, ServiceType.MOBILE_NORMAL.value)
    
    # Mock the query chain for specific rule
    mock_db.query.return_value.filter.return_value.first.return_value = specific_rule
    
    result = get_pricing_rule(mock_db, 1, ServiceType.MOBILE_NORMAL)
    
    assert result is not None
    price, method = result
    assert price == pytest.approx(1500.0)
    assert method == BillingMethod.DEFAULT
    # Verify filter calls (optional, but good practice)
    # Check that filter was called at least once (for the specific rule)
    mock_db.query.return_value.filter.assert_called()
    # More specific checks on filter arguments could be added

def test_get_pricing_rule_fallback_to_default_active():
    """Test falling back to an active default rule when specific is not found."""
    mock_db = MagicMock(spec=Session)
    default_rule = create_mock_pricing_rule(1000.0, BillingMethod.DEFAULT, service_type=None) # Default has no service_type
    
    # Mock the query chain: first() returns None for specific, then returns default_rule for default
    mock_db.query.return_value.filter.return_value.first.side_effect = [None, default_rule]
    
    result = get_pricing_rule(mock_db, 1, ServiceType.MOBILE_NORMAL)
    
    assert result is not None
    price, method = result
    assert price == pytest.approx(1000.0)
    assert method == BillingMethod.DEFAULT
    assert mock_db.query.return_value.filter.return_value.first.call_count == 2 # Called for specific and default

def test_get_pricing_rule_specific_inactive_fallback_to_default_active():
    """Test specific rule inactive, falling back to active default rule."""
    mock_db = MagicMock(spec=Session)
    # Specific rule exists but is inactive (mock returns it first, but function logic should ignore it)
    inactive_specific_rule = create_mock_pricing_rule(1500.0, BillingMethod.DEFAULT, ServiceType.MOBILE_NORMAL.value, is_active=False)
    active_default_rule = create_mock_pricing_rule(1000.0, BillingMethod.DEFAULT, service_type=None, is_active=True)

    # IMPORTANT: The mock needs to simulate the *actual* DB query behavior. 
    # The function itself applies the is_active filter. So the mock should return
    # None when the active specific rule is queried, then return the active default rule.
    mock_db.query.return_value.filter.return_value.first.side_effect = [None, active_default_rule]

    result = get_pricing_rule(mock_db, 1, ServiceType.MOBILE_NORMAL)

    assert result is not None
    price, method = result
    assert price == pytest.approx(1000.0)
    assert method == BillingMethod.DEFAULT
    assert mock_db.query.return_value.filter.return_value.first.call_count == 2

def test_get_pricing_rule_service_type_none_finds_default():
    """Test finding default rule when service_type is None."""
    mock_db = MagicMock(spec=Session)
    default_rule = create_mock_pricing_rule(999.0, BillingMethod.DEFAULT, service_type=None)
    
    # Mock the query chain for default rule (since service_type is None, only default is queried)
    mock_db.query.return_value.filter.return_value.first.return_value = default_rule
    
    result = get_pricing_rule(mock_db, 1, None)
    
    assert result is not None
    price, method = result
    assert price == pytest.approx(999.0)
    assert method == BillingMethod.DEFAULT
    # Verify filter() was called once (only for default)
    mock_db.query.return_value.filter.return_value.first.assert_called_once()

def test_get_pricing_rule_no_rule_found():
    """Test when neither specific nor default active rules are found."""
    mock_db = MagicMock(spec=Session)
    
    # Mock the query chain to return None for both specific and default queries
    mock_db.query.return_value.filter.return_value.first.side_effect = [None, None]
    
    result = get_pricing_rule(mock_db, 1, ServiceType.MOBILE_NORMAL)
    
    assert result is None
    assert mock_db.query.return_value.filter.return_value.first.call_count == 2

def test_get_pricing_rule_no_default_rule_found():
    """Test when specific is not found and no active default exists."""
    mock_db = MagicMock(spec=Session)
    
    # Mock the query chain: first() returns None for specific, then None for default
    mock_db.query.return_value.filter.return_value.first.side_effect = [None, None]
    
    result = get_pricing_rule(mock_db, 1, ServiceType.MOBILE_NORMAL)
    
    assert result is None
    assert mock_db.query.return_value.filter.return_value.first.call_count == 2

def test_get_pricing_rule_price_conversion():
    """Test that price (Decimal) is converted to float."""
    mock_db = MagicMock(spec=Session)
    # Price is Decimal
    specific_rule = create_mock_pricing_rule(price=1234.56, billing_method=BillingMethod.DEFAULT, service_type=ServiceType.FIXED_NOI_HAT.value)
    mock_db.query.return_value.filter.return_value.first.return_value = specific_rule
    
    result = get_pricing_rule(mock_db, 1, ServiceType.FIXED_NOI_HAT)
    
    assert result is not None
    price, method = result
    assert isinstance(price, float) 
    assert price == pytest.approx(1234.56)
    assert method == BillingMethod.DEFAULT 