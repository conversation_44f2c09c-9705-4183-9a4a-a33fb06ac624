# backend/tests/utils/test_enhanced_phone_utils.py

import pytest
from typing import Optional, Tuple
from datetime import datetime

# Import các module cần test và các model liên quan
# Sử dụng import tuy<PERSON>t đ<PERSON><PERSON> từ gốc project (src)
from src.utils.enhanced_phone_utils import extract_area_code, determine_service_type, normalize_phone_number_enhanced, get_phone_details, matches_caller
from src.utils.dscd_processor import parse_and_standardize_dau_so
from src.models.call_log import CallLog, NumberType, CallType
from src.models.enums import ServiceType, DauSoType

# === Tests for extract_area_code ===

@pytest.mark.parametrize(
    "phone_number, expected_area_code",
    [
        # Valid VN Fixed Lines (start with 0)
        ("0241234567", "24"),
        ("0287654321", "28"),
        ("0290111222", "290"),
        ("0210987654", "210"),
        ("************", "24"), # With spaces
        ("************", "28"), # With dots
        ("02", None), # Too short
        ("024", None), # Too short for number part
        ("0345678901", None), # Mobile prefix
        ("0123456789", None), # Invalid prefix
        
        # Valid VN Fixed Lines (start with 84)
        ("84241234567", "24"),
        ("84287654321", "28"),
        ("84290111222", "290"),
        ("84210987654", "210"),
        ("84 24 123 4567", "24"), # With spaces
        ("84", None), # Too short
        ("842", None), # Too short
        ("8424", None), # Too short for number part
        ("84345678901", None), # Mobile prefix
        ("84912345678", None), # Mobile prefix

        # Other cases
        ("19001234", None), # Service number
        ("18005678", None), # Service number
        ("+84241234567", "24"), # Intl format - SHOULD extract area code after cleaning
        ("+12125551212", None), # Other intl
        ("0912345678", None), # Mobile
        ("invalid string", None),
        ("", None),
        (None, None),
    ]
)
def test_extract_area_code(phone_number: Optional[str], expected_area_code: Optional[str]):
    """Test the extract_area_code function with various inputs."""
    assert extract_area_code(phone_number) == expected_area_code

# === Tests for determine_service_type ===

# Helper to create dummy CallLog objects easily
def create_dummy_call_log(
    caller="0241111111", 
    callee="0242222222", 
    caller_type=NumberType.FIXED, 
    callee_type=NumberType.FIXED,
    call_type=CallType.OUT # Assuming OUT for DSCD context
) -> CallLog:
    # Create a basic CallLog instance - other fields are not needed for this test
    # Note: This is not a SQLAlchemy model instance, just a structure for the test
    log = CallLog(
        id=1, # Dummy ID
        file_id=1,
        call_type=call_type,
        caller=caller,
        callee=callee,
        begin_time=datetime.now(), # Dummy times
        end_time=datetime.now(),
        duration=60,
        caller_gateway="gw1",
        called_gateway="gw2",
        caller_type=caller_type,
        callee_type=callee_type,
        call_date=datetime.now().date(),
        call_hour=datetime.now().hour
        # area_prefix etc. are not directly used by determine_service_type
    )
    return log

# Test data for determine_service_type
# Create mock CallLog by assigning attributes
MOCK_CALL_LOG_FIXED_LH = CallLog()
MOCK_CALL_LOG_FIXED_LH.id=1
MOCK_CALL_LOG_FIXED_LH.call_log_file_id=1
MOCK_CALL_LOG_FIXED_LH.caller="02431234567"
MOCK_CALL_LOG_FIXED_LH.caller_type=NumberType.FIXED
MOCK_CALL_LOG_FIXED_LH.callee="02437654321"
MOCK_CALL_LOG_FIXED_LH.callee_type=NumberType.FIXED
MOCK_CALL_LOG_FIXED_LH.duration=60
MOCK_CALL_LOG_FIXED_LH.call_time=datetime(2024, 1, 1, 10, 0, 0)
MOCK_CALL_LOG_FIXED_LH.call_type=CallType.OUT
MOCK_CALL_LOG_FIXED_LH.begin_time=datetime(2024, 1, 1, 10, 0, 0) # Add missing required fields
MOCK_CALL_LOG_FIXED_LH.end_time=datetime(2024, 1, 1, 10, 1, 0) # Add missing required fields
MOCK_CALL_LOG_FIXED_LH.caller_gateway = "GW1" # Add missing required fields
MOCK_CALL_LOG_FIXED_LH.called_gateway = "GW2" # Add missing required fields
MOCK_CALL_LOG_FIXED_LH.file_id = 1 # Add missing required fields (assuming file_id is the FK)

MOCK_CALL_LOG_FIXED_NH = CallLog()
MOCK_CALL_LOG_FIXED_NH.id=2
MOCK_CALL_LOG_FIXED_NH.call_log_file_id=1
MOCK_CALL_LOG_FIXED_NH.caller="02831234567"
MOCK_CALL_LOG_FIXED_NH.caller_type=NumberType.FIXED
MOCK_CALL_LOG_FIXED_NH.callee="02437654321"
MOCK_CALL_LOG_FIXED_NH.callee_type=NumberType.FIXED
MOCK_CALL_LOG_FIXED_NH.duration=60
MOCK_CALL_LOG_FIXED_NH.call_time=datetime(2024, 1, 1, 10, 0, 0)
MOCK_CALL_LOG_FIXED_NH.call_type=CallType.OUT
MOCK_CALL_LOG_FIXED_NH.begin_time=datetime(2024, 1, 1, 10, 0, 0) # Add missing required fields
MOCK_CALL_LOG_FIXED_NH.end_time=datetime(2024, 1, 1, 10, 1, 0) # Add missing required fields
MOCK_CALL_LOG_FIXED_NH.caller_gateway = "GW1" # Add missing required fields
MOCK_CALL_LOG_FIXED_NH.called_gateway = "GW2" # Add missing required fields
MOCK_CALL_LOG_FIXED_NH.file_id = 1 # Add missing required fields

@pytest.mark.parametrize(
    "call_log_input, expected_service_type",
    [
        (MOCK_CALL_LOG_FIXED_LH, ServiceType.FIXED_NOI_HAT),
        (MOCK_CALL_LOG_FIXED_NH, ServiceType.FIXED_LIEN_TINH_KHAC_MANG),
        # Add more test cases here for other NumberTypes and scenarios
        # (e.g., mobile, 1900, international)
    ]
)
def test_determine_service_type(call_log_input, expected_service_type):
    """Test the determine_service_type function with various CallLog scenarios."""
    assert determine_service_type(call_log_input) == expected_service_type 