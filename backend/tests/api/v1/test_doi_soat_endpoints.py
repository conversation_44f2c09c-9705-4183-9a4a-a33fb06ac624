import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from typing import Optional

# Import your FastAPI app and schemas
from src.main import app # Fix: Removed 'backend.' prefix
from src.schemas.dscd import <PERSON>i<PERSON><PERSON><PERSON>o<PERSON>inh, DauSoDich<PERSON>u, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TongKet # Fix: Removed 'backend.' prefix
from src.models.enums import DauSoType # Fix: Removed 'backend.' prefix

client = TestClient(app)

# --- Helper function to create sample DSCD data ---
def create_sample_dscd_data(
    thang_doi_soat: str = "2024-07", 
    partner_id: Optional[int] = 1, 
    file_name: str = "test_dscd_file.xlsx",
    stt: int = 1,
    raw_dau_so: str = "123456"
) -> DoiSoatCoDinh:
    """Creates a sample DoiSoatCoDinh Pydantic object for testing."""
    
    # Sample data for DauSoDichVu
    dau_so_item = DauSoDichVu(
        stt=stt,
        raw_dau_so=raw_dau_so,
        standardized_display="123456", # Example standardization
        dau_so_type=DauSoType.SINGLE,
        number_count=1,
        co_dinh_noi_hat=CuocGoi(thoi_gian_goi=120, cuoc=500.0),
        co_dinh_lien_tinh=CuocGoi(thoi_gian_goi=60, cuoc=300.0),
        di_dong=CuocGoi(), # Empty CuocGoi if no calls
        cuoc_1900=CuocGoi(),
        quoc_te=CuocGoi(),
        cuoc_thue_bao=CuocThueBao(thue_bao_thang=10000.0),
        cuoc_thu_khach=15000.0,
        cuoc_tra_htc=8000.0
    )
    
    # Sample data for TongKet
    tong_ket_item = TongKet(
        cong_tien_dich_vu=800.0, # Sum of cuoc from DauSoDichVu items
        tien_thue_gtgt=80.0,    # Example VAT
        tong_cong_tien=880.0     # Example total
    )

    # Sample data for DoiSoatCoDinh
    dscd_data = DoiSoatCoDinh(
        thang_doi_soat=thang_doi_soat,
        nam_doi_soat=thang_doi_soat.split('-')[0], # Fix: Removed unnecessary backslashes
        tu_mang="HTC", # Example
        den_doi_tac="Partner A", # Example
        hop_dong_so="HD123",
        partner_id=partner_id,
        du_lieu=[dau_so_item], # List containing the sample DauSoDichVu
        tong_ket=tong_ket_item,
        file_name=file_name
    )
    return dscd_data

# --- Test Cases ---

def test_create_dscd_reconciliation_endpoint_success():
    """Test successful creation of DSCD reconciliation via API endpoint."""
    # 1. Prepare sample input data
    sample_data = create_sample_dscd_data(thang_doi_soat="2024-08", partner_id=5)
    request_payload = sample_data.model_dump() # Use model_dump() for Pydantic v2+
    
    # REMOVE template_id query parameter
    # template_id = 101 
    # endpoint_url = f"/api/v1/doi-soat/dscd/?template_id={template_id}"
    endpoint_url = "/api/v1/doi-soat/dscd/" # Endpoint without template_id

    # 2. Send POST request
    response = client.post(endpoint_url, json=request_payload)

    # 3. Assert Status Code
    assert response.status_code == 201, f"Expected status code 201, but got {response.status_code}. Response: {response.text}"

    # 4. Assert Response Body Structure and Content (Basic)
    response_json = response.json()
    assert "id" in response_json
    assert isinstance(response_json["id"], int)
    assert response_json["thang_doi_soat"] == "2024-08"
    assert response_json["hop_dong_so"] == "HD123" # From sample data
    assert response_json["file_name"] == "test_dscd_file.xlsx" # From sample data
    assert "tong_tien" in response_json
    assert isinstance(response_json["tong_tien"], float)
    # Check if tong_tien matches the placeholder calculation for now
    assert response_json["tong_tien"] == 880.0 # Matches tong_ket.tong_cong_tien in sample data

def test_get_dscd_reconciliations_list():
    """Test retrieving list of DSCD reconciliations."""
    # 1. First create a reconciliation record to ensure there's data
    sample_data = create_sample_dscd_data(thang_doi_soat="2024-09", partner_id=7, file_name="list_test.xlsx")
    client.post("/api/v1/doi-soat/dscd/", json=sample_data.model_dump())
    
    # 2. Call the GET endpoint
    response = client.get("/api/v1/doi-soat/dscd/?limit=5")
    
    # 3. Assert Status Code
    assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}. Response: {response.text}"
    
    # 4. Assert Response Structure
    response_json = response.json()
    assert "total" in response_json
    assert "items" in response_json
    assert "page" in response_json
    assert "page_size" in response_json
    assert "pages" in response_json
    
    # 5. Verify there's at least one item in the list
    assert response_json["total"] > 0
    assert len(response_json["items"]) > 0
    
    # 6. Check item structure for first item
    first_item = response_json["items"][0]
    assert "id" in first_item
    assert "thang_doi_soat" in first_item
    assert "hop_dong_so" in first_item
    assert "file_name" in first_item
    assert "tong_tien" in first_item
    assert "tong_dau_so" in first_item
    
    # 7. Test pagination
    response_page_2 = client.get("/api/v1/doi-soat/dscd/?skip=5&limit=5")
    assert response_page_2.status_code == 200
    response_json_page_2 = response_page_2.json()
    assert "items" in response_json_page_2
    
    # 8. Test filtering
    response_filtered = client.get("/api/v1/doi-soat/dscd/?thang_doi_soat=2024-09")
    assert response_filtered.status_code == 200
    response_json_filtered = response_filtered.json()
    if response_json_filtered["total"] > 0:
        for item in response_json_filtered["items"]:
            assert item["thang_doi_soat"] == "2024-09"

def test_get_dscd_reconciliation_by_id():
    """Test retrieving a specific DSCD reconciliation by ID."""
    # 1. First create a reconciliation record to ensure there's data
    sample_data = create_sample_dscd_data(thang_doi_soat="2024-10", partner_id=8, file_name="detail_test.xlsx")
    create_response = client.post("/api/v1/doi-soat/dscd/", json=sample_data.model_dump())
    create_response_json = create_response.json()
    dscd_id = create_response_json["id"]
    
    # 2. Call the GET endpoint with the ID
    response = client.get(f"/api/v1/doi-soat/dscd/{dscd_id}")
    
    # 3. Assert Status Code
    assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}. Response: {response.text}"
    
    # 4. Assert Response Structure
    response_json = response.json()
    assert "id" in response_json
    assert response_json["id"] == dscd_id
    assert "thang_doi_soat" in response_json
    assert response_json["thang_doi_soat"] == "2024-10"
    assert "hop_dong_so" in response_json
    assert response_json["hop_dong_so"] == "HD123"
    assert "file_name" in response_json
    assert response_json["file_name"] == "detail_test.xlsx"
    
    # 5. Check if du_lieu and tong_ket are included
    assert "du_lieu" in response_json
    assert isinstance(response_json["du_lieu"], list)
    assert len(response_json["du_lieu"]) > 0
    
    assert "tong_ket" in response_json
    assert response_json["tong_ket"] is not None
    assert "cong_tien_dich_vu" in response_json["tong_ket"]
    assert "tien_thue_gtgt" in response_json["tong_ket"]
    assert "tong_cong_tien" in response_json["tong_ket"]
    
    # 6. Check error case for non-existent ID
    non_existent_id = 99999
    error_response = client.get(f"/api/v1/doi-soat/dscd/{non_existent_id}")
    assert error_response.status_code == 404

# --- Add more tests later ---
# def test_create_dscd_reconciliation_endpoint_missing_thang():
#     ...

# def test_create_dscd_reconciliation_endpoint_missing_tong_ket():
#     ...
    
# def test_create_dscd_reconciliation_endpoint_db_error(mocker): # Example using mocker
#     ... 