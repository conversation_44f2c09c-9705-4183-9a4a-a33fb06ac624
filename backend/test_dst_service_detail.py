import os
import sys
import re
import pandas as pd
import logging
from typing import Dict, List, Any, Optional, Tuple
import json

# Cấu hình logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def normalize_service_number(service_number: str) -> str:
    """Chuẩn hóa định dạng số dịch vụ"""
    service_number = service_number.strip()
    
    # Thay thế các ký tự đặc biệt bằng dấu -
    service_number = re.sub(r'->|→|~|–|:|;', '-', service_number)
    
    # Xóa các ký tự không cần thiết
    service_number = re.sub(r'[)(,"]', '', service_number)
    
    return service_number

def is_valid_service_number(text: str) -> bool:
    """
    Kiểm tra xem chuỗi có phải là số dịch vụ hợp lệ không
    
    Args:
        text: Chuỗi cần kiểm tra
    
    Returns:
        True nếu là số dịch vụ hợp lệ, False nếu không phải
    """
    # Loại bỏ các chuỗi dài quá 30 ký tự (thường là văn bản, không phải số dịch vụ)
    if len(text) > 30:
        return False
    
    # Kiểm tra nếu chứa các từ khóa không mong muốn
    unwanted_keywords = [
        "doanh thu", "thanh toán", "sau bù trừ", "tổng cộng", 
        "của", "cho", "phải", "là", "vnđ", "tiền", "gtgt", "vat",
        "được", "gồm", "thuế"
    ]
    
    text_lower = text.lower()
    for keyword in unwanted_keywords:
        if keyword in text_lower:
            return False
    
    # Kiểm tra xem có chứa số dịch vụ không
    if re.search(r'1[89]00\d*', text):
        return True
    
    # Kiểm tra mẫu đặc biệt dạng "18004xxx" hoặc "19004xxx"
    if re.match(r'^1[89]00\d*x+$', text):
        return True
    
    return False

def parse_service_range(service_str: str) -> List[str]:
    """
    Phân tích dãy số dịch vụ từ các định dạng khác nhau
    
    Args:
        service_str: Chuỗi chứa số dịch vụ (ví dụ: "1900400000-1900400099", "1900400000->1900400099")
        
    Returns:
        Danh sách các số dịch vụ đã được phân tích
    """
    # Kiểm tra nếu không phải số dịch vụ hợp lệ
    if not is_valid_service_number(service_str):
        return []
    
    # Chuẩn hóa chuỗi đầu vào
    service_str = normalize_service_number(service_str)
    
    # Kiểm tra định dạng khoảng số (range)
    range_match = re.search(r'(\d+)\s*-\s*(\d+)', service_str)
    if range_match:
        start = range_match.group(1)
        end = range_match.group(2)
        
        # Trường hợp dạng 19004000xx-19004009xx
        if len(start) != len(end):
            return [f"{start}-{end}"]
        
        # Trường hợp đơn giản như 1900400000-1900400099
        return [f"{start}-{end}"]
    
    # Kiểm tra danh sách số được phân tách bằng dấu phẩy
    if ',' in service_str:
        return [item.strip() for item in service_str.split(',') if item.strip() and is_valid_service_number(item.strip())]
    
    # Nếu chỉ là một số đơn lẻ
    if re.match(r'^\d+$', service_str):
        return [service_str]
    
    # Đối với mẫu đặc biệt như "18004xxx" hoặc "19004xxx"
    if re.match(r'^1[89]00\d*x+$', service_str):
        return [service_str]
    
    # Trả về chuỗi gốc nếu là mẫu hợp lệ khác
    if re.search(r'1[89]00\d*', service_str):
        return [service_str]
    
    return []

def extract_service_details(file_path: str) -> Dict[str, Any]:
    """
    Trích xuất chi tiết các nhóm dịch vụ từ file Excel đối soát 1800/1900,
    giữ nguyên định dạng gốc của các số dịch vụ
    
    Args:
        file_path: Đường dẫn đến file đối soát
        
    Returns:
        Dict chứa thông tin chi tiết các nhóm và các số dịch vụ
    """
    try:
        # Đọc file Excel
        file_name = os.path.basename(file_path)
        logger.info(f"Phân tích file: {file_name}")
        
        xls = pd.ExcelFile(file_path)
        sheet_name = xls.sheet_names[0]  # Lấy sheet đầu tiên
        df = pd.read_excel(xls, sheet_name=sheet_name, header=None)
        
        # Kết quả trích xuất
        result = {
            "file_name": file_name,
            "hop_dong_so": None,
            "service_groups": []
        }
        
        # 1. Tìm hợp đồng số (nếu có)
        try:
            for i in range(min(10, df.shape[0])):
                cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                if "HỢP ĐỒNG" in cell_value.upper():
                    hop_dong_match = re.search(r'HỢP ĐỒNG\s*(?:SỐ)?\s*[.:…]?\s*(\S+)', cell_value, re.IGNORECASE)
                    if hop_dong_match:
                        result["hop_dong_so"] = hop_dong_match.group(1)
                        logger.info(f"Tìm thấy hợp đồng số: {result['hop_dong_so']}")
                        break
        except Exception as e:
            logger.warning(f"Lỗi khi tìm số hợp đồng: {str(e)}")
        
        # 2. Tìm các nhóm dịch vụ từ tiêu đề
        service_headers = []
        for i in range(df.shape[0]):
            cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
            
            # Tìm các tiêu đề nhóm dịch vụ
            if re.search(r'(I{1,3}\.?\s*Dịch\s*[Vv]ụ|Dịch\s*[Vv]ụ)\s*(1[89]00|109)', cell_value, re.IGNORECASE):
                service_type = ""
                if "1900" in cell_value:
                    service_type = "1900xxxx"
                elif "1800" in cell_value:
                    service_type = "1800xxxx"
                elif "109" in cell_value:
                    service_type = "109x"
                
                service_headers.append({
                    "row": i,
                    "service_type": service_type,
                    "service_name": cell_value.strip(),
                    "source": "header"
                })
                logger.info(f"Tìm thấy nhóm dịch vụ: {service_type} (dòng {i+1}): {cell_value.strip()}")
        
        # 3. Tìm các bảng dịch vụ
        tables = []
        for i in range(df.shape[0]):
            row_text = " ".join([str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else "" for j in range(min(5, df.shape[1]))])
            if "STT" in row_text.upper() and ("SỐ DỊCH VỤ" in row_text.upper() or "ĐẦU SỐ" in row_text.upper()):
                # Tìm cột chứa số dịch vụ
                service_col = None
                for j in range(min(10, df.shape[1])):
                    if j >= df.shape[1]:
                        continue
                        
                    header_text = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                    if "SỐ DỊCH VỤ" in header_text.upper() or "ĐẦU SỐ" in header_text.upper() or "MÃ DỊCH VỤ" in header_text.upper():
                        service_col = j
                        break
                
                if service_col is not None:
                    # Tìm dòng kết thúc bảng
                    table_end = None
                    for k in range(i + 1, min(i + 50, df.shape[0])):
                        if k >= df.shape[0]:
                            break
                            
                        cell_value = str(df.iloc[k, 0]) if not pd.isna(df.iloc[k, 0]) else ""
                        if "TỔNG CỘNG" in cell_value.upper() or "CỘNG" in cell_value.upper():
                            table_end = k
                            break
                    
                    if table_end is None:
                        table_end = min(i + 30, df.shape[0])
                    
                    # Xác định loại dịch vụ của bảng này
                    service_type = None
                    for k in range(i + 1, min(table_end, df.shape[0])):
                        if k >= df.shape[0] or service_col >= df.shape[1]:
                            continue
                            
                        cell_value = str(df.iloc[k, service_col]) if not pd.isna(df.iloc[k, service_col]) else ""
                        if "1900" in cell_value:
                            service_type = "1900xxxx"
                            break
                        elif "1800" in cell_value:
                            service_type = "1800xxxx"
                            break
                        elif "109" in cell_value:
                            service_type = "109x"
                            break
                    
                    if service_type:
                        tables.append({
                            "start_row": i,
                            "end_row": table_end,
                            "service_col": service_col,
                            "service_type": service_type
                        })
                        logger.info(f"Tìm thấy bảng dịch vụ {service_type} từ dòng {i+1} đến dòng {table_end+1}")
        
        # 4. Kết hợp thông tin nhóm dịch vụ và bảng
        for header in service_headers:
            group_info = {
                "service_type": header["service_type"],
                "service_name": header["service_name"],
                "service_numbers": [],
                "raw_service_numbers": []
            }
            
            # Tìm bảng tương ứng với nhóm dịch vụ này
            matched_table = None
            for table in tables:
                if (table["service_type"] == header["service_type"] and 
                    abs(table["start_row"] - header["row"]) < 15):
                    matched_table = table
                    break
            
            if matched_table:
                # Trích xuất số dịch vụ từ bảng
                for i in range(matched_table["start_row"] + 1, matched_table["end_row"]):
                    if i >= df.shape[0] or matched_table["service_col"] >= df.shape[1]:
                        continue
                        
                    cell_value = str(df.iloc[i, matched_table["service_col"]]) if not pd.isna(df.iloc[i, matched_table["service_col"]]) else ""
                    cell_value = cell_value.strip()
                    
                    if cell_value and (header["service_type"][:4].lower() in cell_value.lower() or
                                      (header["service_type"] == "109x" and "109" in cell_value)):
                        group_info["raw_service_numbers"].append({
                            "row": i,
                            "text": cell_value,
                            "has_tru": "trừ" in cell_value.lower()
                        })
                
                group_info["extraction_method"] = "table"
                group_info["table_range"] = f"{matched_table['start_row']+1}-{matched_table['end_row']}"
            else:
                # Tìm số dịch vụ trong các ô văn bản
                for i in range(header["row"] + 1, min(header["row"] + 20, df.shape[0])):
                    for j in range(min(5, df.shape[1])):
                        if j >= df.shape[1]:
                            continue
                            
                        cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                        cell_value = cell_value.strip()
                        
                        if cell_value and (header["service_type"][:4].lower() in cell_value.lower() or 
                                          (header["service_type"] == "109x" and "109" in cell_value)):
                            # Kiểm tra thêm để loại bỏ các cụm từ như "dịch vụ 1900xxxx"
                            if "dịch vụ" not in cell_value.lower() and "thanh toán" not in cell_value.lower() and "doanh thu" not in cell_value.lower():
                                if cell_value not in [item["text"] for item in group_info["raw_service_numbers"]]:
                                    group_info["raw_service_numbers"].append({
                                        "row": i,
                                        "text": cell_value,
                                        "has_tru": "trừ" in cell_value.lower()
                                    })
                
                group_info["extraction_method"] = "text"
            
            # Chỉ thêm nhóm nếu có số dịch vụ
            if group_info["raw_service_numbers"]:
                # Cũng lưu lại danh sách các số dịch vụ dưới dạng chuỗi thông thường
                for item in group_info["raw_service_numbers"]:
                    group_info["service_numbers"].append(item["text"])
                
                result["service_groups"].append(group_info)
        
        # 5. Tìm thêm các bảng chưa được gán cho nhóm dịch vụ nào
        for table in tables:
            # Kiểm tra xem bảng đã được gán cho nhóm nào chưa
            is_matched = False
            for group in result["service_groups"]:
                if "table_range" in group and group["table_range"] == f"{table['start_row']+1}-{table['end_row']}":
                    is_matched = True
                    break
            
            if not is_matched:
                # Tìm tên nhóm dịch vụ từ các dòng trên bảng
                service_name = table["service_type"]
                for i in range(max(0, table["start_row"] - 10), table["start_row"]):
                    cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
                    if "DỊCH VỤ" in cell_value.upper() and (
                        table["service_type"][:4].lower() in cell_value.lower() or
                        (table["service_type"] == "109x" and "109" in cell_value)
                    ):
                        service_name = cell_value.strip()
                        break
                
                group_info = {
                    "service_type": table["service_type"],
                    "service_name": service_name,
                    "service_numbers": [],
                    "raw_service_numbers": [],
                    "extraction_method": "table_only",
                    "table_range": f"{table['start_row']+1}-{table['end_row']}"
                }
                
                # Trích xuất số dịch vụ từ bảng
                for i in range(table["start_row"] + 1, table["end_row"]):
                    if i >= df.shape[0] or table["service_col"] >= df.shape[1]:
                        continue
                        
                    cell_value = str(df.iloc[i, table["service_col"]]) if not pd.isna(df.iloc[i, table["service_col"]]) else ""
                    cell_value = cell_value.strip()
                    
                    if cell_value and (table["service_type"][:4].lower() in cell_value.lower() or
                                      (table["service_type"] == "109x" and "109" in cell_value)):
                        group_info["raw_service_numbers"].append({
                            "row": i,
                            "text": cell_value,
                            "has_tru": "trừ" in cell_value.lower()
                        })
                        group_info["service_numbers"].append(cell_value)
                
                # Chỉ thêm nhóm nếu có số dịch vụ
                if group_info["raw_service_numbers"]:
                    result["service_groups"].append(group_info)
        
        # 6. Tìm thông tin thanh toán
        result["thanh_toan"] = {"found": False, "has_bu_tru": False}
        
        for i in range(df.shape[0]):
            cell_value = str(df.iloc[i, 0]) if not pd.isna(df.iloc[i, 0]) else ""
            if "THANH TOÁN" in cell_value.upper() or "III" in cell_value.upper() and "THANH TOÁN" in cell_value.upper():
                result["thanh_toan"]["found"] = True
                logger.info(f"Tìm thấy phần thanh toán (dòng {i+1})")
                break
        
        # Tìm thông tin sau bù trừ
        for i in range(df.shape[0]):
            row_text = " ".join([str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else "" for j in range(min(5, df.shape[1]))])
            if "SAU BÙ TRỪ" in row_text.upper() and "PHẢI THANH TOÁN CHO" in row_text.upper():
                result["thanh_toan"]["has_bu_tru"] = True
                logger.info(f"Tìm thấy thông tin sau bù trừ (dòng {i+1})")
                break
        
        return result
        
    except Exception as e:
        logger.error(f"Lỗi khi phân tích file {file_path}: {str(e)}")
        return {
            "file_name": os.path.basename(file_path),
            "error": str(e),
            "service_groups": []
        }

def analyze_all_files(directory_path: str) -> None:
    """
    Phân tích tất cả các file trong thư mục để trích xuất thông tin nhóm dịch vụ
    
    Args:
        directory_path: Đường dẫn đến thư mục chứa các file cần phân tích
    """
    if not os.path.exists(directory_path):
        logger.error(f"Thư mục {directory_path} không tồn tại!")
        return
    
    # Danh sách các file Excel
    excel_files = []
    
    # Duyệt qua tất cả các file trong thư mục
    for root, _, files in os.walk(directory_path):
        for filename in files:
            if filename.endswith('.xlsx') or filename.endswith('.xls'):
                # Bỏ qua các file lock
                if filename.startswith('.~lock.'):
                    continue
                    
                file_path = os.path.join(root, filename)
                excel_files.append(file_path)
    
    logger.info(f"Tìm thấy {len(excel_files)} file Excel")
    
    # Phân tích từng file
    all_results = []
    
    for file_path in excel_files:
        # Trích xuất thông tin từ file
        result = extract_service_details(file_path)
        all_results.append(result)
    
    # Tóm tắt kết quả
    logger.info("\n====== TỔNG KẾT PHÂN TÍCH CHI TIẾT ======")
    logger.info(f"Tổng số file đã phân tích: {len(all_results)}")
    
    file_with_services = 0
    total_service_groups = 0
    total_service_numbers = 0
    
    for result in all_results:
        service_groups = result.get("service_groups", [])
        
        if service_groups:
            file_with_services += 1
            total_service_groups += len(service_groups)
            
            # In chi tiết từng file
            logger.info(f"\n* File: {result['file_name']}")
            logger.info(f"  - Số hợp đồng: {result.get('hop_dong_so', 'Không có')}")
            
            for idx, group in enumerate(service_groups, 1):
                service_count = len(group.get("raw_service_numbers", []))
                total_service_numbers += service_count
                
                logger.info(f"  - Nhóm {idx}: {group.get('service_name', group.get('service_type', 'Không xác định'))}")
                logger.info(f"    + Phương thức trích xuất: {group.get('extraction_method', 'N/A')}")
                logger.info(f"    + Số lượng dòng dịch vụ: {service_count}")
                
                # In chi tiết các số dịch vụ nguyên bản
                if service_count > 0:
                    logger.info(f"    + Chi tiết các số dịch vụ:")
                    for item in group.get("raw_service_numbers", []):
                        logger.info(f"      {item['text']}")
    
    logger.info(f"\n=== KẾT QUẢ THỐNG KÊ ===")
    logger.info(f"- Số file có nhóm dịch vụ: {file_with_services}/{len(all_results)}")
    logger.info(f"- Tổng số nhóm dịch vụ đã nhận dạng: {total_service_groups}")
    logger.info(f"- Tổng số dòng chứa chi tiết dịch vụ đã nhận dạng: {total_service_numbers}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        directory_path = sys.argv[1]
    else:
        directory_path = "BBDS/1800_1900"  # Mặc định thư mục 1800_1900
    
    logger.info(f"Bắt đầu phân tích chi tiết các nhóm dịch vụ trong thư mục: {directory_path}")
    analyze_all_files(directory_path) 