import os
import re
import logging
import pandas as pd
import sys
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger(__name__)

# Các hàm đã copy từ module dst_1800_1900_processor
def is_valid_service_number(text: str) -> bool:
    """
    Kiểm tra xem chuỗi có phải là số dịch vụ hợp lệ không
    
    Args:
        text: Chuỗi cần kiểm tra
    
    Returns:
        True nếu là số dịch vụ hợp lệ, False nếu không phải
    """
    if not isinstance(text, str):
        return False
        
    # Loại bỏ các chuỗi dài quá 100 ký tự (thường là văn bản, không phải số dịch vụ)
    if len(text) > 100:
        return False
    
    # Kiểm tra nếu chứa các từ khóa không mong muốn (gi<PERSON> lại "trừ" và "gồm" vì có thể là phần của mô tả dịch vụ)
    unwanted_keywords = [
        "doanh thu", "thanh toán", "sau bù trừ", "tổng cộng", 
        "của", "cho", "phải", "là", "vnđ", "tiền", "gtgt", "vat",
        "được", "thuế", "cước", "cuộc"
    ]
    
    text_lower = text.lower()
    
    # Loại bỏ các string bắt đầu với keyword không mong muốn
    for keyword in unwanted_keywords:
        if keyword in text_lower:
            # Nếu keyword là sub-string hoàn toàn hoặc nằm ở đầu chuỗi
            if text_lower.startswith(keyword) or f" {keyword} " in text_lower:
                return False
    
    # Trường hợp đặc biệt: Cho phép chuỗi chỉ chứa "1800" hoặc "1900"
    if text.strip() in ["1800", "1900"]:
        return True
    
    # Đặc biệt: Nếu là dạng tiêu đề dịch vụ
    if re.search(r'dịch\s*vụ\s*1[89]00', text_lower):
        # Kiểm tra nếu là tiêu đề chính như "I. Dịch vụ 1900"
        if re.match(r'[IVX]+\.\s*dịch\s*vụ\s*1[89]00', text_lower):
            return True
        # Hoặc mẫu như "MBC sử dụng 1800 của HTC"
        if re.search(r'sử\s*dụng\s*1[89]00', text_lower):
            return True
    
    # Kiểm tra xem có chứa số dịch vụ không
    if re.search(r'1[89]00\d*', text):
        return True
    
    # Kiểm tra mẫu đặc biệt dạng khoảng 1900xxx-1900yyy
    if re.search(r'1[89]00.*-.*1[89]00', text):
        return True
    
    # Kiểm tra mẫu đặc biệt dạng "18004xxx" hoặc "19004xxx"
    if re.search(r'1[89]00\d*x+', text):
        return True
    
    # Kiểm tra dạng danh sách có dấu ";"
    if ";" in text and re.search(r'1[89]00', text):
        return True
    
    # Kiểm tra dạng loại trừ như "190032xx trừ..."
    if "trừ" in text_lower and re.search(r'1[89]00', text):
        return True
    
    # Kiểm tra dạng mô tả phạm vi như "xx gồm..."
    if re.search(r'xx\s*gồm', text_lower) and re.search(r'1[89]00', text):
        return True
    
    # Kiểm tra dạng mô tả đặc biệt trong ngoặc đơn với xx
    if re.search(r'1[89]00\d+xx\s*\(', text):
        return True
        
    return False

def has_complex_list(text: str) -> bool:
    """Kiểm tra nếu chuỗi chứa danh sách phức tạp (dạng liệt kê nhiều lớp)"""
    # Mẫu như: 190031xx (14;18;35;38;66;68;79;99)
    pattern1 = r'1[89]00\d+xx\s*\([\d;-]+\)'
    
    # Mẫu như: 190032xx (xx gồm 01-27;29-31;...)
    pattern2 = r'1[89]00\d+xx\s*\(xx\s*gồm\s*[\d;-]+\)'
    
    # Mẫu như: 190033xx (xx trừ 00-03;05-18;...)
    pattern3 = r'1[89]00\d+xx\s*\(xx\s*trừ\s*[\d;-]+\)'
    
    # Mẫu phức tạp hơn
    pattern4 = r'1[89]00\d+xx\s*\(.+\)'
    
    return bool(re.search(pattern1, text) or re.search(pattern2, text) or re.search(pattern3, text) or re.search(pattern4, text))

def has_multiple_spaces(text: str) -> bool:
    """Kiểm tra nếu chuỗi chứa nhiều dấu cách liên tiếp"""
    return bool(re.search(r'  +', text))

def has_x_pattern(text: str) -> bool:
    """Kiểm tra nếu chuỗi chứa mẫu x cho số dịch vụ"""
    return bool(re.search(r'1[89]00\d*xx', text) or re.search(r'1[89]00\d*x+', text))

def has_semicolon_list(text: str) -> bool:
    """Kiểm tra nếu chuỗi chứa danh sách phân cách bằng dấu chấm phẩy"""
    return ";" in text and re.search(r'1[89]00', text)

def has_exclusion(text: str) -> bool:
    """Kiểm tra nếu chuỗi chứa từ loại trừ"""
    return bool(re.search(r'(trừ|loại trừ)', text.lower()))

def has_multi_line_format(text: str) -> bool:
    """Kiểm tra nếu chuỗi có định dạng nhiều dòng"""
    return '\n' in text

def analyze_special_cases(file_path: str) -> Dict[str, Any]:
    """
    Xử lý các trường hợp đặc biệt trong file Excel đối soát 1800/1900
    
    Args:
        file_path: Đường dẫn đến file Excel
    
    Returns:
        Dict với thông tin về các trường hợp đặc biệt đã tìm thấy
    """
    try:
        file_name = os.path.basename(file_path)
        logger.info(f"Đang xử lý file: {file_name}")
        
        # Đọc file Excel
        xls = pd.ExcelFile(file_path)
        sheet_name = xls.sheet_names[0]  # Lấy sheet đầu tiên
        df = pd.read_excel(xls, sheet_name=sheet_name, header=None)
        
        logger.info(f"Kích thước dữ liệu: {df.shape[0]} dòng x {df.shape[1]} cột")
        
        # Kết quả các trường hợp đặc biệt
        special_cases = {
            "multi_line_formats": 0,
            "multiple_spaces": 0,
            "x_patterns": 0,
            "list_formats": 0,
            "exclusions": 0,
            "complex_lists": 0
        }
        
        total_service_cells = 0
        
        # Quét toàn bộ file để tìm các ô chứa thông tin số dịch vụ
        raw_service_numbers = []
        
        for i in range(df.shape[0]):
            for j in range(df.shape[1]):  # Check all columns
                if j >= df.shape[1]:
                    continue
                    
                cell_value = str(df.iloc[i, j]) if not pd.isna(df.iloc[i, j]) else ""
                cell_value = cell_value.strip()
                
                # Bỏ qua nếu ô rỗng
                if not cell_value:
                    continue
                
                # Log any cell containing 1800 or 1900 pattern for debugging
                if re.search(r'1[89]00', cell_value):
                    logger.info(f"Service number found - Row {i+1}, Column {j+1}: `{cell_value}`")
                
                # Kiểm tra các trường hợp đặc biệt
                if is_valid_service_number(cell_value):
                    total_service_cells += 1
                    
                    # Kiểm tra từng trường hợp đặc biệt
                    if has_multi_line_format(cell_value):
                        special_cases["multi_line_formats"] += 1
                        logger.info(f"Multi-line format found - Row {i+1}, Column {j+1}: `{cell_value}`")
                    
                    if has_multiple_spaces(cell_value):
                        special_cases["multiple_spaces"] += 1
                        logger.info(f"Multiple spaces found - Row {i+1}, Column {j+1}: `{cell_value}`")
                    
                    if has_x_pattern(cell_value):
                        special_cases["x_patterns"] += 1
                        logger.info(f"X pattern found - Row {i+1}, Column {j+1}: `{cell_value}`")
                    
                    if has_semicolon_list(cell_value):
                        special_cases["list_formats"] += 1
                        logger.info(f"Semicolon list found - Row {i+1}, Column {j+1}: `{cell_value}`")
                    
                    if has_exclusion(cell_value):
                        special_cases["exclusions"] += 1
                        logger.info(f"Exclusion pattern found - Row {i+1}, Column {j+1}: `{cell_value}`")
                    
                    if has_complex_list(cell_value):
                        special_cases["complex_lists"] += 1
                        logger.info(f"Complex list found - Row {i+1}, Column {j+1}: `{cell_value}`")
                    
                    # Thêm vào danh sách số dịch vụ
                    raw_service_numbers.append({
                        "row": i,
                        "col": j,
                        "text": cell_value
                    })
        
        # In thông tin tổng kết
        logger.info("\n=== KẾT QUẢ PHÂN TÍCH TRƯỜNG HỢP ĐẶC BIỆT ===")
        logger.info(f"Tổng số ô chứa thông tin dịch vụ: {total_service_cells}")
        logger.info("Special cases summary:")
        logger.info(f"- Multi-line formats: {special_cases['multi_line_formats']}")
        logger.info(f"- Multiple spaces: {special_cases['multiple_spaces']}")
        logger.info(f"- Sample patterns (x): {special_cases['x_patterns']}")
        logger.info(f"- List formats (semicolons): {special_cases['list_formats']}")
        logger.info(f"- Exclusions: {special_cases['exclusions']}")
        logger.info(f"- Complex lists: {special_cases['complex_lists']}")
        
        # In tất cả số dịch vụ đã nhận dạng
        logger.info("\n=== TẤT CẢ SỐ DỊCH VỤ ĐÃ NHẬN DẠNG ===")
        for item in raw_service_numbers:
            logger.info(f"Dòng {item['row']+1}, Cột {item['col']+1}: {item['text']}")
        
        return {
            "file_name": file_name,
            "service_numbers": raw_service_numbers,
            "special_cases": special_cases
        }
    
    except Exception as e:
        logger.error(f"Lỗi khi xử lý file {file_path}: {str(e)}")
        return {
            "file_name": os.path.basename(file_path),
            "error": str(e)
        }

def main():
    if len(sys.argv) < 2:
        logger.error("Vui lòng cung cấp đường dẫn đến file Excel")
        return
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        logger.error(f"File không tồn tại: {file_path}")
        return
    
    # Xử lý file
    analyze_special_cases(file_path)

if __name__ == "__main__":
    main() 