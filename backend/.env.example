# Environment
ENVIRONMENT=development

# Database
DATABASE_URL=**************************************/audit_call
POSTGRES_SERVER=db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_strong_production_password
POSTGRES_DB=audit_call_prod
POSTGRES_PORT=5432
# Redis
REDIS_URL=redis://redis:6379/0

# JWT
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://127.0.0.1:3000"]

# API
API_V1_STR=/api/v1

# Timezone
TZ=Asia/Ho_Chi_Minh 