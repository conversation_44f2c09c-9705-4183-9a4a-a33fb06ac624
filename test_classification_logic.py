"""
A minimal test script that directly implements the classification logic
to avoid circular import issues.
"""

import re
import os
from enum import Enum, auto

# Define a minimal TemplateType enum to match the original
class TemplateType(Enum):
    DSCD = auto()  # Đối soát cố định
    DSC = auto()   # Đối soát cước
    CKN = auto()   # C<PERSON>ớc kết nối
    DST_1800_1900 = auto()  # Đối soát tổng đài 1800/1900

def normalize_text(text):
    # <PERSON>yể<PERSON> về chữ thường
    text = text.lower()
    
    # Thay thế trực tiếp các ký tự tiếng Việt phổ biến
    vietnamese_chars = {
        'á': 'a', 'à': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ắ': 'a', 'ằ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ấ': 'a', 'ầ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'é': 'e', 'è': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ế': 'e', 'ề': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'í': 'i', 'ì': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ó': 'o', 'ò': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ố': 'o', 'ồ': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ớ': 'o', 'ờ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ú': 'u', 'ù': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ứ': 'u', 'ừ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ý': 'y', 'ỳ': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd'  # Quan trọng: Chuyển đổi đ thành d
    }
    
    for vietnamese, latin in vietnamese_chars.items():
        text = text.replace(vietnamese, latin)
    
    # Loại bỏ tất cả ký tự không phải a-z và 0-9 (bao gồm khoảng trắng)
    text = re.sub(r'[^a-z0-9]', '', text)
    
    return text

# Define search patterns
DSCD_SIMPLE_STRINGS = [
    "codinh", "cd_", "_cd", "_cd_", "dscd", "dscodinh", 
    "thuebao", "tbcodinh", "dscodinh",
    "doisoatcodinh", "cdinh",
    # Các mẫu đặc biệt
    "bidv", "doctorcheck", "shield" 
]

DSC_SIMPLE_STRINGS = [
    "cuoc", "doanhthu", "chitietcuocgoi", "itc", "dsc", "dscuoc",
    "giacuoc", "phi", "dthu"
]

# Function that implements the classification logic from your fixed code
def classify_from_filename(filename):
    basename = os.path.basename(filename)
    filename_lower = basename.lower()
    filename_norm = normalize_text(basename)
    
    print(f"DEBUG: filename_norm = {filename_norm}")
    
    # Kiểm tra chuỗi đặc trưng mạnh trước
    # Kiểm tra DSC (đối soát cước) trước nếu có từ "cuoc" trong tên file
    if "cuoc" in filename_norm or "cước" in filename_lower:
        for simple_str in DSC_SIMPLE_STRINGS:
            if simple_str in filename_norm:
                print(f"DEBUG: Matched DSC simple string: {simple_str}")
                return TemplateType.DSC
    
    # Kiểm tra DSCD
    for simple_str in DSCD_SIMPLE_STRINGS:
        print(f"DEBUG: Checking DSCD pattern: {simple_str}")
        # Kiểm tra cho chuỗi "cd" - phải là chuỗi riêng biệt hoặc được phân cách
        if simple_str == "cd":
            # Tìm "cd" như một từ riêng biệt hoặc trong các ngữ cảnh cụ thể
            patterns = [r'\bcd\b', r'_cd_', r'_cd\b', r'\bcd_']
            if any(re.search(pattern, filename_norm) for pattern in patterns):
                print(f"DEBUG: Matched cd as a whole word or in specific context")
                return TemplateType.DSCD
        elif simple_str in filename_norm:
            print(f"DEBUG: Matched DSCD simple string: {simple_str}")
            return TemplateType.DSCD
                
    # Kiểm tra DSC 
    for simple_str in DSC_SIMPLE_STRINGS:
        if simple_str in filename_norm:
            print(f"DEBUG: Matched DSC simple string: {simple_str}")
            return TemplateType.DSC
    
    return None

# Test files
test_files = [
    "Doi soat cuoc_HTC_DIGINET_T1_2025.xlsx",  # Should be DSC
    "DS cố định VNTEL_T1_2025.xlsx",          # Should be DSCD
    "Đối soát cước VNPT.xlsx",                # Should be DSC
    "File with DIGINET and HTC.xlsx"          # No clear type
]

# Run tests
print("=== Testing file classification logic with the fix ===\n")
for filename in test_files:
    result = classify_from_filename(filename)
    
    # Determine expected result based on simple checks
    expected = None
    if "cuoc" in normalize_text(filename) or "cước" in filename.lower():
        expected = TemplateType.DSC
    elif "codinh" in normalize_text(filename) or "định" in filename.lower():
        expected = TemplateType.DSCD
    
    print(f"\nFile: {filename}")
    print(f"Classification: {result}")
    
    if expected:
        print(f"Expected: {expected}")
        is_correct = result == expected
        print(f"Correct: {'✓' if is_correct else '✗'}")
    else:
        print("No clear expected classification")
    
    print("-" * 50) 