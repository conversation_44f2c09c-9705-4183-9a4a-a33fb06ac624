#!/bin/bash
# <PERSON>ript to restart Celery worker in case of Redis connection issues

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}AuditCall Celery Worker Restart Utility${NC}"
echo "----------------------------------------"

# Check if docker-compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: docker-compose is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "docker-compose.prod.yml" ]; then
    echo -e "${YELLOW}Warning: docker-compose.prod.yml not found in current directory.${NC}"
    echo -e "You should run this script from the project root directory."
    read -p "Continue anyway? (y/n): " CONTINUE
    if [ "$CONTINUE" != "y" ]; then
        exit 1
    fi
fi

echo -e "${YELLOW}Checking Celery worker status...${NC}"
WORKER_RUNNING=$(docker-compose -f docker-compose.prod.yml ps | grep celery_worker | grep Up | wc -l)

if [ "$WORKER_RUNNING" -eq 0 ]; then
    echo -e "${RED}Celery worker is not running. Starting it...${NC}"
    docker-compose -f docker-compose.prod.yml up -d celery_worker
    echo -e "${GREEN}Celery worker started.${NC}"
    exit 0
fi

echo -e "${YELLOW}Restarting Celery worker...${NC}"
echo -e "${BLUE}This will stop and start the Celery worker container.${NC}"
echo -e "${BLUE}Any running tasks will be interrupted and requeued.${NC}"

read -p "Are you sure you want to proceed? (y/n): " CONFIRM
if [ "$CONFIRM" != "y" ]; then
    echo -e "${YELLOW}Restart cancelled.${NC}"
    exit 0
fi

echo -e "${YELLOW}Stopping Celery worker...${NC}"
docker-compose -f docker-compose.prod.yml stop celery_worker

# Wait a moment to ensure it's fully stopped
sleep 2

echo -e "${YELLOW}Starting Celery worker...${NC}"
docker-compose -f docker-compose.prod.yml up -d celery_worker

echo -e "${GREEN}Celery worker has been restarted.${NC}"
echo -e "${BLUE}Checking worker logs...${NC}"

# Show recent logs
docker-compose -f docker-compose.prod.yml logs --tail=20 celery_worker

echo -e "${GREEN}Done!${NC}"
echo -e "${BLUE}You can check full logs with:${NC}"
echo -e "docker-compose -f docker-compose.prod.yml logs -f celery_worker"
