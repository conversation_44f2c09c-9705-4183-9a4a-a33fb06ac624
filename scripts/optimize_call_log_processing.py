import os
import logging
import time
import re
from typing import List, Dict, Any, Optional
from datetime import datetime

# --- <PERSON>ần c<PERSON>i đặt: pip install python-dotenv sqlalchemy psycopg2-binary ---
from dotenv import load_dotenv
from sqlalchemy import create_engine, extract, func
from sqlalchemy.orm import sessionmaker, Session, joinedload

# --- <PERSON><PERSON><PERSON> h<PERSON>nh Logging ---
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s')
log = logging.getLogger(__name__)
# ---

# --- <PERSON><PERSON><PERSON><PERSON> thực thi chính ---
if __name__ == "__main__":
    log.info("Starting call log processing optimization script...")
    script_start_time = time.time()

    # --- Load Environment Variables Explicitly ---
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    # Construct path to .env inside the 'backend' directory
    dotenv_path = os.path.join(project_root, 'backend', '.env')
    log.info(f"Calculated .env path: {dotenv_path}") # Print path for debugging
    found_dotenv = load_dotenv(dotenv_path=dotenv_path)
    if not found_dotenv:
         log.warning(f"load_dotenv failed to find or load file at: {dotenv_path}. Relying on system environment variables.")
    # --

    # --- Delay Backend Imports until after load_dotenv ---
    import sys
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

    try:
        # Import ONLY Models and Utils - Avoid database/config modules
        from backend.src.models.call_log import CallLog, NumberType, CallType
        from backend.src.models.dscd import DoiSoatCoDinh, DauSoDichVu
        from backend.src.models.template import ReconciliationTemplate
        from backend.src.models.partner import Partner
        from backend.src.utils.enhanced_phone_utils import matches_caller, determine_service_type
        from backend.src.utils.pricing_utils import get_pricing_rule, apply_billing_method
    except ImportError as e:
        log.exception(f"Failed to import backend modules. Check PYTHONPATH and ensure backend dependencies are installed in the venv: {e}")
        sys.exit(1)
    # --- End Backend Imports ---

    # --- Define Processing Function Locally ---
    def process_call_logs(db: Session, year: int, month: int, partner_id: int, dau_so_dich_vu_list: List[DauSoDichVu]) -> tuple[List[Dict[str, Any]], Dict[str, Any]]:
        log.info(f"Querying call logs for {year}-{month:02d}, caller_type=FIXED, call_type=OUT")
        start_query_time = time.time()

        # --- Query CallLog ---
        call_logs_query = db.query(CallLog).filter(
            extract('year', CallLog.begin_time) == year,
            extract('month', CallLog.begin_time) == month,
            CallLog.caller_type == NumberType.FIXED,
            CallLog.call_type == CallType.OUT
        )
        # TODO: Consider optimization: Add database index on (begin_time, caller_type, call_type)
        # TODO: Consider optimization: Query only necessary columns: .with_entities(CallLog.caller, CallLog.called, CallLog.duration, ...)
        # TODO: Consider optimization: Use stream_results() or yield_per() for very large datasets to reduce memory usage
        call_logs = call_logs_query.all()
        query_time = time.time() - start_query_time
        log.info(f"Found {len(call_logs)} relevant call logs in {query_time:.2f} seconds")

        if not call_logs:
            return [], {}

        # --- Log first 10 DauSoDichVu entries for debugging ---
        log.info("--- Sample DauSoDichVu entries from Template (Max 10) ---")
        for i, ds in enumerate(dau_so_dich_vu_list[:10]):
            log.info(f"  Template DauSo {i+1}: ID={ds.id}, Display='{ds.standardized_display}', Type={ds.dau_so_type}, Prefix='{ds.prefix}', Start='{ds.start_num_str}', End='{ds.end_num_str}'")
        log.info("------------------------------------------------------")
        # --- End Log DauSoDichVu ---

        log.info("Starting call log processing...")
        start_processing_time = time.time()

        results_aggregator = {}
        processed_call_count = 0
        skipped_no_match_count = 0
        skipped_no_service_type_count = 0
        skipped_no_pricing_count = 0
        skipped_invalid_caller_count = 0
        log_counter = 0 # < NEW counter for logging
        MAX_LOGS_TO_SHOW = 10 # < NEW limit for logging

        # --- Process Call Logs Loop ---
        for call_log in call_logs:
            log_counter += 1 # < NEW increment counter
            matched_dau_so_object = None

            caller_raw = call_log.caller
            caller_normalized = re.sub(r'[^\d]', '', caller_raw)

            # --- Log first few callers for debugging --- < NEW
            if log_counter <= MAX_LOGS_TO_SHOW:
                log.info(f"---> Processing Call Log #{log_counter}: Raw Caller='{caller_raw}', Normalized='{caller_normalized}'")
            # --- End Log Callers ---

            if not caller_normalized:
                skipped_invalid_caller_count += 1
                if log_counter <= MAX_LOGS_TO_SHOW:
                    log.info(f"     Skipping Call Log #{log_counter}: Invalid (empty normalized caller)") # < NEW log skip reason
                continue

            # --- Original Matching Logic (Keep for now, optimize later) ---
            match_found_for_this_log = False # < NEW flag
            for dau_so in dau_so_dich_vu_list:
                template_info = {
                    "type": dau_so.dau_so_type,
                    "standardized_display": dau_so.standardized_display,
                    "start_num_str": dau_so.start_num_str,
                    "end_num_str": dau_so.end_num_str,
                    "prefix": dau_so.prefix
                }

                # --- Log matching attempt for first few logs --- < NEW
                if log_counter <= MAX_LOGS_TO_SHOW:
                    log.debug(f"     Attempting match for '{caller_normalized}' against Template DauSo ID={dau_so.id} ('{dau_so.standardized_display}')") 
                # --- End Log Matching Attempt ---

                if matches_caller(caller_normalized, template_info):
                    matched_dau_so_object = dau_so
                    match_found_for_this_log = True # < NEW set flag
                    if log_counter <= MAX_LOGS_TO_SHOW:
                        log.info(f"     MATCH FOUND for Call Log #{log_counter} with Template DauSo ID={dau_so.id} ('{dau_so.standardized_display}')") 
                    break
            # --- End Matching Logic ---

            if not match_found_for_this_log: # Use flag instead of matched_dau_so_object check
                skipped_no_match_count += 1
                if log_counter <= MAX_LOGS_TO_SHOW:
                    log.info(f"     Skipping Call Log #{log_counter}: No match found against any template DauSo.") # < NEW log skip reason
                continue

            # TODO: Optimize determine_service_type function
            service_type = determine_service_type(call_log)
            if not service_type:
                skipped_no_service_type_count += 1
                if log_counter <= MAX_LOGS_TO_SHOW:
                    log.info(f"     Skipping Call Log #{log_counter}: Could not determine service type.") # < NEW log skip reason
                continue

            # TODO: Optimize get_pricing_rule function (Database query inside loop!)
            pricing_info = get_pricing_rule(db, partner_id, service_type)
            if not pricing_info:
                skipped_no_pricing_count += 1
                if log_counter <= MAX_LOGS_TO_SHOW:
                    log.info(f"     Skipping Call Log #{log_counter}: No pricing rule found for service type '{service_type}'.") # < NEW log skip reason
                continue

            price, billing_method = pricing_info
            # apply_billing_method is likely fast, but check if needed
            cost = apply_billing_method(call_log.duration, price, billing_method)

            processed_call_count += 1 # Increment only if everything passed

            grouping_key_display = matched_dau_so_object.standardized_display
            grouping_key = (grouping_key_display, service_type.value)

            if grouping_key not in results_aggregator:
                results_aggregator[grouping_key] = {
                    'dau_so_display': grouping_key_display,
                    'dau_so_id_from_template_data': matched_dau_so_object.id,
                    'service_type': service_type.value,
                    'total_duration_seconds': 0,
                    'total_cost_before_vat': 0.0,
                    'call_count': 0,
                    'dau_so_type': matched_dau_so_object.dau_so_type.value,
                    'dau_so_count_template': matched_dau_so_object.number_count,
                    'start_num_str': matched_dau_so_object.start_num_str,
                    'end_num_str': matched_dau_so_object.end_num_str,
                    'prefix': matched_dau_so_object.prefix,
                    'raw_dau_so_from_template': matched_dau_so_object.raw_dau_so
                }

            results_aggregator[grouping_key]['total_duration_seconds'] += call_log.duration
            results_aggregator[grouping_key]['total_cost_before_vat'] += cost
            results_aggregator[grouping_key]['call_count'] += 1
        # --- End Call Logs Loop ---

        processing_time = time.time() - start_processing_time
        log.info(f"Finished processing call logs loop in {processing_time:.2f} seconds.") # Renamed log message slightly

        # --- Log final counts --- < NEW location for this log
        log.info(f"Processing Summary: Queried={len(call_logs)}, Processed={processed_call_count}, Skipped (No Match)={skipped_no_match_count}, "
                 f"Skipped (No Service Type)={skipped_no_service_type_count}, Skipped (No Pricing)={skipped_no_pricing_count}, "
                 f"Skipped (Invalid Caller)={skipped_invalid_caller_count}")

        # --- Calculate VAT ---
        start_vat_time = time.time()
        VAT_RATE = 0.10 # TODO: Move VAT_RATE to config or fetch from DB if variable
        total_cost_all_services_before_vat = 0.0
        total_vat_all_services = 0.0

        for key in results_aggregator:
            cost_before_vat = results_aggregator[key]['total_cost_before_vat']
            vat_amount = cost_before_vat * VAT_RATE
            results_aggregator[key]['vat_amount'] = vat_amount
            results_aggregator[key]['total_cost_after_vat'] = cost_before_vat + vat_amount
            total_cost_all_services_before_vat += cost_before_vat
            total_vat_all_services += vat_amount

        total_cost_all_services_after_vat = total_cost_all_services_before_vat + total_vat_all_services
        vat_time = time.time() - start_vat_time
        log.info(f"Calculated VAT in {vat_time:.2f} seconds.")
        # --- End Calculate VAT ---

        processed_results_list = list(results_aggregator.values())
        summary_data = {
             "total_cost_before_vat": total_cost_all_services_before_vat,
             "total_vat": total_vat_all_services,
             "total_cost_after_vat": total_cost_all_services_after_vat,
             "total_call_logs_queried": len(call_logs),
             "processed_call_count": processed_call_count,
             "skipped_no_match_count": skipped_no_match_count,
             "skipped_no_service_type_count": skipped_no_service_type_count,
             "skipped_no_pricing_count": skipped_no_pricing_count,
             "skipped_invalid_caller_count": skipped_invalid_caller_count,
        }

        return processed_results_list, summary_data
    # --- End Local Function Definition ---

    # --- Database Setup (Directly in script) ---
    DATABASE_URL = os.getenv("DATABASE_URL")
    if not DATABASE_URL:
        log.error("DATABASE_URL not found in environment variables. Ensure .env file is loaded correctly.")
        sys.exit(1)

    db: Optional[Session] = None # Initialize db to None
    engine = None
    try:
        log.info(f"Connecting to database: {DATABASE_URL}") # Log the URL being used
        engine = create_engine(DATABASE_URL, pool_pre_ping=True)
        # Test connection explicitly
        with engine.connect() as connection:
            log.info("Database connection successful.")

        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        log.info("Database session established.")
    except Exception as e:
        log.exception(f"Failed to connect to database or create session: {e}")
        if db:
            db.close()
        if engine:
             engine.dispose()
        sys.exit(1)
    # --- End Database Setup ---

    # --- Test Parameters ---
    TEMPLATE_ID_TO_TEST = 31 # Use the ID processed by Celery (from logs)
    KY_DOI_SOAT_TO_TEST = "2025-01" # !!! IMPORTANT: Update this period if needed !!!
    # ---

    try:
        # --- Parse Period ---
        try:
            year_test, month_test = map(int, KY_DOI_SOAT_TO_TEST.split('-'))
            if not (1 <= month_test <= 12 and 1900 < year_test < 2100):
                raise ValueError("Invalid month or year")
        except ValueError:
            log.error(f"Invalid ky_doi_soat format: {KY_DOI_SOAT_TO_TEST}. Expected YYYY-MM.")
            if db: db.close()
            if engine: engine.dispose()
            sys.exit(1)
        # ---

        # --- Get Partner ID from Template ---
        # Query Template directly using the session 'db' created in this script
        template = db.query(ReconciliationTemplate).filter(ReconciliationTemplate.id == TEMPLATE_ID_TO_TEST).first()
        if not template:
            log.error(f"Template with ID {TEMPLATE_ID_TO_TEST} not found.")
            if db: db.close()
            if engine: engine.dispose()
            sys.exit(1)
        if not template.partner_id:
            log.error(f"Template {TEMPLATE_ID_TO_TEST} does not have an associated partner.")
            if db: db.close()
            if engine: engine.dispose()
            sys.exit(1)
        partner_id_test = template.partner_id
        log.info(f"Using Template ID: {TEMPLATE_ID_TO_TEST}, Partner ID: {partner_id_test}, Period: {KY_DOI_SOAT_TO_TEST}")
        # ---

        # --- Get DauSoDichVu list from Template Data ---
        # Query DoiSoatCoDinh directly using the session 'db' created in this script
        log.info(f"Fetching DauSoDichVu list for template {TEMPLATE_ID_TO_TEST}...")
        start_dau_so_time = time.time()
        doi_soat_template_data = db.query(DoiSoatCoDinh).options(
            joinedload(DoiSoatCoDinh.du_lieu)
        ).filter(
            DoiSoatCoDinh.template_id == TEMPLATE_ID_TO_TEST,
            DoiSoatCoDinh.is_template_data == True
        ).first()

        if not doi_soat_template_data or not doi_soat_template_data.du_lieu:
            log.error(f"Could not find DauSoDichVu data for template {TEMPLATE_ID_TO_TEST}")
            if db: db.close()
            if engine: engine.dispose()
            sys.exit(1)

        dau_so_list_test: List[DauSoDichVu] = doi_soat_template_data.du_lieu
        dau_so_time = time.time() - start_dau_so_time
        log.info(f"Retrieved {len(dau_so_list_test)} DauSoDichVu entries in {dau_so_time:.2f} seconds.")
        # ---

        # --- Call Processing Function & Measure Time ---
        log.info("Calling process_call_logs function...")
        processing_start_time = time.time()
        processed_results, summary_totals = process_call_logs(
            db=db,
            year=year_test,
            month=month_test,
            partner_id=partner_id_test,
            dau_so_dich_vu_list=dau_so_list_test
        )
        processing_total_time = time.time() - processing_start_time
        log.info(f"process_call_logs function finished in {processing_total_time:.2f} seconds.")
        # ---

        # --- Print Results ---
        log.info("--- Summary ---")
        for key, value in summary_totals.items():
             if isinstance(value, float):
                 log.info(f"{key.replace('_', ' ').title()}: {value:.2f}")
             else:
                 log.info(f"{key.replace('_', ' ').title()}: {value}")

        log.info(f"\\n--- All Processed Results ({len(processed_results)} items) ---")
        if processed_results:
            # Limit printing if too many results, uncomment if needed
            # MAX_RESULTS_TO_PRINT = 1000
            # if len(processed_results) > MAX_RESULTS_TO_PRINT:
            #     log.warning(f"Printing only the first {MAX_RESULTS_TO_PRINT} results out of {len(processed_results)}.")
            #     results_to_print = processed_results[:MAX_RESULTS_TO_PRINT]
            # else:
            results_to_print = processed_results

            for i, item in enumerate(results_to_print):
                log.info(f"Result {i+1}: {item}")
        else:
            log.info("No results were processed.")
        # ---

    except Exception as e:
        log.exception(f"An error occurred during script execution: {e}")
    finally:
        # --- Close Session and Dispose Engine ---
        if db:
            try:
                db.close()
                log.info("Database session closed.")
            except Exception as e_close:
                log.error(f"Error closing database session: {e_close}")
        if engine:
            try:
                engine.dispose()
                log.info("Database engine disposed.")
            except Exception as e_dispose:
                 log.error(f"Error disposing database engine: {e_dispose}")
        # ---
        script_total_time = time.time() - script_start_time
        log.info(f"Script finished in {script_total_time:.2f} seconds.") 