#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re

def normalize_text_old(text):
    """Phương pháp chuẩn hóa cũ sử dụng unicodedata.normalize"""
    import unicodedata
    # Chuyển về chữ thường
    text = text.lower()
    # Loại bỏ dấu
    text = unicodedata.normalize('NFKD', text).encode('ASCII', 'ignore').decode('ASCII')
    # Loại bỏ ký tự đặc biệt
    text = re.sub(r'[^\w\s]', ' ', text)
    # Thay thế nhiều khoảng trắng bằng một khoảng trắng
    text = re.sub(r'\s+', ' ', text).strip()
    return text

def normalize_text_new1(text):
    """Phương pháp chuẩn hóa mới sử dụng bảng mapping tiếng Việt"""
    # Chuyển về chữ thường
    text = text.lower()
    
    # Thay thế trực tiếp các ký tự tiếng Việt phổ biến
    vietnamese_chars = {
        'á': 'a', 'à': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ắ': 'a', 'ằ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ấ': 'a', 'ầ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'é': 'e', 'è': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ế': 'e', 'ề': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'í': 'i', 'ì': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ó': 'o', 'ò': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ố': 'o', 'ồ': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ớ': 'o', 'ờ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ú': 'u', 'ù': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ứ': 'u', 'ừ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ý': 'y', 'ỳ': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd'  # Quan trọng: Chuyển đổi đ thành d
    }
    
    for vietnamese, latin in vietnamese_chars.items():
        text = text.replace(vietnamese, latin)
    
    # Thêm khoảng trắng trước các ký tự đặc biệt để tránh mất từ khi loại bỏ
    text = re.sub(r'([^\w\s])', r' \1 ', text)
    # Loại bỏ ký tự đặc biệt nhưng giữ lại khoảng trắng
    text = re.sub(r'[^\w\s]', ' ', text)
    # Thay thế nhiều khoảng trắng bằng một khoảng trắng
    text = re.sub(r'\s+', ' ', text).strip()
    return text

def normalize_text_new2(text):
    """Phương pháp chuẩn hóa cải tiến để xử lý vấn đề khoảng trắng"""
    # Chuyển về chữ thường
    text = text.lower()
    
    # Thay thế trực tiếp các ký tự tiếng Việt phổ biến
    vietnamese_chars = {
        'á': 'a', 'à': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ắ': 'a', 'ằ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ấ': 'a', 'ầ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'é': 'e', 'è': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ế': 'e', 'ề': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'í': 'i', 'ì': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ó': 'o', 'ò': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ố': 'o', 'ồ': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ớ': 'o', 'ờ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ú': 'u', 'ù': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ứ': 'u', 'ừ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ý': 'y', 'ỳ': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd'  # Quan trọng: Chuyển đổi đ thành d
    }
    
    for vietnamese, latin in vietnamese_chars.items():
        text = text.replace(vietnamese, latin)
    
    # Loại bỏ ký tự đặc biệt, chỉ giữ lại chữ cái, số và khoảng trắng
    text = re.sub(r'[^a-z0-9\s]', ' ', text)
    # Thay thế nhiều khoảng trắng bằng một khoảng trắng
    text = re.sub(r'\s+', ' ', text).strip()
    return text

def main():
    # Các string để kiểm tra
    test_strings = [
        "DS cố định VNTEL_T1_2025.xlsx",
        "cố định",
        "co dinh",
        "ds cố định",
        "ds co dinh",
        "định",
        "ĐS CỐ ĐỊNH HITC T1.2025 (chốt).xlsx",
        "Đối soát cước cố định HTC - CGV T1-2025.xlsx"
    ]
    
    # Các chuỗi simple string trong phân loại
    search_strings = [
        "co dinh",
        "ds co dinh"
    ]
    
    print("=== So sánh các phương pháp chuẩn hóa ===\n")
    
    for test_str in test_strings:
        old = normalize_text_old(test_str)
        new1 = normalize_text_new1(test_str)
        new2 = normalize_text_new2(test_str)
        
        print(f"Chuỗi gốc: '{test_str}'")
        print(f"  Chuẩn hóa cũ: '{old}'")
        print(f"  Chuẩn hóa mới 1: '{new1}'")
        print(f"  Chuẩn hóa mới 2: '{new2}'")
        
        # Kiểm tra phát hiện
        for search in search_strings:
            search_norm = normalize_text_new2(search)
            detected_old = search in old
            detected_new1 = search in new1
            detected_new2 = search_norm in new2
            
            print(f"  Tìm kiếm '{search}' ('{search_norm}'):")
            print(f"     - Cũ: {detected_old}")
            print(f"     - Mới 1: {detected_new1}")
            print(f"     - Mới 2: {detected_new2}")
        
        print()
    
    print("=== Chi tiết các ký tự trong chuỗi ===")
    for test_str in ["cố định", "định"]:
        print(f"\nChuỗi gốc: '{test_str}'")
        print("Mã ký tự Unicode:")
        for i, c in enumerate(test_str):
            print(f"  Ký tự {i+1}: '{c}' - Mã Unicode: {ord(c)} - Hex: {hex(ord(c))}")
        
        # Sau khi chuẩn hóa
        norm = normalize_text_new2(test_str)
        print(f"Sau chuẩn hóa: '{norm}'")
        print("Mã ký tự Unicode sau chuẩn hóa:")
        for i, c in enumerate(norm):
            print(f"  Ký tự {i+1}: '{c}' - Mã Unicode: {ord(c)} - Hex: {hex(ord(c))}")

if __name__ == "__main__":
    main() 