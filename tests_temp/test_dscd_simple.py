#!/usr/bin/env python3
import re
import os
from typing import Optional, Tuple

# Các mẫu regex để nhận dạng file DSCD
DSCD_PATTERNS = [
    r'(ĐS\s*CĐ|ĐS\s*CỐ\s*ĐỊNH|DS\s*cố\s*định|Đ<PERSON>i\s*soát\s*cước\s*cố\s*định|Doi\s*soat\s*so\s*co\s*dinh).+\.(xlsx|xls)$',
    r'(DS\s*cuoc\s*co\s*dinh).+\.(xlsx|xls)$',
    r'(BIDV|Doctor-check|Shield).+\.(xlsx|xls)$'
]

def is_dscd_file(filename: str) -> bool:
    """
    Kiểm tra xem file có phải là file đối soát cố định không
    """
    filename = os.path.basename(filename)
    for pattern in DSCD_PATTERNS:
        if re.search(pattern, filename, re.IGNORECASE):
            return True
    return False

def extract_month_year(filename: str) -> <PERSON><PERSON>[Optional[int], Optional[int]]:
    """
    Trích xuất tháng và năm từ tên file
    """
    filename = os.path.basename(filename)
    
    # Mẫu 1: T1_2025, Tháng 1-2025, Tháng 1_2025
    pattern1 = r'(T|Th[áa]ng)\s*(\d{1,2})[-_\.\s](\d{4})'
    match = re.search(pattern1, filename, re.IGNORECASE)
    if match:
        return int(match.group(2)), int(match.group(3))
    
    # Mẫu 2: 012025, 202501 (năm tháng hoặc tháng năm)
    pattern2 = r'(\d{2})(\d{4})'
    match = re.search(pattern2, filename)
    if match:
        if int(match.group(1)) <= 12:
            return int(match.group(1)), int(match.group(2))
        else:
            # Định dạng năm trước tháng sau
            year_str = match.group(1) + match.group(2)[:2]
            month_str = match.group(2)[2:]
            if int(month_str) <= 12:
                return int(month_str), int(year_str)
    
    # Mẫu 3: thang 1_2025
    pattern3 = r'thang\s*(\d{1,2})[-_\.\s](\d{4})'
    match = re.search(pattern3, filename, re.IGNORECASE)
    if match:
        return int(match.group(1)), int(match.group(2))
    
    return None, None

def extract_partner_name(filename: str) -> Optional[str]:
    """
    Trích xuất tên đối tác từ tên file
    """
    filename = os.path.basename(filename)
    
    # Trường hợp đặc biệt
    special_cases = {
        "THÁI BÌNH DƯƠNG": "THÁI BÌNH DƯƠNG",
        "Realtime": "Realtime",
        "BIDV": "BIDV",
        "Doctor-check": "Doctor-check",
        "Shield VN": "Shield VN",
        "HITC": "HITC"
    }
    
    # Kiểm tra các trường hợp đặc biệt
    for key, value in special_cases.items():
        if key in filename:
            return value
    
    # Mẫu: ĐS CĐ_PARTNER_Tháng, DS cố định PARTNER_T
    patterns = [
        r'ĐS\s*CĐ_([A-Za-z0-9\s]+)_',
        r'DS\s*cố\s*định\s+([A-Za-z0-9\s]+)_',
        r'Đối\s*soát\s*cước\s*cố\s*định\s+HTC\s*-\s*([A-Za-z0-9\s]+)\s+',
        r'Doi\s*soat\s*so\s*co\s*dinh\s+([A-Za-z0-9\s]+)_',
        r'DS\s*cuoc\s*co\s*dinh\s+([A-Za-z0-9\s]+)\s+thang'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename, re.IGNORECASE)
        if match:
            return match.group(1).strip()
    
    return None

# Danh sách các file cần kiểm tra
files = [
    'BIDV Hà Tây Tháng 1-2025.xlsx',
    'Doctor-check-Tháng 1-2025.xlsx',
    'Đối soát cước cố định HTC - CGV T1-2025.xlsx',
    'Doi soat so co dinh HGC_T1_2025 (1).xlsx',
    'Doi soat so co dinh HGC_T1_2025.xlsx',
    'ĐS CĐ_AVITOUR_Tháng 1-2025.xlsx',
    'ĐS CĐ_HALI_Tháng 1_2025.xlsx',
    'ĐS CĐ_MDC_Tháng 1-2025.xlsx',
    'DS cố định AIC_T2_2025.xlsx',
    'ĐS CỐ ĐỊNH HITC T1.2025 (chốt).xlsx',
    'DS cố định ITS_T1_2025.xlsx',
    'DS cố định Realtime T1.2025.xlsx',
    'DS cố định THÁI BÌNH DƯƠNG_T1_2025.xlsx',
    'DS cố định VNTEL_T1_2025.xlsx',
    'DS cuoc co dinh Vietdgtel thang 1_2025.xlsx',
    'Shield VN T1.2025.xlsx'
]

# Kiểm tra
print("=== Kiểm tra nhận dạng file DSCD ===\n")
print(f"Tổng số file: {len(files)}\n")

# Kết quả nhận dạng
results = []
for file in files:
    is_dscd = is_dscd_file(file)
    month, year = extract_month_year(file)
    partner = extract_partner_name(file)
    
    results.append({
        "file": file,
        "is_dscd": is_dscd,
        "month": month,
        "year": year,
        "partner": partner
    })
    
    print(f"File: {file}")
    print(f"  Là file DSCD: {is_dscd}")
    print(f"  Tháng/Năm: {month}/{year}")
    print(f"  Đối tác: {partner}")
    print()

# Tóm tắt kết quả
print("\n=== Tóm tắt kết quả ===")
print(f"Tổng số file DSCD: {sum(1 for r in results if r['is_dscd'])}")
print(f"Số file có tháng/năm: {sum(1 for r in results if r['month'] is not None)}")
print(f"Số file có tên đối tác: {sum(1 for r in results if r['partner'] is not None)}")

# Những file cần kiểm tra lại
print("\n=== File cần kiểm tra lại ===")
for r in results:
    issues = []
    if not r["is_dscd"]:
        issues.append("Không nhận dạng là DSCD")
    if r["month"] is None:
        issues.append("Không tìm thấy tháng/năm")
    if r["partner"] is None:
        issues.append("Không tìm thấy đối tác")
        
    if issues:
        print(f"File: {r['file']}")
        for issue in issues:
            print(f"  - {issue}")
        print() 