#!/usr/bin/env python3
import re
import os
import pandas as pd
from typing import Optional, Tuple, List, Dict, Any
import sys
import csv

def extract_phone_numbers_from_excel(file_path: str) -> List[str]:
    """
    Trích xuất danh sách số điện thoại từ file đối soát cố định Excel
    
    Args:
        file_path: Đường dẫn đến file Excel đối soát cố định
        
    Returns:
        <PERSON>h sách các số điện thoại
    """
    try:
        # Đọc file Excel
        print(f"Đang đọc file: {file_path}")
        df = pd.read_excel(file_path)
        
        # Tìm cột chứa số điện thoại (thường ở cột thứ 2)
        column_names = df.columns.tolist()
        print(f"Các cột trong file: {column_names}")
        
        # Tìm cột chứa "ĐẦU SỐ" hoặc "SỐ THUÊ BAO" hoặc tương tự
        dau_so_column = None
        phone_column_keywords = ["ĐẦU SỐ", "DAU SO", "SỐ THUÊ BAO", "SO THUE BAO", "THUÊ BAO", "THUE BAO", 
                               "SỐ ĐIỆN THOẠI", "SO DIEN THOAI", "ĐIỆN THOẠI", "DIEN THOAI"]
        
        for col in column_names:
            col_upper = str(col).upper()
            if any(keyword in col_upper for keyword in phone_column_keywords):
                dau_so_column = col
                print(f"Tìm thấy cột chứa số điện thoại: {col}")
                break
                
        # Nếu không tìm thấy bằng tên cột, kiểm tra dữ liệu trong từng cột
        if dau_so_column is None:
            print("Không tìm thấy cột theo tên, kiểm tra dữ liệu...")
            
            # Kiểm tra dữ liệu mẫu từ mỗi cột
            for col in df.columns:
                # Lấy giá trị không phải NaN để kiểm tra
                sample_values = df[col].dropna().astype(str).head(20).tolist()
                
                # Kiểm tra xem các giá trị có phù hợp với mẫu số điện thoại không
                phone_pattern_matches = sum(1 for val in sample_values 
                                          if re.search(r'(\d{2,}\.|\d{3,}[-\s]|\d{8,})', str(val)))
                
                # Nếu có ít nhất 50% giá trị khớp với mẫu số điện thoại
                if phone_pattern_matches >= min(1, len(sample_values) / 2):
                    dau_so_column = col
                    print(f"Phát hiện cột chứa số điện thoại từ dữ liệu: {col}")
                    break
         
        # Nếu vẫn không tìm thấy, dùng cột mặc định
        if dau_so_column is None and len(column_names) >= 2:
            dau_so_column = column_names[1]  # Lấy cột thứ 2 nếu không tìm thấy
            print(f"Sử dụng cột mặc định: {dau_so_column}")
        
        # Kiểm tra dữ liệu 10 dòng đầu tiên
        print("\nDữ liệu 10 dòng đầu tiên:")
        try:
            print(df.head(10))
        except:
            print("Không thể hiển thị dữ liệu")
        
        if dau_so_column is None:
            print("Không tìm thấy cột chứa số điện thoại!")
            return []
        
        # Lấy và lọc danh sách số điện thoại (loại bỏ các giá trị không phải số điện thoại)
        raw_values = df[dau_so_column].astype(str).tolist()
        phone_numbers = []
        
        for val in raw_values:
            val = str(val).strip()
            # Bỏ qua giá trị nan, None, chuỗi rỗng, và giá trị không phải số điện thoại
            if val and val.lower() not in ('nan', 'none', '', 'đầu số dịch vụ') and re.search(r'\d{3,}', val):
                phone_numbers.append(val)
        
        return phone_numbers
    
    except Exception as e:
        print(f"Lỗi khi trích xuất số điện thoại: {str(e)}")
        return []

def normalize_phone_number(phone: str) -> str:
    """
    Chuẩn hóa số điện thoại, loại bỏ kí tự không phải số
    """
    return re.sub(r'[^\d]', '', phone)

def filter_phone_numbers(phones: List[str], prefix: Optional[str] = None) -> List[str]:
    """
    Lọc số điện thoại theo tiền tố
    """
    normalized_phones = [normalize_phone_number(phone) for phone in phones]
    
    if prefix:
        return [phone for phone in normalized_phones if phone.startswith(prefix)]
    
    return normalized_phones

def find_excel_files(directory: str) -> List[str]:
    """
    Tìm tất cả các file Excel trong thư mục
    """
    excel_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith(('.xlsx', '.xls')) and not file.startswith('~$'):
                excel_files.append(os.path.join(root, file))
    return excel_files

# Tìm một file Excel trong thư mục DSCD để test
def get_sample_excel_file():
    directories = [
        'backend/BBDS/DSCD',
        'BBDS/DSCD'
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            excel_files = find_excel_files(directory)
            if excel_files:
                # Ưu tiên file có kích thước nhỏ
                sorted_files = sorted(excel_files, key=lambda f: os.path.getsize(f))
                if sorted_files:
                    # Lấy file Excel đầu tiên có kích thước < 1MB
                    for file_path in sorted_files:
                        if os.path.getsize(file_path) < 1_000_000:  # < 1MB
                            return file_path
                    # Nếu không tìm thấy file < 1MB, lấy file đầu tiên
                    return sorted_files[0]
    
    # Nếu không tìm thấy, trả về None
    return None

def analyze_excel_file(file_path: str) -> None:
    """
    Phân tích file Excel để trích xuất và thống kê số điện thoại
    """
    print(f"Phân tích file: {file_path}")
    print("\n=== Trích xuất số điện thoại từ file Excel ===\n")
    
    # Trích xuất số điện thoại
    phones = extract_phone_numbers_from_excel(file_path)
    
    print(f"\nSố lượng số điện thoại đã trích xuất: {len(phones)}")
    if phones:
        print("Mẫu số điện thoại (tối đa 10 số):")
        for phone in phones[:10]:
            print(f"  {phone}")
        
        # Chuẩn hóa số điện thoại
        print("\n=== Chuẩn hóa số điện thoại ===")
        normalized_phones = filter_phone_numbers(phones)
        print("Mẫu số điện thoại sau khi chuẩn hóa (tối đa 10 số):")
        for phone in normalized_phones[:10]:
            print(f"  {phone}")
        
        # Thống kê theo tiền tố
        print("\n=== Thống kê số điện thoại theo tiền tố ===")
        prefix_counts = {}
        for phone in normalized_phones:
            if len(phone) >= 3:
                prefix = phone[:3]  # Lấy 3 số đầu tiên
                prefix_counts[prefix] = prefix_counts.get(prefix, 0) + 1
        
        # Sắp xếp theo số lượng giảm dần
        sorted_prefixes = sorted(prefix_counts.items(), key=lambda x: x[1], reverse=True)
        for prefix, count in sorted_prefixes:
            print(f"  Tiền tố {prefix}: {count} số")
        
        # Thống kê theo độ dài số điện thoại
        print("\n=== Thống kê theo độ dài số điện thoại ===")
        length_counts = {}
        for phone in normalized_phones:
            length = len(phone)
            length_counts[length] = length_counts.get(length, 0) + 1
        
        for length, count in sorted(length_counts.items()):
            print(f"  Độ dài {length} số: {count} số điện thoại")
        
        # Lưu trữ số điện thoại vào file
        base_filename = os.path.splitext(os.path.basename(file_path))[0]
        output_dir = os.path.dirname(file_path)
        
        # Lưu file text đơn giản
        txt_output_file = os.path.join(output_dir, f"{base_filename}_so_dien_thoai.txt")
        with open(txt_output_file, 'w', encoding='utf-8') as f:
            for phone in normalized_phones:
                f.write(f"{phone}\n")
        print(f"\nĐã lưu {len(normalized_phones)} số điện thoại vào file: {txt_output_file}")
        
        # Lưu file CSV với thông tin chi tiết
        csv_output_file = os.path.join(output_dir, f"{base_filename}_so_dien_thoai.csv")
        with open(csv_output_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            # Viết header
            writer.writerow(['Số gốc', 'Số chuẩn hóa', 'Tiền tố', 'Độ dài'])
            
            # Viết dữ liệu
            for i, normalized in enumerate(normalized_phones):
                original = phones[i] if i < len(phones) else ''
                prefix = normalized[:3] if len(normalized) >= 3 else ''
                writer.writerow([original, normalized, prefix, len(normalized)])
        
        print(f"Đã lưu thông tin chi tiết vào file CSV: {csv_output_file}")
        
        return True
    else:
        print("Không tìm thấy số điện thoại nào!")
        return False

def main():
    # Kiểm tra nếu có tham số dòng lệnh chỉ định file Excel
    if len(sys.argv) > 1 and os.path.exists(sys.argv[1]):
        excel_file = sys.argv[1]
        print(f"Sử dụng file Excel từ tham số: {excel_file}")
        analyze_excel_file(excel_file)
    else:
        # Tìm file Excel mẫu
        excel_file = get_sample_excel_file()
        
        if excel_file:
            print(f"Tìm thấy file Excel: {excel_file}")
            result = analyze_excel_file(excel_file)
            
            # Nếu file đầu tiên không có số điện thoại, thử với file thứ hai
            if not result and os.path.exists('backend/BBDS/DSCD'):
                excel_files = find_excel_files('backend/BBDS/DSCD')
                
                if len(excel_files) > 1:
                    print("\n\nThử với file Excel khác...")
                    excel_file = excel_files[1]  # Thử với file thứ hai
                    analyze_excel_file(excel_file)
        else:
            print("Không tìm thấy file Excel nào trong thư mục DSCD!")
            print("Sử dụng: python test_dscd_extract_excel.py [đường_dẫn_file_excel]")

if __name__ == "__main__":
    main() 