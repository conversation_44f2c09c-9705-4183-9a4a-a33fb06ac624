#!/usr/bin/env python
"""
Script để kiểm tra các chức năng xử lý file đối soát cước
"""
import os
import sys
import json
import re
import pandas as pd
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Set

# Không import trực tiếp, copy code cần thiết

# Các mẫu regex để nhận dạng file đối soát cước
DSC_PATTERNS = [
    r'Doi\s*soat\s*cuoc',
    r'DoiSoatCuoc',
    r'Đối\s*soát\s*cước',
    r'Doanh\s*thu\s*cước',
]

# Keywords để tìm cột số điện thoại
PHONE_COLUMN_KEYWORDS = [
    "ĐẦU SỐ", "DAU SO", "SỐ ĐIỆN THOẠI", "SO DIEN THOAI", 
    "SĐT", "SDT", "HOTLINE", "PHONE", "TÊN ĐẦU SỐ", "TEN DAU SO"
]

def is_dsc_file(filename: str) -> bool:
    """
    Kiểm tra xem file có phải là file đối soát cước không
    
    Args:
        filename: Tên file cần kiểm tra
        
    Returns:
        True nếu là file đối soát cước, False nếu không phải
    """
    # Chuyển filename thành lowercase để so sánh không phân biệt chữ hoa/thường
    filename_lower = filename.lower()
    
    # Kiểm tra các mẫu regex
    for pattern in DSC_PATTERNS:
        if re.search(pattern, filename, re.IGNORECASE):
            return True
    
    return False

def extract_month_year(filename: str):
    """
    Trích xuất thông tin tháng và năm từ tên file
    
    Args:
        filename: Tên file đối soát cước
        
    Returns:
        Tuple chứa (tháng, năm) hoặc (None, None) nếu không trích xuất được
    """
    filename = os.path.basename(filename)
    
    # Mẫu "T1_2025" hoặc "T1/2025" hoặc "thang 1 2025"
    pattern1 = r'[Tt]h[aá]ng\s*(\d{1,2})[\s\/_\-]*(\d{4})'
    pattern2 = r'[Tt](\d{1,2})[\s\/_\-]*(\d{4})'
    pattern3 = r'(\d{1,2})[\s\/_\-](\d{4})'
    pattern4 = r'(\d{2})_(\d{4})'
    pattern5 = r'_(\d{2})_(\d{4})'
    
    # Thử từng mẫu
    for pattern in [pattern1, pattern2, pattern3, pattern4, pattern5]:
        matches = re.search(pattern, filename)
        if matches:
            month, year = matches.groups()
            return month, year
    
    return None, None
    
def extract_partners(filename: str):
    """
    Trích xuất thông tin đối tác từ tên file
    
    Args:
        filename: Tên file đối soát cước
        
    Returns:
        Tuple chứa (từ_mạng, đến_đối_tác) hoặc (None, None) nếu không trích xuất được
    """
    filename = os.path.basename(filename)
    
    # Mẫu HTC_TO_ITEL hoặc HTC TO ITEL
    pattern_to = r'([A-Za-z0-9]+)[_\-\s]+[Tt][Oo][_\-\s]+([A-Za-z0-9]+)'
    matches = re.search(pattern_to, filename)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Mẫu cụ thể: Doi soat cuoc_HTC_ITEL_T1_2025.xlsx
    pattern_dsc = r'Doi\s*soat\s*cuoc[_\-\s]+([A-Za-z0-9]+)[_\-\s]+([A-Za-z0-9]+)'
    matches = re.search(pattern_dsc, filename, re.IGNORECASE)
    if matches:
        return matches.group(1), matches.group(2)
    
    # Kiểm tra mẫu phổ biến: HTC_<ĐỐI TÁC>_<THÔNG TIN KHÁC>
    pattern_partner = r'([A-Za-z0-9]+)[_\-]([A-Za-z0-9]+)[_\-]'
    matches = re.search(pattern_partner, filename)
    if matches:
        partner1, partner2 = matches.groups()
        
        # Loại bỏ số và các thông tin tháng năm
        if re.match(r'\d+', partner2) or re.match(r'[tT]\d+', partner2):
            # Thử tìm partner khác trong tên file
            for word in re.findall(r'[A-Za-z]{3,}', filename):
                if word.upper() not in ['DOI', 'SOAT', 'CUOC', 'THANG', 'NAM', 'DOANH', 'THU', 'REPORT', 'FILE']:
                    if word.upper() != partner1.upper():
                        return partner1, word
        
        # Thường HTC là mạng nguồn
        if partner1.upper() == "HTC":
            return partner1, partner2
        elif partner2.upper() == "HTC":
            return partner2, partner1
        
        # Nếu không có HTC, giả định partner1 là mạng nguồn
        return partner1, partner2
    
    # Thử cho các trường hợp tên file phức tạp hơn
    words = re.findall(r'[A-Za-z0-9]+', filename)
    htc_idx = None
    
    # Tìm HTC trong danh sách từ
    for i, word in enumerate(words):
        if word.upper() == "HTC":
            htc_idx = i
            break
    
    if htc_idx is not None:
        # Ưu tiên từ phía sau HTC
        if htc_idx + 1 < len(words):
            next_word = words[htc_idx + 1]
            # Nếu từ tiếp theo dường như là một đối tác (không phải số/tháng/lệnh)
            if (not re.match(r'^\d+$', next_word) and 
                next_word.upper() not in ['TO', 'T', 'THANG', 'NAM', 'CUOC', 'SOAT']):
                return 'HTC', next_word
        
        # Nếu không tìm thấy đối tác phù hợp sau HTC, tìm các từ khác
        for word in words:
            # Tìm từ dài ít nhất 3 ký tự, không phải là từ phổ biến,
            # không phải là HTC, và không phải là số
            if (len(word) >= 3 and 
                word.upper() not in ['DOI', 'SOAT', 'CUOC', 'THANG', 'NAM', 'DOANH', 'THU', 'HTC', 'REPORT', 'FILE'] and
                not re.match(r'^\d+$', word) and
                not re.match(r'^[tT]\d+$', word)):
                return 'HTC', word
    
    # Nếu không tìm được đối tác phù hợp
    return None, None

def normalize_phone_number(phone: str) -> str:
    """
    Chuẩn hóa số điện thoại
    
    Args:
        phone: Số điện thoại cần chuẩn hóa
        
    Returns:
        Số điện thoại đã chuẩn hóa
    """
    if not phone or not isinstance(phone, str):
        return ""
    
    # Loại bỏ các ký tự không phải số
    normalized = re.sub(r'[^\d]', '', phone)
    
    # Loại bỏ mã quốc gia 84 ở đầu nếu có và thêm số 0
    if normalized.startswith('84') and len(normalized) >= 10:
        normalized = '0' + normalized[2:]
    
    return normalized

def extract_phone_numbers_from_excel(file_path: str) -> List[str]:
    """
    Trích xuất số điện thoại từ file Excel
    
    Args:
        file_path: Đường dẫn đến file Excel
        
    Returns:
        Danh sách các số điện thoại đã trích xuất
    """
    try:
        # Đọc file Excel
        df = pd.read_excel(file_path, header=None)
        
        phone_numbers = []
        phone_column_idx = None
        
        # Tìm cột chứa số điện thoại
        for row_idx in range(min(10, df.shape[0])):  # Chỉ kiểm tra 10 dòng đầu
            for col_idx in range(df.shape[1]):
                cell_value = str(df.iloc[row_idx, col_idx]) if not pd.isna(df.iloc[row_idx, col_idx]) else ""
                cell_upper = cell_value.upper()
                
                # Kiểm tra từ khóa trong tên cột
                if any(keyword in cell_upper for keyword in PHONE_COLUMN_KEYWORDS):
                    phone_column_idx = col_idx
                    print(f"Đã tìm thấy cột số điện thoại tại cột {col_idx+1}: '{cell_value}'")
                    break
            
            if phone_column_idx is not None:
                break
        
        # Nếu không tìm thấy cột theo từ khóa, thử tìm cột có định dạng giống số điện thoại
        if phone_column_idx is None:
            print("Không tìm thấy cột số điện thoại bằng từ khóa, đang tìm theo mẫu...")
            
            # Mẫu số điện thoại Việt Nam
            phone_pattern = re.compile(r'(0|\+84|84)?[35789]\d{8}')
            
            max_matches = 0
            for col_idx in range(df.shape[1]):
                matches = 0
                for row_idx in range(10, min(30, df.shape[0])):  # Kiểm tra từ dòng 10 đến 30
                    cell_value = str(df.iloc[row_idx, col_idx]) if not pd.isna(df.iloc[row_idx, col_idx]) else ""
                    if phone_pattern.search(cell_value):
                        matches += 1
                
                if matches > max_matches:
                    max_matches = matches
                    phone_column_idx = col_idx
            
            if max_matches > 0:
                print(f"Tìm thấy cột số điện thoại theo mẫu tại cột {phone_column_idx+1} với {max_matches} kết quả")
        
        # Nếu vẫn không tìm thấy, sử dụng cột thứ 2 làm mặc định (thường là cột đầu số)
        if phone_column_idx is None:
            phone_column_idx = 1
            print(f"Không tìm thấy cột số điện thoại, sử dụng cột mặc định (cột {phone_column_idx+1})")
        
        # Lặp qua các dòng để trích xuất số điện thoại
        for row_idx in range(df.shape[0]):
            if pd.isna(df.iloc[row_idx, phone_column_idx]):
                continue
                
            cell_value = str(df.iloc[row_idx, phone_column_idx])
            
            # Kiểm tra nếu giá trị là số điện thoại hợp lệ
            normalized = normalize_phone_number(cell_value)
            if normalized and len(normalized) >= 4:  # Số điện thoại có ít nhất 4 chữ số
                phone_numbers.append(normalized)
        
        print(f"Đã trích xuất {len(phone_numbers)} số điện thoại từ file")
        return phone_numbers
    
    except Exception as e:
        print(f"Lỗi khi trích xuất số điện thoại: {str(e)}")
        return []

def print_section(title):
    """In tiêu đề phần"""
    print(f"\n=== {title} ===\n")

def test_is_dsc_file():
    """Kiểm tra chức năng nhận dạng file đối soát cước"""
    print_section("Kiểm tra nhận dạng file đối soát cước")
    
    test_files = [
        'Doi soat cuoc_HTC_ITEL_T1_2025.xlsx',
        'Đối soát cước HTC-CGV T1/2025.xlsx',
        'DoiSoatCuoc_HTC_THAISON_01_2025.xlsx',
        'Doanh thu cước HTC_VIETDIGITAL_thang 1 2025.xlsx',
        'File_khong_phai_doi_soat_cuoc.xlsx',
        'DSCD_HTC_ITEL_T1_2025.xlsx',
        'Report_doanh_thu_call_center.xlsx'
    ]
    
    for file in test_files:
        is_dsc = is_dsc_file(file)
        print(f"File: {file}")
        print(f"  Là file đối soát cước: {is_dsc}")
        print()

def test_extract_month_year():
    """Kiểm tra chức năng trích xuất tháng năm"""
    print_section("Kiểm tra trích xuất tháng/năm")
    
    test_files = [
        'Doi soat cuoc_HTC_ITEL_T1_2025.xlsx',
        'Đối soát cước HTC-CGV T2/2025.xlsx',
        'DoiSoatCuoc_HTC_THAISON_01_2025.xlsx',
        'Doanh thu cước HTC_VIETDIGITAL_thang 1_2025.xlsx',
        'Doi soat cuoc_HTC_VADCOM_thang 3 nam 2025.xlsx',
        'HTC_ITEL_1-2025_DoiSoatCuoc.xlsx',
        'Report_12_2025.xlsx'
    ]
    
    for file in test_files:
        month, year = extract_month_year(file)
        print(f"File: {file}")
        print(f"  Tháng: {month}")
        print(f"  Năm: {year}")
        print()

def test_extract_partners():
    """Kiểm tra chức năng trích xuất tên đối tác"""
    print_section("Kiểm tra trích xuất đối tác")
    
    test_files = [
        'Doi soat cuoc_HTC_ITEL_T1_2025.xlsx',
        'Đối soát cước HTC-CGV T2/2025.xlsx',
        'DoiSoatCuoc_HTC_THAISON_01_2025.xlsx',
        'HTC_TO_VIETDIGITAL_thang 1_2025.xlsx',
        'Doi soat cuoc_HTC_VADCOM_thang 3 nam 2025.xlsx',
        'HTC_DIGINET_1-2025_DoiSoatCuoc.xlsx',
        'Report_12_2025.xlsx'
    ]
    
    for file in test_files:
        tu_mang, den_doi_tac = extract_partners(file)
        print(f"File: {file}")
        print(f"  Từ mạng: {tu_mang}")
        print(f"  Đến đối tác: {den_doi_tac}")
        print()

def test_extract_phone_numbers():
    """Kiểm tra chức năng trích xuất số điện thoại"""
    print_section("Kiểm tra trích xuất số điện thoại")
    
    # Tìm một file trong thư mục Doi_soat_cuoc
    dsc_dirs = [
        'backend/BBDS/Doi_soat_cuoc',
        'BBDS/Doi_soat_cuoc'
    ]
    
    file_path = None
    for dir_path in dsc_dirs:
        if os.path.exists(dir_path):
            files = [f for f in os.listdir(dir_path) if f.endswith('.xlsx') and not f.startswith('~$')]
            if files:
                file_path = os.path.join(dir_path, files[0])
                break
    
    if not file_path:
        print("Không tìm thấy file đối soát cước để kiểm tra!")
        return
    
    try:
        print(f"Đang trích xuất số điện thoại từ file: {file_path}")
        phone_numbers = extract_phone_numbers_from_excel(file_path)
        
        # Hiển thị kết quả
        if phone_numbers:
            print(f"\nĐã tìm thấy {len(phone_numbers)} số điện thoại:")
            
            # Phân loại số điện thoại theo đầu số
            prefix_counts = {}
            for phone in phone_numbers:
                prefix = phone[:4] if len(phone) >= 4 else phone
                prefix_counts[prefix] = prefix_counts.get(prefix, 0) + 1
            
            # Hiển thị thống kê theo đầu số
            print("\nThống kê theo đầu số:")
            for prefix, count in sorted(prefix_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"  Đầu số {prefix}: {count} số")
            
            # Hiển thị mẫu số điện thoại
            print("\nMẫu số điện thoại:")
            for i, phone in enumerate(phone_numbers[:10]):
                print(f"  {i+1}. {phone}")
            
            if len(phone_numbers) > 10:
                print(f"  ... và {len(phone_numbers) - 10} số khác")
        else:
            print("Không tìm thấy số điện thoại nào trong file!")
        
    except Exception as e:
        print(f"Lỗi khi trích xuất số điện thoại: {str(e)}")

def test_process_dsc_file():
    """Kiểm tra chức năng xử lý file đối soát cước đầy đủ"""
    print_section("Kiểm tra xử lý file đối soát cước")
    
    # Tìm một file trong thư mục Doi_soat_cuoc
    dsc_dirs = [
        'backend/BBDS/Doi_soat_cuoc',
        'BBDS/Doi_soat_cuoc'
    ]
    
    file_path = None
    for dir_path in dsc_dirs:
        if os.path.exists(dir_path):
            files = [f for f in os.listdir(dir_path) if f.endswith('.xlsx') and not f.startswith('~$')]
            if files:
                file_path = os.path.join(dir_path, files[0])
                break
    
    if not file_path:
        print("Không tìm thấy file đối soát cước để kiểm tra!")
        return
    
    try:
        print(f"Đang xử lý file: {file_path}")
        print("Chức năng process_dsc_file tạm thời bị bỏ qua")
        
    except Exception as e:
        print(f"Lỗi khi xử lý file: {str(e)}")

def main():
    """Hàm chính của chương trình"""
    test_is_dsc_file()
    test_extract_month_year()
    test_extract_partners()
    test_extract_phone_numbers()
    test_process_dsc_file()

if __name__ == "__main__":
    main() 