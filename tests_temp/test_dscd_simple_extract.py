#!/usr/bin/env python3
import re
import os
import pandas as pd
from typing import Optional, Tuple, List, Dict, Any

# Mô phỏng dữ liệu bảng từ hình ảnh
sample_data = {
    "stt": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "dau_so": [
        "024.5678.6000",
        "024.5678.6001",
        "024.5678.6002",
        "024.5678.6003",
        "024.5678.6004",
        "024.5678.6005",
        "024.5678.6006",
        "024.5678.6007",
        "024.5678.6008",
        "024.5678.6009"
    ],
    "cuoc_thu_khach": [
        20713, 29161, 20481, 21364, 32586, 1371089, 28153, 3216612, 636621, 122889
    ],
    "cuoc_tra_htc": [
        20570, 27328, 20384, 21091, 30069, 1100871, 26522, 2577289, 513297, 102311
    ]
}

def extract_phone_numbers_from_excel(file_path: str) -> List[str]:
    """
    Trích xuất danh sách số điện thoại từ file đối soát cố định
    
    Args:
        file_path: Đường dẫn đến file Excel đối soát cố định
        
    Returns:
        Danh sách các số điện thoại
    """
    try:
        # Đọc file Excel (ở đây dùng file mẫu từ dataframe)
        df = pd.DataFrame(sample_data)
        
        # Lấy và lọc danh sách số điện thoại (loại bỏ các giá trị rỗng)
        phone_numbers = [str(dau_so) for dau_so in df["dau_so"] 
                        if dau_so and str(dau_so).strip()]
        
        return phone_numbers
    
    except Exception as e:
        print(f"Lỗi khi trích xuất số điện thoại: {str(e)}")
        return []

def normalize_phone_number(phone: str) -> str:
    """
    Chuẩn hóa số điện thoại, loại bỏ kí tự không phải số
    """
    return re.sub(r'[^\d]', '', phone)

def filter_phone_numbers(phones: List[str], prefix: Optional[str] = None) -> List[str]:
    """
    Lọc số điện thoại theo tiền tố
    """
    normalized_phones = [normalize_phone_number(phone) for phone in phones]
    
    if prefix:
        return [phone for phone in normalized_phones if phone.startswith(prefix)]
    
    return normalized_phones

# Test trích xuất số điện thoại
print("=== Trích xuất số điện thoại từ bảng đối soát ===\n")

# Trích xuất số điện thoại
phones = extract_phone_numbers_from_excel("")
print(f"Số lượng số điện thoại: {len(phones)}")
print("Danh sách số điện thoại:")
for phone in phones:
    print(f"  {phone}")

# Chuẩn hóa số điện thoại
print("\n=== Chuẩn hóa số điện thoại ===")
normalized_phones = filter_phone_numbers(phones)
print("Danh sách số điện thoại sau khi chuẩn hóa:")
for phone in normalized_phones:
    print(f"  {phone}")

# Lọc số điện thoại có tiền tố 024
print("\n=== Lọc số điện thoại bắt đầu bằng 024 ===")
filtered_phones = filter_phone_numbers(phones, "024")
print(f"Số lượng số điện thoại có tiền tố 024: {len(filtered_phones)}")
for phone in filtered_phones:
    print(f"  {phone}")

# Thống kê theo tiền tố
print("\n=== Thống kê số điện thoại theo tiền tố ===")
prefix_counts = {}
for phone in normalized_phones:
    prefix = phone[:3]  # Lấy 3 số đầu tiên
    prefix_counts[prefix] = prefix_counts.get(prefix, 0) + 1

for prefix, count in prefix_counts.items():
    print(f"  Tiền tố {prefix}: {count} số") 