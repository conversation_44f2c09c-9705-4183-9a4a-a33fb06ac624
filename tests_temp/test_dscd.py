#!/usr/bin/env python3
import sys
import os

# Thêm đường dẫn hiện tại vào sys.path
sys.path.append(os.path.abspath('.'))

# Import các hàm cần kiểm tra
from backend.src.utils.dscd_processor import is_dscd_file, extract_month_year, extract_partner_name

# Danh sách các file cần kiểm tra
files = [
    'BIDV Hà Tây Tháng 1-2025.xlsx',
    'Doctor-check-Tháng 1-2025.xlsx',
    'Đ<PERSON>i soát cước cố định HTC - CGV T1-2025.xlsx',
    'Doi soat so co dinh HGC_T1_2025 (1).xlsx',
    'Doi soat so co dinh HGC_T1_2025.xlsx',
    'ĐS CĐ_AVITOUR_Tháng 1-2025.xlsx',
    'ĐS CĐ_HALI_Tháng 1_2025.xlsx',
    'Đ<PERSON> CĐ_MDC_Tháng 1-2025.xlsx',
    'DS cố định AIC_T2_2025.xlsx',
    'ĐS CỐ ĐỊNH HITC T1.2025 (chốt).xlsx',
    'DS cố định ITS_T1_2025.xlsx',
    'DS cố định Realtime T1.2025.xlsx',
    'DS cố định THÁI BÌNH DƯƠNG_T1_2025.xlsx',
    'DS cố định VNTEL_T1_2025.xlsx',
    'DS cuoc co dinh Vietdgtel thang 1_2025.xlsx',
    'Shield VN T1.2025.xlsx'
]

# Kiểm tra
print("=== Kiểm tra nhận dạng file DSCD ===\n")
print(f"Tổng số file: {len(files)}\n")

for file in files:
    is_dscd = is_dscd_file(file)
    month, year = extract_month_year(file)
    partner = extract_partner_name(file)
    
    print(f"File: {file}")
    print(f"  Là file DSCD: {is_dscd}")
    print(f"  Tháng/Năm: {month}/{year}")
    print(f"  Đối tác: {partner}")
    print() 