services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
      - TZ=Asia/Ho_Chi_Minh
    networks:
      - audit-network
    depends_on:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    volumes:
      - ./backend:/app
      - template_uploads:/app/uploads/templates
      - call_log_uploads:/app/uploads/call_logs
    ports:
      - "8000:8000"
    env_file:
      - ./backend/.env.production
    environment:
      - TZ=Asia/Ho_Chi_Minh
    networks:
      - audit-network
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: celery -A src.worker.celery_app worker --loglevel=info --concurrency=2 --max-memory-per-child=1500000 -Q call_logs,templates,maintenance
    volumes:
      - ./backend:/app
      - call_log_uploads:/app/uploads/call_logs
      - template_uploads:/app/uploads/templates
    env_file:
      - ./backend/.env.production
    environment:
      - C_FORCE_ROOT=true
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Ho_Chi_Minh
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 1.5G
    networks:
      - audit-network
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./docker/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=hieuda87
      - POSTGRES_DB=audit_call
      - TZ=Asia/Ho_Chi_Minh
    command: postgres -c config_file=/etc/postgresql/postgresql.conf -c hba_file=/etc/postgresql/pg_hba.conf
    ports:
      - "5432:5432"
    networks:
      - audit-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: on-failure
    security_opt:
      - no-new-privileges:true

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru --save 900 1 --save 300 10 --save 60 10000
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    environment:
      - TZ=Asia/Ho_Chi_Minh
    networks:
      - audit-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1.5G

volumes:
  postgres_data:
  redis_data:
  call_log_uploads:
  template_uploads:

networks:
  audit-network:
    driver: bridge
