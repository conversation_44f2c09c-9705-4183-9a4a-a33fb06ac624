services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://backend:8000
      - TZ=Asia/Ho_Chi_Minh
    networks:
      - audit-network
    depends_on:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    volumes:
      - ./backend:/app
      - template_uploads:/app/uploads/templates
      - call_log_uploads:/app/uploads/call_logs
    ports:
      - "8000:8000"
    env_file:
      - ./backend/.env.local
    environment:
      - TZ=Asia/Ho_Chi_Minh
    networks:
      - audit-network
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: celery -A src.worker.celery_app worker --loglevel=debug --concurrency=2 --max-memory-per-child=1500000 -Q call_logs,templates,maintenance
    volumes:
      - ./backend:/app
      - call_log_uploads:/app/uploads/call_logs
      - template_uploads:/app/uploads/templates
    env_file:
      - ./backend/.env.local
    environment:
      - C_FORCE_ROOT=true
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Ho_Chi_Minh
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 1.5G
    networks:
      - audit-network
    depends_on:
      - backend
      - redis

  db:
    image: postgres:15-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./docker/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=hieuda87
      - POSTGRES_DB=audit_call
      - TZ=Asia/Ho_Chi_Minh
    command: postgres -c config_file=/etc/postgresql/postgresql.conf -c hba_file=/etc/postgresql/pg_hba.conf
    ports:
      - "5432:5432"
    networks:
      - audit-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: on-failure
    security_opt:
      - no-new-privileges:true

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    environment:
      - TZ=Asia/Ho_Chi_Minh
    networks:
      - audit-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  redis_data:
  call_log_uploads:
  template_uploads:

networks:
  audit-network:
    driver: bridge