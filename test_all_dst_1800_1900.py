#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import re

# Thêm đường dẫn hiện tại vào sys.path
sys.path.append(os.path.abspath('.'))

def normalize_text(text):
    """
    Chuẩn hóa văn bản để dễ so sánh bằng cách:
    - Chuyển về chữ thường
    - Giữ nguyên hoặc thay thế các ký tự tiếng Việt
    - Loại bỏ tất cả ký tự không phải a-z, 0-9
    """
    # Chuyển về chữ thường
    text = text.lower()
    
    # Thay thế trực tiếp các ký tự tiếng Việt phổ biến
    vietnamese_chars = {
        'á': 'a', 'à': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ắ': 'a', 'ằ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ấ': 'a', 'ầ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'é': 'e', 'è': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ế': 'e', 'ề': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'í': 'i', 'ì': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ó': 'o', 'ò': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ố': 'o', 'ồ': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ớ': 'o', 'ờ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ú': 'u', 'ù': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ứ': 'u', 'ừ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ý': 'y', 'ỳ': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd'  # Quan trọng: Chuyển đổi đ thành d
    }
    
    for vietnamese, latin in vietnamese_chars.items():
        text = text.replace(vietnamese, latin)
    
    # Loại bỏ tất cả ký tự không phải a-z và 0-9
    text = re.sub(r'[^a-z0-9]', '', text)
    return text

def check_file_classification(filename, simple_strings):
    """Kiểm tra phân loại file 1800/1900"""
    normalized = normalize_text(filename)
    
    # Kiểm tra nếu file match với bất kỳ chuỗi nào trong simple_strings
    matched = False
    matching_string = None
    
    for simple_str in simple_strings:
        normalized_str = normalize_text(simple_str)
        if normalized_str in normalized:
            matched = True
            matching_string = simple_str
            break
    
    return {
        "filename": filename,
        "normalized": normalized,
        "matched": matched,
        "matching_string": matching_string
    }

def main():
    print("=== Kiểm tra phân loại tất cả các file 1800/1900 ===\n")
    
    # Đường dẫn đến thư mục chứa các file 1800/1900
    dst_dir = "backend/BBDS/1800_1900"
    
    # Danh sách các chuỗi đơn giản cho 1800/1900 (không dùng regex)
    DST_1800_1900_SIMPLE_STRINGS = [
        "1800", "1900", "1819", "18001900", "1800_1900", 
        "dich_vu_1800", "dich_vu_1900", "dichvu1800"
    ]
    
    # Lấy danh sách tất cả các file trong thư mục
    dst_files = sorted([f for f in os.listdir(dst_dir) if f.endswith(('.xlsx', '.xls', '.pdf'))])
    
    print(f"Tổng số file: {len(dst_files)}\n")
    
    # Kiểm tra từng file
    results = []
    for filename in dst_files:
        result = check_file_classification(filename, DST_1800_1900_SIMPLE_STRINGS)
        results.append(result)
    
    # Hiển thị kết quả
    success_count = 0
    failure_count = 0
    
    for result in results:
        if result["matched"]:
            success_count += 1
            status = "✅ Thành công"
        else:
            failure_count += 1
            status = "❌ Thất bại"
            
        print(f"{status}: {result['filename']}")
        print(f"  Normalized: '{result['normalized']}'")
        if result["matched"]:
            print(f"  Phân loại: DST_1800_1900, Mẫu đã khớp: '{result['matching_string']}'")
        else:
            print(f"  Không thể phân loại là DST_1800_1900")
        print()
    
    # Tổng kết
    print("\n=== Tổng kết ===")
    print(f"Tổng số file: {len(dst_files)}")
    print(f"Thành công: {success_count} ({success_count/len(dst_files)*100:.1f}%)")
    print(f"Thất bại: {failure_count} ({failure_count/len(dst_files)*100:.1f}%)")
    
    if failure_count > 0:
        print("\nDanh sách file thất bại:")
        for result in results:
            if not result["matched"]:
                print(f"- {result['filename']}")
                print(f"  Normalized: '{result['normalized']}'")
    
    print("\n=== Kết thúc kiểm tra ===")

if __name__ == "__main__":
    main() 