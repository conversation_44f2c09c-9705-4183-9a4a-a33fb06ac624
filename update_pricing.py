#!/usr/bin/env python3

import os
import sys
import subprocess

def run_docker_command(command):
    """Chạy lệnh trong container Docker"""
    full_command = f"docker compose -f docker-compose.local.yml exec db psql -U postgres -d audit_call -c \"{command}\""
    print(f"Executing: {full_command}")
    
    try:
        result = subprocess.run(full_command, shell=True, check=True, text=True, capture_output=True)
        print(result.stdout)
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error executing command: {e}")
        print(f"Error output: {e.stderr}")
        return None

def check_revenue_pricing():
    """Kiểm tra dữ liệu trong bảng revenue_pricing"""
    print("\n=== REVENUE PRICING RULES ===")
    run_docker_command("SELECT id, partner_id, billing_method, service_type_id, volume_range_id, price, is_active FROM revenue_pricing;")

def check_cost_pricing():
    """Kiểm tra dữ liệu trong bảng cost_pricing"""
    print("\n=== COST PRICING RULES ===")
    run_docker_command("SELECT id, partner_id, billing_method, service_type_id, volume_range_id, price, is_active FROM cost_pricing;")

def create_cost_pricing(partner_id=1, service_type_id=1, volume_range_id=1):
    """Tạo một quy tắc chi mới"""
    print("\n=== CREATING NEW COST PRICING RULE ===")
    
    # Kiểm tra xem đã có quy tắc tương tự chưa
    check_query = f"SELECT id FROM cost_pricing WHERE partner_id = {partner_id} AND service_type_id = {service_type_id} AND volume_range_id = {volume_range_id};"
    result = run_docker_command(check_query)
    
    if "0 rows" not in result:
        print(f"Đã tồn tại quy tắc chi với thông số này")
        return
    
    # Tạo quy tắc mới
    insert_query = f"INSERT INTO cost_pricing (partner_id, billing_method, service_type_id, volume_range_id, price, is_active) VALUES ({partner_id}, 'block_6s', {service_type_id}, {volume_range_id}, 100.00, true);"
    run_docker_command(insert_query)
    
    print("Đã tạo quy tắc chi mới thành công!")

def update_pricing_rule(rule_type, rule_id, new_price):
    """Cập nhật giá của một quy tắc"""
    print(f"\n=== UPDATING {rule_type.upper()} PRICING RULE ===")
    
    table_name = f"{rule_type}_pricing"
    
    # Kiểm tra quy tắc tồn tại
    check_query = f"SELECT id, price FROM {table_name} WHERE id = {rule_id};"
    result = run_docker_command(check_query)
    
    if "0 rows" in result:
        print(f"Không tìm thấy quy tắc {rule_type} với ID: {rule_id}")
        return
    
    # Cập nhật giá
    update_query = f"UPDATE {table_name} SET price = {new_price} WHERE id = {rule_id};"
    run_docker_command(update_query)
    
    print(f"Đã cập nhật quy tắc {rule_type} ID: {rule_id} với giá mới: {new_price}")

def main():
    """Hàm chính thực thi script"""
    try:
        # Kiểm tra dữ liệu hiện tại
        check_revenue_pricing()
        check_cost_pricing()
        
        # Tạo quy tắc chi mới nếu chưa có
        create_cost_pricing()
        
        # Kiểm tra lại sau khi tạo
        check_cost_pricing()
        
        # Cập nhật giá của quy tắc thu đầu tiên
        if len(sys.argv) > 1 and sys.argv[1] == "update":
            update_pricing_rule("revenue", 1, 150.00)
            # Kiểm tra lại sau khi cập nhật
            check_revenue_pricing()
        
        print("\nHoàn thành!")
        
    except Exception as e:
        print(f"Lỗi: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()