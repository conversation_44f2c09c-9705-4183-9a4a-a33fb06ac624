#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re

def normalize_text(text):
    """
    Chuẩn hóa văn bản để dễ so sánh bằng cách:
    - <PERSON>yển về chữ thường
    - Giữ nguyên hoặc thay thế các ký tự tiếng Việt
    - Loại bỏ ký tự đặc biệt
    """
    # Chuyển về chữ thường
    text = text.lower()
    
    # Thay thế trực tiếp các ký tự tiếng Việt phổ biến
    vietnamese_chars = {
        'á': 'a', 'à': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ắ': 'a', 'ằ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ấ': 'a', 'ầ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'é': 'e', 'è': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ế': 'e', 'ề': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'í': 'i', 'ì': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ó': 'o', 'ò': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ố': 'o', 'ồ': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ớ': 'o', 'ờ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ú': 'u', 'ù': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ứ': 'u', 'ừ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ý': 'y', 'ỳ': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd'  # Quan trọng: Chuyển đổi đ thành d
    }
    
    for vietnamese, latin in vietnamese_chars.items():
        text = text.replace(vietnamese, latin)
    
    # Loại bỏ ký tự đặc biệt
    text = re.sub(r'[^\w\s]', ' ', text)
    # Thay thế nhiều khoảng trắng bằng một khoảng trắng
    text = re.sub(r'\s+', ' ', text).strip()
    return text

def main():
    print("=== Bắt đầu test phân loại file DS cố định VNTEL_T1_2025.xlsx với cách chuẩn hóa MỚI ===")
    
    # Tên file của chúng ta
    filename = "DS cố định VNTEL_T1_2025.xlsx"
    
    # Danh sách các chuỗi đơn giản cho DSCD (không dùng regex)
    DSCD_SIMPLE_STRINGS = [
        "co dinh", "cd", "dscd", "ds cd", "ds-cd", 
        "thue bao", "tb co dinh", "ds co dinh",
        "co định", "cố dinh", "cố định",  # Các biến thể khác nhau
    ]
    
    # Kiểm tra normalize_text
    print("\n=== Kiểm tra normalize_text MỚI ===")
    normalized = normalize_text(filename)
    print(f"Original: '{filename}'")
    print(f"Normalized: '{normalized}'")
    
    # Kiểm tra simple string matching
    print("\n=== Kiểm tra khớp chuỗi đơn giản ===")
    for simple_str in DSCD_SIMPLE_STRINGS:
        normalized_str = normalize_text(simple_str)
        is_match = normalized_str in normalized
        print(f"  '{simple_str}' ('{normalized_str}') in '{normalized}': {is_match}")
    
    # Kiểm tra các trường hợp thực tế khác
    test_cases = [
        "DS cố định VNTEL_T1_2025.xlsx",
        "Doi soat so co dinh HGC_T1_2025.xlsx",
        "ĐS CĐ_AVITOUR_Tháng 1-2025.xlsx",
        "DS cuoc co dinh Vietdgtel thang 1_2025.xlsx"
    ]
    
    print("\n=== Kiểm tra các trường hợp thực tế ===")
    for test_case in test_cases:
        normalized_case = normalize_text(test_case)
        matched = False
        matching_string = None
        
        for simple_str in DSCD_SIMPLE_STRINGS:
            normalized_str = normalize_text(simple_str)
            if normalized_str in normalized_case:
                matched = True
                matching_string = simple_str
                break
                
        print(f"File: '{test_case}'")
        print(f"  Normalized: '{normalized_case}'")
        print(f"  Matched: {matched}, Pattern: '{matching_string}'")
        print()
    
    print("\n=== Kết thúc test ===")

if __name__ == "__main__":
    main() 