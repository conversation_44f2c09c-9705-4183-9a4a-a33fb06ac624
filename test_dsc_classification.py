"""
A simplified test for filename classification focusing on the specific issue with DSC vs DSCD
"""

import re

def normalize_text(text):
    """
    Chuẩn hóa văn bản để dễ so sánh bằng cách:
    - Chuyển về chữ thường
    - Giữ nguyên hoặc thay thế các ký tự tiếng Việt
    - Loại bỏ tất cả ký tự không phải a-z, 0-9 (b<PERSON> gồ<PERSON> khoảng trắng)
    """
    # Chuyển về chữ thường
    text = text.lower()
    
    # Thay thế trực tiếp các ký tự tiếng Việt phổ biến
    vietnamese_chars = {
        'á': 'a', 'à': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ắ': 'a', 'ằ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ấ': 'a', 'ầ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'é': 'e', 'è': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ế': 'e', 'ề': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'í': 'i', 'ì': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ó': 'o', 'ò': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ố': 'o', 'ồ': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ớ': 'o', 'ờ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ú': 'u', 'ù': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ứ': 'u', 'ừ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ý': 'y', 'ỳ': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd'  # Quan trọng: Chuyển đổi đ thành d
    }
    
    for vietnamese, latin in vietnamese_chars.items():
        text = text.replace(vietnamese, latin)
    
    # Loại bỏ tất cả ký tự không phải a-z và 0-9 (bao gồm khoảng trắng)
    text = re.sub(r'[^a-z0-9]', '', text)
    
    return text

# Test file with "Doi soat cuoc" in the name
test_filename = "Doi soat cuoc_HTC_DIGINET_T1_2025.xlsx"

# Normalize the filename
filename_norm = normalize_text(test_filename)
print(f"Original filename: {test_filename}")
print(f"Normalized filename: {filename_norm}")

# Check if it contains specific patterns
print("\nChecking for patterns:")
if "cuoc" in filename_norm:
    print("✓ Contains 'cuoc' keyword")
else:
    print("✗ Does NOT contain 'cuoc' keyword")

if "htc" in filename_norm:
    print("✓ Contains 'htc' keyword")

if "cd" in filename_norm:
    print("✓ Contains 'cd' substring in the normalized filename")
    # Check if "cd" appears as a whole word or within specific context
    patterns = [r'\bcd\b', r'_cd_', r'_cd\b', r'\bcd_']
    if any(re.search(pattern, filename_norm) for pattern in patterns):
        print("  ✓ 'cd' appears as a whole word or in a specific context")
    else:
        print("  ✗ 'cd' only appears as a substring within other words (e.g., in 'diginet')")

# Explain the problem
print("\nExplanation:")
print("The issue is that 'cd' appears as a substring within 'diginet' after normalization.")
print("When classifying, the original code treats any occurrence of 'cd' as a match for DSCD,")
print("even when it's just part of another word, without checking for word boundaries.")
print("This is why a file with 'Doi soat cuoc' was being classified as DSCD (đối soát cố định).")
print("The fix ensures 'cd' is only matched when it appears as a whole word or in specific contexts.") 