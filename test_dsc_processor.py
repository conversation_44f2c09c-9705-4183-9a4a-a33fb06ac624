#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import pandas as pd

# <PERSON><PERSON> định đường dẫn để đảm bả<PERSON> c<PERSON>c import
sys.path.insert(0, os.path.abspath('.'))

# Nhập trực tiếp các module c<PERSON><PERSON> thiết
from backend.src.utils.dsc_processor import is_dsc_file, process_dsc_file, extract_month_year, extract_partners
from backend.src.schemas.dsc import DoiSoatCuoc, DauSoDichVuCuoc, TongKetCuoc

# File mẫu
SAMPLE_FILE = 'backend/BBDS/DoanhThu_HTC_VNM_012025.xlsx'

def display_file_info(file_path):
    """Hiển thị thông tin cơ bản về file"""
    if not os.path.exists(file_path):
        print(f"File không tồn tại: {file_path}")
        return False
        
    print(f"Thông tin file: {os.path.basename(file_path)}")
    print(f"Kích thước: {os.path.getsize(file_path) / 1024:.2f} KB")
    
    try:
        df = pd.read_excel(file_path)
        print(f"Số hàng: {df.shape[0]}, Số cột: {df.shape[1]}")
        return True
    except Exception as e:
        print(f"Lỗi đọc file: {str(e)}")
        return False

def test_dsc_processor(file_path):
    """Test DSC processor với file mẫu"""
    print("\n" + "="*60)
    print("TEST DSC PROCESSOR")
    print("="*60)
    
    # Thông tin file
    display_file_info(file_path)
    
    # Kiểm tra định dạng file
    filename = os.path.basename(file_path)
    if is_dsc_file(filename):
        print("\n✅ File được nhận dạng là file đối soát cước")
        
        # Trích xuất thông tin từ tên file
        month, year = extract_month_year(filename)
        print(f"Tháng/Năm: {month}/{year}")
        
        tu_mang, den_doi_tac = extract_partners(filename)
        print(f"Đối tác: {tu_mang} → {den_doi_tac}")
        
        try:
            # Xử lý file
            print("\nĐang xử lý file...")
            result = process_dsc_file(file_path)
            
            # Hiển thị kết quả
            print("\nKẾT QUẢ XỬ LÝ:")
            print(f"- Tháng đối soát: {result.thang_doi_soat}")
            print(f"- Từ mạng: {result.tu_mang}")
            print(f"- Đến đối tác: {result.den_doi_tac}")
            print(f"- Hợp đồng số: {result.hop_dong_so}")
            print(f"- Số đầu số dịch vụ: {len(result.du_lieu)}")
            print(f"- Tổng cộng tiền: {result.tong_ket.tong_cong_tien:,.2f} VND")
            
            # Hiển thị một số đầu số dịch vụ
            if result.du_lieu:
                print("\nĐầu số dịch vụ (5 đầu tiên):")
                for i, item in enumerate(result.du_lieu[:5], 1):
                    print(f"  {i}. {item.dau_so} - Tổng thanh toán: {item.tong_thanh_toan:,.2f} VND")
            
            # Tóm tắt
            print("\nĐÁNH GIÁ:")
            print("✅ DSC Processor hoạt động tốt với file mẫu!")
            
            return True
        except Exception as e:
            print(f"\n❌ Lỗi khi xử lý file: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
    else:
        print("\n❌ File không được nhận dạng là file đối soát cước")
        return False

if __name__ == "__main__":
    # Sử dụng file từ tham số dòng lệnh hoặc file mẫu
    file_path = sys.argv[1] if len(sys.argv) > 1 else SAMPLE_FILE
    
    if not os.path.isabs(file_path):
        file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), file_path)
    
    if not os.path.exists(file_path):
        print(f"❌ File không tồn tại: {file_path}")
        sys.exit(1)
        
    # Chạy test
    test_dsc_processor(file_path) 