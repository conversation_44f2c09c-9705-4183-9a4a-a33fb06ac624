# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Docker
.dockerignore

# Database
*.sqlite
*.sqlite3
*.db

# Logs
logs/
*.log

# Environment variables

# IDE
.idea/
.vscode/
*.swp
*.swo

# Git
.git/
.gitignore

# OS specific
.DS_Store
Thumbs.db

# Project specific
postgres_data/
redis_data/ 

backend/uploads/
backend/BBDS/
backend/.venv/
test_temp/