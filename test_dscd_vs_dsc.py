"""
Test script to check if the classifier correctly differentiates between DSC and DSCD files
"""

# Define the test filenames
test_files = [
    "Doi soat cuoc_HTC_DIGINET_T1_2025.xlsx",     # Should be DSC (contains "cuoc")
    "DS cố định VNTEL_T1_2025.xlsx",             # Should be DSCD (contains "cố định")
    "Đối soát cước VNPT.xlsx",                   # Should be DSC (contains "cước")
    "File with DIGINET and HTC.xlsx"             # No clear indicators, should not match on "cd" in DIGINET
]

# Import the required classes directly
from src.utils.doi_soat_classifier import DoiSoatClassifier
from src.models.template import TemplateType

# Test each file and print the results
print("=== Testing DSC vs DSCD classification ===\n")
for filename in test_files:
    # Classify the file
    template_type = DoiSoatClassifier.classify_from_filename(filename)
    
    # Normalize for debugging
    normalized = DoiSoatClassifier.normalize_text(filename)
    
    # Determine expected type based on keywords
    expected = None
    if "cuoc" in normalized or "cước" in filename.lower():
        expected = TemplateType.DSC
    elif "codinh" in normalized or "định" in filename.lower():
        expected = TemplateType.DSCD
    
    # Print results
    print(f"Filename: {filename}")
    print(f"Normalized: {normalized}")
    print(f"Classification result: {template_type}")
    if expected:
        print(f"Expected: {expected}")
        print(f"Correct: {'✓' if template_type == expected else '✗'}")
    else:
        print(f"No clear expectation for this file")
    print("-" * 50) 