#!/usr/bin/env python3
import os
import sys
import unicodedata
import re
import logging

# <PERSON><PERSON><PERSON> hình logging để debug
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Thêm đường dẫn hiện tại vào sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import classes từ project
try:
    from backend.src.models.template import TemplateType
    from backend.src.utils.doi_soat_classifier import DoiSoatClassifier
except ImportError as e:
    logger.error(f"Lỗi import: {e}")
    print(f"Không thể import các module cần thiết: {e}")
    sys.exit(1)

def test_normalize_text():
    """Kiểm tra hàm normalize_text"""
    print("\n=== Kiểm tra normalize_text ===")
    
    test_texts = [
        "<PERSON> c<PERSON> định VNTEL_T1_2025.xlsx",
        "ds cố định",
        "DS CỐ ĐỊNH",
        "ds co dinh",
        "đối soát cố định"
    ]
    
    for text in test_texts:
        normalized = DoiSoatClassifier.normalize_text(text)
        print(f"Original: '{text}'")
        print(f"Normalized: '{normalized}'")
        print()

def test_simple_string_matching():
    """Kiểm tra việc khớp chuỗi đơn giản"""
    print("\n=== Kiểm tra khớp chuỗi đơn giản ===")
    
    filename = "DS cố định VNTEL_T1_2025.xlsx"
    normalized = DoiSoatClassifier.normalize_text(filename)
    
    print(f"Filename: '{filename}'")
    print(f"Normalized: '{normalized}'")
    print("\nDSCD_SIMPLE_STRINGS:")
    
    for simple_str in DoiSoatClassifier.DSCD_SIMPLE_STRINGS:
        is_match = simple_str in normalized
        print(f"  '{simple_str}' in '{normalized}': {is_match}")

def test_regex_matching():
    """Kiểm tra việc khớp regex pattern"""
    print("\n=== Kiểm tra khớp regex pattern ===")
    
    filename = "DS cố định VNTEL_T1_2025.xlsx"
    
    print(f"Filename: '{filename}'")
    print("\nDSCD_PATTERNS:")
    
    for pattern in DoiSoatClassifier.DSCD_PATTERNS:
        match = re.search(pattern, filename, re.IGNORECASE)
        is_match = match is not None
        matched_text = match.group(0) if match else "N/A"
        print(f"  Pattern '{pattern}': {is_match}, Matched: '{matched_text}'")

def test_classify_file():
    """Kiểm tra phân loại file cụ thể"""
    print("\n=== Kiểm tra phân loại file ===")
    
    test_files = [
        "DS cố định VNTEL_T1_2025.xlsx",
        "Doi soat so co dinh HGC_T1_2025.xlsx",
        "ĐS CĐ_AVITOUR_Tháng 1-2025.xlsx",
        "DS cuoc co dinh Vietdgtel thang 1_2025.xlsx"
    ]
    
    for filename in test_files:
        print(f"\nPhân loại file: {filename}")
        
        # Phân loại từ tên file
        file_type = DoiSoatClassifier.classify_from_filename(filename)
        print(f"  Kết quả phân loại từ tên file: {file_type}")
        
        # Kiểm tra bằng hàm is_dscd_file
        from backend.src.utils.dscd_processor import is_dscd_file
        is_dscd = is_dscd_file(filename)
        print(f"  Là file DSCD theo is_dscd_file: {is_dscd}")

# Test file with "Doi soat cuoc" in the name
test_filename = "Doi soat cuoc_HTC_DIGINET_T1_2025.xlsx"

# Classify the file
template_type = DoiSoatClassifier.classify_from_filename(test_filename)

# Print results
print(f"Filename: {test_filename}")
print(f"Normalized: {DoiSoatClassifier.normalize_text(test_filename)}")
print(f"Classification: {template_type}")

if __name__ == "__main__":
    print("=== Bắt đầu debug phân loại file DS cố định VNTEL_T1_2025.xlsx ===")
    
    test_normalize_text()
    test_simple_string_matching()
    test_regex_matching()
    test_classify_file()
    
    print("\n=== Kết thúc debug ===") 