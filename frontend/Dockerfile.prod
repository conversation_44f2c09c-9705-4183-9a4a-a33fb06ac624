FROM node:20-slim as development

RUN ln -snf /usr/share/zoneinfo/Asia/Ho_Chi_Minh /etc/localtime

WORKDIR /app

# Copy entire project first
COPY . .

# Install dependencies including Vite
RUN npm install
RUN npm install -g vite

EXPOSE 3000

CMD ["sh", "-c", "npm run dev -- --host 0.0.0.0"]

FROM node:20-slim as builder

WORKDIR /app

# Copy entire project first
COPY . .

# Install dependencies including Vite
RUN npm install
RUN npm install -g vite

RUN npm run build

FROM nginx:alpine as production

COPY --from=builder /app/dist /usr/share/nginx/html

COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"] 