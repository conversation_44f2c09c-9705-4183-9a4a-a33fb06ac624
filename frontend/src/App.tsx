import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from '@/components/ui/toaster';
import LoginPage from '@/pages/auth/login-page';
import { ProtectedLayout } from '@/components/layout/protected-layout';
import DashboardPage from '@/pages/dashboard-page';
import PartnersPage from '@/pages/partners';
import PricingPage from '@/app/pricing/page';
import CallLogsPage from '@/pages/call-logs';
import ViewCallLogsPage from '@/pages/call-logs/view';
import CallLogListPage from '@/pages/call-logs/list';
import ReconciliationsPage from '@/pages/reconciliations';
import ReconciliationDetailPage from '@/pages/reconciliations/detail-page';
import ReconciliationPeriodsPage from '@/pages/reconciliation-periods';
import ReconciliationPeriodDetailPage from '@/pages/reconciliation-periods/detail';
import TemplatesPage from '@/pages/templates';
import TemplateDetail from '@/pages/templates/detail';
import { AuthProvider } from '@/contexts/AuthContext';
import { ReconciliationEditPage } from '@/pages/reconciliations/edit-page';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
  },
});

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<LoginPage />} />

            {/* Protected routes */}
            <Route element={<ProtectedLayout />}>
              <Route path="/dashboard" element={<DashboardPage />} />
              <Route path="/templates" element={<TemplatesPage />} />
              <Route path="/templates/:id" element={<TemplateDetail />} />
              <Route path="/reconciliations" element={<ReconciliationsPage />} />
              <Route path="/reconciliations/:type/:id" element={<ReconciliationDetailPage />} />
              <Route path="/reconciliation-periods" element={<ReconciliationPeriodsPage />} />
              <Route path="/reconciliation-periods/:id" element={<ReconciliationPeriodDetailPage />} />
              <Route path="/reconciliations/:type/:id/edit" element={<ReconciliationEditPage />} />
              {/* <Route path="/call-logs" element={<CallLogsPage />} />
              <Route path="/call-logs/view/:fileId" element={<ViewCallLogsPage />} />
              <Route path="/call-logs/list" element={<CallLogListPage />} /> */}
              {/* Add more protected routes here */}
            </Route>

            {/* Admin routes */}
            <Route element={<ProtectedLayout requireAdmin />}>
              <Route path="/partners" element={<PartnersPage />} />
              <Route path="/pricing" element={<PricingPage />} />
              <Route path="/call-logs" element={<CallLogsPage />} />
              <Route path="/call-logs/view/:fileId" element={<ViewCallLogsPage />} />
              <Route path="/call-logs/list" element={<CallLogListPage />} />
              
              {/* Add more admin routes here */}
            </Route>

            {/* Default redirect */}
            <Route path="/" element={<Navigate to="/reconciliations" replace />} />
          </Routes>
        </Router>

        {/* Toast notifications */}
        <Toaster />
      </AuthProvider>
    </QueryClientProvider>
  );
} 