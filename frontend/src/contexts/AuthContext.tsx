import React, { createContext, useCallback, useEffect } from 'react';
import type { AuthContextType, LoginRequest, RegisterRequest } from '@/types/auth';
import { useAuthStore } from '@/store/auth';

export const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  // Use the auth store for state management
  const { 
    user, 
    isLoading, 
    isAuthenticated, 
    login: storeLogin, 
    register: storeRegister, 
    logout: storeLogout,
    updateUser
  } = useAuthStore();

  // Fetch user data on initial render
  useEffect(() => {
    // Only try to fetch user data if we're not already loading
    // and we have a token in localStorage
    if (!isLoading && localStorage.getItem('access_token')) {
      updateUser();
    }
  }, [updateUser, isLoading]);

  // Wrap store methods for context compatibility
  const login = useCallback(async (email: string, password: string) => {
    return storeLogin({ username_or_email: email, password });
  }, [storeLogin]);

  const register = useCallback(async (data: RegisterRequest) => {
    return storeRegister(data);
  }, [storeRegister]);

  const logout = useCallback(() => {
    storeLogout();
  }, [storeLogout]);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
} 