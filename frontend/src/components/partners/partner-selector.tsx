"use client"

import { useEffect, useState } from "react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { usePartnerStore } from "@/stores/partner-store"
import { api } from "@/lib/api"
import { Partner } from "@/types/partner"
import { useToast } from "@/components/ui/use-toast"

export function PartnerSelector() {
  const { selectedPartner, selectPartner } = usePartnerStore()
  const [partners, setPartners] = useState<Partner[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    const fetchPartners = async () => {
      setIsLoading(true)
      try {
        const response = await api.get<Partner[]>("/api/v1/partners")
        const partnersList = response.data || []
        setPartners(partnersList)
        
        // Auto-select the first partner if none is selected and we have partners
        if (partnersList.length > 0 && !selectedPartner) {
          selectPartner(partnersList[0])
        }
      } catch (error) {
        console.error("Failed to fetch partners:", error)
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load partners. Please try again."
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchPartners()
  }, [])

  const handlePartnerChange = (partnerId: string) => {
    const partner = partners.find(p => p.id.toString() === partnerId)
    if (partner) {
      selectPartner(partner)
    }
  }

  return (
    <div className="w-[250px]">
      <Select
        disabled={isLoading}
        value={selectedPartner?.id.toString()}
        onValueChange={handlePartnerChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Select a partner" />
        </SelectTrigger>
        <SelectContent>
          {partners.map((partner) => (
            <SelectItem key={partner.id} value={partner.id.toString()}>
              {partner.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
} 