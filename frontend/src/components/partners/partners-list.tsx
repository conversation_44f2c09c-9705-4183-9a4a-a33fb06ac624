import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Partner, PartnerType } from "@/types/partner";
import { formatDate } from "@/lib/utils";
import { MoreHorizontal, Pencil, Trash2 } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface PartnersListProps {
  partners: Partner[];
  onEdit: (partner: Partner) => void;
  onDelete: (partner: Partner) => Promise<void>;
  onToggleActive: (partner: Partner) => Promise<void>;
  onFilterChange?: (type: PartnerType | null) => void;
  activeFilter?: PartnerType | null;
}

export function PartnersList({ 
  partners, 
  onEdit, 
  onDelete, 
  onToggleActive,
  onFilterChange,
  activeFilter 
}: PartnersListProps) {
  const [partnerToDelete, setPartnerToDelete] = useState<Partner | null>(null);

  const handleDelete = async () => {
    if (partnerToDelete) {
      await onDelete(partnerToDelete);
      setPartnerToDelete(null);
    }
  };

  const getTypeColor = (type: PartnerType) => {
    return type === "telco" ? "blue" : "green";
  };

  return (
    <>
      {onFilterChange && (
        <div className="flex items-center mb-4 space-x-2">
          <span className="text-sm font-medium">Filter:</span>
          <div className="flex space-x-2">
            <Button 
              variant={activeFilter === null ? "default" : "outline"} 
              size="sm" 
              onClick={() => onFilterChange(null)}
            >
              All
            </Button>
            <Button 
              variant={activeFilter === "telco" ? "default" : "outline"} 
              size="sm" 
              onClick={() => onFilterChange("telco")}
              className={activeFilter === "telco" ? "bg-blue-600" : ""}
            >
              TELCO
            </Button>
            <Button 
              variant={activeFilter === "cp" ? "default" : "outline"} 
              size="sm" 
              onClick={() => onFilterChange("cp")}
              className={activeFilter === "cp" ? "bg-green-600" : ""}
            >
              CP
            </Button>
          </div>
          <div className="ml-4 text-sm text-muted-foreground">
            {partners.length} partners
          </div>
        </div>
      )}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Raw Names</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>CP Error Adjustment</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {partners.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                  No partners found
                </TableCell>
              </TableRow>
            ) : (
              partners.map((partner) => (
                <TableRow key={partner.id}>
                  <TableCell className="font-medium">{partner.name}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className={`bg-${getTypeColor(partner.type)}-50`}>
                      {partner.type.toUpperCase()}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {partner.raw_names.slice(0, 2).map((rawName) => (
                        <Badge key={rawName.id} variant="outline">
                          {rawName.raw_name}
                        </Badge>
                      ))}
                      {partner.raw_names.length > 2 && (
                        <Badge variant="outline">+{partner.raw_names.length - 2} more</Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={partner.is_active ? "success" : "secondary"}
                      className="cursor-pointer"
                      onClick={() => onToggleActive(partner)}
                    >
                      {partner.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {partner.type === "cp" ? (
                      partner.error_adjustment === -1 ? (
                        <Badge variant="destructive">-1: Lỗi tất cả cuộc gọi</Badge>
                      ) : partner.error_adjustment === 1 ? (
                        <Badge variant="default">+1: CP có lỗi</Badge>
                      ) : (
                        <Badge variant="outline">Không điều chỉnh</Badge>
                      )
                    ) : (
                      <Badge variant="outline">N/A</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit(partner)}>
                          <Pencil className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={() => setPartnerToDelete(partner)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <AlertDialog open={!!partnerToDelete} onOpenChange={() => setPartnerToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the partner "{partnerToDelete?.name}". This action cannot
              be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 