import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Partner, PartnerFormData, PartnerType } from "@/types/partner";
import { LoadingSpinner } from "../ui/loading-spinner";
import { PlusCircle, X } from "lucide-react";

const formSchema = z.object({
  type: z.enum(["telco", "cp"] as const),
  name: z.string().min(1, "Name is required").max(100),
  raw_names: z.array(
    z.string().min(1, "Raw name is required").max(100)
  )
  .min(1, "At least one raw name is required")
  .refine(values => {
    // Check for duplicates by comparing array length with unique values length
    const uniqueValues = new Set(values);
    return uniqueValues.size === values.length;
  }, { message: "Raw names must be unique. Please remove duplicates." }),
  description: z.string().max(500).optional(),
  error_adjustment: z.number().int().min(-1).max(1),
  contact_name: z.string().max(100).optional().transform(val => val || null),
  contact_email: z.union([
    z.string().max(255).email(),
    z.string().length(0)
  ]).transform(val => val || null),
  contact_phone: z.string().max(20).optional().transform(val => val || null),
});

interface PartnerFormProps {
  initialData?: Partner;
  onSubmit: (data: PartnerFormData) => Promise<void>;
  isLoading?: boolean;
}

export function PartnerForm({ initialData, onSubmit, isLoading }: PartnerFormProps) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: initialData?.type || "telco",
      name: initialData?.name || "",
      raw_names: initialData?.raw_names?.map(rn => rn.raw_name) || [""],
      description: initialData?.description || "",
      error_adjustment: initialData?.error_adjustment || 0,
      contact_name: initialData?.contact_name || "",
      contact_email: initialData?.contact_email || "",
      contact_phone: initialData?.contact_phone || "",
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "raw_names",
  });

  // Function to check for duplicate raw names
  const checkForDuplicates = () => {
    const rawNames = form.getValues("raw_names");
    const uniqueValues = new Set(rawNames);
    
    if (uniqueValues.size !== rawNames.length) {
      form.setError("raw_names", { 
        type: "custom", 
        message: "Raw names must be unique. Please remove duplicates." 
      });
      return true;
    }
    return false;
  };

  const handleSubmit = async (data: z.infer<typeof formSchema>) => {
    try {
      // Check for duplicates before submission
      if (checkForDuplicates()) {
        return;
      }
      
      await onSubmit(data);
      if (!initialData) {
        form.reset();
      }
    } catch (error) {
      // Error will be handled by the parent component
      throw error;
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Type</FormLabel>
              <Select
                disabled={isLoading}
                onValueChange={(value) => {
                  field.onChange(value);
                  // Reset error_adjustment to 0 if changing to telco
                  if (value === "telco") {
                    form.setValue("error_adjustment", 0);
                  }
                }}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select partner type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="telco">Telco</SelectItem>
                  <SelectItem value="cp">CP</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input disabled={isLoading} placeholder="Enter partner name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <FormLabel>Raw Names</FormLabel>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => append("")}
              disabled={isLoading}
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Add Raw Name
            </Button>
          </div>
          
          {fields.map((field, index) => (
            <div key={field.id} className="flex items-center gap-2">
              <FormField
                control={form.control}
                name={`raw_names.${index}`}
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Input 
                        disabled={isLoading} 
                        placeholder="Enter raw name" 
                        {...field} 
                        onChange={(e) => {
                          field.onChange(e);
                          // Clear error when user starts typing
                          form.clearErrors("raw_names");
                        }}
                        onBlur={(e) => {
                          field.onBlur();
                          // Check for duplicates when focus leaves
                          checkForDuplicates();
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {fields.length > 1 && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    remove(index);
                    // Check for duplicates after removing
                    setTimeout(() => checkForDuplicates(), 0);
                  }}
                  disabled={isLoading}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          ))}
          {form.formState.errors.raw_names?.message && (
            <p className="text-sm font-medium text-destructive">
              {form.formState.errors.raw_names.message}
            </p>
          )}
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  disabled={isLoading}
                  placeholder="Enter description"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="error_adjustment"
          render={({ field }) => (
            <FormItem>
              <FormLabel>CP Error Adjustment</FormLabel>
              <Select
                disabled={isLoading || form.watch("type") !== "cp"}
                onValueChange={(value) => field.onChange(parseInt(value))}
                value={field.value.toString()}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select CP error adjustment" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="-1">-1: Lỗi tất cả cuộc gọi</SelectItem>
                  <SelectItem value="0">0: Không điều chỉnh</SelectItem>
                  <SelectItem value="1">+1: CP có lỗi (Tăng 0s lên 1s)</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Contact Information</h3>
          <div className="grid gap-4 md:grid-cols-2">
            <FormField
              control={form.control}
              name="contact_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contact Name</FormLabel>
                  <FormControl>
                    <Input
                      disabled={isLoading}
                      placeholder="Enter contact name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="contact_email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contact Email</FormLabel>
                  <FormControl>
                    <Input
                      disabled={isLoading}
                      type="email"
                      placeholder="Enter contact email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="contact_phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contact Phone</FormLabel>
                  <FormControl>
                    <Input
                      disabled={isLoading}
                      placeholder="Enter contact phone"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              {initialData ? "Updating..." : "Creating..."}
            </>
          ) : (
            <>{initialData ? "Update Partner" : "Create Partner"}</>
          )}
        </Button>
      </form>
    </Form>
  );
} 