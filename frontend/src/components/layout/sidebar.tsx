import { NavLink } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  BarChart3,
  FileText,
  LayoutDashboard,
  Settings,
  Users,
  Building2,
  DollarSign,
  PhoneCall,
  ListIcon,
  FileSpreadsheet,
  CalendarRange,
  FileUp,
} from "lucide-react";
import { useAuth } from "@/hooks/auth/useAuth";
import { Separator } from "@/components/ui/separator";

// Tạo nhóm menu với tiêu đề cho mỗi nhóm
const menuGroups = [
  {
    title: "Tổng quan",
    items: [
      {
        title: "Dashboard",
        href: "/dashboard",
        icon: LayoutDashboard,
      },
    ]
  },
  {
    title: "Đối soát",
    items: [
      {
        title: "Đối soát",
        href: "/reconciliations",
        icon: BarChart3,
        description: "Xem và quản lý đối soát",
      },
      {
        title: "Mẫu đối soát",
        href: "/templates",
        icon: FileText,
        description: "Quản lý mẫu đối soát",
      },
      {
        title: "<PERSON><PERSON> đối soát",
        href: "/reconciliation-periods",
        icon: CalendarRange,
        description: "Quản lý kỳ đối soát",
      },
    ]
  },
  {
    title: "Call Logs",
    items: [
      {
        title: "Upload",
        href: "/call-logs",
        icon: FileUp,
      },
      {
        title: "Call Logs",
        href: "/call-logs/list",
        icon: ListIcon,
      },
    ]
  },
  {
    title: "Quản trị",
    items: [
      {
        title: "Partners",
        href: "/partners",
        icon: Building2,
        adminOnly: true,
      },
      {
        title: "Pricing",
        href: "/pricing",
        icon: DollarSign,
        adminOnly: true,
      },
      // {
      //   title: "Users",
      //   href: "/users",
      //   icon: Users,
      //   adminOnly: true,
      // },
      // {
      //   title: "Settings",
      //   href: "/settings",
      //   icon: Settings,
      // },
    ]
  }
];

export function Sidebar() {
  const { user } = useAuth();
  const isAdmin = user?.is_admin;

  return (
    <div className="hidden border-r bg-gray-100/40 lg:block dark:bg-gray-800/40">
      <div className="flex h-full flex-col gap-2">
        <div className="flex h-14 items-center border-b px-6">
          <NavLink
            to="/dashboard"
            className="flex items-center gap-2 font-semibold"
          >
            <img src="/logo.svg" alt="Logo" className="h-6 w-6" />
            <span>Audit Call</span>
          </NavLink>
        </div>
        
        <div className="flex-1 overflow-auto py-2">
          <nav className="grid items-start px-3 text-sm font-medium gap-2">
            {menuGroups.map((group, groupIndex) => (
              <div key={groupIndex} className="space-y-2">
                <div className="px-2 pt-2">
                  <div className="mb-2 text-xs font-semibold text-gray-500 dark:text-gray-400">
                    {group.title}
                  </div>
                  {group.items.filter(item => !item.adminOnly || isAdmin).map((item) => (
                    <NavLink
                      key={item.href}
                      to={item.href}
                      className={({ isActive }) =>
                        cn(
                          "flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-50",
                          isActive
                            ? "bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-50"
                            : "hover:bg-gray-100 dark:hover:bg-gray-800"
                        )
                      }
                    >
                      <item.icon className="h-4 w-4" />
                      {item.title}
                    </NavLink>
                  ))}
                </div>
                {groupIndex < menuGroups.length - 1 && (
                  <Separator className="my-2" />
                )}
              </div>
            ))}
          </nav>
        </div>
      </div>
    </div>
  );
} 