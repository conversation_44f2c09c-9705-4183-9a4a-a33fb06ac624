import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';

const MainNav = () => {
  const location = useLocation();
  
  const items = [
    {
      title: 'Tổng quan',
      href: '/',
    },
    {
      title: 'Call Logs',
      href: '/call-logs',
    },
    {
      title: 'Mẫu đối soát',
      href: '/templates',
    },
    {
      title: 'Kỳ đối soát',
      href: '/reconciliation-periods',
    },
    {
      title: 'Đ<PERSON>i soát',
      href: '/doi-soat',
    },
  ];

  return (
    <nav className="flex items-center space-x-4 lg:space-x-6">
      {items.map((item) => (
        <Link
          key={item.href}
          to={item.href}
          className={cn(
            "text-sm font-medium transition-colors hover:text-primary",
            location.pathname === item.href
              ? "text-primary"
              : "text-muted-foreground"
          )}
        >
          {item.title}
        </Link>
      ))}
    </nav>
  );
};

export default MainNav; 