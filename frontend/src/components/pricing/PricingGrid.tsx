"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { usePartnerStore } from "@/stores/partner-store"
import { usePricingStore } from "@/stores/pricing-store"
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { PlusIcon, InfoCircledIcon, TrashIcon } from "@radix-ui/react-icons"
import { formatBillingMethod, formatCurrency } from "@/lib/formatters"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import type { PricingFormValues } from "./PricingForm"
import * as React from "react"
import { DeleteConfirmDialog } from "./DeleteConfirmDialog"

interface PricingRule {
  id: number
  type: 'revenue' | 'cost'
  billing_method: string
  billingMethod?: string
  price: number
  service_type?: string
  service_type_name?: string
  service_type_id?: number
  volume_range?: {
    id: number
    min_value: number | null
    max_value: number | null
    unit: string
    description: string
  }
}

interface ServiceType {
  id: number
  name: string
  code: string
  category?: string
}

interface VolumeRange {
  id: number
  min_value: number | null
  max_value: number | null
  unit: string
  description: string
}

interface GroupedRow {
  billingMethod: string
  volumeRange: VolumeRange
}

interface PricingGridProps {
  type: 'revenue' | 'cost'
  onAddRule: (serviceId?: number, serviceName?: string, serviceCode?: string) => void
  onEditRule: (rule: PricingRule) => void
  onDeleteRule: (id: number, type: 'revenue' | 'cost') => Promise<void>
  onRefresh?: (refreshFn: () => Promise<void>) => void
}

// Hàm ánh xạ mã dịch vụ sang tên đầy đủ
const getServiceDisplayName = (serviceCode: string, serviceTypes: ServiceType[] = []): string => {
  // Tìm tên dịch vụ từ danh sách serviceTypes
  const serviceType = serviceTypes.find(type => type.code === serviceCode);
  if (serviceType) {
    return serviceType.name;
  }
  
  // Danh sách ánh xạ mã dịch vụ sang tên đầy đủ (fallback)
  const SERVICE_TYPE_NAMES: Record<string, string> = {
    "FIXED_NOI_HAT": "Gọi cố định nội hạt",
    "FIXED_LIEN_TINH_NOI_MANG": "Gọi cố định liên tỉnh nội mạng",
    "FIXED_LIEN_TINH_KHAC_MANG": "Gọi cố định liên tỉnh mạng khác",
    "FIXED_QUOC_TE": "Gọi quốc tế",
    "MOBILE_NORMAL": "Gọi di động tại VN",
    "MOBILE_MNP": "Gọi di động chuyển mạng giữ số",
    "SERVICE_1800_VOICE_CD": "1800 - Thoại CD",
    "SERVICE_1800_VOICE_MOBILE": "1800 - Thoại Di động",
    "SERVICE_1900_VOICE_VAS": "1900 - Thoại/GTGT",
    "SERVICE_1900_SMS": "1900 - SMS",
    "INTL_OUTBOUND": "Quốc tế - Chiều đi",
    "INTL_INBOUND_VND": "Quốc tế - Chiều về (VNĐ)",
    "INTL_INBOUND_USD": "Quốc tế - Chiều về (USD)",
  };
  
  return SERVICE_TYPE_NAMES[serviceCode] || serviceCode;
};

export function PricingGrid({ type, onAddRule, onEditRule, onDeleteRule, onRefresh }: PricingGridProps) {
  const [pricingRules, setPricingRules] = useState<PricingRule[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { selectedPartner } = usePartnerStore()
  const { formData, fetchFormData } = usePricingStore()
  const { toast } = useToast()
  
  // State cho dialog xác nhận xóa
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [ruleToDelete, setRuleToDelete] = useState<PricingRule | null>(null)

  // Tạo danh sách dịch vụ duy nhất từ các quy tắc
  const uniqueServices: ServiceType[] = []
  pricingRules.forEach((rule: PricingRule) => {
    if (rule.service_type) {
      // Kiểm tra xem dịch vụ đã tồn tại trong danh sách chưa
      const existingService = uniqueServices.find(s => s.code === rule.service_type)
      if (!existingService) {
        // Lấy tên dịch vụ từ service_type_name, hoặc từ hàm getServiceDisplayName, hoặc từ service_type
        const serviceName = rule.service_type_name || 
                           (formData?.service_types ? 
                             getServiceDisplayName(rule.service_type, formData.service_types) : 
                             getServiceDisplayName(rule.service_type));
        
        uniqueServices.push({
          id: uniqueServices.length + 1, // Tạo ID tạm thời nếu không có
          name: serviceName,
          code: rule.service_type
        })
      }
    }
  })

  // Log để debug
  console.log("Unique services:", uniqueServices)
  console.log("Pricing rules:", pricingRules)
  console.log("Form data service types:", formData?.service_types)

  // Lấy danh sách phương thức tính cước và mức sản lượng duy nhất
  const uniqueBillingMethods = [...new Set(
    pricingRules.map((rule: PricingRule) => rule.billing_method || rule.billingMethod)
  )]

  const uniqueVolumeRanges = [...new Map(
    pricingRules
      .filter((rule: PricingRule) => rule.volume_range)
      .map((rule: PricingRule) => [rule.volume_range!.id, rule.volume_range])
  ).values()] as VolumeRange[]

  const fetchPricingRules = async () => {
    if (!selectedPartner) return

    try {
      setIsLoading(true)
      
      // Đảm bảo formData đã được tải
      if (!formData) {
        await fetchFormData()
      }
      
      const endpoint = type === 'revenue' 
        ? `/api/v1/pricing/revenue-pricing/by-partner/${selectedPartner.id}`
        : `/api/v1/pricing/cost-pricing/by-partner/${selectedPartner.id}`
      
      const response = await fetch(endpoint)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        throw new Error(errorData?.message || `Failed to fetch ${type} pricing rules`)
      }
      
      const data = await response.json()
      console.log(`${type} pricing response:`, data)
      
      // Đảm bảo mỗi quy tắc có thuộc tính type đúng
      const rules = Array.isArray(data) ? data.map(rule => ({
        ...rule,
        type: type // Đảm bảo type được đặt đúng (revenue hoặc cost)
      })) : []
      
      console.log(`${type} pricing rules with type:`, rules)
      setPricingRules(rules)
    } catch (error) {
      console.error(`Error fetching ${type} pricing rules:`, error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : `Could not load ${type} pricing rules`
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (selectedPartner) {
      fetchPricingRules()
    }
  }, [selectedPartner, type])

  useEffect(() => {
    if (onRefresh) {
      onRefresh(fetchPricingRules)
    }
  }, [onRefresh])

  // Tìm quy tắc cho một ô cụ thể (giao giữa dịch vụ, phương thức tính cước và mức sản lượng)
  const findRule = (serviceId: number, billingMethod: string, volumeRangeId: number) => {
    const service = uniqueServices.find(s => s.id === serviceId);
    if (!service) return undefined;
    
    return pricingRules.find((rule: PricingRule) => 
      (rule.service_type_id === serviceId || rule.service_type === service.code) && 
      (rule.billing_method || rule.billingMethod) === billingMethod && 
      rule.volume_range?.id === volumeRangeId
    )
  }

  // Kiểm tra xem một dịch vụ đã có quy tắc cho phương thức tính cước và mức sản lượng chưa
  const hasRuleForService = (serviceId: number, billingMethod: string, volumeRangeId: number) => {
    const service = uniqueServices.find(s => s.id === serviceId);
    if (!service) return false;
    
    return pricingRules.some((rule: PricingRule) => 
      (rule.service_type_id === serviceId || rule.service_type === service.code) && 
      (rule.billing_method || rule.billingMethod) === billingMethod && 
      rule.volume_range?.id === volumeRangeId
    )
  }

  // Xử lý khi click vào ô để chỉnh sửa quy tắc
  const handleCellClick = (rule?: PricingRule, serviceId?: number, serviceName?: string, serviceCode?: string, billingMethod?: string, volumeRangeId?: number) => {
    if (rule) {
      onEditRule(rule)
    } else if (serviceId !== undefined && serviceCode !== undefined && billingMethod && volumeRangeId) {
      // Lấy tên dịch vụ từ uniqueServices hoặc từ hàm getServiceDisplayName
      const displayName = serviceName || getServiceDisplayName(serviceCode, formData?.service_types) || serviceCode;
      
      // Tạo quy tắc mới với phương thức tính cước và mức sản lượng đã chọn
      onAddRule(serviceId, displayName, serviceCode)
    }
  }

  // Xử lý khi click vào nút xóa quy tắc
  const handleDeleteRule = async (e: React.MouseEvent<HTMLButtonElement>, rule: PricingRule) => {
    e.stopPropagation(); // Ngăn chặn sự kiện click lan ra ô chứa nút xóa
    
    console.log("=== GRID DELETE RULE DEBUG ===");
    console.log("Rule to delete:", rule);
    console.log("Component type prop:", type);
    
    // Mở dialog xác nhận xóa
    setRuleToDelete(rule);
    setIsDeleteDialogOpen(true);
  };
  
  // Xử lý khi xác nhận xóa
  const handleConfirmDelete = async () => {
    if (!ruleToDelete) return;
    
    try {
      // Sử dụng type từ prop của component thay vì từ rule
      console.log("Calling onDeleteRule with ID:", ruleToDelete.id, "and type:", type);
      await onDeleteRule(ruleToDelete.id, type);
      toast({
        title: "Xóa thành công",
        description: "Quy tắc tính cước đã được xóa",
      });
    } catch (error) {
      console.error("Error deleting rule:", error);
      toast({
        variant: "destructive",
        title: "Lỗi",
        description: "Không thể xóa quy tắc tính cước. Vui lòng thử lại sau.",
      });
    } finally {
      // Đóng dialog
      setIsDeleteDialogOpen(false);
      setRuleToDelete(null);
    }
  };

  if (!selectedPartner) {
    return (
      <div className="text-center p-4 text-muted-foreground">
        Vui lòng chọn đối tác để xem bảng giá cước
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-full max-w-md" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Nếu không có dữ liệu
  if (pricingRules.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold tracking-tight">
            Bảng giá cước {type === 'revenue' ? 'Thu' : 'Chi'} - {selectedPartner.name}
          </h2>
          <Button onClick={() => onAddRule()} disabled={isLoading}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Thêm quy tắc {type === 'revenue' ? 'Thu' : 'Chi'}
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center p-8 text-muted-foreground">
              Không tìm thấy quy tắc tính cước {type === 'revenue' ? 'Thu' : 'Chi'}.
              <div className="mt-4">
                <Button onClick={() => onAddRule()}>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Thêm quy tắc đầu tiên
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Nhóm các phương thức tính cước và mức sản lượng
  const groupedRows = uniqueBillingMethods.flatMap(billingMethod => 
    uniqueVolumeRanges.map(volumeRange => ({
      billingMethod,
      volumeRange
    }))
  ) as GroupedRow[]

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">
          Bảng giá cước {type === 'revenue' ? 'Thu' : 'Chi'} - {selectedPartner.name}
        </h2>
        <Button onClick={() => onAddRule()} disabled={isLoading}>
          <PlusIcon className="h-4 w-4 mr-2" />
          Thêm quy tắc {type === 'revenue' ? 'Thu' : 'Chi'}
        </Button>
      </div>

      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <InfoCircledIcon className="h-4 w-4" />
        <span>Mỗi dịch vụ chỉ có một quy tắc duy nhất cho mỗi phương thức tính cước và mức sản lượng</span>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Bảng giá theo dịch vụ</CardTitle>
          <CardDescription>
            Giá cước cho từng dịch vụ theo phương thức tính cước và mức sản lượng
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[180px]">Phương thức tính cước</TableHead>
                  <TableHead className="w-[180px]">Mức sản lượng</TableHead>
                  {uniqueServices.map(service => (
                    <TableHead key={service.id} className="text-center">
                      {service.name || getServiceDisplayName(service.code, formData?.service_types) || service.code}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {groupedRows.map((row, rowIndex) => (
                  <TableRow key={`${row.billingMethod}-${row.volumeRange.id}-${rowIndex}`}>
                    {rowIndex === 0 || groupedRows[rowIndex - 1].billingMethod !== row.billingMethod ? (
                      <TableCell 
                        className="font-medium bg-muted/30"
                        rowSpan={groupedRows.filter(r => r.billingMethod === row.billingMethod).length}
                      >
                        {formatBillingMethod(row.billingMethod)}
                      </TableCell>
                    ) : null}
                    <TableCell>{row.volumeRange.description}</TableCell>
                    {uniqueServices.map(service => {
                      const rule = findRule(service.id, row.billingMethod, row.volumeRange.id)
                      return (
                        <TableCell 
                          key={`${service.id}-${row.billingMethod}-${row.volumeRange.id}`}
                          className={`text-center cursor-pointer hover:bg-muted/50 ${rule ? 'font-medium' : 'text-muted-foreground'}`}
                          onClick={() => handleCellClick(
                            rule, 
                            service.id, 
                            service.name, 
                            service.code, 
                            row.billingMethod, 
                            row.volumeRange.id
                          )}
                        >
                          {rule ? (
                            <div className="flex items-center justify-center gap-2">
                              <Badge variant="outline" className="px-2 py-1">
                                {formatCurrency(rule.price)}
                              </Badge>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <button
                                      onClick={(e) => handleDeleteRule(e, rule)}
                                      className="text-muted-foreground hover:text-destructive transition-colors"
                                    >
                                      <TrashIcon className="h-4 w-4" />
                                    </button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Xóa quy tắc</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          ) : (
                            <span className="text-xs">Chưa có</span>
                          )}
                        </TableCell>
                      )
                    })}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Dialog xác nhận xóa */}
      <DeleteConfirmDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Xác nhận xóa quy tắc tính cước"
        description="Bạn có chắc chắn muốn xóa quy tắc tính cước này không? Hành động này không thể hoàn tác."
      />
    </div>
  )
} 