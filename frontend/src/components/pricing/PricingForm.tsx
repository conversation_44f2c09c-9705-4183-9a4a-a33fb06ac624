"use client"

import React, { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"
import { usePartnerStore } from "@/stores/partner-store"
import { usePricingStore } from "@/stores/pricing-store"
import { ServiceType, VolumeRange, BillingMethod } from "@/types/pricing"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { formatBillingMethod } from "@/lib/formatters"

const pricingFormSchema = z.object({
  type: z.enum(["revenue", "cost"]),
  billingMethod: z.string(),
  serviceType: z.string().optional().nullable(),
  volumeRangeId: z.number().optional().nullable(),
  price: z.number().min(0, "Price must be a positive number"),
  description: z.string().optional(),
})

export type PricingFormValues = z.infer<typeof pricingFormSchema>

interface PricingFormProps {
  initialData?: PricingFormValues & { id?: number }
  onSubmit: (data: PricingFormValues) => Promise<void>
  onCancel: () => void
  isSubmitting: boolean
  preSelectedServiceId?: number
  preSelectedServiceCode?: string
}

export function PricingForm({ 
  initialData, 
  onSubmit, 
  onCancel, 
  isSubmitting,
  preSelectedServiceId,
  preSelectedServiceCode
}: PricingFormProps) {
  console.log("Rendering PricingForm with initialData:", initialData, "isSubmitting:", isSubmitting)
  
  const { selectedPartner } = usePartnerStore()
  const { formData, fetchFormData } = usePricingStore()
  const [isLoadingFormData, setIsLoadingFormData] = useState(false)
  const { toast } = useToast()
  // Thêm state để theo dõi billing method hiện tại
  const [currentBillingMethod, setCurrentBillingMethod] = useState<string | undefined>(initialData?.billingMethod)

  // Determine if we're editing an existing rule
  const isEditing = !!initialData?.id
  
  // Determine if we have predefined values
  const hasPredefinedType = !!initialData?.type
  const hasPredefinedBillingMethod = !!initialData?.billingMethod
  const hasPredefinedVolumeRange = !!initialData?.volumeRangeId

  // Kiểm tra xem billing method có phải là subscription hay không
  const isSubscription = currentBillingMethod === "subscription"

  // Create form with default values
  const form = useForm<PricingFormValues>({
    resolver: zodResolver(pricingFormSchema),
    defaultValues: {
      type: initialData?.type || "revenue",
      billingMethod: initialData?.billingMethod || "",
      serviceType: initialData?.serviceType || "",
      volumeRangeId: initialData?.volumeRangeId || null,
      price: initialData?.price || 0,
      description: initialData?.description || ""
    }
  })

  // Theo dõi sự thay đổi của billing method
  useEffect(() => {
    const subscription = form.watch((value: any, { name }: { name: string | undefined }) => {
      if (name === "billingMethod") {
        setCurrentBillingMethod(value.billingMethod as string);
        
        // Nếu chọn subscription, reset các trường service type và volume range
        if (value.billingMethod === "subscription") {
          form.setValue("serviceType", "");
          form.setValue("volumeRangeId", null);
        }
      }
    });
    
    return () => subscription.unsubscribe();
  }, [form]);

  // Load form data on mount
  useEffect(() => {
    const loadFormData = async () => {
      if (!formData) {
        setIsLoadingFormData(true)
        try {
          await fetchFormData()
        } catch (error) {
          console.error("Failed to fetch form data:", error)
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to load form data"
          })
        } finally {
          setIsLoadingFormData(false)
        }
      }
      
      // Pre-select service type if provided
      if (preSelectedServiceId && formData?.service_types) {
        const serviceType = formData.service_types.find(
          (type) => type.id === preSelectedServiceId
        )
        
        if (serviceType) {
          form.setValue("serviceType", serviceType.code)
        }
      }
    }
    
    loadFormData()
  }, [fetchFormData, formData, form, preSelectedServiceId, toast])

  // Nếu có preSelectedServiceCode, sử dụng nó
  useEffect(() => {
    if (preSelectedServiceCode && form) {
      form.setValue("serviceType", preSelectedServiceCode);
    }
  }, [preSelectedServiceCode, form]);

  // Handle form submission
  const handleSubmit = async (data: PricingFormValues) => {
    if (!selectedPartner) return
    
    console.log("PricingForm handleSubmit called with data:", data);
    console.log("Initial data:", initialData);
    console.log("Is editing:", isEditing);
    console.log("Data type before formatting:", data.type);
    
    // Đảm bảo dữ liệu hợp lệ trước khi gửi
    const formattedData = {
      ...data,
      // Đảm bảo price là số
      price: typeof data.price === 'string' ? parseFloat(data.price) : data.price,
      // Đảm bảo các trường khác được giữ nguyên khi chỉnh sửa
      billingMethod: isEditing ? initialData?.billingMethod || data.billingMethod : data.billingMethod,
      serviceType: isEditing ? initialData?.serviceType || data.serviceType : data.serviceType,
      volumeRangeId: isEditing ? initialData?.volumeRangeId || data.volumeRangeId : data.volumeRangeId,
      // Đảm bảo giữ nguyên type khi chỉnh sửa
      type: isEditing ? initialData?.type || data.type : data.type,
    }
    
    console.log("Submitting form data:", formattedData);
    console.log("Data type after formatting:", formattedData.type);
    
    await onSubmit(formattedData)
  }

  // Thêm hàm xử lý trực tiếp cho nút Save
  const handleDirectSubmit = async () => {
    console.log("Direct submit button clicked");
    
    if (!selectedPartner) {
      console.error("No partner selected");
      return;
    }
    
    // Lấy giá trị hiện tại từ form
    const currentValues = form.getValues();
    console.log("Current form values:", currentValues);
    console.log("Initial data:", initialData);
    console.log("Is editing:", isEditing);
    console.log("Current values type before formatting:", currentValues.type);
    
    // Đảm bảo dữ liệu hợp lệ trước khi gửi
    const formattedData = {
      ...currentValues,
      // Đảm bảo price là số
      price: typeof currentValues.price === 'string' ? parseFloat(currentValues.price) : currentValues.price,
      // Đảm bảo các trường khác được giữ nguyên khi chỉnh sửa
      billingMethod: isEditing ? initialData?.billingMethod || currentValues.billingMethod : currentValues.billingMethod,
      serviceType: isEditing ? initialData?.serviceType || currentValues.serviceType : currentValues.serviceType,
      volumeRangeId: isEditing ? initialData?.volumeRangeId || currentValues.volumeRangeId : currentValues.volumeRangeId,
      // Đảm bảo giữ nguyên type khi chỉnh sửa
      type: isEditing ? initialData?.type || currentValues.type : currentValues.type,
      // Thêm ID nếu đang chỉnh sửa
      ...(isEditing && initialData?.id ? { id: initialData.id } : {})
    }
    
    console.log("Submitting form data directly:", formattedData);
    console.log("Data type after formatting:", formattedData.type);
    
    try {
      await onSubmit(formattedData);
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  }

  // Show loading state while fetching form data
  if (isLoadingFormData) {
    console.log("Rendering loading state");
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    )
  }

  // Require a partner to be selected
  if (!selectedPartner) {
    console.log("No partner selected");
    return (
      <div className="text-center p-4 text-red-500">
        Please select a partner first
      </div>
    )
  }

  // Check if we have the required data
  if (!formData || !formData.service_types || !formData.volume_ranges) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Missing Reference Data</AlertTitle>
        <AlertDescription>
          You need to create service types and volume ranges before adding pricing rules.
          Please go to the Settings page to create these first.
        </AlertDescription>
      </Alert>
    )
  }

  // Check if service types and volume ranges are empty
  if (formData.service_types.length === 0 || formData.volume_ranges.length === 0) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Missing Reference Data</AlertTitle>
        <AlertDescription>
          {formData.service_types.length === 0 && "No service types found. "}
          {formData.volume_ranges.length === 0 && "No volume ranges found. "}
          Please create these items first before adding pricing rules.
        </AlertDescription>
      </Alert>
    )
  }

  console.log("Rendering full form with", 
    formData.service_types.length, "service types,", 
    formData.volume_ranges.length, "volume ranges, and",
    formData.billing_methods.length, "billing methods"
  );

  // Tìm tên dịch vụ và mô tả mức sản lượng nếu đang chỉnh sửa
  let serviceName = "";
  let volumeRangeDescription = "";
  let billingMethodName = "";
  
  if (isEditing) {
    // Tìm tên dịch vụ
    if (initialData?.serviceType && formData?.service_types) {
      const serviceType = formData.service_types.find(
        (type) => type.code === initialData.serviceType
      );
      if (serviceType) {
        serviceName = serviceType.name;
      }
    }
    
    // Tìm mô tả mức sản lượng
    if (initialData?.volumeRangeId && formData?.volume_ranges) {
      const volumeRange = formData.volume_ranges.find(
        (range) => range.id === initialData.volumeRangeId
      );
      if (volumeRange) {
        volumeRangeDescription = volumeRange.description;
      }
    }
    
    // Tìm tên phương thức tính cước
    if (initialData?.billingMethod) {
      billingMethodName = formatBillingMethod(initialData.billingMethod);
    }
  } else if (hasPredefinedBillingMethod || hasPredefinedVolumeRange) {
    // Tìm tên phương thức tính cước cho trường hợp thêm mới với phương thức đã xác định
    if (initialData?.billingMethod) {
      billingMethodName = formatBillingMethod(initialData.billingMethod);
    }
    
    // Tìm mô tả mức sản lượng cho trường hợp thêm mới với mức sản lượng đã xác định
    if (initialData?.volumeRangeId && formData?.volume_ranges) {
      const volumeRange = formData.volume_ranges.find(
        (range) => range.id === initialData.volumeRangeId
      );
      if (volumeRange) {
        volumeRangeDescription = volumeRange.description;
      }
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Hiển thị thông tin quy tắc khi đang chỉnh sửa */}
        {isEditing && (
          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="text-sm font-medium mb-3">Thông tin quy tắc</h3>
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div className="border rounded-md p-3">
                  <p className="text-sm font-medium text-muted-foreground mb-1">Loại quy tắc:</p>
                  <p className="font-medium text-base">
                    {initialData?.type === 'revenue' ? 'Thu (Revenue)' : 'Chi (Cost)'}
                  </p>
                </div>
                <div className="border rounded-md p-3">
                  <p className="text-sm font-medium text-muted-foreground mb-1">Phương thức tính cước:</p>
                  <p className="font-medium text-base">{billingMethodName || "N/A"}</p>
                </div>
              </div>
              <div className="border rounded-md p-3">
                <p className="text-sm font-medium text-muted-foreground mb-1">Dịch vụ:</p>
                <p className="font-medium text-base">{serviceName || "N/A"}</p>
              </div>
              <div className="border rounded-md p-3">
                <p className="text-sm font-medium text-muted-foreground mb-1">Mức sản lượng:</p>
                <p className="font-medium text-base">{volumeRangeDescription || "N/A"}</p>
              </div>
            </div>
          </div>
        )}
        
        {/* Hiển thị thông tin phương thức tính cước và mức sản lượng đã xác định khi thêm mới */}
        {!isEditing && (hasPredefinedBillingMethod || hasPredefinedVolumeRange || hasPredefinedType) && (
          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="text-sm font-medium mb-3">Thông tin đã xác định</h3>
            <div className="space-y-3">
              {hasPredefinedType && (
                <div className="border rounded-md p-3">
                  <p className="text-sm font-medium text-muted-foreground mb-1">Loại quy tắc:</p>
                  <p className="font-medium text-base">{initialData?.type === 'revenue' ? 'Thu (Revenue)' : 'Chi (Cost)'}</p>
                </div>
              )}
              {hasPredefinedBillingMethod && (
                <div className="border rounded-md p-3">
                  <p className="text-sm font-medium text-muted-foreground mb-1">Phương thức tính cước:</p>
                  <p className="font-medium text-base">{billingMethodName || "N/A"}</p>
                </div>
              )}
              {hasPredefinedVolumeRange && (
                <div className="border rounded-md p-3">
                  <p className="text-sm font-medium text-muted-foreground mb-1">Mức sản lượng:</p>
                  <p className="font-medium text-base">{volumeRangeDescription || "N/A"}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Step 1: Choose Revenue or Cost - Chỉ hiển thị khi thêm mới và chưa có type */}
        {!isEditing && !hasPredefinedType && (
          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="text-sm font-medium mb-3">Bước 1: Chọn loại quy tắc</h3>
            <FormField
              key="type-field"
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Loại quy tắc</FormLabel>
                  <Select
                    disabled={isSubmitting}
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn loại quy tắc" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="revenue">Thu (Revenue)</SelectItem>
                      <SelectItem value="cost">Chi (Cost)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {/* Step 2: Choose Billing Method - Chỉ hiển thị khi thêm mới và chưa có billingMethod */}
        {!isEditing && !hasPredefinedBillingMethod && (
          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="text-sm font-medium mb-3">Bước 2: Chọn phương thức tính cước</h3>
            <FormField
              key="billing-method-field"
              control={form.control}
              name="billingMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phương thức tính cước</FormLabel>
                  <Select
                    disabled={isSubmitting}
                    onValueChange={(value) => {
                      field.onChange(value);
                      // Nếu chọn subscription, reset các trường service type và volume range
                      if (value === "subscription") {
                        form.setValue("serviceType", "");
                        form.setValue("volumeRangeId", null);
                      }
                    }}
                    defaultValue={field.value}
                    value={field.value || undefined}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn phương thức tính cước" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {formData.billing_methods.map((method) => (
                        <SelectItem key={method} value={method}>
                          {formatBillingMethod(method)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {/* Step 3: Choose Service Type - Hiển thị khi thêm mới và không phải subscription */}
        {!isEditing && !isSubscription && (
          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="text-sm font-medium mb-3">Bước 3: Chọn loại dịch vụ</h3>
            <FormField
              key="service-type-field"
              control={form.control}
              name="serviceType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Loại dịch vụ</FormLabel>
                  <Select
                    disabled={isSubmitting}
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value || undefined}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn loại dịch vụ" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {formData.service_types.map((type) => (
                        <SelectItem key={type.id} value={type.code}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {/* Step 4: Choose Volume Range - Hiển thị khi thêm mới, không phải subscription và chưa có volumeRangeId */}
        {!isEditing && !isSubscription && !hasPredefinedVolumeRange && (
          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="text-sm font-medium mb-3">Bước 4: Chọn mức sản lượng</h3>
            <FormField
              key="volume-range-field"
              control={form.control}
              name="volumeRangeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mức sản lượng</FormLabel>
                  <Select
                    disabled={isSubmitting}
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    defaultValue={field.value?.toString()}
                    value={field.value?.toString() || undefined}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn mức sản lượng" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {formData.volume_ranges.map((range) => (
                        <SelectItem key={range.id} value={range.id.toString()}>
                          {range.description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {/* Step 5: Set Price - Luôn hiển thị */}
        <div className="p-4 border rounded-md bg-muted/30">
          <h3 className="text-sm font-medium mb-3">
            {isEditing 
              ? "Chỉnh sửa giá cước" 
              : isSubscription 
                ? "Nhập giá cước thuê bao" 
                : "Bước 5: Nhập giá cước"}
          </h3>
          <FormField
            key="price-field"
            control={form.control}
            name="price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Giá cước</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="0.00"
                    disabled={isSubmitting}
                    {...field}
                    onChange={(e) => {
                      const value = e.target.value;
                      const numValue = parseFloat(value);
                      if (!isNaN(numValue)) {
                        field.onChange(numValue);
                      } else {
                        field.onChange(0);
                      }
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Optional: Description (for cost only) - Chỉ hiển thị khi thêm mới hoặc đang chỉnh sửa quy tắc chi */}
        {((!isEditing && form.watch("type") === "cost") || (isEditing && initialData?.type === "cost")) && (
          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="text-sm font-medium mb-3">Tùy chọn: Thêm mô tả</h3>
            <FormField
              key="description-field"
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mô tả</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Nhập mô tả" {...field} disabled={isSubmitting} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        )}

        <div className="flex justify-end space-x-2 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Hủy
          </Button>
          <Button 
            type="button" 
            disabled={isSubmitting}
            onClick={handleDirectSubmit}
          >
            {isSubmitting ? (
              <React.Fragment>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Đang lưu...
              </React.Fragment>
            ) : (
              "Lưu"
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
} 