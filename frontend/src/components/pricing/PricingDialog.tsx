"use client"

import React from "react"
import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { PricingForm } from "./PricingForm"
import type { PricingFormValues } from "./PricingForm"
import { useToast } from "@/components/ui/use-toast"

interface PricingDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: PricingFormValues) => Promise<void>
  initialData?: PricingFormValues & { id?: number }
  preSelectedServiceId?: number
  preSelectedServiceName?: string
  preSelectedServiceCode?: string
}

export function PricingDialog({ 
  isOpen, 
  onClose, 
  onSubmit, 
  initialData,
  preSelectedServiceId,
  preSelectedServiceName,
  preSelectedServiceCode
}: PricingDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (data: PricingFormValues) => {
    console.log("PricingDialog handleSubmit called with data:", data);
    console.log("Current isSubmitting state:", isSubmitting);
    console.log("Initial data:", initialData);
    console.log("Data type before check:", data.type);
    
    setIsSubmitting(true)
    console.log("Set isSubmitting to true");
    
    try {
      console.log("Calling parent onSubmit function");
      if (initialData?.type && !data.type) {
        console.log("Setting data.type from initialData.type:", initialData.type);
        data.type = initialData.type;
      }
      console.log("Data type after check:", data.type);
      await onSubmit(data)
      console.log("Parent onSubmit completed successfully");
    } catch (error) {
      console.error('Error submitting form:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to submit pricing rule"
      })
    } finally {
      console.log("Setting isSubmitting back to false");
      setIsSubmitting(false)
    }
  }

  console.log("Rendering PricingDialog, isOpen:", isOpen, "isSubmitting:", isSubmitting);
  console.log("Initial data for PricingDialog:", initialData);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {initialData?.id ? "Chỉnh sửa quy tắc tính cước" : "Thêm quy tắc tính cước mới"}
            {preSelectedServiceName && ` - ${preSelectedServiceName}`}
          </DialogTitle>
          <DialogDescription>
            {initialData?.id 
              ? "Cập nhật thông tin quy tắc tính cước hiện có" 
              : "Thêm quy tắc tính cước mới cho đối tác"}
          </DialogDescription>
        </DialogHeader>
        <PricingForm 
          initialData={initialData} 
          onSubmit={handleSubmit} 
          onCancel={onClose}
          isSubmitting={isSubmitting}
          preSelectedServiceId={preSelectedServiceId}
          preSelectedServiceCode={preSelectedServiceCode}
        />
      </DialogContent>
    </Dialog>
  )
} 