"use client"

import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { formatCurrency } from "@/lib/formatters"

interface ServiceDetailsPopupProps {
  isOpen: boolean
  onClose: () => void
  serviceName: string
  serviceDetails: ServiceDetail[]
}

interface ServiceDetail {
  id: number
  billingMethod: string
  volumeRange?: {
    min_value: string | number
    max_value: string | number | null
    unit: string
    description: string
  }
  price: string | number
}

export function ServiceDetailsPopup({
  isOpen,
  onClose,
  serviceName,
  serviceDetails
}: ServiceDetailsPopupProps) {
  // Hàm định dạng phương thức tính cước
  const formatBillingMethod = (method: string): string => {
    switch (method) {
      case 'block_6s':
        return 'Block 6s';
      case 'one_sec_plus':
        return '1s+';
      case 'one_min_plus':
        return '1\'+ (phút)';
      case 'subscription':
        return 'Cước thuê bao';
      default:
        return method;
    }
  };

  // Hàm định dạng đơn vị
  const formatUnit = (unit: string): string => {
    switch (unit) {
      case 'minute_per_month':
        return 'phút/tháng';
      case 'minute':
        return 'phút';
      case 'VND':
        return 'VNĐ';
      default:
        return unit;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Chi tiết dịch vụ: {serviceName}</DialogTitle>
          <DialogDescription>
            Danh sách các mức giá theo phương thức tính cước và khối lượng
          </DialogDescription>
        </DialogHeader>
        
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Phương thức tính cước</TableHead>
              <TableHead>Khoảng khối lượng</TableHead>
              <TableHead>Giá</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {serviceDetails.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="text-center">
                  Không có dữ liệu
                </TableCell>
              </TableRow>
            ) : (
              serviceDetails.map((detail) => (
                <TableRow key={detail.id}>
                  <TableCell>{formatBillingMethod(detail.billingMethod)}</TableCell>
                  <TableCell>
                    {detail.volumeRange 
                      ? `${detail.volumeRange.min_value || 0} - ${detail.volumeRange.max_value || '∞'} ${formatUnit(detail.volumeRange.unit)}`
                      : 'N/A'
                    }
                  </TableCell>
                  <TableCell>{formatCurrency(detail.price)}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </DialogContent>
    </Dialog>
  )
} 