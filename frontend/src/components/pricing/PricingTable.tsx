"use client"

import { useState, useEffect, useRef } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { PlusIcon, TrashIcon } from "@radix-ui/react-icons"
import { useToast } from "@/components/ui/use-toast"
import { usePartnerStore } from "@/stores/partner-store"
import { usePricingStore } from "@/stores/pricing-store"
import type { PricingFormValues } from "./PricingForm"
import { ServiceDetailsPopup } from "./ServiceDetailsPopup"
import { formatBillingMethod, formatCurrency, formatPricingType, formatUnit } from "@/lib/formatters"
import { DeleteConfirmDialog } from "./DeleteConfirmDialog"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"

interface PricingRule extends PricingFormValues {
  id: number
  created_at: string
  updated_at: string
  service_type?: {
    id: number
    name: string
    code: string
    category: string
  }
  volume_range?: {
    id: number
    min_value: number | null
    max_value: number | null
    unit: string
    description: string
  }
}

interface PricingTableProps {
  type?: 'revenue' | 'cost'  // Thêm prop type để lọc quy tắc
  onAddRule: (serviceId?: number, serviceName?: string, serviceCode?: string, billingMethod?: string, volumeRangeId?: number) => void
  onEditRule: (rule: PricingRule) => void
  onDeleteRule: (id: number, type: 'revenue' | 'cost') => Promise<void>
  onRefresh?: (refreshFn: () => Promise<void>) => void
}

export function PricingTable({ type, onAddRule, onEditRule, onDeleteRule, onRefresh }: PricingTableProps) {
  const [revenuePricing, setRevenuePricing] = useState<PricingRule[]>([])
  const [costPricing, setCostPricing] = useState<PricingRule[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { selectedPartner } = usePartnerStore()
  const { formData, fetchFormData } = usePricingStore()
  const { toast } = useToast()
  const tableRef = useRef<HTMLTableElement>(null)
  
  // State cho popup
  const [isPopupOpen, setIsPopupOpen] = useState(false)
  const [selectedService, setSelectedService] = useState<{
    name: string;
    details: any[];
  }>({ name: '', details: [] })
  
  // State cho dialog xác nhận xóa
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [ruleToDelete, setRuleToDelete] = useState<{id: number, type: 'revenue' | 'cost'} | null>(null)

  // Đảm bảo formData được tải
  useEffect(() => {
    if (!formData) {
      fetchFormData().catch(error => {
        console.error('Error fetching form data:', error)
        toast({
          variant: "destructive",
          title: "Error",
          description: "Could not load form data"
        })
      })
    }
  }, [])

  // Hàm helper để lấy tên dịch vụ từ mã dịch vụ
  const getServiceName = (serviceCode: string): string => {
    if (!serviceCode) return "N/A";
    
    // Tìm trong formData.service_types
    if (formData?.service_types) {
      const serviceType = formData.service_types.find((t: any) => t.code === serviceCode);
      if (serviceType?.name) return serviceType.name;
    }
    
    // Nếu không tìm thấy, trả về mã dịch vụ
    return serviceCode;
  }

  const fetchPricingRules = async () => {
    if (!selectedPartner) return

    try {
      setIsLoading(true)
      const [revenueResponse, costResponse] = await Promise.all([
        fetch(`/api/v1/pricing/revenue-pricing/by-partner/${selectedPartner.id}`),
        fetch(`/api/v1/pricing/cost-pricing/by-partner/${selectedPartner.id}`)
      ])
      
      if (!revenueResponse.ok || !costResponse.ok) {
        const errorData = await (revenueResponse.ok ? costResponse : revenueResponse).json().catch(() => null)
        throw new Error(errorData?.message || "Failed to fetch pricing rules")
      }
      
      const [revenueData, costData] = await Promise.all([
        revenueResponse.json(),
        costResponse.json()
      ])
      
      console.log('Revenue pricing response:', revenueData)
      console.log('Cost pricing response:', costData)
      
      const revenueRules = Array.isArray(revenueData) ? revenueData : []
      const costRules = Array.isArray(costData) ? costData : []
      
      setRevenuePricing(revenueRules)
      setCostPricing(costRules)
    } catch (error) {
      console.error('Error fetching pricing rules:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Could not load pricing rules"
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (selectedPartner) {
      fetchPricingRules()
    }
  }, [selectedPartner])

  useEffect(() => {
    if (onRefresh) {
      onRefresh(fetchPricingRules)
    }
  }, [onRefresh])

  const handleDelete = async (id: number, ruleType: 'revenue' | 'cost') => {
    console.log("=== TABLE DELETE RULE DEBUG ===");
    console.log("Deleting rule with ID:", id);
    console.log("Rule type:", ruleType);
    console.log("Component type prop:", type);
    
    // Mở dialog xác nhận xóa
    setRuleToDelete({id, type: ruleType});
    setIsDeleteDialogOpen(true);
  };
  
  // Xử lý khi xác nhận xóa
  const handleConfirmDelete = async () => {
    if (!ruleToDelete) return;
    
    try {
      await onDeleteRule(ruleToDelete.id, ruleToDelete.type)
      await fetchPricingRules() // Refresh the list after deletion
    } catch (error) {
      console.error('Error deleting pricing rule:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: `Could not delete ${ruleToDelete.type} pricing rule`
      })
    } finally {
      // Đóng dialog
      setIsDeleteDialogOpen(false);
      setRuleToDelete(null);
    }
  };

  // Hàm xử lý khi click vào dịch vụ
  const handleServiceClick = (serviceName: string, serviceId: number) => {
    // Lọc tất cả các quy tắc có cùng dịch vụ
    const revenueRules = revenuePricing.filter(
      (rule) => rule.service_type?.id === serviceId
    ).map(rule => ({...rule, type: 'revenue'})) // Đảm bảo type là 'revenue'
    
    const costRules = costPricing.filter(
      (rule) => rule.service_type?.id === serviceId
    ).map(rule => ({...rule, type: 'cost'})) // Đảm bảo type là 'cost'
    
    // Kết hợp cả hai loại quy tắc
    const allRules = [...revenueRules, ...costRules]
    
    console.log("Service rules:", allRules)
    
    setSelectedService({
      name: serviceName,
      details: allRules.map(rule => ({
        id: rule.id,
        type: rule.type, // Thêm trường type
        billingMethod: rule.billing_method || rule.billingMethod,
        volumeRange: rule.volume_range,
        price: rule.price
      }))
    })
    
    setIsPopupOpen(true)
  }

  if (!selectedPartner) {
    return (
      <div className="text-center p-4 text-muted-foreground">
        Please select a partner to view pricing rules
      </div>
    )
  }

  // Lọc quy tắc theo loại (thu/chi) nếu có
  const filteredRules = type 
    ? type === 'revenue' ? revenuePricing : costPricing
    : [...revenuePricing, ...costPricing];
  
  // Nhóm các quy tắc theo phương thức tính cước và mức sản lượng
  const groupedRules = new Map();
  
  // Sắp xếp các quy tắc theo thời gian tạo giảm dần trước khi nhóm
  const sortedRules = [...filteredRules].sort((a, b) => {
    // Chuyển đổi chuỗi thời gian thành đối tượng Date để so sánh
    const dateA = new Date(a.created_at);
    const dateB = new Date(b.created_at);
    return dateB.getTime() - dateA.getTime(); // Sắp xếp giảm dần (mới nhất lên đầu)
  });
  
  // Lưu thời gian tạo mới nhất cho mỗi nhóm để sắp xếp các nhóm
  const groupCreationTimes = new Map();
  
  sortedRules.forEach(rule => {
    const billingMethod = rule.billing_method || rule.billingMethod;
    const volumeRange = rule.volume_range?.description || 'N/A';
    const volumeRangeId = rule.volume_range?.id;
    
    const key = `${billingMethod}|${volumeRange}`;
    
    // Lưu thời gian tạo mới nhất cho nhóm
    if (!groupCreationTimes.has(key)) {
      groupCreationTimes.set(key, new Date(rule.created_at).getTime());
    } else {
      const currentNewestTime = groupCreationTimes.get(key);
      const ruleTime = new Date(rule.created_at).getTime();
      // Nếu quy tắc hiện tại mới hơn, cập nhật thời gian mới nhất cho nhóm
      if (ruleTime > currentNewestTime) {
        groupCreationTimes.set(key, ruleTime);
      }
    }
    
    if (!groupedRules.has(key)) {
      groupedRules.set(key, {
        billingMethod,
        volumeRange,
        volumeRangeId,
        rules: [rule]
      });
    } else {
      const group = groupedRules.get(key);
      group.rules.push(rule);
      groupedRules.set(key, group);
    }
  });
  
  // Sắp xếp các quy tắc trong mỗi nhóm theo thời gian tạo giảm dần
  groupedRules.forEach((group) => {
    group.rules.sort((a, b) => {
      const dateA = new Date(a.created_at);
      const dateB = new Date(b.created_at);
      return dateB.getTime() - dateA.getTime(); // Sắp xếp giảm dần (mới nhất lên đầu)
    });
  });
  
  // Chuyển Map thành mảng để render và sắp xếp các nhóm theo thời gian tạo giảm dần
  const groupedRulesList = Array.from(groupedRules.values()).sort((a, b) => {
    const keyA = `${a.billingMethod}|${a.volumeRange}`;
    const keyB = `${b.billingMethod}|${b.volumeRange}`;
    
    const timeA = groupCreationTimes.get(keyA);
    const timeB = groupCreationTimes.get(keyB);
    
    return timeB - timeA; // Sắp xếp giảm dần (mới nhất lên đầu)
  });

  // Tiêu đề dựa trên loại quy tắc
  const tableTitle = type === 'revenue' 
    ? "Quy tắc tính cước - Thu" 
    : type === 'cost' 
      ? "Quy tắc tính cước - Chi" 
      : "Quy tắc tính cước";

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">
          {tableTitle} {selectedPartner.name ? `- ${selectedPartner.name}` : ''}
        </h2>
        <Button onClick={() => onAddRule()} disabled={isLoading}>
          <PlusIcon className="h-4 w-4 mr-2" />
          Thêm quy tắc {type === 'revenue' ? 'Thu' : type === 'cost' ? 'Chi' : ''}
        </Button>
      </div>
      
      <Table ref={tableRef}>
        <TableHeader>
          <TableRow>
            <TableHead className="font-medium text-base">Phương thức tính cước</TableHead>
            <TableHead className="font-medium text-base">Mức sản lượng</TableHead>
            <TableHead className="font-medium text-base">Quy tắc</TableHead>
            <TableHead className="font-medium text-base">Giá</TableHead>
            <TableHead className="font-medium text-base text-right">Thao tác</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={4} className="text-center">
                Đang tải...
              </TableCell>
            </TableRow>
          ) : (
            groupedRulesList.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center">
                  Không tìm thấy quy tắc tính cước {type === 'revenue' ? 'Thu' : type === 'cost' ? 'Chi' : ''}
                </TableCell>
              </TableRow>
            ) : (
              groupedRulesList.map((group, index) => (
                <TableRow key={`group-${index}`}>
                  <TableCell>
                    <div className="font-medium">{formatBillingMethod(group.billingMethod)}</div>
                  </TableCell>
                  <TableCell>{group.volumeRange}</TableCell>
                  <TableCell>
                    <div className="flex flex-col space-y-2">
                      {group.rules.map((rule) => (
                        <div key={`rule-${rule.id}`} className="flex items-center">
                          <Button 
                            variant="link" 
                            className="p-0 h-auto text-left justify-start"
                            onClick={() => {
                              console.log("Clicked rule:", rule);
                              
                              // Đảm bảo rule có trường type đúng dựa trên component type
                              const ruleWithType = {
                                ...rule,
                                // Nếu component có prop type, sử dụng nó
                                // Nếu không, sử dụng type từ rule nếu có
                                // Nếu cả hai không có, kiểm tra xem rule có trong mảng nào
                                type: type || rule.type || (revenuePricing.some(r => r.id === rule.id) ? 'revenue' : 'cost')
                              };
                              
                              console.log("Rule type:", ruleWithType.type);
                              console.log("Component type prop:", type);
                              
                              onEditRule(ruleWithType);
                            }}
                          >
                            {!type && (
                              <span className="mr-2">
                                {rule.type === 'revenue' ? 
                                  <span className="text-emerald-600 font-medium">Thu</span> : 
                                  <span className="text-rose-600 font-medium">Chi</span>
                                }
                              </span>
                            )}
                            {
                              // Cải thiện logic hiển thị tên dịch vụ
                              typeof rule.service_type === 'object' && rule.service_type?.name
                                ? rule.service_type.name
                                : rule.service_type_name 
                                  ? rule.service_type_name
                                  : typeof rule.service_type === 'string'
                                    ? getServiceName(rule.service_type)
                                    : "N/A"
                            }
                          </Button>
                        </div>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col space-y-2">
                      {group.rules.map((rule) => (
                        <div key={`rule-price-${rule.id}`} className="flex items-center justify-between">
                          <Badge variant="outline" className="px-2 py-1">
                            {formatCurrency(rule.price)}
                          </Badge>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive transition-colors"
                                  onClick={() => handleDelete(rule.id, type as 'revenue' | 'cost')}
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Xóa quy tắc</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => onAddRule(undefined, undefined, undefined, group.billingMethod, group.volumeRangeId)}
                    >
                      Thêm quy tắc
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )
          )}
        </TableBody>
      </Table>
      
      {/* Popup chi tiết dịch vụ */}
      <ServiceDetailsPopup 
        isOpen={isPopupOpen}
        onClose={() => setIsPopupOpen(false)}
        serviceName={selectedService.name}
        serviceDetails={selectedService.details}
      />
      
      {/* Dialog xác nhận xóa */}
      <DeleteConfirmDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Xác nhận xóa quy tắc tính cước"
        description="Bạn có chắc chắn muốn xóa quy tắc tính cước này không? Hành động này không thể hoàn tác."
      />
    </div>
  )
} 