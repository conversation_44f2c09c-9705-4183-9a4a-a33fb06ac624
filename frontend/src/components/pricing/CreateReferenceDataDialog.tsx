"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"
import { api } from "@/lib/api"

// Service Type Form Schema
const serviceTypeSchema = z.object({
  code: z.string().min(1, "Code is required"),
  name: z.string().min(1, "Name is required"),
  category: z.enum(["FIXED_LINE", "MOBILE", "1900", "1800", "INTERNATIONAL"])
})

// Volume Range Form Schema
const volumeRangeSchema = z.object({
  min_value: z.coerce.number().optional(),
  max_value: z.coerce.number().optional(),
  unit: z.enum(["VND", "minute"]),
  description: z.string().min(1, "Description is required")
})

type ServiceTypeFormValues = z.infer<typeof serviceTypeSchema>
type VolumeRangeFormValues = z.infer<typeof volumeRangeSchema>

interface CreateReferenceDataDialogProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  defaultTab?: "service-types" | "volume-ranges"
}

export function CreateReferenceDataDialog({
  isOpen,
  onClose,
  onSuccess,
  defaultTab = "service-types"
}: CreateReferenceDataDialogProps) {
  const [activeTab, setActiveTab] = useState<"service-types" | "volume-ranges">(defaultTab)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Service Type Form
  const serviceTypeForm = useForm<ServiceTypeFormValues>({
    resolver: zodResolver(serviceTypeSchema),
    defaultValues: {
      code: "",
      name: "",
      category: "FIXED_LINE"
    }
  })

  // Volume Range Form
  const volumeRangeForm = useForm<VolumeRangeFormValues>({
    resolver: zodResolver(volumeRangeSchema),
    defaultValues: {
      min_value: 0,
      max_value: undefined,
      unit: "minute",
      description: ""
    }
  })

  const handleSubmitServiceType = async (data: ServiceTypeFormValues) => {
    setIsSubmitting(true)
    try {
      await api.post("/api/v1/pricing/service-types", data)
      toast({
        title: "Success",
        description: "Service type created successfully",
      })
      serviceTypeForm.reset()
      onSuccess()
    } catch (error) {
      console.error("Error creating service type:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create service type"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSubmitVolumeRange = async (data: VolumeRangeFormValues) => {
    setIsSubmitting(true)
    try {
      await api.post("/api/v1/pricing/volume-ranges", data)
      toast({
        title: "Success",
        description: "Volume range created successfully",
      })
      volumeRangeForm.reset()
      onSuccess()
    } catch (error) {
      console.error("Error creating volume range:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create volume range"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create Reference Data</DialogTitle>
          <DialogDescription>
            Create service types and volume ranges for your pricing rules.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="service-types">Service Types</TabsTrigger>
            <TabsTrigger value="volume-ranges">Volume Ranges</TabsTrigger>
          </TabsList>
          
          <TabsContent value="service-types">
            <Form {...serviceTypeForm}>
              <form onSubmit={serviceTypeForm.handleSubmit(handleSubmitServiceType)} className="space-y-4 py-4">
                <FormField
                  control={serviceTypeForm.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Code</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., FIXED_LOCAL" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={serviceTypeForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Fixed Line Local" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={serviceTypeForm.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="FIXED_LINE">Fixed Line</SelectItem>
                          <SelectItem value="MOBILE">Mobile</SelectItem>
                          <SelectItem value="1900">1900 Service</SelectItem>
                          <SelectItem value="1800">1800 Service</SelectItem>
                          <SelectItem value="INTERNATIONAL">International</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      'Create Service Type'
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </TabsContent>
          
          <TabsContent value="volume-ranges">
            <Form {...volumeRangeForm}>
              <form onSubmit={volumeRangeForm.handleSubmit(handleSubmitVolumeRange)} className="space-y-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={volumeRangeForm.control}
                    name="min_value"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Min Value</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={volumeRangeForm.control}
                    name="max_value"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Max Value (optional)</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={volumeRangeForm.control}
                  name="unit"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unit</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="minute">Minute</SelectItem>
                          <SelectItem value="VND">VND</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={volumeRangeForm.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., 0-1,000 minutes" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      'Create Volume Range'
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
} 