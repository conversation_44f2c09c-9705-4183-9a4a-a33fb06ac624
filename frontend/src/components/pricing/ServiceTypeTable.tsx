import { UseFormReturn } from "react-hook-form";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ServiceType, VolumeRange, BillingMethod } from "@/types/pricing";
import { ChangeEvent } from "react";

interface ServiceTypeTableProps {
  serviceTypes: ServiceType[];
  volumeRanges: VolumeRange[];
  billingMethods: BillingMethod[];
  form: UseFormReturn<{
    billingMethod: BillingMethod;
    items: Array<{
      volumeRangeId?: number;
      price: number;
      description?: string;
    }>;
  }>;
  isLoading?: boolean;
}

export function ServiceTypeTable({
  serviceTypes,
  volumeRanges,
  billingMethods,
  form,
  isLoading = false,
}: ServiceTypeTableProps) {
  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name="billingMethod"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Billing Method</FormLabel>
            <Select
              onValueChange={field.onChange}
              defaultValue={field.value}
              disabled={isLoading}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select billing method" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {billingMethods.map((method) => (
                  <SelectItem key={method} value={method}>
                    {method}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Service Type</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Volume Range</TableHead>
            <TableHead>Price</TableHead>
            <TableHead>Description</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {serviceTypes.map((serviceType, index) => (
            <TableRow key={serviceType.id}>
              <TableCell>{serviceType.name}</TableCell>
              <TableCell>{serviceType.category}</TableCell>
              <TableCell>
                <FormField
                  control={form.control}
                  name={`items.${index}.volumeRangeId`}
                  render={({ field }) => (
                    <FormItem>
                      <Select
                        onValueChange={(value: string) => field.onChange(parseInt(value))}
                        value={field.value?.toString()}
                        disabled={isLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select volume range" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {volumeRanges.map((range) => (
                            <SelectItem key={range.id} value={range.id.toString()}>
                              {range.description}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TableCell>
              <TableCell>
                <FormField
                  control={form.control}
                  name={`items.${index}.price`}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          {...field}
                          onChange={(e: ChangeEvent<HTMLInputElement>) =>
                            field.onChange(parseFloat(e.target.value))
                          }
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TableCell>
              <TableCell>
                <FormField
                  control={form.control}
                  name={`items.${index}.description`}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          placeholder="Optional description"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
} 