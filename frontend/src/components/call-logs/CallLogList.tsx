import { useState, useEffect, useRef } from "react";
import { useToast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/ui/date-picker";
import { 
  RefreshCw, 
  PhoneCallIcon, 
  PhoneOutgoingIcon,
  PhoneIncomingIcon,
  ArrowDown, 
  ArrowUp,
  SlidersHorizontal,
  Smartphone,
  Phone,
  Globe,
  ArrowRightLeft,
  HelpCircle,
  ChevronUp
} from "lucide-react";
import { CallType, CallLog, PaginatedCallLogs, NumberType } from "@/types/call-log";
import { callLogService } from "@/services/call-log-service";
import { formatDate, formatTime, formatDuration } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface CallLogListProps {
  fileId?: number;
  refreshTrigger?: number;
  onRefresh?: () => void;
}

export function CallLogList({ fileId, refreshTrigger = 0, onRefresh }: CallLogListProps) {
  const [logs, setLogs] = useState<PaginatedCallLogs | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [callTypeFilter, setCallTypeFilter] = useState<CallType | "all">("all");
  const [callerFilter, setCallerFilter] = useState("");
  const [calleeFilter, setCalleeFilter] = useState("");
  const [dateFromFilter, setDateFromFilter] = useState<Date | undefined>(undefined);
  const [dateToFilter, setDateToFilter] = useState<Date | undefined>(undefined);
  const [callerTypeFilter, setCallerTypeFilter] = useState<string>("all");
  const [calleeTypeFilter, setCalleeTypeFilter] = useState<string>("all");
  const [sortField, setSortField] = useState<string>("begin_time");
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [pageSize, setPageSize] = useState<number>(50);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const { toast } = useToast();

  // Fetch logs when filters or page changes
  useEffect(() => {
    fetchLogs();
  }, [page, callTypeFilter, callerFilter, calleeFilter, dateFromFilter, dateToFilter, 
      callerTypeFilter, calleeTypeFilter, sortField, sortOrder, pageSize, fileId, refreshTrigger]);

  // Handle scroll to show/hide the scroll-to-top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const fetchLogs = async () => {
    setIsLoading(true);
    try {
      const params: any = {
        skip: (page - 1) * pageSize,
        limit: pageSize,
      };
      
      if (fileId) {
        params.file_id = fileId;
      }
      
      if (callTypeFilter !== "all") {
        params.call_type = callTypeFilter;
      }
      
      if (callerFilter) {
        params.caller = callerFilter;
      }
      
      if (calleeFilter) {
        params.callee = calleeFilter;
      }
      
      if (dateFromFilter) {
        params.call_date_from = dateFromFilter.toISOString().split('T')[0];
      }
      
      if (dateToFilter) {
        params.call_date_to = dateToFilter.toISOString().split('T')[0];
      }
      
      // Thêm các tham số lọc nâng cao
      if (callerTypeFilter !== "all") {
        params.caller_type = callerTypeFilter;
      }
      
      if (calleeTypeFilter !== "all") {
        params.callee_type = calleeTypeFilter;
      }
      
      // Tham số sắp xếp
      params.sort_field = sortField;
      params.sort_order = sortOrder;
      
      const data = await callLogService.getLogs(params);
      setLogs(data);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error fetching call logs",
        description: error.message || "An unknown error occurred",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchLogs();
    if (onRefresh) {
      onRefresh();
    }
  };

  const handleClearFilters = () => {
    setCallTypeFilter("all");
    setCallerFilter("");
    setCalleeFilter("");
    setDateFromFilter(undefined);
    setDateToFilter(undefined);
    setCallerTypeFilter("all");
    setCalleeTypeFilter("all");
    setSortField("begin_time");
    setSortOrder("desc");
    setPage(1);
  };

  const handleSortChange = (field: string) => {
    if (sortField === field) {
      // Nếu đang sắp xếp theo field này rồi, đổi hướng sắp xếp
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Đổi field sắp xếp, mặc định là desc
      setSortField(field);
      setSortOrder('desc');
    }
  };

  const getSortIcon = (field: string) => {
    if (sortField !== field) return null;
    return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  const getCallTypeIcon = (callType: CallType) => {
    switch (callType) {
      case CallType.IN:
        return <PhoneIncomingIcon className="h-4 w-4 text-blue-500" />;
      case CallType.OUT:
        return <PhoneOutgoingIcon className="h-4 w-4 text-purple-500" />;
      default:
        return null;
    }
  };

  const getNumberTypeBadge = (type: string, isCallee: boolean = false) => {
    let label = "";
    let icon = null;
    let bgColor = "";
    let textColor = "";
    let borderColor = "";
    
    switch (type) {
      case "mobile":
        label = "Di động";
        icon = <Smartphone className="w-3 h-3 mr-1.5" strokeWidth={2.5} />;
        bgColor = "bg-gradient-to-r from-blue-50 to-blue-100";
        textColor = "text-blue-700";
        borderColor = "border-blue-200";
        break;
      case "fixed":
        label = "Cố định";
        icon = <Phone className="w-3 h-3 mr-1.5" strokeWidth={2.5} />;
        bgColor = "bg-gradient-to-r from-green-50 to-green-100";
        textColor = "text-green-700";
        borderColor = "border-green-200";
        break;
      case "intl":
        label = "Quốc tế";
        icon = <Globe className="w-3 h-3 mr-1.5" strokeWidth={2.5} />;
        bgColor = "bg-gradient-to-r from-amber-50 to-amber-100";
        textColor = "text-amber-700";
        borderColor = "border-amber-200";
        break;
      case "ported":
        label = "Chuyển mạng";
        icon = <ArrowRightLeft className="w-3 h-3 mr-1.5" strokeWidth={2.5} />;
        bgColor = "bg-gradient-to-r from-purple-50 to-purple-100";
        textColor = "text-purple-700";
        borderColor = "border-purple-200";
        break;
      case "unknown":
      default:
        label = "Không xác định";
        icon = <HelpCircle className="w-3 h-3 mr-1.5" strokeWidth={2.5} />;
        bgColor = "bg-gradient-to-r from-gray-50 to-gray-100";
        textColor = "text-gray-700";
        borderColor = "border-gray-200";
        break;
    }
    
    return (
      <div 
        className={`${bgColor} ${textColor} border ${borderColor} flex items-center px-2.5 py-1 rounded-full text-xs font-medium shadow-sm transition-all duration-200 ease-in-out hover:shadow-md`} 
        title={`${isCallee ? "Người nhận" : "Người gọi"}: ${label}`}
      >
        {icon}
        <span>{label}</span>
      </div>
    );
  };

  const renderPagination = () => {
    if (!logs) return null;
    
    const totalPages = Math.ceil(logs.total / pageSize);
    if (totalPages <= 1) return null;
    
    return (
      <Pagination className="mt-4">
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious 
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
            />
          </PaginationItem>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            // Logic to show pages around current page
            let pageNum = page;
            if (page <= 3) {
              pageNum = i + 1;
            } else if (page >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = page - 2 + i;
            }
            
            // Ensure page number is within valid range
            if (pageNum < 1 || pageNum > totalPages) return null;
            
            return (
              <PaginationItem key={pageNum}>
                <PaginationLink
                  onClick={() => setPage(pageNum)}
                  isActive={page === pageNum}
                >
                  {pageNum}
                </PaginationLink>
              </PaginationItem>
            );
          })}
          
          <PaginationItem>
            <PaginationNext 
              onClick={() => setPage(p => Math.min(totalPages, p + 1))}
              disabled={page === totalPages}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    );
  };

  const renderSkeletonRows = () => {
    return Array.from({ length: 5 }).map((_, index) => (
      <TableRow key={index}>
        <TableCell><Skeleton className="h-4 w-4" /></TableCell>
        <TableCell><Skeleton className="h-4 w-24" /></TableCell>
        <TableCell><Skeleton className="h-4 w-24" /></TableCell>
        <TableCell><Skeleton className="h-4 w-20" /></TableCell>
        <TableCell><Skeleton className="h-4 w-32" /></TableCell>
        <TableCell><Skeleton className="h-4 w-32" /></TableCell>
        <TableCell><Skeleton className="h-4 w-16" /></TableCell>
        <TableCell><Skeleton className="h-4 w-32" /></TableCell>
      </TableRow>
    ));
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4 md:flex-row md:items-end md:justify-between">
        <div className="flex flex-col gap-2">
          <h2 className="text-xl font-semibold">Nhật Ký Cuộc Gọi</h2>
          {logs && (
            <p className="text-sm text-muted-foreground">
              Hiển thị {logs.items.length} trong tổng số {logs.total} nhật ký cuộc gọi
            </p>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          >
            <SlidersHorizontal className="mr-2 h-4 w-4" />
            {showAdvancedFilters ? "Ẩn bộ lọc nâng cao" : "Bộ lọc nâng cao"}
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                {pageSize} dòng/trang
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Số dòng mỗi trang</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem
                checked={pageSize === 25}
                onCheckedChange={() => {
                  setPageSize(25);
                  setPage(1);
                }}
              >
                25 dòng
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={pageSize === 50}
                onCheckedChange={() => {
                  setPageSize(50);
                  setPage(1);
                }}
              >
                50 dòng
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={pageSize === 100}
                onCheckedChange={() => {
                  setPageSize(100);
                  setPage(1);
                }}
              >
                100 dòng
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={pageSize === 200}
                onCheckedChange={() => {
                  setPageSize(200);
                  setPage(1);
                }}
              >
                200 dòng
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Làm mới
          </Button>
          
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleClearFilters}
          >
            Xóa bộ lọc
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div>
          <Label htmlFor="call-type">Loại Cuộc Gọi</Label>
          <Select
            value={callTypeFilter}
            onValueChange={(value) => {
              setCallTypeFilter(value as CallType | "all");
              setPage(1);
            }}
          >
            <SelectTrigger id="call-type">
              <SelectValue placeholder="Tất cả loại" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả loại</SelectItem>
              <SelectItem value={CallType.IN}>Cuộc gọi đến</SelectItem>
              <SelectItem value={CallType.OUT}>Cuộc gọi đi</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="caller">Người Gọi</Label>
          <Input
            id="caller"
            placeholder="Nhập số điện thoại"
            value={callerFilter}
            onChange={(e) => setCallerFilter(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                setPage(1);
                fetchLogs();
              }
            }}
          />
        </div>
        
        <div>
          <Label htmlFor="callee">Người Nhận</Label>
          <Input
            id="callee"
            placeholder="Nhập số điện thoại"
            value={calleeFilter}
            onChange={(e) => setCalleeFilter(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                setPage(1);
                fetchLogs();
              }
            }}
          />
        </div>
      </div>
      
      {showAdvancedFilters && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border rounded-md bg-muted/10">
          <div>
            <Label htmlFor="date-from">Từ Ngày</Label>
            <DatePicker
              id="date-from"
              date={dateFromFilter}
              onSelect={(date) => {
                setDateFromFilter(date);
                setPage(1);
              }}
            />
          </div>
          
          <div>
            <Label htmlFor="date-to">Đến Ngày</Label>
            <DatePicker
              id="date-to"
              date={dateToFilter}
              onSelect={(date) => {
                setDateToFilter(date);
                setPage(1);
              }}
            />
          </div>
          
          <div>
            <Label htmlFor="caller-type">Loại Số Người Gọi</Label>
            <Select
              value={callerTypeFilter}
              onValueChange={(value) => {
                setCallerTypeFilter(value);
                setPage(1);
              }}
            >
              <SelectTrigger id="caller-type">
                <SelectValue placeholder="Tất cả loại" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả loại</SelectItem>
                <SelectItem value="mobile">Di động</SelectItem>
                <SelectItem value="fixed">Cố định</SelectItem>
                <SelectItem value="intl">Quốc tế</SelectItem>
                <SelectItem value="ported">Chuyển mạng</SelectItem>
                <SelectItem value="unknown">Không xác định</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="callee-type">Loại Số Người Nhận</Label>
            <Select
              value={calleeTypeFilter}
              onValueChange={(value) => {
                setCalleeTypeFilter(value);
                setPage(1);
              }}
            >
              <SelectTrigger id="callee-type">
                <SelectValue placeholder="Tất cả loại" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả loại</SelectItem>
                <SelectItem value="mobile">Di động</SelectItem>
                <SelectItem value="fixed">Cố định</SelectItem>
                <SelectItem value="intl">Quốc tế</SelectItem>
                <SelectItem value="ported">Chuyển mạng</SelectItem>
                <SelectItem value="unknown">Không xác định</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]">
                <div className="flex items-center gap-1">
                  Loại
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1">
                  Người Gọi
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1">
                  Người Nhận
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center justify-center gap-1">
                  Loại Số
                </div>
              </TableHead>
              <TableHead onClick={() => handleSortChange("begin_time")}>
                <div className="flex items-center gap-1">
                  Thời Gian Bắt Đầu
                  {getSortIcon("begin_time")}
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1">
                  Thời Gian Kết Thúc
                </div>
              </TableHead>
              <TableHead onClick={() => handleSortChange("duration")}>
                <div className="flex items-center gap-1">
                  Thời Lượng
                  {getSortIcon("duration")}
                </div>
              </TableHead>
              <TableHead>Gateway</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              renderSkeletonRows()
            ) : logs && logs.items.length > 0 ? (
              logs.items.map((log) => (
                <TableRow key={log.id}>
                  <TableCell>
                    {getCallTypeIcon(log.call_type)}
                  </TableCell>
                  <TableCell>
                    <span className="font-medium whitespace-nowrap">{log.caller}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="font-medium whitespace-nowrap">{log.callee}</span>
                      {log.area_name && (
                        <span className="text-xs text-muted-foreground mt-0.5">
                          {log.area_name}
                        </span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex flex-wrap justify-center gap-2">
                      {log.caller_type && log.callee_type ? (
                        <div className="flex items-center gap-3">
                          {getNumberTypeBadge(log.caller_type, false)}
                          
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                            <path d="M5 12H19M19 12L13 6M19 12L13 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                          
                          {getNumberTypeBadge(log.callee_type, true)}
                        </div>
                      ) : (
                        <>
                          {log.caller_type && (
                            <div>
                              {getNumberTypeBadge(log.caller_type, false)}
                            </div>
                          )}
                          {log.callee_type && (
                            <div>
                              {getNumberTypeBadge(log.callee_type, true)}
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span>{formatDate(new Date(log.begin_time))}</span>
                      <span className="text-muted-foreground">{formatTime(new Date(log.begin_time))}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span>{formatDate(new Date(log.end_time))}</span>
                      <span className="text-muted-foreground">{formatTime(new Date(log.end_time))}</span>
                    </div>
                  </TableCell>
                  <TableCell>{formatDuration(log.duration)}</TableCell>
                  <TableCell>
                    <div className="flex flex-col gap-1">
                      <div className="flex flex-col">
                        <span className="text-xs text-muted-foreground">Người gọi:</span>
                        <span className="text-sm">{log.caller_gateway}</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs text-muted-foreground">Người nhận:</span>
                        <span className="text-sm">{log.called_gateway}</span>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center">
                  Không tìm thấy dữ liệu nào.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {renderPagination()}
      
      {/* Scroll to top button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 bg-primary text-primary-foreground rounded-full shadow-lg hover:bg-primary/90 transition-all duration-300 z-50"
          aria-label="Lên đầu trang"
        >
          <ChevronUp className="h-5 w-5" />
        </button>
      )}
    </div>
  );
} 