import { useState, useEffect, useRef } from "react";
import { useToast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";
import { MoreHorizontalIcon, FileIcon, TrashIcon, RefreshCw, ListIcon } from "lucide-react";
import { FileType, FileStatus, CallLogFile, PaginatedCallLogFiles } from "@/types/call-log";
import { callLogService } from "@/services/call-log-service";
import { formatDate } from "@/lib/utils";
import { useNavigate } from "react-router-dom";

interface CallLogFileListProps {
  refreshTrigger?: number;
  onRefresh?: () => void;
}

export function CallLogFileList({ refreshTrigger = 0, onRefresh }: CallLogFileListProps) {
  const [files, setFiles] = useState<PaginatedCallLogFiles | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [fileTypeFilter, setFileTypeFilter] = useState<FileType | "all">("all");
  const [statusFilter, setStatusFilter] = useState<FileStatus | "all">("all");
  const [fileToDelete, setFileToDelete] = useState<CallLogFile | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  
  // Ref để theo dõi các file đang xử lý
  const processingFilesRef = useRef<Record<number, boolean>>({});
  
  const pageSize = 10;

  // Fetch files when filters or page changes
  useEffect(() => {
    fetchFiles();
  }, [page, fileTypeFilter, statusFilter, refreshTrigger]);
  
  // Auto-refresh chỉ cho các file đang xử lý
  useEffect(() => {
    // Lưu trữ các file đang xử lý
    const processingFiles: Record<number, boolean> = {};
    if (files) {
      files.items.forEach(file => {
        if (file.status === FileStatus.PROCESSING) {
          processingFiles[file.id] = true;
        }
      });
    }
    processingFilesRef.current = processingFiles;
    
    // Chỉ tự động refresh khi có file đang xử lý
    if (Object.keys(processingFiles).length > 0) {
      const interval = setInterval(() => {
        updateProcessingFiles();
      }, 3000); // Cập nhật mỗi 3 giây
      
      return () => clearInterval(interval);
    }
  }, [files]);

  const fetchFiles = async () => {
    setIsLoading(true);
    try {
      const params: any = {
        skip: (page - 1) * pageSize,
        limit: pageSize,
      };
      
      if (fileTypeFilter !== "all") {
        params.file_type = fileTypeFilter;
      }
      
      if (statusFilter !== "all") {
        params.status = statusFilter;
      }
      
      const data = await callLogService.getFiles(params);
      setFiles(data);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error fetching files",
        description: error.message || "An unknown error occurred",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm chỉ cập nhật các file đang xử lý
  const updateProcessingFiles = async () => {
    if (!files) return;
    
    const processingFileIds = Object.keys(processingFilesRef.current).map(Number);
    if (processingFileIds.length === 0) return;
    
    try {
      // Lấy thông tin mới nhất cho từng file đang xử lý
      const updatedFiles = await Promise.all(
        processingFileIds.map(id => callLogService.getFile(id))
      );
      
      // Cập nhật state mà không làm mới toàn bộ bảng
      setFiles(prevFiles => {
        if (!prevFiles) return prevFiles;
        
        const updatedItems = [...prevFiles.items];
        
        updatedFiles.forEach(updatedFile => {
          const index = updatedItems.findIndex(file => file.id === updatedFile.id);
          if (index !== -1) {
            updatedItems[index] = updatedFile;
            
            // Nếu file đã hoàn thành hoặc thất bại, xóa khỏi danh sách theo dõi
            if (updatedFile.status !== FileStatus.PROCESSING) {
              delete processingFilesRef.current[updatedFile.id];
            }
          }
        });
        
        return {
          ...prevFiles,
          items: updatedItems
        };
      });
    } catch (error) {
      // Xử lý lỗi một cách im lặng để không làm gián đoạn trải nghiệm người dùng
      console.error("Error updating processing files:", error);
    }
  };

  const handleRefresh = () => {
    fetchFiles();
    if (onRefresh) {
      onRefresh();
    }
  };

  const handleDelete = async () => {
    if (!fileToDelete) return;
    
    try {
      await callLogService.deleteFile(fileToDelete.id);
      toast({
        title: "File deleted",
        description: `${fileToDelete.filename} has been deleted successfully`,
      });
      fetchFiles();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error deleting file",
        description: error.message || "An unknown error occurred",
      });
    } finally {
      setFileToDelete(null);
    }
  };

  const getStatusBadge = (status: FileStatus, file: CallLogFile) => {
    switch (status) {
      case FileStatus.PENDING:
        return <Badge variant="outline">Pending</Badge>;
      case FileStatus.PROCESSING:
        // Tính toán phần trăm hoàn thành
        const progressPercent = file.total_rows && file.total_rows > 0
          ? Math.min(100, Math.round((file.processed_rows / file.total_rows) * 100))
          : 0;
        
        return (
          <div className="flex flex-col gap-1 w-full max-w-[150px]">
            <div className="flex items-center gap-2">
              <Badge variant="secondary">Processing</Badge>
            </div>
            <Progress value={progressPercent} className="h-2" />
          </div>
        );
      case FileStatus.COMPLETED:
        return <Badge variant="success">Completed</Badge>;
      case FileStatus.FAILED:
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getFileTypeBadge = (fileType: FileType) => {
    switch (fileType) {
      case FileType.CALL_IN:
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Call In</Badge>;
      case FileType.CALL_OUT:
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Call Out</Badge>;
      default:
        return <Badge variant="outline">{fileType}</Badge>;
    }
  };

  const renderPagination = () => {
    if (!files) return null;
    
    const totalPages = Math.ceil(files.total / pageSize);
    if (totalPages <= 1) return null;
    
    return (
      <Pagination className="mt-4">
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious 
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
            />
          </PaginationItem>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1)
            .filter(p => Math.abs(p - page) < 2 || p === 1 || p === totalPages)
            .map((p, i, arr) => {
              // Add ellipsis
              if (i > 0 && p > arr[i - 1] + 1) {
                return (
                  <PaginationItem key={`ellipsis-${p}`}>
                    <span className="px-4">...</span>
                  </PaginationItem>
                );
              }
              
              return (
                <PaginationItem key={p}>
                  <PaginationLink
                    isActive={page === p}
                    onClick={() => setPage(p)}
                  >
                    {p}
                  </PaginationLink>
                </PaginationItem>
              );
            })}
          
          <PaginationItem>
            <PaginationNext 
              onClick={() => setPage(p => Math.min(totalPages, p + 1))}
              disabled={page === totalPages}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    );
  };

  const renderSkeletonRows = () => {
    return Array.from({ length: 5 }).map((_, i) => (
      <TableRow key={`skeleton-${i}`}>
        <TableCell><Skeleton className="h-4 w-4" /></TableCell>
        <TableCell><Skeleton className="h-4 w-40" /></TableCell>
        <TableCell><Skeleton className="h-6 w-20" /></TableCell>
        <TableCell><Skeleton className="h-6 w-24" /></TableCell>
        <TableCell><Skeleton className="h-4 w-32" /></TableCell>
        <TableCell><Skeleton className="h-8 w-8" /></TableCell>
      </TableRow>
    ));
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="text-xl font-semibold">Call Log Files</div>
        
        <div className="flex flex-wrap gap-2">
          <Select
            value={fileTypeFilter}
            onValueChange={(value) => {
              setFileTypeFilter(value as FileType | "all");
              setPage(1);
            }}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="File Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value={FileType.CALL_IN}>Call In</SelectItem>
              <SelectItem value={FileType.CALL_OUT}>Call Out</SelectItem>
            </SelectContent>
          </Select>
          
          <Select
            value={statusFilter}
            onValueChange={(value) => {
              setStatusFilter(value as FileStatus | "all");
              setPage(1);
            }}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value={FileStatus.PENDING}>Pending</SelectItem>
              <SelectItem value={FileStatus.PROCESSING}>Processing</SelectItem>
              <SelectItem value={FileStatus.COMPLETED}>Completed</SelectItem>
              <SelectItem value={FileStatus.FAILED}>Failed</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="icon"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]"></TableHead>
              <TableHead>Filename</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Uploaded</TableHead>
              <TableHead className="w-[60px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              renderSkeletonRows()
            ) : files && files.items.length > 0 ? (
              files.items.map((file) => (
                <TableRow key={file.id}>
                  <TableCell>
                    <FileIcon className="h-5 w-5 text-muted-foreground" />
                  </TableCell>
                  <TableCell className="font-medium">
                    {file.filename}
                    {file.error_message && (
                      <div className="text-xs text-destructive mt-1 truncate max-w-[250px]" title={file.error_message}>
                        {file.error_message}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>{getFileTypeBadge(file.file_type)}</TableCell>
                  <TableCell>{getStatusBadge(file.status, file)}</TableCell>
                  <TableCell>{formatDate(file.uploaded_at)}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontalIcon className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => navigate(`/call-logs/view/${file.id}`)}
                          disabled={file.status !== FileStatus.COMPLETED}
                        >
                          <ListIcon className="mr-2 h-4 w-4" />
                          View Logs
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setFileToDelete(file)}
                          disabled={file.status === FileStatus.PROCESSING}
                        >
                          <TrashIcon className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                  No files found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {renderPagination()}
      
      <AlertDialog open={!!fileToDelete} onOpenChange={(open) => !open && setFileToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Bạn có chắc chắn muốn xóa?</AlertDialogTitle>
            <AlertDialogDescription>
              Thao tác này sẽ xóa vĩnh viễn file "{fileToDelete?.filename}" và <strong>tất cả dữ liệu call log</strong> đã được nhập từ file này.
              <br />
              Dữ liệu sau khi xóa sẽ không thể khôi phục.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 