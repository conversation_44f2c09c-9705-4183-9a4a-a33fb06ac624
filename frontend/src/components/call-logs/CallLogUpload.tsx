import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { useToast } from "@/components/ui/use-toast";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Upload, File, AlertCircle, X } from "lucide-react";
import { FileType, CallLogFile } from "@/types/call-log";
import { callLogService } from "@/services/call-log-service";
import { bytesToSize } from "@/lib/utils";

// Giới hạn kích thước file (80MB)
const MAX_FILE_SIZE = 80 * 1024 * 1024;

interface CallLogUploadProps {
  onUploadSuccess?: (file: CallLogFile) => void;
}

export function CallLogUpload({ onUploadSuccess }: CallLogUploadProps) {
  const [fileType, setFileType] = useState<FileType>(FileType.CALL_IN);
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [fileError, setFileError] = useState<string | null>(null);
  const { toast } = useToast();

  // Xử lý khi file được chọn
  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Reset error state
    setFileError(null);
    
    // Xử lý file bị từ chối
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(rejection => 
        `${rejection.file.name}: ${rejection.errors.map((e: any) => e.message).join(', ')}`
      );
      setFileError(errors.join('\n'));
      return;
    }
    
    // Xử lý file được chấp nhận
    if (acceptedFiles.length > 0) {
      const selectedFile = acceptedFiles[0];
      
      // Kiểm tra kích thước file
      if (selectedFile.size > MAX_FILE_SIZE) {
        setFileError(`File too large. Maximum size is ${bytesToSize(MAX_FILE_SIZE)}`);
        return;
      }
      
      // Kiểm tra định dạng file
      if (!selectedFile.name.endsWith('.xlsx')) {
        setFileError('Only XLSX files are supported');
        return;
      }
      
      setFile(selectedFile);
    }
  }, []);

  // Cấu hình dropzone
  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
    },
    maxFiles: 1,
    disabled: isUploading
  });

  // Xử lý upload file
  const handleUpload = async () => {
    if (!file) {
      toast({
        variant: "destructive",
        title: "No file selected",
        description: "Please select a file to upload"
      });
      return;
    }
    
    setIsUploading(true);
    setUploadProgress(0);
    
    try {
      // Sử dụng service với progress tracking
      const abortUpload = callLogService.uploadFileWithProgress(
        file,
        fileType,
        (progress) => {
          setUploadProgress(progress);
        },
        (data) => {
          setIsUploading(false);
          toast({
            title: "Upload successful",
            description: "File is now being processed"
          });
          
          // Reset state
          setFile(null);
          setUploadProgress(0);
          
          // Callback
          if (onUploadSuccess) {
            onUploadSuccess(data);
          }
        },
        (error) => {
          setIsUploading(false);
          toast({
            variant: "destructive",
            title: "Upload failed",
            description: error.detail || error.message || "An unknown error occurred"
          });
        }
      );
      
      // Cleanup function
      return () => {
        if (isUploading) {
          abortUpload();
        }
      };
    } catch (error: any) {
      setIsUploading(false);
      toast({
        variant: "destructive",
        title: "Upload failed",
        description: error.message || "An unknown error occurred"
      });
    }
  };

  // Xử lý xóa file đã chọn
  const handleRemoveFile = () => {
    setFile(null);
    setFileError(null);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Upload</CardTitle>
        <CardDescription>
          Upload call log files in XLSX format. Files can contain up to 2 million rows.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex space-x-4">
            <RadioGroup
              value={fileType}
              onValueChange={(v) => setFileType(v as FileType)}
              className="flex space-x-4"
              disabled={isUploading}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={FileType.CALL_IN} id="call_in" />
                <Label htmlFor="call_in">Call In (Gọi vào)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={FileType.CALL_OUT} id="call_out" />
                <Label htmlFor="call_out">Call Out (Gọi ra)</Label>
              </div>
            </RadioGroup>
          </div>

          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive ? 'border-primary bg-primary/5' : 'border-border'}
              ${isDragReject || fileError ? 'border-destructive bg-destructive/5' : ''}
              ${isUploading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent hover:border-accent'}
            `}
          >
            <input {...getInputProps()} />
            
            {file ? (
              <div className="flex items-center justify-center space-x-2">
                <File className="h-8 w-8 text-primary" />
                <div className="flex-1 text-left">
                  <p className="font-medium">{file.name}</p>
                  <p className="text-sm text-muted-foreground">{bytesToSize(file.size)}</p>
                </div>
                {!isUploading && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveFile();
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <Upload className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="text-lg font-medium">Drag and drop your file here</h3>
                <p className="text-sm text-muted-foreground">
                  or click to select a file (XLSX format only)
                </p>
                <p className="text-xs text-muted-foreground">
                  Maximum file size: {bytesToSize(MAX_FILE_SIZE)}
                </p>
              </div>
            )}
          </div>

          {fileError && (
            <div className="flex items-center text-destructive text-sm">
              <AlertCircle className="h-4 w-4 mr-2" />
              {fileError}
            </div>
          )}

          {isUploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Uploading...</span>
                <span className="text-sm text-muted-foreground">{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}

          <div className="flex justify-end">
            <Button
              onClick={handleUpload}
              disabled={!file || isUploading || !!fileError}
              className="w-full sm:w-auto"
            >
              {isUploading ? 'Uploading...' : 'Upload File'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 