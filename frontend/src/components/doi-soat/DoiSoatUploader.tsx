import { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { CheckCircle, AlertCircle, Upload, Loader2 } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { api } from "@/lib/api";
import { Partner } from "@/types/partner";

type FileType = 'CO_DINH' | 'CUOC' | '1800_1900' | 'UNKNOWN';

interface UploadResponse {
  success: boolean;
  file_id: string;
  filename: string;
  detected_type: FileType;
  confidence: number;
  partner_id?: number;
  partner_name?: string;
  message: string;
}

export default function DoiSoatUploader() {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<UploadResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [detectedType, setDetectedType] = useState<FileType | null>(null);
  const [manualType, setManualType] = useState<FileType | null>(null);
  const [partners, setPartners] = useState<Partner[]>([]);
  const [selectedPartnerId, setSelectedPartnerId] = useState<string>("");
  const [isLoadingPartners, setIsLoadingPartners] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch partners when component mounts
  useEffect(() => {
    const fetchPartners = async () => {
      setIsLoadingPartners(true);
      try {
        const response = await api.get<Partner[]>('/api/v1/partners');
        setPartners(response.data || []);
      } catch (error) {
        console.error('Failed to fetch partners:', error);
        setError('Không thể tải danh sách đối tác. Vui lòng thử lại.');
      } finally {
        setIsLoadingPartners(false);
      }
    };

    fetchPartners();
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setResult(null);
      setError(null);
      setDetectedType(null);
      setManualType(null);
    }
  };

  const handlePartnerChange = (value: string) => {
    setSelectedPartnerId(value);
  };

  const uploadFile = async () => {
    if (!file) return;

    setUploading(true);
    setProgress(0);
    setError(null);
    
    const formData = new FormData();
    formData.append('file', file);
    
    // Thêm manual_type nếu đã chọn
    if (manualType) {
      formData.append('manual_type', manualType);
    }
    
    // Thêm partner_id nếu đã chọn
    if (selectedPartnerId) {
      formData.append('partner_id', selectedPartnerId);
    }

    try {
      // Giả lập progress
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 5, 90));
      }, 100);

      const response = await fetch('/api/v1/doi-soat/upload', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setProgress(100);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Upload failed');
      }

      const data: UploadResponse = await response.json();
      setResult(data);
      
      // Nếu server phát hiện loại file
      if (data.detected_type && data.detected_type !== 'UNKNOWN') {
        setDetectedType(data.detected_type as FileType);
      }

    } catch (err: any) {
      setError(err.message || 'Upload failed');
      setProgress(0);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Upload File Đối Soát</CardTitle>
          <CardDescription>
            Tải lên file Excel đối soát để phân loại tự động
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col gap-4">
            <label className="text-sm font-medium">
              Chọn file đối soát (.xlsx, .xls)
            </label>
            
            <Input
              ref={fileInputRef}
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileChange}
              className="cursor-pointer"
            />
            
            {file && (
              <div className="mt-2 text-sm">
                <p>File đã chọn: <span className="font-medium">{file.name}</span></p>
                <p>Kích thước: <span className="font-medium">{(file.size / 1024 / 1024).toFixed(2)} MB</span></p>
              </div>
            )}
            
            {/* Thêm Partner Selector */}
            <div className="space-y-2">
              <Label htmlFor="partner">Đối tác</Label>
              <Select 
                value={selectedPartnerId} 
                onValueChange={handlePartnerChange}
                disabled={isLoadingPartners}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn đối tác" />
                </SelectTrigger>
                <SelectContent>
                  {partners.map((partner) => (
                    <SelectItem key={partner.id} value={partner.id.toString()}>
                      {partner.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                Chọn đối tác liên quan đến file đối soát
              </p>
            </div>
            
            {detectedType && (
              <Alert variant="info" className="mt-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Loại file được phát hiện</AlertTitle>
                <AlertDescription>
                  Hệ thống phát hiện đây là file đối soát loại: <strong>{getTypeName(detectedType)}</strong>
                  <div className="mt-2">
                    <p className="text-sm text-muted-foreground">
                      Bạn có thể thay đổi loại file nếu phát hiện không chính xác:
                    </p>
                    <RadioGroup
                      value={manualType || detectedType}
                      onValueChange={(value) => setManualType(value as FileType)}
                      className="mt-2 grid grid-cols-3 gap-2"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="CO_DINH" id="co-dinh" />
                        <Label htmlFor="co-dinh">Đối soát cố định</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="CUOC" id="cuoc" />
                        <Label htmlFor="cuoc">Đối soát cước</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1800_1900" id="1800-1900" />
                        <Label htmlFor="1800-1900">Đối soát 1800/1900</Label>
                      </div>
                    </RadioGroup>
                  </div>
                </AlertDescription>
              </Alert>
            )}
            
            {uploading && (
              <div className="mt-4 space-y-2">
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  <span>Đang xử lý...</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>
            )}
            
            {result && (
              <Alert variant="success" className="mt-4">
                <CheckCircle className="h-4 w-4" />
                <AlertTitle>Upload thành công</AlertTitle>
                <AlertDescription>
                  File đã được tải lên và xử lý theo loại: <strong>{getTypeName(result.detected_type)}</strong>
                  {result.partner_name && (
                    <p>Đối tác: <strong>{result.partner_name}</strong></p>
                  )}
                  <div className="mt-2 text-sm">
                    <p>File ID: <span className="font-mono">{result.file_id}</span></p>
                    <p>Độ tin cậy: <span className="font-medium">{(result.confidence * 100).toFixed(0)}%</span></p>
                  </div>
                </AlertDescription>
              </Alert>
            )}
            
            {error && (
              <Alert variant="destructive" className="mt-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Lỗi</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            <Button 
              onClick={uploadFile}
              disabled={!file || uploading || !selectedPartnerId}
              className="mt-4"
            >
              {uploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang xử lý
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Tải lên
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function getTypeName(type: string): string {
  switch(type) {
    case 'CO_DINH': return 'Đối soát cố định';
    case 'CUOC': return 'Đối soát cước';
    case '1800_1900': return 'Đối soát 1800/1900';
    default: return 'Không xác định';
  }
} 