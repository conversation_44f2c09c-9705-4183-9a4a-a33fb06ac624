import React, { useState } from 'react';
import { useFieldArray, useForm<PERSON><PERSON><PERSON><PERSON>, Controller, ControllerRenderProps, FieldError } from 'react-hook-form';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { FormField } from "@/components/ui/form";
import { Trash2, Edit } from 'lucide-react';
import { formatCurrency, formatNumber } from '@/lib/utils';
import type { DauSoDichVuDetail } from '@/types/reconciliation';
import type { EditFormData } from '@/types/reconciliation';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { DauSoDichVuNestedEditForm } from './dau-so-dich-vu-nested-edit-form';

interface DauSoDichVuEditTableProps {
    initialData: DauSoDichVuDetail[]; // To display original values
}

export function DauSoDichVuEditTable({ initialData }: DauSoDichVuEditTableProps) {
    const { control, formState: { errors, isDirty, dirtyFields }, watch } = useFormContext<EditFormData>();

    const { fields, append, remove } = useFieldArray<EditFormData, "du_lieu", "id">({
        control,
        name: "du_lieu",
    });

    // State to manage which row's detail dialog is open (using index)
    const [openDialogIndex, setOpenDialogIndex] = useState<number | null>(null);

    // Helper function to get original value for display
    const getOriginalValue = (index: number, fieldName: 'cuoc_thu_khach' | 'cuoc_tra_htc') => {
        const initialItem = initialData.find(item => item.id === fields[index]?.id);
        return initialItem ? initialItem[fieldName] : 0;
    };

    // Function to check if nested fields for a row are dirty
    const isNestedDirty = (index: number): boolean => {
        const dirtyRow = dirtyFields.du_lieu?.[index];
        if (!dirtyRow) return false;
        // Check if any nested object itself is marked dirty, or specific fields within them
        return !!(dirtyRow.co_dinh_noi_hat || dirtyRow.co_dinh_lien_tinh || dirtyRow.di_dong || dirtyRow.cuoc_1900 || dirtyRow.quoc_te || dirtyRow.cuoc_thue_bao);
    };

    return (
        <div className="w-full overflow-auto">
             <Table>
                 <TableHeader>
                     <TableRow>
                         <TableHead>Loại đầu số</TableHead>
                         <TableHead>Hiển thị</TableHead>
                         <TableHead className="text-right">Cước thu khách (Gốc)</TableHead>
                         <TableHead className="text-right">Cước thu khách (Hiệu chỉnh)</TableHead>
                         <TableHead className="text-right">Cước trả HTC (Gốc)</TableHead>
                         <TableHead className="text-right">Cước trả HTC (Hiệu chỉnh)</TableHead>
                         <TableHead className="text-center">Chi tiết</TableHead>
                         {/* TODO: Add columns for nested details (CuocGoi, CuocThueBao) if needed */}
                         {/* <TableHead className="text-center">Actions</TableHead> */} 
                     </TableRow>
                 </TableHeader>
                 <TableBody>
                     {fields.map((field: Record<"id", string | number>, index: number) => {
                        const fieldId = `du_lieu.${index}` as const;
                        const originalThuKhach = getOriginalValue(index, 'cuoc_thu_khach');
                        const originalTraHtc = getOriginalValue(index, 'cuoc_tra_htc');
                        // Get specific field errors
                        const thuKhachError = errors.du_lieu?.[index]?.cuoc_thu_khach_adjusted;
                        const traHtcError = errors.du_lieu?.[index]?.cuoc_tra_htc_adjusted;
                        const initialItemData = initialData.find(item => item.id === field.id);
                        const nestedDirty = isNestedDirty(index);
                         
                        return (
                             <TableRow key={field.id}>
                                 <TableCell>{initialItemData?.dau_so_type ?? 'N/A'}</TableCell>
                                 <TableCell>{initialItemData?.standardized_display ?? 'N/A'}</TableCell>
                                 <TableCell className="text-right">{formatCurrency(originalThuKhach)}</TableCell>
                                 <TableCell>
                                     <Controller
                                         control={control}
                                         name={`${fieldId}.cuoc_thu_khach_adjusted`}
                                         render={({ field: controllerField }: { field: ControllerRenderProps<EditFormData, `du_lieu.${number}.cuoc_thu_khach_adjusted`> }) => (
                                             <Input 
                                                 type="number"
                                                 step="any"
                                                 className={`w-32 text-right ${thuKhachError ? 'border-destructive' : ''}`}
                                                 placeholder="Nhập hiệu chỉnh"
                                                 {...controllerField}
                                                 value={controllerField.value ?? ''}
                                                 onChange={(e: React.ChangeEvent<HTMLInputElement>) => controllerField.onChange(e.target.value === '' ? null : parseFloat(e.target.value))}
                                             />
                                         )}
                                     />
                                     {thuKhachError && (
                                         <p className="text-xs text-destructive mt-1">{thuKhachError.message || 'Invalid number'}</p>
                                     )}
                                 </TableCell>
                                 <TableCell className="text-right">{formatCurrency(originalTraHtc)}</TableCell>
                                 <TableCell>
                                     <Controller
                                         control={control}
                                         name={`${fieldId}.cuoc_tra_htc_adjusted`}
                                         render={({ field: controllerField }: { field: ControllerRenderProps<EditFormData, `du_lieu.${number}.cuoc_tra_htc_adjusted`> }) => (
                                             <Input
                                                 type="number"
                                                 step="any"
                                                 className={`w-32 text-right ${traHtcError ? 'border-destructive' : ''}`}
                                                 placeholder="Nhập hiệu chỉnh"
                                                 {...controllerField}
                                                 value={controllerField.value ?? ''}
                                                 onChange={(e: React.ChangeEvent<HTMLInputElement>) => controllerField.onChange(e.target.value === '' ? null : parseFloat(e.target.value))}
                                             />
                                         )}
                                     />
                                      {traHtcError && (
                                         <p className="text-xs text-destructive mt-1">{traHtcError.message || 'Invalid number'}</p>
                                     )}
                                 </TableCell>
                                 <TableCell className="text-center">
                                     <Dialog open={openDialogIndex === index} onOpenChange={(isOpen: boolean) => !isOpen && setOpenDialogIndex(null)}>
                                         <DialogTrigger asChild>
                                             <Button 
                                                 type="button" 
                                                 variant={nestedDirty ? "secondary" : "outline"}
                                                 size="icon"
                                             >
                                                 <Edit className="h-4 w-4" />
                                             </Button>
                                         </DialogTrigger>
                                         <DialogContent className="sm:max-w-[750px] max-h-[80vh] overflow-y-auto">
                                             <DialogHeader>
                                                 <DialogTitle>Chỉnh sửa Chi tiết: {initialItemData?.standardized_display ?? 'N/A'}</DialogTitle>
                                                 <DialogDescription>
                                                     Chỉnh sửa các giá trị chi tiết cho loại đầu số này. Các thay đổi sẽ được lưu khi bạn Lưu ở form chính.
                                                 </DialogDescription>
                                             </DialogHeader>
                                             <DauSoDichVuNestedEditForm 
                                                 fieldIndex={index} 
                                                 initialItemData={initialItemData} 
                                             /> 
                                             <DialogFooter className="sm:justify-end">
                                                 <DialogClose asChild>
                                                     <Button type="button" variant="secondary">
                                                         Đóng
                                                     </Button>
                                                 </DialogClose>
                                             </DialogFooter>
                                         </DialogContent>
                                     </Dialog>
                                 </TableCell>
                             </TableRow>
                         );
                     })}
                 </TableBody>
             </Table>
             {/* Optional: Button to add new rows if applicable */}
             {/* <Button type="button" onClick={() => append({ id: Date.now(), ... })} className="mt-4">Add Row</Button> */}
         </div>
    );
} 