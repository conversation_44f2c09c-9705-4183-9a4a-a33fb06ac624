import React, { useEffect } from 'react';
import { useForm<PERSON><PERSON><PERSON><PERSON>, Controller, ControllerRenderProps } from 'react-hook-form';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { formatCurrency, formatNumber, parseFormattedNumber } from '@/lib/utils';
import type { DauSoDichVuDetail as FormDauSoDichVuDetail, CuocGoiDetail, CuocThueBaoDetail } from '@/types/reconciliation';

interface DauSoDichVuNestedEditFormProps {
    fieldIndex: number;
    initialItemData?: FormDauSoDichVuDetail | null;
    // Add onSave or similar prop if needed to trigger actions on dialog close/save
}

// Define props type for CuocGoi helper
type NestedCuocGoiInputGroupProps = {
    fieldIndex: number;
    nestedKey: keyof Pick<FormDauSoDichVuDetail, 'co_dinh_noi_hat' | 'co_dinh_lien_tinh' | 'di_dong' | 'cuoc_1900' | 'quoc_te'>;
    labelPrefix: string;
    initialNestedData?: CuocGoiDetail | null;
};

// Helper component for rendering nested CuocGoi fields
const NestedCuocGoiInputGroup: React.FC<NestedCuocGoiInputGroupProps> = ({ fieldIndex, nestedKey, labelPrefix, initialNestedData }) => {
    const { control, formState: { errors } } = useFormContext<FormDauSoDichVuDetail>();
    const baseFieldName = nestedKey;
    const timeFieldName = `${baseFieldName}.thoi_gian_goi_adjusted` as const;
    const costFieldName = `${baseFieldName}.cuoc_adjusted` as const;
    
    const timeError = (errors as Record<string, any>)?.[nestedKey]?.thoi_gian_goi_adjusted;
    const costError = (errors as Record<string, any>)?.[nestedKey]?.cuoc_adjusted;

    return (
        <>
            <div className="grid grid-cols-3 gap-4 items-start border-t pt-4 mt-4">
                <Label htmlFor={timeFieldName} className="col-span-3 font-medium">{labelPrefix}</Label>
                
                {/* Thoi Gian Goi */} 
                <div className="col-span-3 md:col-span-1 space-y-1">
                    <Label htmlFor={timeFieldName}>Thời gian gọi (giây)</Label>
                    <Controller
                        control={control}
                        name={timeFieldName}
                        render={({ field }: { field: ControllerRenderProps<FormDauSoDichVuDetail, typeof timeFieldName> }) => (
                            <Input
                                id={timeFieldName}
                                type="number"
                                step="any"
                                placeholder="Nhập TG hiệu chỉnh"
                                ref={field.ref}
                                name={field.name}
                                onBlur={field.onBlur}
                                value={field.value === null || field.value === undefined ? '' : String(field.value)}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                    const rawValue = e.target.value;
                                    const parsedValue = parseFloat(rawValue);
                                    field.onChange(rawValue === '' ? null : (isNaN(parsedValue) ? null : parsedValue));
                                }}
                                className={timeError ? 'border-destructive' : ''}
                            />
                        )}
                    />
                     <p className="text-xs text-muted-foreground">
                         Gốc: {formatNumber(initialNestedData?.thoi_gian_goi)}
                     </p>
                     {timeError && <p className="text-xs text-destructive">{timeError?.message || 'Invalid'}</p>}
                 </div>

                 {/* Cuoc */} 
                 <div className="col-span-3 md:col-span-1 space-y-1">
                     <Label htmlFor={costFieldName}>Cước (VNĐ)</Label>
                     <Controller
                         control={control}
                         name={costFieldName}
                         render={({ field }: { field: ControllerRenderProps<FormDauSoDichVuDetail, typeof costFieldName> }) => (
                             <Input
                                 id={costFieldName}
                                 type="number"
                                 step="any"
                                 placeholder="Nhập cước hiệu chỉnh"
                                 ref={field.ref}
                                 name={field.name}
                                 onBlur={field.onBlur}
                                 value={field.value === null || field.value === undefined ? '' : String(field.value)}
                                 onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                     const rawValue = e.target.value;
                                     const parsedValue = parseFloat(rawValue);
                                     field.onChange(rawValue === '' ? null : (isNaN(parsedValue) ? null : parsedValue));
                                 }}
                                 className={costError ? 'border-destructive' : ''}
                             />
                         )}
                     />
                     <p className="text-xs text-muted-foreground">
                         Gốc: {formatCurrency(initialNestedData?.cuoc)}
                     </p>
                     {costError && <p className="text-xs text-destructive">{costError?.message || 'Invalid'}</p>}
                 </div>
            </div>
        </>
    );
};

// Define props type for CuocThueBao helper
type NestedCuocThueBaoInputGroupProps = {
    fieldIndex: number;
    initialNestedData?: CuocThueBaoDetail | null;
};

// Helper component for rendering nested CuocThueBao fields
const NestedCuocThueBaoInputGroup: React.FC<NestedCuocThueBaoInputGroupProps> = ({ fieldIndex, initialNestedData }) => {
    const { control, formState: { errors } } = useFormContext<FormDauSoDichVuDetail>();
    const baseFieldName = 'cuoc_thue_bao' as const;
    const tbFieldName = `${baseFieldName}.thue_bao_thang_adjusted` as const;
    const ckFieldName = `${baseFieldName}.cam_ket_thang_adjusted` as const;
    const ttFieldName = `${baseFieldName}.tra_truoc_thang_adjusted` as const;

    const tbError = (errors as Record<string, any>)?.cuoc_thue_bao?.thue_bao_thang_adjusted;
    const ckError = (errors as Record<string, any>)?.cuoc_thue_bao?.cam_ket_thang_adjusted;
    const ttError = (errors as Record<string, any>)?.cuoc_thue_bao?.tra_truoc_thang_adjusted;

    const handleNumericChange = (e: React.ChangeEvent<HTMLInputElement>, onChangeCallback: (value: number | null) => void) => {
        const rawValue = e.target.value;
        const parsedValue = parseFloat(rawValue);
        onChangeCallback(rawValue === '' ? null : (isNaN(parsedValue) ? null : parsedValue));
    };

    return (
        <>
            <div className="grid grid-cols-3 gap-4 items-start border-t pt-4 mt-4">
                 <Label className="col-span-3 font-medium">Cước thuê bao</Label>

                 {/* Thue bao thang */} 
                 <div className="col-span-3 md:col-span-1 space-y-1">
                     <Label htmlFor={tbFieldName}>Thuê bao tháng (VNĐ)</Label>
                     <Controller
                         control={control}
                         name={tbFieldName}
                         render={({ field }: { field: ControllerRenderProps<FormDauSoDichVuDetail, typeof tbFieldName> }) => (
                             <Input 
                                id={tbFieldName} 
                                type="number" 
                                step="any" 
                                ref={field.ref}
                                name={field.name}
                                onBlur={field.onBlur}
                                value={field.value === null || field.value === undefined ? '' : String(field.value)} 
                                onChange={(e) => handleNumericChange(e, field.onChange)}
                                className={tbError ? 'border-destructive' : ''}
                             />
                         )}
                     />
                      <p className="text-xs text-muted-foreground">Gốc: {formatCurrency(initialNestedData?.thue_bao_thang)}</p>
                     {tbError && <p className="text-xs text-destructive">{tbError?.message || 'Invalid'}</p>}
                 </div>

                 {/* Cam ket thang */} 
                 <div className="col-span-3 md:col-span-1 space-y-1">
                     <Label htmlFor={ckFieldName}>Cam kết tháng (VNĐ)</Label>
                     <Controller 
                        control={control} 
                        name={ckFieldName} 
                        render={({ field }: { field: ControllerRenderProps<FormDauSoDichVuDetail, typeof ckFieldName> }) => (
                            <Input 
                                id={ckFieldName} 
                                type="number" 
                                step="any" 
                                ref={field.ref}
                                name={field.name}
                                onBlur={field.onBlur}
                                value={field.value === null || field.value === undefined ? '' : String(field.value)} 
                                onChange={(e) => handleNumericChange(e, field.onChange)}
                                className={ckError ? 'border-destructive' : ''}
                            />
                        )}
                     />
                     <p className="text-xs text-muted-foreground">Gốc: {formatCurrency(initialNestedData?.cam_ket_thang)}</p>
                     {ckError && <p className="text-xs text-destructive">{ckError?.message || 'Invalid'}</p>}
                 </div>

                 {/* Tra truoc thang */} 
                  <div className="col-span-3 md:col-span-1 space-y-1">
                     <Label htmlFor={ttFieldName}>Trả trước tháng (VNĐ)</Label>
                     <Controller 
                        control={control} 
                        name={ttFieldName} 
                        render={({ field }: { field: ControllerRenderProps<FormDauSoDichVuDetail, typeof ttFieldName> }) => (
                            <Input 
                                id={ttFieldName} 
                                type="number" 
                                step="any" 
                                ref={field.ref}
                                name={field.name}
                                onBlur={field.onBlur}
                                value={field.value === null || field.value === undefined ? '' : String(field.value)} 
                                onChange={(e) => handleNumericChange(e, field.onChange)}
                                className={ttError ? 'border-destructive' : ''}
                            />
                        )}
                    />
                     <p className="text-xs text-muted-foreground">Gốc: {formatCurrency(initialNestedData?.tra_truoc_thang)}</p>
                     {ttError && <p className="text-xs text-destructive">{ttError?.message || 'Invalid'}</p>}
                 </div>
            </div>
        </>
    );
};

export function DauSoDichVuNestedEditForm({ fieldIndex, initialItemData }: DauSoDichVuNestedEditFormProps) {
    useEffect(() => {
        if (initialItemData) {
            console.log("[FE Data Check] initialData:", JSON.stringify(initialItemData, null, 2));
            console.log("[FE Adjusted Value Check] co_dinh_noi_hat.thoi_gian_goi_adjusted:", initialItemData.co_dinh_noi_hat?.thoi_gian_goi_adjusted);
            // Bạn có thể thêm log cho các trường adjusted khác nếu cần
            // console.log("[FE Adjusted Value Check] cuoc_thue_bao.thue_bao_thang_adjusted:", initialData.cuoc_thue_bao?.thue_bao_thang_adjusted);
        }
    }, [initialItemData]); // Chạy effect khi initialData thay đổi

    return (
        <div className="space-y-4 p-1">
            {/* Render input groups for each nested structure that exists in the initial data */}
            {initialItemData?.co_dinh_noi_hat !== undefined && (
                <NestedCuocGoiInputGroup 
                    fieldIndex={fieldIndex}
                    nestedKey="co_dinh_noi_hat" 
                    labelPrefix="Cố định Nội hạt"
                    initialNestedData={initialItemData.co_dinh_noi_hat} 
                />
            )}
             {initialItemData?.co_dinh_lien_tinh !== undefined && (
                 <NestedCuocGoiInputGroup 
                     fieldIndex={fieldIndex}
                     nestedKey="co_dinh_lien_tinh" 
                     labelPrefix="Cố định Liên tỉnh"
                     initialNestedData={initialItemData.co_dinh_lien_tinh} 
                 />
             )}
             {initialItemData?.di_dong !== undefined && (
                 <NestedCuocGoiInputGroup 
                     fieldIndex={fieldIndex}
                     nestedKey="di_dong" 
                     labelPrefix="Di động"
                     initialNestedData={initialItemData.di_dong} 
                 />
             )}
            {initialItemData?.cuoc_1900 !== undefined && (
                 <NestedCuocGoiInputGroup 
                     fieldIndex={fieldIndex}
                     nestedKey="cuoc_1900" 
                     labelPrefix="Cước 1900"
                     initialNestedData={initialItemData.cuoc_1900} 
                 />
             )}
            {initialItemData?.quoc_te !== undefined && (
                 <NestedCuocGoiInputGroup 
                     fieldIndex={fieldIndex}
                     nestedKey="quoc_te" 
                     labelPrefix="Quốc tế"
                     initialNestedData={initialItemData.quoc_te} 
                 />
             )}
            {initialItemData?.cuoc_thue_bao !== undefined && (
                 <NestedCuocThueBaoInputGroup 
                     fieldIndex={fieldIndex}
                     initialNestedData={initialItemData.cuoc_thue_bao} 
                 />
             )}

            {/* Add a Save/Close button within the dialog content if needed */} 
        </div>
    );
} 