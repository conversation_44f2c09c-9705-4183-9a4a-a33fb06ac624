import React from 'react';
import { useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller, ControllerRenderProps } from 'react-hook-form';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { formatCurrency, formatNumber } from '@/lib/utils';
import type { DauSoDichVuCuocDetail } from '@/types/reconciliation';
import type { DscEditFormData } from '@/types/reconciliation-edit';

interface DscNestedEditFormProps {
    fieldIndex: number;
    initialItemData?: DauSoDichVuCuocDetail | null;
}

// Helper type for keys of adjustable fields in the form state
type DscAdjustableFieldKey = keyof Omit<DscEditFormData['du_lieu'][number], 'id'>;

// Helper component for rendering input fields for DSC adjustments
const DscAdjustmentInput: React.FC<{
    fieldIndex: number;
    nestedKey: DscAdjustableFieldKey;
    label: string;
    originalValue?: number | null;
    isCurrency?: boolean;
}> = ({ fieldIndex, nestedKey, label, originalValue, isCurrency = false }) => {
    console.log("[Debug DscAdjustmentInput] Rendering input for:", nestedKey);
    const { control, formState: { errors } } = useFormContext<DscEditFormData>();
    console.log("[Debug DscAdjustmentInput] Context Control:", control);
    const fieldName = `du_lieu.${fieldIndex}.${nestedKey}` as const;
    // Basic error checking - adjust path if nested structure differs
    const error = (errors.du_lieu?.[fieldIndex] as any)?.[nestedKey]; 
    const formatFn = isCurrency ? formatCurrency : formatNumber;

    const handleNumericChange = (e: React.ChangeEvent<HTMLInputElement>, onChangeCallback: (value: number | null) => void) => {
        const rawValue = e.target.value;
        // Allow empty string to represent null
        if (rawValue === '') {
            onChangeCallback(null);
            return;
        }
        // Try parsing as float, allow decimals
        const parsedValue = parseFloat(rawValue.replace(/,/g, '')); // Remove commas before parsing
        onChangeCallback(isNaN(parsedValue) ? null : parsedValue); // Store null if parsing fails
    };

    return (
        <div className="space-y-1">
            <Label htmlFor={fieldName} className="text-xs text-muted-foreground">{label}</Label>
            <Controller
                control={control}
                name={fieldName}
                render={({ field }: { field: ControllerRenderProps<DscEditFormData, typeof fieldName> }) => (
                    <Input
                        id={fieldName}
                        type="number" // Keep type number for browser validation hints, but handle parsing carefully
                        step="any"    // Allow decimals
                        placeholder="Nhập hiệu chỉnh"
                        ref={field.ref}
                        name={field.name}
                        onBlur={field.onBlur}
                        // Display empty string for null/undefined, otherwise the number
                        value={field.value === null || field.value === undefined ? '' : String(field.value)} 
                        onChange={(e) => handleNumericChange(e, field.onChange)}
                        className={`h-8 text-right ${error ? 'border-destructive' : ''}`}
                    />
                )}
            />
             <p className="text-xs text-muted-foreground h-4"> 
                 Gốc: {formatFn(originalValue)}
             </p>
             {error && <p className="text-xs text-destructive">{error?.message || 'Giá trị không hợp lệ'}</p>}
         </div>
    );
};

export function DscNestedEditForm({ fieldIndex, initialItemData }: DscNestedEditFormProps) {
    console.log("[Debug DscNestedEditForm] Rendering form for index:", fieldIndex, "Initial Data:", initialItemData);
    
    const renderInputPair = (telcoPrefix: 'vnm' | 'viettel' | 'vnpt' | 'vms' | 'khac', telcoLabel: string) => {
        console.log("[Debug DscNestedEditForm] Rendering pair for:", telcoLabel);
        const slKey = `${telcoPrefix}_san_luong_adjusted` as DscAdjustableFieldKey;
        const ttKey = `${telcoPrefix}_thanh_tien_adjusted` as DscAdjustableFieldKey;
        const originalSl = initialItemData ? (initialItemData as any)[`${telcoPrefix}_san_luong`] : null;
        const originalTt = initialItemData ? (initialItemData as any)[`${telcoPrefix}_thanh_tien`] : null;

        return (
            <div className="grid grid-cols-2 gap-4 border-t pt-4 mt-4">
                <p className="col-span-2 font-medium text-sm">{telcoLabel}</p>
                <DscAdjustmentInput 
                    fieldIndex={fieldIndex}
                    nestedKey={slKey}
                    label="Sản lượng (Phút)"
                    originalValue={originalSl}
                />
                 <DscAdjustmentInput 
                    fieldIndex={fieldIndex}
                    nestedKey={ttKey}
                    label="Thành tiền (VNĐ)"
                    originalValue={originalTt}
                    isCurrency
                />
            </div>
        );
    }

    return (
        <div className="space-y-4 p-1 max-w-full">
            {/* Render input pairs for each telco */} 
            {renderInputPair('vnm', 'Vietnamobile (VNM)')}
            {renderInputPair('viettel', 'Viettel')}
            {renderInputPair('vnpt', 'VNPT')}
            {renderInputPair('vms', 'Mobifone (VMS)')}
            {renderInputPair('khac', 'Mạng Khác')}

             {/* Display Original Total for reference */} 
             <div className="border-t pt-4 mt-4">
                 <p className="text-sm font-medium">Tổng thanh toán gốc:</p>
                 <p className="text-lg font-semibold">{formatCurrency(initialItemData?.tong_thanh_toan)}</p>
             </div>
         </div>
    );
} 