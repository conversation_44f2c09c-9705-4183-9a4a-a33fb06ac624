import React, { useState } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON>Header,
    DialogTitle,
    DialogDescription,
    DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { reconciliationService } from '@/services/reconciliation-service';
import { Loader2 } from 'lucide-react';
import { ReconciliationType } from '@/types/reconciliation';

interface CreateReconciliationDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    templateId: number | null;
    onSuccess: () => void;
}

export function CreateReconciliationDialog({
    open,
    onOpenChange,
    templateId,
    onSuccess,
}: CreateReconciliationDialogProps) {
    const [kyDoiSoat, setKyDoiSoat] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isPolling, setIsPolling] = useState(false);
    const { toast } = useToast();

    const handleClose = (shouldClose: boolean) => {
        // Always reset states when closing
        setIsLoading(false);
        setIsPolling(false);
        setKyDoiSoat('');
        setError(null);
        onOpenChange(shouldClose);
    };

    const handleKyDoiSoatChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setKyDoiSoat(value);
        
        // Validate format YYYY-MM
        const isValidFormat = /^\d{4}-\d{2}$/.test(value);
        if (!isValidFormat && value !== '') {
            setError('Định dạng không hợp lệ. Sử dụng YYYY-MM (ví dụ: 2024-03)');
        } else {
            setError(null);
        }
    };

    const handleSubmit = async () => {
        if (!templateId) {
            setError('Không có template được chọn');
            return;
        }

        try {
            setIsLoading(true);
            setError(null);

            // Create reconciliation
            const result = await reconciliationService.createReconciliationFromTemplate({
                template_id: templateId.toString(),
                ky_doi_soat: kyDoiSoat
            });

            // --- START: Prepare ID and Type for Polling ---
            const reconciliationId = parseInt(result.id, 10); // Convert ID string to number
            const reconciliationType = result.loai_doi_soat?.toUpperCase() as ReconciliationType | undefined;
            
            if (isNaN(reconciliationId) || !reconciliationType) {
                throw new Error('Phản hồi tạo đối soát không hợp lệ.');
            }
             // --- END: Prepare ID and Type for Polling ---

            // Start polling
            setIsPolling(true);
            toast({
                title: "Đang xử lý",
                description: "Đang tạo đối soát từ template. Vui lòng đợi...",
            });

            try {
                // Poll for status - Pass correct ID (number) and Type (enum)
                const finalResult = await reconciliationService.pollReconciliationStatus(
                    reconciliationId, 
                    reconciliationType,
                    60, // max attempts
                    5000 // interval in ms
                );

                console.log('Final result status:', finalResult.status);
                console.log('Final result:', finalResult);
                
                // Check against ReconciliationStatus enum if possible, otherwise use string comparison
                // Assuming finalResult.status is a string like 'CALCULATED' or 'ERROR'
                const finalStatus = finalResult.status?.toUpperCase();

                if (finalStatus === 'CALCULATED' || finalStatus === 'FINALIZED') { // Also consider FINALIZED as success?
                    toast({
                        title: "Thành công",
                        description: "Đã tạo và xử lý đối soát thành công.",
                    });
                    // Reset states before closing
                    setIsLoading(false);
                    setIsPolling(false);
                    setKyDoiSoat('');
                    setError(null);
                    // Call onSuccess before closing dialog
                    onSuccess();
                    // Finally close the dialog
                    onOpenChange(false);
                } else {
                    // Handle specific error status or generic failure
                     const errorMessage = finalResult.error_message || 'Xử lý đối soát thất bại';
                    throw new Error(errorMessage);
                }
            } catch (pollError: any) {
                console.error("Polling Error:", pollError); // Log the polling error details
                toast({
                    title: "Lỗi xử lý",
                    description: pollError.message || "Có lỗi xảy ra khi theo dõi quá trình xử lý đối soát",
                    variant: "destructive",
                });
                setError(pollError.message || "Có lỗi xảy ra khi xử lý đối soát");
                // Reset states in case of error during polling
                setIsLoading(false);
                setIsPolling(false);
            }
        } catch (createError: any) {
            console.error("Create Error:", createError); // Log the creation error details
            toast({
                title: "Lỗi tạo đối soát",
                description: createError.message || "Có lỗi xảy ra khi gửi yêu cầu tạo đối soát",
                variant: "destructive",
            });
            setError(createError.message || "Có lỗi xảy ra khi tạo đối soát");
            // Reset states in case of error during creation
            setIsLoading(false);
            setIsPolling(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Tạo Đối soát từ Mẫu</DialogTitle>
                    <DialogDescription>
                        Nhập kỳ đối soát (tháng) để tạo bản ghi đối soát mới từ mẫu đã chọn.
                        Mẫu ID: {templateId ?? 'N/A'}
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="kyDoiSoat" className="text-right">
                            Kỳ Đối soát
                        </Label>
                        <Input
                            id="kyDoiSoat"
                            value={kyDoiSoat}
                            onChange={handleKyDoiSoatChange}
                            placeholder="YYYY-MM"
                            className={`col-span-3 ${error ? 'border-red-500' : ''}`}
                            disabled={isLoading || isPolling}
                        />
                    </div>
                    {error && (
                        <p className="col-span-4 text-sm text-red-600 text-center">{error}</p>
                    )}
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => handleClose(false)} disabled={isLoading || isPolling}>
                        Hủy
                    </Button>
                    <Button 
                        type="submit" 
                        onClick={handleSubmit} 
                        disabled={isLoading || isPolling || !!error || !kyDoiSoat}
                    >
                        {isLoading || isPolling ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                {isPolling ? 'Đang xử lý...' : 'Đang tạo...'}
                            </>
                        ) : (
                            'Tạo Đối soát'
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
