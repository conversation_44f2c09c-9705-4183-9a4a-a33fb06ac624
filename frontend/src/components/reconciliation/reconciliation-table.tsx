import React from 'react';
import { format } from 'date-fns'; // Import date-fns
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge"; // Remove BadgeProps import for now
import { MoreHorizontal, ArrowUpDown, Loader2 } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

// Import types from the shared location (assuming it will be created)
import type { GetReconciliationsParams, ReconciliationListItem } from '@/types/reconciliation'; // Sửa ReconciliationItem thành ReconciliationListItem
import { ReconciliationStatus, ReconciliationType } from '@/types/reconciliation'; // Import value của Enum

// Match types used in list-page.tsx
interface MinimalPartnerInfo {
  id: number;
  name: string;
}

interface ReconciliationTableProps {
  data: ReconciliationListItem[]; // Sử dụng ReconciliationListItem
  isLoading: boolean;
  error: Error | null;
  // Add handlers for actions later
  // onViewDetails: (id: number, type: string) => void;
  // onEdit: (id: number, type: string) => void;
  // onFinalize: (id: number, type: string) => void;
  // onDelete: (id: number, type: string) => void;
  // --- Add Sorting Props --- 
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSortChange: (columnId: string) => void;
  // --- END Sorting Props --- 
  // --- START: Add Action Handler Props --- 
  onViewDetails: (item: ReconciliationListItem) => void; // Sử dụng ReconciliationListItem
  onEdit: (item: ReconciliationListItem) => void; // Sử dụng ReconciliationListItem
  onFinalize: (item: ReconciliationListItem) => void; // Sử dụng ReconciliationListItem
  onDelete: (item: ReconciliationListItem) => void; // Sử dụng ReconciliationListItem
  pollingIds: Set<number>; // Add pollingIds prop
   // --- END: Add Action Handler Props --- 
}

// Helper to map status string to Badge variant
// Assuming Shadcn Badge variants: default, secondary, outline, destructive
const getStatusBadgeVariant = (status: ReconciliationStatus) => {
    switch (status) {
        case ReconciliationStatus.TEMPLATE:
            return "secondary";
        case ReconciliationStatus.PROCESSING:
            return "warning";
        case ReconciliationStatus.CALCULATED:
            return "default";
        case ReconciliationStatus.ADJUSTED:
            return "info";
        case ReconciliationStatus.FINALIZED:
            return "success";
        case ReconciliationStatus.ERROR:
            return "destructive";
        default:
            return "default";
    }
};

const getStatusText = (status: ReconciliationStatus) => {
    switch (status) {
        case ReconciliationStatus.TEMPLATE:
            return "Mẫu";
        case ReconciliationStatus.PROCESSING:
            return "Đang xử lý";
        case ReconciliationStatus.CALCULATED:
            return "Đã tính toán";
        case ReconciliationStatus.ADJUSTED:
            return "Đã hiệu chỉnh";
        case ReconciliationStatus.FINALIZED:
            return "Đã chốt";
        case ReconciliationStatus.ERROR:
            return "Lỗi";
        default:
            return status;
    }
};

// --- Column Definitions for Sorting --- 
// Map header label to the actual sort_by key supported by the API
// Ensure these keys are actually supported by the backend API's sorting logic!
const columnSortKeys: { [key: string]: string } = {
  'ID': 'id',
  'Loại': 'reconciliation_type',
  'Kỳ ĐS': 'ky_doi_soat',
  'Trạng thái': 'status',
  'Ngày tạo': 'created_at',
};
// ------------------------------------ 

export function ReconciliationTable({ 
    data, 
    isLoading, 
    error, 
    sortBy, 
    sortOrder, 
    onSortChange, 
    // --- Destructure Action Handlers --- 
    onViewDetails,
    onEdit,
    onFinalize,
    onDelete,
    pollingIds
    // --- End Destructure Action Handlers --- 
}: ReconciliationTableProps) {

  if (error) {
    return <div className="text-red-600 p-4 border rounded-md">Error loading data: {error.message}</div>;
  }

  const renderTableContent = () => {
    if (isLoading) {
      return Array.from({ length: 5 }).map((_, index) => (
        <TableRow key={`skeleton-${index}`}>
          <TableCell className="w-[60px]"><Skeleton className="h-4 w-full" /></TableCell>
          <TableCell className="w-[100px]"><Skeleton className="h-4 w-full" /></TableCell>
          <TableCell className="w-[120px]"><Skeleton className="h-4 w-full" /></TableCell>
          <TableCell><Skeleton className="h-4 w-full" /></TableCell>
          <TableCell className="w-[120px]"><Skeleton className="h-4 w-full" /></TableCell>
          <TableCell className="w-[120px]"><Skeleton className="h-4 w-full" /></TableCell>
          <TableCell className="w-[80px] text-right"><Skeleton className="h-8 w-8 inline-block" /></TableCell>
        </TableRow>
      ));
    }

    if (isLoading && data.length === 0) { // Show skeleton only on initial load
      return Array.from({ length: 5 }).map((_, index) => (
        <TableRow key={`skeleton-${index}`}>
          <TableCell className="w-[60px]"><Skeleton className="h-4 w-full" /></TableCell>
          <TableCell className="w-[100px]"><Skeleton className="h-4 w-full" /></TableCell>
          <TableCell className="w-[120px]"><Skeleton className="h-4 w-full" /></TableCell>
          <TableCell><Skeleton className="h-4 w-full" /></TableCell>
          <TableCell className="w-[120px]"><Skeleton className="h-4 w-full" /></TableCell>
          <TableCell className="w-[120px]"><Skeleton className="h-4 w-full" /></TableCell>
          <TableCell className="w-[80px] text-right"><Skeleton className="h-8 w-8 inline-block" /></TableCell>
        </TableRow>
      ));
    }

    if (!data || data.length === 0) {
      return (
        <TableRow>
          <TableCell colSpan={7} className="h-24 text-center">
            Không có dữ liệu.
          </TableCell>
        </TableRow>
      );
    }

    return data.map((item) => {
        const isPolling = pollingIds.has(item.id);
        const isFinalized = item.status === ReconciliationStatus.FINALIZED;
        const canBeFinalized = (item.status === ReconciliationStatus.CALCULATED || item.status === ReconciliationStatus.ADJUSTED);
        
        return (
          <TableRow key={item.id}>
            <TableCell className="font-medium">{item.id}</TableCell>
            <TableCell>{item.reconciliation_type}</TableCell>
            <TableCell>{item.ky_doi_soat}</TableCell>
            <TableCell>{item.partner?.name ?? '-'}</TableCell>
            <TableCell>
              <div className="flex items-center space-x-1">
                <Badge variant={getStatusBadgeVariant(item.status)} className="capitalize">
                  {getStatusText(item.status)}
                </Badge>
                {isPolling && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />} 
              </div>
            </TableCell>
            <TableCell>
              {/* Format date using date-fns, handle potential invalid date */}
              {item.created_at ? format(new Date(item.created_at), 'dd/MM/yyyy') : '-'}
            </TableCell>
            <TableCell className="text-right">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {/* --- Call Action Handlers --- */}
                  <DropdownMenuItem onClick={() => onViewDetails(item)}>
                    Xem chi tiết
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => onFinalize(item)} 
                    disabled={!canBeFinalized || isPolling}
                  >
                    Chốt
                  </DropdownMenuItem>
                   <DropdownMenuItem
                     className="text-red-600 focus:text-red-700 focus:bg-red-50"
                     onClick={() => onDelete(item)}
                     disabled={isFinalized || isPolling}
                   >
                     Xóa
                   </DropdownMenuItem>
                   {/* --- End Call Action Handlers --- */}
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
        );
    });
  };

  // --- Helper to render TableHead with sorting --- 
  const renderSortableHeader = (label: string, columnKey: string | undefined) => {
      const actualSortKey = columnKey ? columnSortKeys[label] : undefined;
      if (!actualSortKey) { 
          return <TableHead>{label}</TableHead>; 
      }
      const isSorted = sortBy === actualSortKey;
      return (
          <TableHead className={actualSortKey ? 'cursor-pointer hover:bg-muted/50' : ''}>
              <Button variant="ghost" onClick={() => actualSortKey && onSortChange(actualSortKey)} className="px-2 py-1 h-auto -ml-2">
                  {label}
                  {actualSortKey && <ArrowUpDown className={`ml-2 h-3 w-3 ${isSorted ? 'opacity-100' : 'opacity-30'}`} />}
              </Button>
          </TableHead>
      );
  };
  // --------------------------------------------- 

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            {renderSortableHeader('ID', columnSortKeys['ID'])}
            {renderSortableHeader('Loại', columnSortKeys['Loại'])}
            {renderSortableHeader('Kỳ ĐS', columnSortKeys['Kỳ ĐS'])}
            {renderSortableHeader('Đối tác', undefined)}
            {renderSortableHeader('Trạng thái', columnSortKeys['Trạng thái'])}
            {renderSortableHeader('Ngày tạo', columnSortKeys['Ngày tạo'])}
            <TableHead className="w-[80px] text-right">Hành động</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {renderTableContent()}
        </TableBody>
      </Table>
    </div>
  );
} 