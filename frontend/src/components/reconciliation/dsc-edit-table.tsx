import React from 'react';
import { useFieldArray, useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller, ControllerRenderProps } from 'react-hook-form';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from '@/components/ui/button';
import { Edit } from 'lucide-react';
import { formatCurrency, formatNumber } from '@/lib/utils';
import type { DscReconciliationDetail, DauSoDichVuCuocDetail as DscDauSoDichVu } from '@/types/reconciliation';
import type { DscEditFormData } from '@/types/reconciliation-edit';

interface DscEditTableProps {
    initialData: DscDauSoDichVu[];
    onEditRow: (index: number) => void;
}

export function DscEditTable({ initialData, onEditRow }: DscEditTableProps) {
    console.log("[Debug DscEditTable] Component Rendered with initialData:", initialData);
    const { control, formState: { errors, dirtyFields } } = useFormContext<DscEditFormData>();

    const { fields } = useFieldArray<DscEditFormData, "du_lieu", "id">({
        control,
        name: "du_lieu",
    });

    const getInitialItem = (fieldId: string | number): DscDauSoDichVu | undefined => {
        const numericFieldId = typeof fieldId === 'string' ? parseInt(fieldId, 10) : fieldId;
        return initialData.find(item => item.id === numericFieldId);
    };

    const isNestedDirty = (index: number): boolean => {
        const dirtyRow = dirtyFields.du_lieu?.[index];
        if (!dirtyRow) return false;
        return !!(
            dirtyRow.vnm_san_luong_adjusted || dirtyRow.vnm_thanh_tien_adjusted ||
            dirtyRow.viettel_san_luong_adjusted || dirtyRow.viettel_thanh_tien_adjusted ||
            dirtyRow.vnpt_san_luong_adjusted || dirtyRow.vnpt_thanh_tien_adjusted ||
            dirtyRow.vms_san_luong_adjusted || dirtyRow.vms_thanh_tien_adjusted ||
            dirtyRow.khac_san_luong_adjusted || dirtyRow.khac_thanh_tien_adjusted
        );
    };

    return (
        <div className="w-full overflow-auto border rounded-md">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead className="w-[50px]">STT</TableHead>
                        <TableHead className="w-[200px]">Đầu số</TableHead>
                        <TableHead className="text-right">Tổng TT (Gốc)</TableHead>
                        <TableHead className="text-center w-[80px]">Chi tiết</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {fields.map((field: Record<"id", string | number>, index: number) => {
                        const initialItem = getInitialItem(field.id);
                        if (!initialItem) return null;

                        const nestedDirty = isNestedDirty(index);

                        return (
                            <TableRow key={field.id}>
                                <TableCell>{initialItem.stt}</TableCell>
                                <TableCell className="font-medium">{initialItem.dau_so}</TableCell>
                                <TableCell className="text-right">
                                    {formatCurrency(initialItem.tong_thanh_toan)}
                                </TableCell>
                                <TableCell className="text-center">
                                    <Button 
                                        type="button" 
                                        variant={nestedDirty ? "secondary" : "outline"}
                                        size="icon"
                                        onClick={() => {
                                            console.log(`[DEBUG DscEditTable] Edit button clicked for index: ${index}. Calling onEditRow.`); 
                                            onEditRow(index);
                                        }}
                                    >
                                        Sửa
                                    </Button>
                                </TableCell>
                            </TableRow>
                        );
                    })}
                </TableBody>
            </Table>
        </div>
    );
} 