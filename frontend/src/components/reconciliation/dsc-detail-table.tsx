import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { DauSoDichVuCuocDetail } from '@/types/reconciliation';
import { formatCurrency, formatNumber } from '@/lib/utils';

interface DscDetailTableProps {
  data: DauSoDichVuCuocDetail[];
  // Add adjustedData if needed later for comparison
  // adjustedData?: DauSoDichVuCuocDetail[];
  // displayMode?: 'actual' | 'adjusted'; 
}

// Helper to format number or show placeholder
const formatNumberDisplay = (val: number | null | undefined): string => {
    return val !== null && val !== undefined ? formatNumber(val) : '-';
};

// Helper to render value with potential adjustments shown
// Simplified for now, assumes we pass the data item which has both original and adjusted fields
const renderDisplayValue = (
    original: number | null | undefined,
    adjusted: number | null | undefined,
    isCurrency: boolean = false
) => {
    const displayValue = adjusted ?? original;
    const showOriginal = adjusted !== null && adjusted !== undefined && adjusted !== original;
    const formatFunc = isCurrency ? formatCurrency : formatNumberDisplay;

    return (
        <span className="flex flex-col items-end">
            {showOriginal && (
                <span className="text-xs text-muted-foreground line-through">{formatFunc(original)}</span>
            )}
            <span className={showOriginal ? 'text-blue-600 font-medium' : ''}>{formatFunc(displayValue)}</span>
        </span>
    );
};

export function DscDetailTable({ data }: DscDetailTableProps) {

  if (!data || data.length === 0) {
    return <p className="text-sm text-muted-foreground text-center py-4">Không có dữ liệu chi tiết cước.</p>;
  }

  return (
    <div className="rounded-md border overflow-x-auto">
      <Table className="min-w-full text-xs">
        <TableHeader>
          {/* Header Row 1: Nhà mạng */}
          <TableRow>
            <TableHead rowSpan={2} className="px-2 py-2 align-bottom whitespace-nowrap">STT</TableHead>
            <TableHead rowSpan={2} className="px-2 py-2 align-bottom whitespace-nowrap">Đầu số Dịch vụ</TableHead>
            <TableHead colSpan={2} className="text-center px-2 py-2 border-l whitespace-nowrap">VNM</TableHead>
            <TableHead colSpan={2} className="text-center px-2 py-2 border-l whitespace-nowrap">VIETTEL</TableHead>
            <TableHead colSpan={2} className="text-center px-2 py-2 border-l whitespace-nowrap">VNPT</TableHead>
            <TableHead colSpan={2} className="text-center px-2 py-2 border-l whitespace-nowrap">VMS</TableHead>
            <TableHead colSpan={2} className="text-center px-2 py-2 border-l whitespace-nowrap">Các mạng còn lại</TableHead>
            <TableHead rowSpan={2} className="text-right px-2 py-2 align-bottom border-l whitespace-nowrap">Tổng thanh toán</TableHead>
            {/* Add Action column header if needed */}
          </TableRow>
          {/* Header Row 2: Sản lượng / Thành tiền */}
          <TableRow>
            <TableHead className="text-right px-2 py-2 text-xs font-medium text-muted-foreground border-l whitespace-nowrap">SL (Phút)</TableHead>
            <TableHead className="text-right px-2 py-2 text-xs font-medium text-muted-foreground whitespace-nowrap">Thành tiền</TableHead>
            <TableHead className="text-right px-2 py-2 text-xs font-medium text-muted-foreground border-l whitespace-nowrap">SL (Phút)</TableHead>
            <TableHead className="text-right px-2 py-2 text-xs font-medium text-muted-foreground whitespace-nowrap">Thành tiền</TableHead>
            <TableHead className="text-right px-2 py-2 text-xs font-medium text-muted-foreground border-l whitespace-nowrap">SL (Phút)</TableHead>
            <TableHead className="text-right px-2 py-2 text-xs font-medium text-muted-foreground whitespace-nowrap">Thành tiền</TableHead>
            <TableHead className="text-right px-2 py-2 text-xs font-medium text-muted-foreground border-l whitespace-nowrap">SL (Phút)</TableHead>
            <TableHead className="text-right px-2 py-2 text-xs font-medium text-muted-foreground whitespace-nowrap">Thành tiền</TableHead>
            <TableHead className="text-right px-2 py-2 text-xs font-medium text-muted-foreground border-l whitespace-nowrap">SL (Phút)</TableHead>
            <TableHead className="text-right px-2 py-2 text-xs font-medium text-muted-foreground whitespace-nowrap">Thành tiền</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item, index) => (
            <TableRow key={item.id}>
              <TableCell className="whitespace-nowrap px-2 py-2">{item.stt || index + 1}</TableCell>
              <TableCell className="whitespace-nowrap px-2 py-2 font-medium">{item.dau_so}</TableCell>
              
              {/* VNM */}
              <TableCell className="text-right whitespace-nowrap px-2 py-2 border-l">
                {renderDisplayValue(item.vnm_san_luong, item.vnm_san_luong_adjusted)}
              </TableCell>
              <TableCell className="text-right whitespace-nowrap px-2 py-2">
                {renderDisplayValue(item.vnm_thanh_tien, item.vnm_thanh_tien_adjusted, true)}
              </TableCell>
              
              {/* VIETTEL */}
              <TableCell className="text-right whitespace-nowrap px-2 py-2 border-l">
                 {renderDisplayValue(item.viettel_san_luong, item.viettel_san_luong_adjusted)}
              </TableCell>
              <TableCell className="text-right whitespace-nowrap px-2 py-2">
                 {renderDisplayValue(item.viettel_thanh_tien, item.viettel_thanh_tien_adjusted, true)}
              </TableCell>
              
              {/* VNPT */}
              <TableCell className="text-right whitespace-nowrap px-2 py-2 border-l">
                 {renderDisplayValue(item.vnpt_san_luong, item.vnpt_san_luong_adjusted)}
              </TableCell>
              <TableCell className="text-right whitespace-nowrap px-2 py-2">
                 {renderDisplayValue(item.vnpt_thanh_tien, item.vnpt_thanh_tien_adjusted, true)}
              </TableCell>
              
              {/* VMS */}
              <TableCell className="text-right whitespace-nowrap px-2 py-2 border-l">
                 {renderDisplayValue(item.vms_san_luong, item.vms_san_luong_adjusted)}
              </TableCell>
              <TableCell className="text-right whitespace-nowrap px-2 py-2">
                 {renderDisplayValue(item.vms_thanh_tien, item.vms_thanh_tien_adjusted, true)}
              </TableCell>
              
              {/* Khac */}
              <TableCell className="text-right whitespace-nowrap px-2 py-2 border-l">
                 {renderDisplayValue(item.khac_san_luong, item.khac_san_luong_adjusted)}
              </TableCell>
              <TableCell className="text-right whitespace-nowrap px-2 py-2">
                 {renderDisplayValue(item.khac_thanh_tien, item.khac_thanh_tien_adjusted, true)}
              </TableCell>
              
              {/* Tổng thanh toán */}
              <TableCell className="text-right whitespace-nowrap px-2 py-2 border-l font-medium">
                 {renderDisplayValue(item.tong_thanh_toan, item.tong_thanh_toan_adjusted, true)}
              </TableCell>
              
              {/* Add Action cell if needed */}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
} 