import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from '@/components/ui/input';
import type { DauSoDichVuDetail } from '@/types/reconciliation';
import { Button } from '@/components/ui/button';
import { Pencil } from 'lucide-react';
import { formatCurrency, formatNumber } from '@/lib/utils';

interface DauSoDichVuTableProps {
  data: DauSoDichVuDetail[];
  adjustedData?: DauSoDichVuDetail[];
  isEditing?: boolean;
  activeEditIndex?: number | null;
  displayMode?: 'actual' | 'adjusted';
  onAdjustedDataChange?: (index: number, field: keyof DauSoDichVuDetail, value: string) => void;
  onEditRow?: (index: number) => void;
}

// Helper to format number or show placeholder
const formatNumberDisplay = (val: number | null | undefined): string => {
    return val !== null && val !== undefined ? formatNumber(val) : '-';
};

// Helper to render static value or *display* adjusted value (no input)
const renderDisplayValue = (
    original: number | null | undefined,
    adjusted: number | null | undefined,
    isCurrency: boolean = false
) => {
    const displayValue = adjusted ?? original;
    const showOriginal = adjusted !== null && adjusted !== undefined && adjusted !== original;
    const formatFunc = isCurrency ? formatCurrency : formatNumberDisplay;

    return (
        <span className="flex flex-col items-end">
            {showOriginal && (
                <span className="text-xs text-muted-foreground line-through">{formatFunc(original)}</span>
            )}
            <span className={showOriginal ? 'text-blue-600 font-medium' : ''}>{formatFunc(displayValue)}</span>
        </span>
    );
};

// Helper to show which sub-detail exists
const renderSubDetailType = (item: DauSoDichVuDetail): string => {
    if (item.co_dinh_noi_hat || item.co_dinh_lien_tinh || item.di_dong || item.cuoc_1900 || item.quoc_te) {
        // Could be more specific later
        return "Cước gọi";
    }
    if (item.cuoc_thue_bao) {
        return "Thuê bao";
    }
    return "Khác";
};

export function DauSoDichVuTable({ 
    data, 
    adjustedData = [], 
    isEditing = false, 
    activeEditIndex = null, 
    displayMode = 'adjusted',
    onAdjustedDataChange, 
    onEditRow 
}: DauSoDichVuTableProps) {
  // Use adjustedData length for rendering rows if available, otherwise original data
  const displayData = (adjustedData && adjustedData.length > 0 && isEditing) ? adjustedData : data;

  if (!displayData || displayData.length === 0) {
    return <p className="text-sm text-muted-foreground text-center py-4">Không có dữ liệu chi tiết dịch vụ.</p>;
  }

  // Kiểm tra dữ liệu đầu vào để quyết định hiển thị loại bảng nào
  const hasDetailedCalls = displayData.some(item => 
    item.co_dinh_noi_hat || item.co_dinh_lien_tinh || item.di_dong || item.cuoc_1900 || item.quoc_te || item.cuoc_thue_bao
  );
  
  // Hiển thị bảng chi tiết với các thông tin cuộc gọi (HTC)
  if (hasDetailedCalls) {
    return (
      <div className="rounded-md border overflow-x-auto">
        <Table className="min-w-full text-xs table-layout-fixed">
          {/* --- HTC Header (Restructured to Two Rows) --- */}
          <TableHeader>
            {/* --- Row 1: Main Headers --- */}
            <TableRow key="htc-header-row-1">
              {/* STT and Đầu số span both rows */}
              <TableHead rowSpan={2} className="whitespace-nowrap px-2 py-2 sticky left-0 bg-background z-10 align-middle w-[60px]">STT</TableHead>
              {/* Increased width further for Đầu số column */}
              <TableHead rowSpan={2} className="whitespace-nowrap px-2 py-2 sticky left-[64px] bg-background z-10 align-middle w-[200px]">Đầu số</TableHead>

              {/* Call Type Headers spanning 2 columns */}
              <TableHead colSpan={2} className="text-center whitespace-nowrap px-2 py-2 border-l border-r">CĐ Nội hạt</TableHead>
              <TableHead colSpan={2} className="text-center whitespace-nowrap px-2 py-2 border-r">CĐ Liên tỉnh</TableHead>
              <TableHead colSpan={2} className="text-center whitespace-nowrap px-2 py-2 border-r">Di động</TableHead>
              <TableHead colSpan={2} className="text-center whitespace-nowrap px-2 py-2 border-r">1900</TableHead>
              <TableHead colSpan={2} className="text-center whitespace-nowrap px-2 py-2 border-r">Quốc tế</TableHead>
              
              {/* Subscription Fee Headers spanning both rows */}
              <TableHead rowSpan={2} className="text-right whitespace-nowrap px-2 py-2 align-middle border-l">TB Tháng</TableHead>
              <TableHead rowSpan={2} className="text-right whitespace-nowrap px-2 py-2 align-middle">CK Tháng</TableHead>
              <TableHead rowSpan={2} className="text-right whitespace-nowrap px-2 py-2 align-middle border-r">TT Tháng</TableHead>
              
              {/* Final Cost Headers spanning both rows */}
              <TableHead rowSpan={2} className="text-right whitespace-nowrap px-2 py-2 align-middle border-l">Cước Thu KH</TableHead>
              <TableHead rowSpan={2} className="text-right whitespace-nowrap px-2 py-2 align-middle border-r">Cước Trả HTC</TableHead>
              
              {/* Edit Action spanning both rows (if applicable) */}
              {isEditing && <TableHead rowSpan={2} className="text-center whitespace-nowrap px-2 py-2 sticky right-0 bg-background z-10 align-middle">Sửa</TableHead>}
            </TableRow>
            
            {/* --- Row 2: Sub-headers (TG Gọi / Cước) --- */}
            <TableRow key="htc-header-row-2">
                {/* Placeholders for STT and Dau So handled by rowSpan */}
                {/* CĐ Nội hạt Sub-headers */}
                <TableHead className="text-right whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground border-l">TG Gọi</TableHead>
                <TableHead className="text-right whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground border-r">Cước</TableHead>
                {/* CĐ Liên tỉnh Sub-headers */}
                <TableHead className="text-right whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground">TG Gọi</TableHead>
                <TableHead className="text-right whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground border-r">Cước</TableHead>
                {/* Di động Sub-headers */}
                <TableHead className="text-right whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground">TG Gọi</TableHead>
                <TableHead className="text-right whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground border-r">Cước</TableHead>
                {/* 1900 Sub-headers */}
                <TableHead className="text-right whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground">TG Gọi</TableHead>
                <TableHead className="text-right whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground border-r">Cước</TableHead>
                {/* Quốc tế Sub-headers */}
                <TableHead className="text-right whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground">TG Gọi</TableHead>
                <TableHead className="text-right whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground border-r">Cước</TableHead>
                {/* Placeholders for Subscription, Final Costs, and Edit handled by rowSpan */}
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayData.map((item, index) => {
              // Find the corresponding original item from the `data` prop
              const originalItem = data.find(orig => orig.id === item.id);

              // Helper to get original nested data for comparison
              const getOriginalNested = <T extends object, K extends keyof T>(key: K): T[K] | undefined => originalItem ? (originalItem as unknown as T)[key] : undefined;

              return (
                <TableRow key={item.id || index} className={activeEditIndex === index ? "bg-muted/50" : ""}>
                  {/* Sticky Cells */}
                  <TableCell className="whitespace-nowrap px-2 py-2 sticky left-0 bg-background z-[5]">{index + 1}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2 sticky left-[64px] bg-background z-[5] font-medium">{item.standardized_display}</TableCell>

                  {/* CĐ Nội hạt - Removed Loại column */}
                  {/* <TableCell className="whitespace-nowrap px-2 py-2 border-l">{item.co_dinh_noi_hat?.loai_cuoc ?? '-'}</TableCell> */}
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-l">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'co_dinh_noi_hat'>('co_dinh_noi_hat')?.thoi_gian_goi,
                        displayMode === 'adjusted' ? (item.co_dinh_noi_hat as any)?.thoi_gian_goi_adjusted : null
                    )}
                  </TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-r">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'co_dinh_noi_hat'>('co_dinh_noi_hat')?.cuoc,
                        displayMode === 'adjusted' ? (item.co_dinh_noi_hat as any)?.cuoc_adjusted : null,
                        true
                    )}
                  </TableCell>

                  {/* CĐ Liên tỉnh - Removed Loại column */}
                  {/* <TableCell className="whitespace-nowrap px-2 py-2">{item.co_dinh_lien_tinh?.loai_cuoc ?? '-'}</TableCell> */}
                  <TableCell className="text-right whitespace-nowrap px-2 py-2">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'co_dinh_lien_tinh'>('co_dinh_lien_tinh')?.thoi_gian_goi,
                        displayMode === 'adjusted' ? (item.co_dinh_lien_tinh as any)?.thoi_gian_goi_adjusted : null
                    )}
                  </TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-r">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'co_dinh_lien_tinh'>('co_dinh_lien_tinh')?.cuoc,
                        displayMode === 'adjusted' ? (item.co_dinh_lien_tinh as any)?.cuoc_adjusted : null,
                        true
                    )}
                  </TableCell>

                  {/* Di động - Removed Loại column */}
                  {/* <TableCell className="whitespace-nowrap px-2 py-2">{item.di_dong?.loai_cuoc ?? '-'}</TableCell> */}
                  <TableCell className="text-right whitespace-nowrap px-2 py-2">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'di_dong'>('di_dong')?.thoi_gian_goi,
                        displayMode === 'adjusted' ? (item.di_dong as any)?.thoi_gian_goi_adjusted : null
                    )}
                  </TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-r">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'di_dong'>('di_dong')?.cuoc,
                        displayMode === 'adjusted' ? (item.di_dong as any)?.cuoc_adjusted : null,
                        true
                    )}
                  </TableCell>

                  {/* 1900 - Removed Loại column */}
                  {/* <TableCell className="whitespace-nowrap px-2 py-2">{item.cuoc_1900?.loai_cuoc ?? '-'}</TableCell> */}
                  <TableCell className="text-right whitespace-nowrap px-2 py-2">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'cuoc_1900'>('cuoc_1900')?.thoi_gian_goi,
                        displayMode === 'adjusted' ? (item.cuoc_1900 as any)?.thoi_gian_goi_adjusted : null
                    )}
                  </TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-r">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'cuoc_1900'>('cuoc_1900')?.cuoc,
                        displayMode === 'adjusted' ? (item.cuoc_1900 as any)?.cuoc_adjusted : null,
                        true
                    )}
                  </TableCell>

                  {/* Quốc tế - Removed Loại column */}
                  {/* <TableCell className="whitespace-nowrap px-2 py-2">{item.quoc_te?.loai_cuoc ?? '-'}</TableCell> */}
                  <TableCell className="text-right whitespace-nowrap px-2 py-2">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'quoc_te'>('quoc_te')?.thoi_gian_goi,
                        displayMode === 'adjusted' ? (item.quoc_te as any)?.thoi_gian_goi_adjusted : null
                    )}
                  </TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-r">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'quoc_te'>('quoc_te')?.cuoc,
                        displayMode === 'adjusted' ? (item.quoc_te as any)?.cuoc_adjusted : null,
                        true
                    )}
                  </TableCell>

                  {/* Cước thuê bao */}
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-l">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'cuoc_thue_bao'>('cuoc_thue_bao')?.thue_bao_thang,
                        displayMode === 'adjusted' ? (item.cuoc_thue_bao as any)?.thue_bao_thang_adjusted : null,
                        true
                    )}
                  </TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'cuoc_thue_bao'>('cuoc_thue_bao')?.cam_ket_thang,
                        displayMode === 'adjusted' ? (item.cuoc_thue_bao as any)?.cam_ket_thang_adjusted : null,
                        true
                    )}
                  </TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-r">
                    {renderDisplayValue(
                        getOriginalNested<DauSoDichVuDetail, 'cuoc_thue_bao'>('cuoc_thue_bao')?.tra_truoc_thang,
                        displayMode === 'adjusted' ? (item.cuoc_thue_bao as any)?.tra_truoc_thang_adjusted : null,
                        true
                    )}
                  </TableCell>

                  {/* Cước thu khách & Cước trả HTC */}
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-l">
                    {renderDisplayValue(
                        originalItem?.cuoc_thu_khach,
                        displayMode === 'adjusted' ? (item as any).cuoc_thu_khach_adjusted : null,
                        true
                    )}
                  </TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-r">
                    {renderDisplayValue(
                        originalItem?.cuoc_tra_htc,
                        displayMode === 'adjusted' ? (item as any).cuoc_tra_htc_adjusted : null,
                        true
                    )}
                  </TableCell>

                  {/* Action Cell */}
                  {isEditing && (
                    <TableCell className="text-center whitespace-nowrap px-2 py-1 sticky right-0 bg-background z-[5]">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onEditRow?.(index)}
                        className={`h-7 w-7 p-1 ${activeEditIndex === index ? 'text-primary' : 'text-muted-foreground hover:text-foreground'}`}
                      >
                        <Pencil className="h-3.5 w-3.5" />
                      </Button>
                    </TableCell>
                  )}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    );
  } else {
    // Hiển thị bảng tóm tắt theo nhà mạng (DSC)
    return (
      <div className="rounded-md border overflow-x-auto">
        <Table className="min-w-full text-xs table-layout-fixed">
          {/* --- DSC Header (Two Rows) --- */}
          <TableHeader>
            {/* --- Row 1: Main Headers --- */}
            <TableRow key="dsc-header-row-1">
              {/* Defined fixed width and removed left offset */}
              <TableHead rowSpan={2} className="whitespace-nowrap px-2 py-2 sticky left-0 bg-background z-10 align-middle w-[60px]">STT</TableHead>
              {/* Defined fixed width and set left offset based on STT width */}
              <TableHead rowSpan={2} className="whitespace-nowrap px-2 py-2 sticky left-[64px] bg-background z-10 align-middle w-[250px]">Đầu số</TableHead>
              {/* VNM */}
              <TableHead colSpan={2} className="text-center whitespace-nowrap px-2 py-2 border-l border-r">VNM</TableHead>
              {/* VIETTEL */}
              <TableHead colSpan={2} className="text-center whitespace-nowrap px-2 py-2 border-r">VIETTEL</TableHead>
              {/* VNPT */}
              <TableHead colSpan={2} className="text-center whitespace-nowrap px-2 py-2 border-r">VNPT</TableHead>
              {/* VMS */}
              <TableHead colSpan={2} className="text-center whitespace-nowrap px-2 py-2 border-r">VMS</TableHead>
              {/* Khac */}
              <TableHead colSpan={2} className="text-center whitespace-nowrap px-2 py-2 border-r">Khác</TableHead>
              <TableHead rowSpan={2} className="text-right whitespace-nowrap px-2 py-2 align-middle border-l">Tổng TT</TableHead>
              {isEditing && <TableHead rowSpan={2} className="text-center whitespace-nowrap px-2 py-2 sticky right-0 bg-background z-10 align-middle">Sửa</TableHead>}
            </TableRow>
            {/* --- Row 2: Sub-headers (SL/TT) --- */}
            <TableRow key="dsc-header-row-2">
              {/* Placeholders for STT and Dau So are handled by rowSpan in Row 1 */}
              {/* VNM */}
              <TableHead className="text-center whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground border-l">SL</TableHead>
              <TableHead className="text-center whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground border-r">TT</TableHead>
              {/* VIETTEL */}
              <TableHead className="text-center whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground">SL</TableHead>
              <TableHead className="text-center whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground border-r">TT</TableHead>
              {/* VNPT */}
              <TableHead className="text-center whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground">SL</TableHead>
              <TableHead className="text-center whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground border-r">TT</TableHead>
              {/* VMS */}
              <TableHead className="text-center whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground">SL</TableHead>
              <TableHead className="text-center whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground border-r">TT</TableHead>
              {/* Khac */}
              <TableHead className="text-center whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground">SL</TableHead>
              <TableHead className="text-center whitespace-nowrap px-2 py-1 text-xs font-medium text-muted-foreground border-r">TT</TableHead>
              {/* Placeholders for Tong TT and Sua are handled by rowSpan in Row 1 */}
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayData.map((item, index) => {
              // Find the corresponding original item from the `data` prop
              const originalItem = data.find(orig => orig.id === item.id);

              // Helper for rendering values in edit or display mode - Adjusted for DSC structure
              const renderCell = (fieldKey: keyof DauSoDichVuDetail, adjustedFieldKey: keyof DauSoDichVuDetail, isCurrency: boolean = false) => {
                const originalValue = originalItem ? (originalItem as any)[fieldKey] : undefined; 
                const adjustedValue = (item as any)[adjustedFieldKey] as number | null | undefined;
                return renderDisplayValue(originalValue, adjustedValue, isCurrency);
              };

              return (
                <TableRow key={item.id || index} className={activeEditIndex === index ? "bg-muted/50" : ""}>
                  {/* Sticky Cells */}
                  <TableCell className="whitespace-nowrap px-2 py-2 sticky left-0 bg-background z-[5]">{(item as any).stt ?? index + 1}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2 sticky left-[64px] bg-background z-[5] font-medium">
                    {(item as any).standardized_display ?? (item as any).dau_so ?? 'N/A'}
                  </TableCell>

                  {/* VNM */}
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-l">{renderCell('vnm_san_luong' as any, 'vnm_san_luong_adjusted' as any)}</TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-r">{renderCell('vnm_thanh_tien' as any, 'vnm_thanh_tien_adjusted' as any, true)}</TableCell>

                  {/* VIETTEL */}
                  <TableCell className="text-right whitespace-nowrap px-2 py-2">{renderCell('viettel_san_luong' as any, 'viettel_san_luong_adjusted' as any)}</TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-r">{renderCell('viettel_thanh_tien' as any, 'viettel_thanh_tien_adjusted' as any, true)}</TableCell>

                  {/* VNPT */}
                  <TableCell className="text-right whitespace-nowrap px-2 py-2">{renderCell('vnpt_san_luong' as any, 'vnpt_san_luong_adjusted' as any)}</TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-r">{renderCell('vnpt_thanh_tien' as any, 'vnpt_thanh_tien_adjusted' as any, true)}</TableCell>

                  {/* VMS */}
                  <TableCell className="text-right whitespace-nowrap px-2 py-2">{renderCell('vms_san_luong' as any, 'vms_san_luong_adjusted' as any)}</TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-r">{renderCell('vms_thanh_tien' as any, 'vms_thanh_tien_adjusted' as any, true)}</TableCell>

                  {/* Khác */}
                  <TableCell className="text-right whitespace-nowrap px-2 py-2">{renderCell('khac_san_luong' as any, 'khac_san_luong_adjusted' as any)}</TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 border-r">{renderCell('khac_thanh_tien' as any, 'khac_thanh_tien_adjusted' as any, true)}</TableCell>

                  {/* Tổng Thành tiền */}
                   <TableCell className="text-right whitespace-nowrap px-2 py-2 font-medium border-l">
                    {renderDisplayValue(
                      (originalItem as any)?.tong_thanh_toan, 
                      (item as any).tong_thanh_toan_adjusted, 
                      true
                    )}
                  </TableCell>

                  {/* Action Cell */}
                  {isEditing && (
                    <TableCell className="text-center whitespace-nowrap px-2 py-1 sticky right-0 bg-background z-[5]">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onEditRow?.(index)}
                        className={`h-7 w-7 p-1 ${activeEditIndex === index ? 'text-primary' : 'text-muted-foreground hover:text-foreground'}`}
                      >
                        <Pencil className="h-3.5 w-3.5" />
                      </Button>
                    </TableCell>
                  )}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    );
  }
} 