import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { 
    Dialog, 
    DialogContent, 
    DialogDescription, 
    DialogFooter, 
    DialogHeader, 
    DialogTitle 
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { ReloadIcon } from "@radix-ui/react-icons";
import { useToast } from "@/components/ui/use-toast";
import { templateService } from "@/services/template-service";
import { partnerService } from "@/services/partner-service";
import { TemplateTypes } from "@/models/template";
import { Partner } from "@/types/partner";

// Removed mock partner data

const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
const ACCEPTED_FILE_TYPES = [
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
];

const formSchema = z.object({
    name: z.string().min(3, {
        message: "Tên mẫu cần có ít nhất 3 ký tự.",
    }),
    description: z.string().optional(),
    partner_id: z.string({
        required_error: "Vui lòng chọn đối tác.",
    }),
    template_type: z.string({
        required_error: "Vui lòng chọn loại mẫu.",
    }),
    auto_detect: z.boolean().default(false),
    file: z
        .instanceof(File, { message: "Vui lòng chọn file mẫu." })
        .refine((file) => file.size <= MAX_FILE_SIZE, `Kích thước file tối đa là 20MB.`)
        .refine(
            (file) => ACCEPTED_FILE_TYPES.includes(file.type),
            "Chỉ chấp nhận file Excel (.xlsx, .xls)."
        ),
});

type FormValues = z.infer<typeof formSchema>;

export interface TemplateUploadDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onSuccess: () => void;
}

export function TemplateUploadDialog({
    open,
    onOpenChange,
    onSuccess,
}: TemplateUploadDialogProps) {
    const { toast } = useToast();
    const [loading, setLoading] = useState(false);
    const [isDetecting, setIsDetecting] = useState(false);
    const [detectedType, setDetectedType] = useState<string | null>(null);
    const [partners, setPartners] = useState<Partner[]>([]);
    const [isLoadingPartners, setIsLoadingPartners] = useState(false);

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: "",
            description: "",
            auto_detect: true,
            partner_id: undefined,
        },
    });

    // Fetch partners from API
    useEffect(() => {
        const fetchPartners = async () => {
            setIsLoadingPartners(true);
            try {
                // Gọi với limit lớn và lấy items từ response
                const response = await partnerService.getPartners({ limit: 1000 }); 
                setPartners(response.items || []); // Lấy items từ response
            } catch (error) {
                console.error("Failed to fetch partners:", error);
                toast({
                    variant: "destructive",
                    title: "Lỗi",
                    description: "Không thể tải danh sách đối tác. Vui lòng thử lại."
                });
            } finally {
                setIsLoadingPartners(false);
            }
        };

        if (open) {
            fetchPartners();
        }
    }, [open, toast]);

    // Reset form khi dialog được mở/đóng
    useEffect(() => {
        if (open) {
            form.reset({
                name: "",
                description: "",
                auto_detect: true,
                template_type: undefined,
                partner_id: undefined,
            });
            setDetectedType(null);
        }
    }, [open, form]);

    // Xử lý khi file thay đổi
    const handleFileChange = async (file: File | null) => {
        if (!file) return;
        
        console.log("[Debug] handleFileChange called with file:", file.name);
        
        const autoDetect = form.getValues("auto_detect");
        console.log("[Debug] autoDetect value:", autoDetect);
        
        if (autoDetect) {
            setIsDetecting(true);
            try {
                // Tạo FormData để gửi file
                const formData = new FormData();
                formData.append("file", file);
                
                console.log("[Debug] Calling detectTemplateType API");
                
                // Gọi API phân tích
                const result = await templateService.detectTemplateType(formData);
                
                console.log("[Debug] API response:", result);
                
                if (result.detected_type) {
                    setDetectedType(result.detected_type);
                    form.setValue("template_type", result.detected_type);
                    
                    toast({
                        title: "Đã nhận diện loại mẫu",
                        description: `Hệ thống nhận diện đây là mẫu: ${TemplateTypes[result.detected_type as keyof typeof TemplateTypes]} (độ tin cậy: ${Math.round(result.confidence * 100)}%)`,
                    });
                } else {
                    toast({
                        title: "Không thể nhận diện loại mẫu",
                        description: "Hệ thống không thể xác định loại mẫu. Vui lòng chọn thủ công.",
                        variant: "warning",
                    });
                }
            } catch (error) {
                console.error("Detection error:", error);
                toast({
                    title: "Lỗi nhận diện",
                    description: "Không thể nhận diện loại mẫu. Vui lòng chọn thủ công.",
                    variant: "destructive",
                });
            } finally {
                setIsDetecting(false);
            }
        }
    };

    // Theo dõi sự thay đổi của file và auto_detect
    useEffect(() => {
        const subscription = form.watch((value, { name }) => {
            console.log("[Debug] form.watch detected change in:", name, value);
            if (name === "file" && value.file) {
                console.log("[Debug] File changed in form:", value.file);
                handleFileChange(value.file as File);
            }
        });
        
        return () => subscription.unsubscribe();
    }, [form]);

    const onSubmit = async (values: FormValues) => {
        try {
            setLoading(true);
            
            const formData = new FormData();
            formData.append("name", values.name);
            formData.append("template_type", values.template_type);
            formData.append("auto_detect", values.auto_detect.toString());
            formData.append("partner_id", values.partner_id);
            
            if (values.description) {
                formData.append("description", values.description);
            }
            
            formData.append("file", values.file);
            
            const response = await templateService.uploadTemplate(formData);
            
            // Thông báo thành công dựa trên kết quả từ API
            toast({
                title: "Tải lên thành công",
                description: "Mẫu đối soát đã được tải lên thành công.",
            });
            
            onSuccess();
            onOpenChange(false);
        } catch (error) {
            console.error("Upload error:", error);
            toast({
                title: "Lỗi khi tải lên",
                description: "Đã xảy ra lỗi khi tải lên mẫu. Vui lòng thử lại.",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>Tải lên mẫu đối soát</DialogTitle>
                    <DialogDescription>
                        Tải lên mẫu đối soát mới để sử dụng trong quá trình đối soát
                    </DialogDescription>
                </DialogHeader>
                
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Tên mẫu</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Nhập tên mẫu" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        
                        <FormField
                            control={form.control}
                            name="partner_id"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Đối tác</FormLabel>
                                    <Select 
                                        onValueChange={field.onChange} 
                                        value={field.value}
                                        disabled={isLoadingPartners}
                                    >
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Chọn đối tác" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {partners.map((partner) => (
                                                <SelectItem key={partner.id} value={partner.id.toString()}>
                                                    {partner.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {isLoadingPartners && (
                                        <FormDescription>
                                            Đang tải danh sách đối tác...
                                        </FormDescription>
                                    )}
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        
                        <FormField
                            control={form.control}
                            name="description"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Mô tả</FormLabel>
                                    <FormControl>
                                        <Textarea placeholder="Mô tả về mẫu" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        
                        <FormField
                            control={form.control}
                            name="template_type"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Loại mẫu</FormLabel>
                                    <Select 
                                        onValueChange={field.onChange} 
                                        value={field.value}
                                        disabled={isDetecting}
                                    >
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Chọn loại mẫu" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {Object.entries(TemplateTypes).map(([key, value]) => (
                                                <SelectItem key={key} value={key}>
                                                    {value}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {detectedType && (
                                        <FormDescription>
                                            Loại mẫu được nhận diện: {TemplateTypes[detectedType as keyof typeof TemplateTypes]}
                                        </FormDescription>
                                    )}
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        
                        <FormField
                            control={form.control}
                            name="auto_detect"
                            render={({ field }) => (
                                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-3">
                                    <FormControl>
                                        <Checkbox
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                    <div className="space-y-1 leading-none">
                                        <FormLabel>Tự động nhận diện</FormLabel>
                                        <FormDescription>
                                            Hệ thống sẽ tự động nhận diện loại mẫu dựa trên nội dung file
                                        </FormDescription>
                                    </div>
                                </FormItem>
                            )}
                        />
                        
                        <FormField
                            control={form.control}
                            name="file"
                            render={({ field: { value, onChange, ...fieldProps } }) => (
                                <FormItem>
                                    <FormLabel>File mẫu</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="file"
                                            accept=".xlsx,.xls"
                                            onChange={(e) => {
                                                console.log("[Debug] File input changed:", e.target.files?.[0]?.name);
                                                const file = e.target.files?.[0] || null;
                                                onChange(file);
                                            }}
                                            {...fieldProps}
                                        />
                                    </FormControl>
                                    <FormDescription>
                                        Chấp nhận file Excel (.xlsx, .xls), kích thước tối đa 20MB
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        
                        <DialogFooter>
                            <Button 
                                type="submit" 
                                disabled={loading || isDetecting}
                            >
                                {(loading || isDetecting) && <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />}
                                {isDetecting ? "Đang nhận diện..." : loading ? "Đang tải lên..." : "Tải lên"}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}

export default TemplateUploadDialog; 