import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { templateService, DoiSoatTemplate, PaginatedTemplates } from '@/services/template-service';
import { MoreHorizontal, Download, Edit, Trash, Alert<PERSON>ircle, Filter, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { TemplateEditDialog } from './TemplateEditDialog';
import { TemplateTypes, TemplateType } from '@/models/template';
import { useNavigate } from 'react-router-dom';

interface TemplateListProps {
  refreshTrigger?: number;
  onRefresh?: () => void;
}

export function TemplateList({ refreshTrigger = 0, onRefresh }: TemplateListProps) {
  const [templates, setTemplates] = useState<PaginatedTemplates | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [templateTypeFilter, setTemplateTypeFilter] = useState<string | undefined>(undefined);
  const [templateToEdit, setTemplateToEdit] = useState<DoiSoatTemplate | null>(null);
  const [templateToDelete, setTemplateToDelete] = useState<DoiSoatTemplate | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const pageSize = 10;

  // Fetch templates when filters or page changes
  useEffect(() => {
    fetchTemplates();
  }, [page, templateTypeFilter, refreshTrigger]);

  const fetchTemplates = async () => {
    setIsLoading(true);
    try {
      const data = await templateService.getTemplates({
        skip: (page - 1) * pageSize,
        limit: pageSize,
        template_type: templateTypeFilter,
      });
      setTemplates(data);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Lỗi khi tải danh sách mẫu',
        description: error.message || 'Đã có lỗi xảy ra',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Reset page when filter changes
  useEffect(() => {
    setPage(1);
  }, [templateTypeFilter]);

  const handleEdit = (template: DoiSoatTemplate) => {
    setTemplateToEdit(template);
    setIsEditDialogOpen(true);
  };

  const handleDelete = (template: DoiSoatTemplate) => {
    setTemplateToDelete(template);
    setIsDeleteDialogOpen(true);
  };

  const handleDownload = async (template: DoiSoatTemplate) => {
    try {
      await templateService.downloadTemplate(template.id);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Lỗi khi tải xuống mẫu',
        description: error.message || 'Đã có lỗi xảy ra',
      });
    }
  };

  const handleEditSuccess = () => {
    toast({
      title: 'Cập nhật thành công',
      description: 'Mẫu đối soát đã được cập nhật',
    });
    setIsEditDialogOpen(false);
    fetchTemplates();
    if (onRefresh) onRefresh();
  };

  const confirmDelete = async () => {
    if (!templateToDelete) return;
    
    try {
      await templateService.deleteTemplate(templateToDelete.id);
      toast({
        title: 'Xóa thành công',
        description: 'Mẫu đối soát đã được xóa',
      });
      fetchTemplates();
      if (onRefresh) onRefresh();
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Lỗi khi xóa mẫu',
        description: error.message || 'Đã có lỗi xảy ra',
      });
    } finally {
      setIsDeleteDialogOpen(false);
    }
  };

  const getTemplateTypeName = (type: string) => {
    switch (type) {
      case 'dsc':
        return 'Đối soát cước';
      case 'dscd':
        return 'Đối soát cố định';
      case 'dst_1800_1900':
        return 'Đối soát 1800/1900';
      default:
        return type;
    }
  };

  const handleViewDetail = (template: DoiSoatTemplate) => {
    navigate(`/templates/${template.id}`);
  };

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {templates && `Tổng số ${templates.total} mẫu đối soát`}
        </div>
        <div className="flex items-center space-x-2">
          <Select 
            value={templateTypeFilter} 
            onValueChange={(value) => setTemplateTypeFilter(value === "all" ? undefined : value)}
          >
            <SelectTrigger className="w-[200px]">
              <div className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                <span>{templateTypeFilter ? TemplateTypes[templateTypeFilter as TemplateType] : 'Tất cả loại mẫu'}</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả loại mẫu</SelectItem>
              {Object.entries(TemplateTypes).map(([key, value]) => (
                <SelectItem key={key} value={key}>{value}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin h-8 w-8 border-2 border-primary rounded-full border-t-transparent"></div>
        </div>
      ) : (
        <>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">#</TableHead>
                  <TableHead>Tên mẫu</TableHead>
                  <TableHead>Loại</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Ngày tạo</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates && templates.items.length > 0 ? (
                  templates.items.map((template, index) => (
                    <TableRow key={template.id}>
                      <TableCell className="font-medium">
                        {(page - 1) * pageSize + index + 1}
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{template.name}</div>
                        {template.description && (
                          <div className="text-sm text-muted-foreground">{template.description}</div>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{getTemplateTypeName(template.template_type)}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={template.is_active ? "success" : "secondary"}>
                          {template.is_active ? "Đang sử dụng" : "Không sử dụng"}
                        </Badge>
                      </TableCell>
                      <TableCell>{format(new Date(template.created_at), 'dd/MM/yyyy')}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Mở menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewDetail(template)}>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>Xem chi tiết</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDownload(template)}>
                              <Download className="mr-2 h-4 w-4" />
                              <span>Tải xuống</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEdit(template)}>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>Chỉnh sửa</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(template)}>
                              <Trash className="mr-2 h-4 w-4" />
                              <span>Xóa</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <AlertCircle className="h-8 w-8 mb-2" />
                        <p>Không có mẫu đối soát nào</p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {templates && templates.total > pageSize && (
            <div className="flex items-center justify-end space-x-2 py-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page > 1 ? page - 1 : 1)}
                disabled={page === 1}
              >
                Trước
              </Button>
              <div className="text-sm">
                Trang {page} / {Math.ceil(templates.total / pageSize)}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page >= Math.ceil(templates.total / pageSize)}
              >
                Sau
              </Button>
            </div>
          )}
        </>
      )}

      {/* Edit Dialog */}
      {templateToEdit && (
        <TemplateEditDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          template={templateToEdit}
          onSuccess={handleEditSuccess}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa mẫu</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa mẫu đối soát này? Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>Xóa</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 