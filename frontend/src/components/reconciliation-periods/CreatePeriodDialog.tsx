import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

import { reconciliationPeriodService } from '@/services/reconciliation-period-service';

const currentYear = new Date().getFullYear();
const currentMonth = new Date().getMonth() + 1;

const formSchema = z.object({
  year: z.number().int().min(1970, 'Năm phải lớn hơn 1970').max(currentYear + 5, `Năm không quá ${currentYear + 5}`),
  month: z.number().int().min(1, 'Tháng phải từ 1').max(12, 'Tháng phải đến 12'),
  name: z.string().min(1, 'Tên kỳ đối soát không được để trống'),
  notes: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface CreatePeriodDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function CreatePeriodDialog({
  open,
  onOpenChange,
  onSuccess,
}: CreatePeriodDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      year: currentYear,
      month: currentMonth,
      name: `Kỳ đối soát ${currentMonth}/${currentYear}`,
      notes: '',
    },
  });

  const watchedYear = form.watch('year');
  const watchedMonth = form.watch('month');

  useEffect(() => {
    if (typeof watchedYear === 'number' && typeof watchedMonth === 'number' && !isNaN(watchedYear) && !isNaN(watchedMonth)) {
      form.setValue('name', `Kỳ đối soát ${watchedMonth}/${watchedYear}`, { shouldValidate: false });
    }
  }, [watchedYear, watchedMonth, form]);

  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);
      await reconciliationPeriodService.createReconciliationPeriod({
        year: values.year,
        month: values.month,
        name: values.name,
        notes: values.notes,
      });
      toast({
        title: 'Thành công',
        description: 'Tạo kỳ đối soát thành công.',
      });
      onSuccess();
      onOpenChange(false);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Lỗi khi tạo kỳ đối soát',
        description: error.response?.data?.detail || error.message || 'Đã có lỗi xảy ra',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getYearOptions = () => {
    const startYear = 2020;
    const endYear = currentYear + 1;
    const options = [];
    for (let year = endYear; year >= startYear; year--) {
      options.push({ value: year.toString(), label: year.toString() });
    }
    return options;
  };

  const getMonthOptions = () => {
    const options = [];
    for (let month = 1; month <= 12; month++) {
      options.push({ value: month.toString(), label: `Tháng ${month}` });
    }
    return options;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Tạo kỳ đối soát mới</DialogTitle>
          <DialogDescription>
            Nhập thông tin tháng và năm để tạo kỳ đối soát mới.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 pt-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="year"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Năm</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      value={field.value?.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn năm" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {getYearOptions().map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="month"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tháng</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      value={field.value?.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn tháng" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {getMonthOptions().map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tên kỳ đối soát</FormLabel>
                  <FormControl>
                    <Input placeholder="Nhập tên kỳ đối soát" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ghi chú</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Nhập ghi chú (không bắt buộc)"
                      {...field}
                      value={field.value ?? ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
                Hủy
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSubmitting ? 'Đang tạo...' : 'Tạo kỳ đối soát'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 