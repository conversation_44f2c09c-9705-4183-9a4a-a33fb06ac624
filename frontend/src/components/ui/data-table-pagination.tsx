import {
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
  } from "lucide-react"
  
  import { But<PERSON> } from "@/components/ui/button"
  import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
  
  interface DataTablePaginationProps {
    currentPage: number
    totalItems: number
    pageSize: number
    onPageChange: (page: number) => void
    // Optional: Add page sizes and handler if needed
    // availablePageSizes?: number[]
    // onPageSizeChange?: (size: number) => void
  }
  
  export function DataTablePagination({ 
      currentPage, 
      totalItems, 
      pageSize, 
      onPageChange 
  }: DataTablePaginationProps) {
    
    const totalPages = Math.ceil(totalItems / pageSize)
  
    const handlePreviousPage = () => {
      if (currentPage > 1) {
        onPageChange(currentPage - 1)
      }
    }
  
    const handleNextPage = () => {
      if (currentPage < totalPages) {
        onPageChange(currentPage + 1)
      }
    }

    const handleGoToFirstPage = () => {
        onPageChange(1)
    }

    const handleGoToLastPage = () => {
        onPageChange(totalPages)
    }
  
    if (totalPages <= 1) {
      return null // Don't render pagination if there's only one page or less
    }

    return (
      <div className="flex items-center justify-between px-2 pt-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {/* Optional: Show selected rows count if implementing selection */} 
          {/* {table.getFilteredSelectedRowModel().rows.length} of{" "} */}
          Tổng cộng {totalItems} bản ghi.
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          {/* Optional: Page size selector */} 
          {/* <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Rows per page</p>
            <Select
              value={`${pageSize}`}
              onValueChange={(value) => {
                // onPageSizeChange?.(Number(value))
              }}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={pageSize} />
              </SelectTrigger>
              <SelectContent side="top">
                {[10, 20, 30, 40, 50].map((size) => (
                  <SelectItem key={size} value={`${size}`}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div> */}
          <div className="flex w-[120px] items-center justify-center text-sm font-medium">
            Trang {currentPage} của {totalPages}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={handleGoToFirstPage}
              disabled={currentPage === 1}
            >
              <span className="sr-only">Go to first page</span>
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={handlePreviousPage}
              disabled={currentPage === 1}
            >
              <span className="sr-only">Go to previous page</span>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
            >
              <span className="sr-only">Go to next page</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={handleGoToLastPage}
              disabled={currentPage === totalPages}
            >
              <span className="sr-only">Go to last page</span>
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    )
  } 