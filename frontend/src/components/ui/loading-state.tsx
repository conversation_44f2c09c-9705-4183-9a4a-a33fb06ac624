import { Spinner } from "@/components/ui/spinner";
import { cn } from "@/lib/utils";

interface LoadingStateProps {
  variant?: "default" | "card" | "overlay";
  text?: string;
  className?: string;
}

export function LoadingState({ 
  variant = "default", 
  text = "Loading...",
  className 
}: LoadingStateProps) {
  if (variant === "overlay") {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
        <div className="flex flex-col items-center gap-2">
          <Spinner size="lg" />
          <p className="text-sm text-muted-foreground">{text}</p>
        </div>
      </div>
    );
  }

  if (variant === "card") {
    return (
      <div className={cn(
        "flex h-[200px] w-full flex-col items-center justify-center rounded-lg border bg-card p-6",
        className
      )}>
        <Spinner size="md" />
        <p className="mt-2 text-sm text-muted-foreground">{text}</p>
      </div>
    );
  }

  return (
    <div className={cn(
      "flex h-[200px] w-full items-center justify-center",
      className
    )}>
      <div className="flex flex-col items-center gap-2">
        <Spinner size="md" />
        <p className="text-sm text-muted-foreground">{text}</p>
      </div>
    </div>
  );
} 