import React from 'react';
import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  Pagin<PERSON>Ellipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"; // Import building blocks
import { cn } from "@/lib/utils"; // Import cn for class handling

interface ReusablePaginationProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  siblingCount?: number; // Number of page links to show around the current page
  className?: string; // Allow custom styling
}

export const ReusablePagination: React.FC<ReusablePaginationProps> = ({
  currentPage,
  totalItems,
  itemsPerPage,
  onPageChange,
  siblingCount = 1, // Default to 1 sibling page link on each side
  className,
}) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Early return if no pagination needed
  if (totalPages <= 1) {
    return null; 
  }

  // --- Logic to determine page numbers to display ---
  // Based on siblingCount, currentPage, and totalPages
  // This logic can get complex, let's implement a simplified version for now
  // (or use a library hook like usePagination if available)

  const getPageNumbers = (): (number | 'ellipsis')[] => {
    const totalPageNumbersToShow = siblingCount * 2 + 5; // Siblings + First + Last + Current + 2 Ellipses

    // Case 1: Total pages less than the numbers we want to show -> show all pages
    if (totalPages <= totalPageNumbersToShow) {
        return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages);

    const shouldShowLeftEllipsis = leftSiblingIndex > 2;
    const shouldShowRightEllipsis = rightSiblingIndex < totalPages - 1;

    const firstPageIndex = 1;
    const lastPageIndex = totalPages;

    // Case 2: No left ellipsis, but right ellipsis
    if (!shouldShowLeftEllipsis && shouldShowRightEllipsis) {
        let leftItemCount = 3 + 2 * siblingCount;
        let leftRange = Array.from({ length: leftItemCount }, (_, i) => i + 1);
        return [...leftRange, 'ellipsis', lastPageIndex];
    }

    // Case 3: No right ellipsis, but left ellipsis
    if (shouldShowLeftEllipsis && !shouldShowRightEllipsis) {
        let rightItemCount = 3 + 2 * siblingCount;
        let rightRange = Array.from({ length: rightItemCount }, (_, i) => totalPages - rightItemCount + 1 + i);
        return [firstPageIndex, 'ellipsis', ...rightRange];
    }

    // Case 4: Both ellipses shown
    if (shouldShowLeftEllipsis && shouldShowRightEllipsis) {
        let middleRange = Array.from({ length: rightSiblingIndex - leftSiblingIndex + 1 }, (_, i) => leftSiblingIndex + i);
        return [firstPageIndex, 'ellipsis', ...middleRange, 'ellipsis', lastPageIndex];
    }
    
    // Fallback (should not happen with above logic)
     return Array.from({ length: totalPages }, (_, i) => i + 1);
  };

  const pageNumbers = getPageNumbers();

  const handlePrevious = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault(); // Prevent default anchor behavior
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault(); // Prevent default anchor behavior
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const handlePageClick = (e: React.MouseEvent<HTMLAnchorElement>, pageNumber: number) => {
    e.preventDefault(); // Prevent default anchor behavior
    onPageChange(pageNumber);
  };

  return (
    // Use cn to merge classes
    <Pagination className={cn("mt-4", className)}>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious 
            href="#"
            onClick={handlePrevious} 
            aria-disabled={currentPage === 1}
            tabIndex={currentPage === 1 ? -1 : undefined}
            className={currentPage === 1 ? "pointer-events-none opacity-50" : undefined}
           />
        </PaginationItem>

        {pageNumbers.map((pageNumber, index) => (
          <PaginationItem key={index}>
            {pageNumber === 'ellipsis' ? (
              <PaginationEllipsis />
            ) : (
              <PaginationLink 
                href="#" 
                onClick={(e: React.MouseEvent<HTMLAnchorElement>) => handlePageClick(e, pageNumber as number)}
                isActive={currentPage === pageNumber}
                aria-current={currentPage === pageNumber ? "page" : undefined}
              >
                {pageNumber}
              </PaginationLink>
            )}
          </PaginationItem>
        ))}

        <PaginationItem>
          <PaginationNext 
             href="#" 
             onClick={handleNext} 
             aria-disabled={currentPage === totalPages}
             tabIndex={currentPage === totalPages ? -1 : undefined}
             className={currentPage === totalPages ? "pointer-events-none opacity-50" : undefined}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}; 