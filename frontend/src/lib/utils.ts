import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: string | Date): string {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

/**
 * Converts bytes to a human-readable string (KB, MB, GB, etc.)
 */
export function bytesToSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${parseFloat((bytes / Math.pow(1024, i)).toFixed(2))} ${sizes[i]}`;
}

export function formatTime(date: string | Date): string {
  return new Date(date).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  });
}

/**
 * Formats duration in seconds to a human-readable string (e.g., "2h 30m 15s")
 */
export function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${seconds}s`;
  }
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  let result = '';
  
  if (hours > 0) {
    result += `${hours}h `;
  }
  
  if (minutes > 0 || hours > 0) {
    result += `${minutes}m `;
  }
  
  if (remainingSeconds > 0 || (hours === 0 && minutes === 0)) {
    result += `${remainingSeconds}s`;
  }
  
  return result.trim();
}

/**
 * Format a number with thousands separators
 */
export function formatNumber(value: number): string {
  return new Intl.NumberFormat('vi-VN').format(value);
}

// Function to format numbers as currency (e.g., VND)
export function formatCurrency(value: number | null | undefined, currency = 'VND', locale = 'vi-VN'): string {
  if (value == null) {
    return 'N/A'; // Or return '0' or '' depending on desired output for null/undefined
  }
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      // You might want to adjust options like minimumFractionDigits
      minimumFractionDigits: 0, 
      maximumFractionDigits: 2,
    }).format(value);
  } catch (error) {
    console.error("Error formatting currency:", error);
    return String(value); // Fallback to string representation
  }
}

/**
 * Parses a formatted string (potentially with commas, currency symbols etc.) into a number.
 * Returns NaN if parsing fails.
 */
export function parseFormattedNumber(value: string | null | undefined): number {
  if (value === null || value === undefined || typeof value !== 'string') {
    return NaN;
  }
  // Remove common non-numeric characters except decimal point
  // Adjust regex as needed for different formatting (e.g., spaces, currency symbols)
  const cleanedValue = value.replace(/[^\d.-]/g, ''); 
  
  // Handle cases like "-" or "." which are not valid numbers alone
  if (cleanedValue === '' || cleanedValue === '-' || cleanedValue === '.' || cleanedValue === '-.') {
      return NaN;
  }
  
  const parsed = parseFloat(cleanedValue);
  return isNaN(parsed) ? NaN : parsed; // Return NaN if parseFloat fails
}

// TODO: Add other utility functions as needed 