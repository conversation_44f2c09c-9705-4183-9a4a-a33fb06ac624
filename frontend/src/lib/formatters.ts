// Hàm định dạng đơn vị
export const formatUnit = (unit: string): string => {
  switch (unit) {
    case 'minute_per_month':
      return 'phút/tháng';
    case 'minute':
      return 'phút';
    case 'VND':
      return 'VNĐ';
    default:
      return unit;
  }
};

// Hàm định dạng tiền tệ
export const formatCurrency = (amount: number | string): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    maximumFractionDigits: 0
  }).format(numAmount);
};

// Hàm định dạng loại pricing
export const formatPricingType = (type: string): string => {
  switch (type.toLowerCase()) {
    case 'revenue':
      return 'Thu';
    case 'cost':
      return 'Chi';
    default:
      return type;
  }
};

// Hàm định dạng phương thức tính cước
export const formatBillingMethod = (method: string): string => {
  switch (method) {
    case 'block_6s':
      return 'Block 6s';
    case 'one_sec_plus':
      return '1s+';
    case 'one_min_plus':
      return '1\'+ (phút)';
    case 'subscription':
      return 'Cước thuê bao';
    default:
      return method;
  }
}; 