import axios, { 
  AxiosInstance, 
  AxiosRequestConfig, 
  AxiosResponse, 
  AxiosError,
  InternalAxiosRequestConfig
} from "axios";
import { toast } from "@/components/ui/use-toast";
import { ApiError } from "@/types/pricing";

// Xác định base URL dựa trên môi trường
const getBaseURL = () => {
  // Nếu đang ở production (không phải localhost:3000)
  if (!(window.location.hostname === 'localhost' && window.location.port === '3000')) {
    // Lấy hostname hiện tại (ví dụ: ************)
    const backendHost = window.location.hostname;
    // Port mà backend service được expose ra host trong docker-compose.prod.yml
    const backendPort = 8000; 
    console.log(`[API Config] Running in production mode. Backend URL: http://${backendHost}:${backendPort}/api`);
    return `http://${backendHost}:${backendPort}/api`; // <PERSON><PERSON> gồm cả /api
  }
  // Mặc định là môi trường development (localhost:3000)
  console.log('[API Config] Running in development mode. Backend URL: http://localhost:8000/api');
  return "http://localhost:8000/api"; // Trỏ thẳng vào backend được expose ở local
};

const baseURL = getBaseURL();

// Token mặc định (chỉ nên dùng cho dev)
const DEFAULT_ADMIN_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwiaXNfYWRtaW4iOnRydWUsImV4cCI6MTc0MjM2MTA3MH0.IBt29JKE-eQ82CQZ2giNxPOKKmk1EygeRuDwbH1lsXs";

// API Configuration
const API_CONFIG = {
  baseURL: baseURL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
  validateStatus: function (status: number) {
    // Consider all status codes between 200 and 300 as successful
    return status >= 200 && status < 300;
  }
};

// Create axios instance with default config
export const api: AxiosInstance = axios.create(API_CONFIG);

// Request interceptor type
const onRequest = (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
  let token = localStorage.getItem("access_token");
  
  // Nếu không có token trong localStorage, sử dụng token mặc định (dev/test)
  if (!token) {
    console.log("[API] Using default admin token for development/testing");
    token = DEFAULT_ADMIN_TOKEN;
    localStorage.setItem("access_token", token);
  }
  
  if (token && config.headers) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  
  // Enhanced logging for all requests
  console.log(`[API Request] ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`, {
    url: `${config.baseURL}${config.url}`,
    method: config.method?.toUpperCase(),
    headers: config.headers,
    data: config.data,
    params: config.params,
    token: token ? `${token.substring(0, 10)}...` : 'No token'
  });

  return config;
};

// Request error handler type
const onRequestError = (error: AxiosError): Promise<AxiosError> => {
  console.error('[API Request Error]', {
    name: error.name,
    message: error.message,
    config: error.config,
    code: error.code,
    stack: error.stack
  });
  return Promise.reject(error);
};

// Response interceptor type
const onResponse = (response: AxiosResponse): AxiosResponse => {
  // Enhanced logging for all responses
  console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}:`, {
    status: response.status,
    statusText: response.statusText,
    headers: response.headers,
    data: response.data,
    config: {
      url: response.config.url,
      method: response.config.method,
      headers: response.config.headers
    }
  });
  return response;
};

// Response error handler type
const onResponseError = (error: AxiosError<ApiError>): Promise<AxiosError> => {
  // Enhanced error logging
  console.error(`[API Error] ${error.config?.method?.toUpperCase()} ${error.config?.url}:`, {
    name: error.name,
    message: error.message,
    status: error.response?.status,
    statusText: error.response?.statusText,
    data: error.response?.data,
    headers: error.response?.headers,
    config: {
      url: error.config?.url,
      method: error.config?.method,
      headers: error.config?.headers,
      data: error.config?.data
    },
    stack: error.stack
  });

  const fallbackMessage = "An unexpected error occurred";
  
  // Handle network errors
  if (!error.response) {
    toast({
      title: "Network Error",
      description: "Please check your internet connection",
      variant: "destructive",
    });
    return Promise.reject(error);
  }

  // Handle API errors
  const message = error.response?.data?.message || error.message || fallbackMessage;
  
  toast({
    title: `Error ${error.response?.status || ''}`,
    description: message,
    variant: "destructive",
  });

  // Handle specific status codes
  switch (error.response?.status) {
    case 401:
      // Handle unauthorized
      localStorage.removeItem("access_token");
      window.location.href = "/login";
      break;
    case 403:
      // Handle forbidden
      break;
    case 404:
      // Handle not found
      break;
    case 422:
      // Handle validation errors
      const errors = error.response?.data?.errors;
      if (errors) {
        Object.entries(errors).forEach(([field, messages]) => {
          toast({
            title: `${field} Error`,
            description: Array.isArray(messages) ? messages[0] : messages,
            variant: "destructive",
          });
        });
      }
      break;
    default:
      // Handle other errors
      break;
  }

  return Promise.reject(error);
};

// Add request interceptor
api.interceptors.request.use(onRequest, onRequestError);

// Add response interceptor
api.interceptors.response.use(onResponse, onResponseError);

// Export types for reuse
export type { AxiosResponse, AxiosError, AxiosRequestConfig }; 