import axios from 'axios';
import { useAuthStore } from '@/store/auth';

// Create axios instance with default config
const axiosInstance = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Log configuration for debugging
console.log('[axios] Initialized with baseURL:', axiosInstance.defaults.baseURL);

// Flag to prevent multiple refresh token calls
let isRefreshing = false;
// Queue of requests to retry after token refresh
let refreshSubscribers: Array<(token: string) => void> = [];

// Helper to add to retry queue
const subscribeTokenRefresh = (callback: (token: string) => void) => {
  refreshSubscribers.push(callback);
};

// Helper to process retry queue
const onRefreshed = (token: string) => {
  console.log(`[axios] Processing ${refreshSubscribers.length} queued requests with new token`);
  refreshSubscribers.forEach(callback => callback(token));
  refreshSubscribers = [];
};

// Request interceptor for adding auth token
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      
      // Log request details for debugging
      const method = config.method?.toUpperCase() || 'UNKNOWN';
      const url = config.baseURL + config.url;
      console.log(`[axios] ${method} Request to: ${url}`, {
        params: config.params,
        method: config.method,
        contentType: config.headers['Content-Type']
      });
    } else {
      console.warn('[axios] No auth token available for request');
    }
    return config;
  },
  (error) => {
    console.error('[axios] Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for handling token refresh
axiosInstance.interceptors.response.use(
  (response) => {
    console.log(`[axios] Response from ${response.config.url}:`, {
      status: response.status,
      statusText: response.statusText
    });
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    // Log error for debugging
    console.error('[axios] Response error:', {
      url: originalRequest?.url,
      method: originalRequest?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      message: error.message
    });
    
    // If error is 401 Unauthorized and the request hasn't been retried yet
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      console.log('[axios] Attempting to refresh token after 401 error');
      
      // Check if we have a refresh token
      const refreshToken = localStorage.getItem('refresh_token');
      if (!refreshToken) {
        console.error('[axios] No refresh token available - cannot refresh');
        // No refresh token, logout and redirect to login
        useAuthStore.getState().logout();
        return Promise.reject(error);
      }
      
      // If already refreshing, add to queue
      if (isRefreshing) {
        console.log('[axios] Already refreshing, adding request to queue');
        return new Promise((resolve) => {
          subscribeTokenRefresh((token: string) => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            resolve(axiosInstance(originalRequest));
          });
        });
      }
      
      // Start refreshing process
      isRefreshing = true;
      console.log('[axios] Starting token refresh process');
      
      try {
        // Correct refresh token URL to avoid double /api prefix
        const refreshUrl = '/v1/auth/refresh-token';
        console.log(`[axios] Calling refresh token endpoint at: ${axiosInstance.defaults.baseURL}${refreshUrl}`);
        
        // Call refresh token endpoint
        const response = await axios.post(axiosInstance.defaults.baseURL + refreshUrl, {
          refresh_token: refreshToken,
        });
        
        const { access_token, refresh_token } = response.data;
        console.log('[axios] Successfully refreshed token');
        
        // Store new tokens
        localStorage.setItem('access_token', access_token);
        if (refresh_token) {
          localStorage.setItem('refresh_token', refresh_token);
        }
        
        // Update auth header for the original request
        originalRequest.headers.Authorization = `Bearer ${access_token}`;
        
        // Process any queued requests
        onRefreshed(access_token);
        
        // Reset refreshing flag
        isRefreshing = false;
        
        // Retry original request
        console.log('[axios] Retrying original request with new token');
        return axiosInstance(originalRequest);
      } catch (refreshError) {
        // If refresh fails, logout user
        console.error('[axios] Token refresh failed:', refreshError);
        isRefreshing = false;
        refreshSubscribers = [];
        
        useAuthStore.getState().logout();
        
        // Redirect to login page
        window.location.href = '/login';
        
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

export default axiosInstance; 