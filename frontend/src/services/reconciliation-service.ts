import { api } from '@/lib/api';
import { PaginatedResponse } from '@/types/pricing';
import type { ReconciliationListResponse, GetReconciliationsParams, ReconciliationDetail, ReconciliationListItem, DauSoDichVuDetail, DscdReconciliationDetail, DscReconciliationDetail, DscdAdjustmentsPayload, DscAdjustmentsPayload } from '@/types/reconciliation';
import { ReconciliationType } from '@/types/reconciliation';

// Define the base URL for reconciliation endpoints
const API_RECONCILIATIONS = '/v1/doi-soat';

// Define the adjustment payload types directly here or import from types
interface TongKetAdjustments {
    cong_tien_dich_vu_adjusted?: number | null;
    tien_thue_gtgt_adjusted?: number | null;
    tong_cong_tien_adjusted?: number | null;
}

interface DauSoDichVuAdjustments {
    id: number;
    cuoc_thu_khach_adjusted?: number | null;
    cuoc_tra_htc_adjusted?: number | null;
    // Add other adjustable fields if needed
}

interface ReconciliationAdjustmentsPayload {
    tong_ket?: TongKetAdjustments | null;
    du_lieu?: DauSoDichVuAdjustments[] | null;
}

// --- START: Add Types for Create from Template ---
interface CreateReconciliationRequest {
    template_id: string;
    ky_doi_soat: string; // YYYY-MM
}

interface CreateReconciliationResponse {
    id: string; // or number, depending on backend API
    // Include other relevant fields returned by the API
    template_id: string;
    ky_doi_soat: string;
    loai_doi_soat: string;
    trang_thai: string;
    ngay_tao: string; // Or Date
    task_id?: string; // Add task_id
}
// --- END: Add Types for Create from Template ---

/**
 * Fetches a list of reconciliations based on the provided parameters using the configured api instance.
 */
async function getReconciliations(params: GetReconciliationsParams = {}): Promise<ReconciliationListResponse> {
    console.log(`[API Service] Fetching reconciliations with params:`, params);
    try {
        // Use api.get with params object
        const response = await api.get<ReconciliationListResponse>(`${API_RECONCILIATIONS}/`, { params });
        console.log(`[API Service] Received ${response.data.items?.length ?? 0} reconciliation items.`);
        return response.data;
    } catch (error) {
        console.error("[API Service] Error fetching reconciliations:", error);
        // Error handling might be centralized in api interceptor, but can add specific logging here
        throw error; // Re-throw for components to handle
    }
}

/**
 * Fetches the details of a specific reconciliation record by ID and type using the configured api instance.
 */
async function getReconciliationById(id: number, type: ReconciliationType): Promise<DscdReconciliationDetail | DscReconciliationDetail> {
    let apiUrlSegment = '';
    switch (type) {
        case ReconciliationType.DSCD:
            apiUrlSegment = 'dscd';
            break;
        case ReconciliationType.DSC:
            apiUrlSegment = 'dsc';
            break;
        // Add cases for other types when available
        default:
             const errorMsg = `[API Service] Unsupported reconciliation type for getById: ${type}`;
             console.error(errorMsg);
             // Throw specific error or handle as needed
             throw new Error(`Unsupported reconciliation type: ${type}`);
    }

    const apiUrl = `${API_RECONCILIATIONS}/${apiUrlSegment}/${id}`;
    console.log(`[API Service] Getting details: ${apiUrl}`);

    try {
        // Use api.get, the response type depends on the backend's schema for that endpoint
        const response = await api.get<DscdReconciliationDetail | DscReconciliationDetail>(apiUrl);
        console.log(`[API Service] Successfully fetched details for ID: ${id}, Type: ${type}`);
        return response.data;
    } catch (error) {
        console.error(`[API Service] Error fetching reconciliation details ID ${id}, Type ${type}:`, error);
        throw error;
    }
}

// --- START: Add Function to Create Reconciliation from Template ---
async function createReconciliationFromTemplate(data: CreateReconciliationRequest): Promise<CreateReconciliationResponse> {
    console.log("[API Service] Creating reconciliation from template:", data);
    try {
        const response = await api.post<CreateReconciliationResponse>(`${API_RECONCILIATIONS}/create-from-template`, data);
        console.log("[API Service] Reconciliation creation initiated successfully:", response.data);
        return response.data;
    } catch (error) {
        console.error("[API Service] Error creating reconciliation:", error);
        throw error;
    }
}

// Add polling function for reconciliation status
async function pollReconciliationStatus(id: number, type: ReconciliationType, maxAttempts = 60, interval = 5000): Promise<DscdReconciliationDetail | DscReconciliationDetail | any> {
    console.log(`[API Service] Starting to poll reconciliation status for ID: ${id}, Type: ${type}`);
    let attempts = 0;

    let apiUrlSegment = '';
     switch (type) {
         case ReconciliationType.DSCD:
             apiUrlSegment = 'dscd';
             break;
         case ReconciliationType.DSC:
             apiUrlSegment = 'dsc';
             break;
         default:
              throw new Error(`Unsupported reconciliation type for polling: ${type}`);
     }
     const pollUrl = `${API_RECONCILIATIONS}/${apiUrlSegment}/${id}`;


    // Function to check status
    const checkStatus = async () => {
        const response = await api.get(pollUrl);
        // Status might be nested differently depending on response structure
        // Adjust based on actual DscdReconciliationDetail / DscReconciliationDetail structure
        const status = response.data?.status?.toUpperCase();
        console.log(`[API Service] Polling attempt ${attempts + 1}: ID ${id}, Type ${type}, Status ${status}`);
        return { data: response.data, status };
    };

    // Initial check
    try {
        const { data: initialData, status: initialStatus } = await checkStatus();
        if (initialStatus === 'CALCULATED' || initialStatus === 'FINALIZED' || initialStatus === 'ERROR') {
            console.log(`[API Service] Reconciliation ${id}, Type ${type} already in final state: ${initialStatus}`);
            return initialData;
        }
    } catch (error) {
        console.error(`[API Service] Error checking initial status for ID ${id}, Type ${type}:`, error);
        throw error; // Stop polling if initial check fails
    }

    // Start polling loop
    return new Promise((resolve, reject) => {
        const intervalId = setInterval(async () => {
            if (attempts >= maxAttempts) {
                clearInterval(intervalId);
                console.error(`[API Service] Polling timeout exceeded for ID ${id}, Type ${type}`);
                return reject(new Error('Polling timeout exceeded'));
            }
            attempts++;
            try {
                const { data, status } = await checkStatus();
                if (status === 'CALCULATED' || status === 'FINALIZED' || status === 'ERROR') {
                    clearInterval(intervalId);
                    console.log(`[API Service] Reconciliation ${id}, Type ${type} finished with status: ${status}`);
                    resolve(data);
                }
                // Continue polling if still PROCESSING or other intermediate state
            } catch (error) {
                clearInterval(intervalId);
                console.error(`[API Service] Error during polling for ID ${id}, Type ${type}:`, error);
                reject(error);
            }
        }, interval);
    });
}

/**
 * Deletes a specific reconciliation record by ID and type.
 */
async function deleteReconciliation(id: number, type: ReconciliationType): Promise<void> {
    console.log('[deleteReconciliation] Starting with params:', { id, type });

    let apiUrlSegment = '';
    switch (type) {
        case ReconciliationType.DSCD:
            apiUrlSegment = 'dscd';
            break;
        case ReconciliationType.DSC:
             apiUrlSegment = 'dsc';
             break;
        // Add cases for other types when available
        default:
            const errorMsg = `[API Service] Unsupported reconciliation type for delete: ${type}`;
            console.error(errorMsg);
            throw new Error(`Unsupported reconciliation type for delete: ${type}`);
    }

    const apiUrl = `${API_RECONCILIATIONS}/${apiUrlSegment}/${id}`;
    console.log(`[API Service] Constructed delete URL: ${apiUrl}`);

    try {
        console.log(`[API Service] Sending DELETE request to: ${apiUrl}`);
        const response = await api.delete(apiUrl); // Use the standard api instance
        console.log(`[API Service] Delete response status: ${response.status}`);
        console.log(`[API Service] Successfully deleted ID: ${id}, Type: ${type}`);
        // No return needed for void
    } catch (error: any) {
        // Handle specific error cases
        console.error(`[API Service] Delete error for ID ${id}, Type ${type}:`, error);

        if (error.response) {
            const status = error.response.status;
            const detail = error.response.data?.detail || 'Lỗi không xác định từ server.';
            switch (status) {
                case 404:
                    throw new Error(`Bản ghi đối soát không tồn tại (ID: ${id}, Type: ${type})`);
                case 409: // Conflict, e.g., trying to delete a finalized record
                    throw new Error(`Không thể xóa bản ghi đối soát: ${detail}`);
                 // Add 403 for permission denied if applicable
                 // case 403:
                 //     throw new Error(`Không có quyền xóa bản ghi đối soát (ID: ${id}, Type: ${type})`);
                default:
                    // Rethrow other HTTP errors
                    throw new Error(`Lỗi khi xóa đối soát (Code: ${status}): ${detail}`);
            }
        }
        // Rethrow non-HTTP errors or errors without a response
        throw error;
    }
}

// --- START: Add Function to Update Adjustments ---
/**
 * Updates the adjusted values for a specific reconciliation record.
 */
async function updateReconciliationAdjustments(
    id: number,
    type: ReconciliationType,
    payload: DscdAdjustmentsPayload | DscAdjustmentsPayload
): Promise<DscdReconciliationDetail | DscReconciliationDetail> {
    let apiUrlSegment = '';
    switch (type) {
        case ReconciliationType.DSCD:
            apiUrlSegment = 'dscd';
            break;
        case ReconciliationType.DSC:
            apiUrlSegment = 'dsc';
            break;
        default:
             const errorMsg = `[API Service] Unsupported reconciliation type for update adjustments: ${type}`;
             console.error(errorMsg);
             throw new Error(`Unsupported reconciliation type for update adjustments: ${type}`);
    }

    const apiUrl = `${API_RECONCILIATIONS}/${apiUrlSegment}/${id}/adjustments`;
    console.log(`[API Service] Updating adjustments: ${apiUrl}`);

    try {
        // Use api.put for the adjustments endpoint
        const response = await api.put<DscdReconciliationDetail | DscReconciliationDetail>(apiUrl, payload);
        console.log(`[API Service] Successfully updated adjustments for ID: ${id}, Type: ${type}`);
        return response.data;
    } catch (error: any) {
        console.error(`[API Service] Error updating adjustments ID ${id}, Type ${type}:`, error);
         // Optionally, handle specific errors like 404, 400, 409 from backend
         if (error.response) {
             const status = error.response.status;
             const detail = error.response.data?.detail || 'Lỗi không xác định từ server.';
             if (status === 404) {
                 throw new Error(`Đối soát không tồn tại (ID: ${id})`);
             }
             if (status === 400 || status === 422) {
                throw new Error(`Dữ liệu hiệu chỉnh không hợp lệ: ${detail}`);
             }
             if (status === 409) { // Assuming 409 for conflict (e.g., already finalized)
                 throw new Error(`Không thể hiệu chỉnh đối soát: ${detail}`);
             }
         }
        throw error; // Re-throw generic or unhandled errors
    }
}
// --- END: Add Function to Update Adjustments ---

// --- START: Add Finalize function ---
/**
 * Finalizes a specific reconciliation record by ID and type.
 */
async function finalizeReconciliation(id: number, type: ReconciliationType): Promise<DscdReconciliationDetail | DscReconciliationDetail> {
    let apiUrlSegment = '';
    switch (type) {
        case ReconciliationType.DSCD:
            apiUrlSegment = 'dscd';
            break;
        case ReconciliationType.DSC:
            apiUrlSegment = 'dsc';
            break;
        default:
            throw new Error(`Unsupported reconciliation type for finalization: ${type}`);
    }

    const apiUrl = `${API_RECONCILIATIONS}/${apiUrlSegment}/${id}/finalize`;
    console.log(`[API Service] Finalizing reconciliation: ${apiUrl}`);

    try {
        // Use api.post for finalizing
        const response = await api.post<DscdReconciliationDetail | DscReconciliationDetail>(apiUrl);
        console.log(`[API Service] Successfully finalized ID: ${id}, Type: ${type}`);
        return response.data;
    } catch (error: any) {
        console.error(`[API Service] Error finalizing reconciliation ID ${id}, Type ${type}:`, error);
        if (error.response) {
            const status = error.response.status;
            const detail = error.response.data?.detail || 'Lỗi không xác định từ server.';
            if (status === 404) {
                throw new Error(`Đối soát không tồn tại (ID: ${id})`);
            }
            if (status === 409) {
                throw new Error(`Không thể chốt đối soát: ${detail}`);
            }
        }
        throw error;
    }
}
// --- END: Add Finalize function ---

export const reconciliationService = {
    getReconciliations,
    getReconciliationById,
    createReconciliationFromTemplate,
    pollReconciliationStatus,
    deleteReconciliation,
    updateReconciliationAdjustments,
    finalizeReconciliation
}; 