import { DoiSoatTemplate, TemplateType } from '@/models/template';
import { api } from '@/lib/api';

export interface PaginatedTemplates {
  items: DoiSoatTemplate[];
  total: number;
  page: number;
  size: number;
}

// Helper để lấy token và tạo header cho request
const getAuthHeaders = () => {
  const token = localStorage.getItem('access_token');
  if (!token) {
    console.warn('[templateService] No token available!');
    return {};
  }
  return {
    Authorization: `Bearer ${token}`
  };
};

// Bỏ /api đi vì nó đã có trong baseURL
const API_TEMPLATES = '/v1/templates';

// Export the interface
export interface GetTemplatesParams {
    skip?: number;
    limit?: number;
    is_active?: boolean;
    template_type?: string;
    partner_id?: number;
}

// Export the interface
export interface GetTemplatesResponse {
    items: DoiSoatTemplate[];
    total: number;
    page: number;
    size: number;
}

// Export the interface
export interface DetectTemplateResponse {
    detected_type: string | null;
    confidence: number;
    filename: string;
}

export const templateService = {
  getTemplates: async (params?: GetTemplatesParams): Promise<GetTemplatesResponse> => {
    const response = await api.get<GetTemplatesResponse>(API_TEMPLATES, { params });
    return response.data;
  },

  getTemplate: async (id: number): Promise<DoiSoatTemplate> => {
    const response = await api.get<DoiSoatTemplate>(`${API_TEMPLATES}/${id}`);
    return response.data;
  },

  uploadTemplate: async (formData: FormData): Promise<DoiSoatTemplate> => {
    const response = await api.post<DoiSoatTemplate>(`${API_TEMPLATES}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  detectTemplateType: async (formData: FormData): Promise<DetectTemplateResponse> => {
    console.log("[Debug] detectTemplateType called with formData:", formData.get("file"));
    try {
      const response = await api.post<DetectTemplateResponse>(`${API_TEMPLATES}/detect`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      console.log("[Debug] detectTemplateType response:", response);
      return response.data;
    } catch (error) {
      console.error("[Debug] detectTemplateType error:", error);
      throw error;
    }
  },

  updateTemplate: async (id: number, data: Partial<DoiSoatTemplate>): Promise<DoiSoatTemplate> => {
    const response = await api.put<DoiSoatTemplate>(`${API_TEMPLATES}/${id}`, data);
    return response.data;
  },

  deleteTemplate: async (id: number): Promise<void> => {
    await api.delete(`${API_TEMPLATES}/${id}`);
  },

  downloadTemplate: async (id: number): Promise<Blob> => {
    const response = await api.get(`${API_TEMPLATES}/${id}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Function to trigger template processing
  processTemplate: async (id: number): Promise<{ message: string }> => {
    const response = await api.post<{ message: string }>(`${API_TEMPLATES}/${id}/process`);
    return response.data;
  },

  // Function to get the processing status of a template
  getTemplateStatus: async (id: number): Promise<{ status: string; message?: string }> => {
    const response = await api.get<{ status: string; message?: string }>(`${API_TEMPLATES}/${id}/status`);
    return response.data;
  },

  // Function to get the structured data from a processed template
  getTemplateStructuredData: async (id: number): Promise<any> => {
    const response = await api.get<any>(`${API_TEMPLATES}/${id}/structured-data`);
    // TODO: Define a proper type for the structured data response
    return response.data;
  },

  // Function to get a preview of the template data
  getTemplatePreview: async (id: number): Promise<any[]> => {
    const response = await api.get<any[]>(`${API_TEMPLATES}/${id}/preview`);
    // TODO: Define a proper type for the preview data response
    return response.data;
  },
}; 