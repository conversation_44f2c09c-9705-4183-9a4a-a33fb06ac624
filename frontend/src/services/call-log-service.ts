import { api } from '@/lib/api';
import { FileType, FileStatus, CallLogFile, PaginatedCallLogFiles, CallLog, PaginatedCallLogs, CallType } from '@/types/call-log';

export const callLogService = {
  /**
   * Upload a call log file
   */
  async uploadFile(file: File, fileType: FileType): Promise<CallLogFile> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('file_type', fileType);

    const response = await api.post<CallLogFile>('/v1/call-logs/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  /**
   * Get a list of call log files
   */
  async getFiles(params: {
    skip?: number;
    limit?: number;
    file_type?: FileType;
    status?: FileStatus;
  }): Promise<PaginatedCallLogFiles> {
    const response = await api.get<PaginatedCallLogFiles>('/v1/call-logs/files', {
      params,
    });

    return response.data;
  },

  /**
   * Get a call log file by ID
   */
  async getFile(id: number): Promise<CallLogFile> {
    const response = await api.get<CallLogFile>(`/v1/call-logs/files/${id}`);
    return response.data;
  },

  /**
   * Delete a call log file
   */
  async deleteFile(id: number): Promise<void> {
    await api.delete(`/v1/call-logs/files/${id}`);
  },

  /**
   * Upload a file with progress tracking
   */
  uploadFileWithProgress(
    file: File,
    fileType: FileType,
    onProgress: (progress: number) => void,
    onSuccess: (data: CallLogFile) => void,
    onError: (error: any) => void
  ): () => void {
    const xhr = new XMLHttpRequest();
    const formData = new FormData();
    
    formData.append('file', file);
    formData.append('file_type', fileType);
    
    // Track upload progress
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const progress = Math.round((event.loaded / event.total) * 100);
        onProgress(progress);
      }
    });
    
    // Handle response
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const data = JSON.parse(xhr.responseText);
          onSuccess(data);
        } catch (error) {
          onError(new Error('Invalid response format'));
        }
      } else {
        try {
          const errorData = JSON.parse(xhr.responseText);
          onError(errorData);
        } catch (error) {
          onError(new Error(`Upload failed: ${xhr.statusText}`));
        }
      }
    });
    
    // Handle network errors
    xhr.addEventListener('error', () => {
      onError(new Error('Network error occurred'));
    });
    
    // Open and send request
    xhr.open('POST', `${api.defaults.baseURL}/v1/call-logs/upload`);
    
    // Add authorization header if available
    const token = localStorage.getItem('access_token');
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
    }
    
    xhr.send(formData);
    
    // Return abort function
    return () => xhr.abort();
  },

  /**
   * Get call logs with filtering
   */
  async getLogs(params: {
    skip?: number;
    limit?: number;
    file_id?: number;
    call_type?: CallType;
    caller?: string;
    callee?: string;
    call_date_from?: string;
    call_date_to?: string;
    caller_type?: string;
    callee_type?: string;
    sort_field?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<PaginatedCallLogs> {
    const response = await api.get<PaginatedCallLogs>('/v1/call-logs/logs', {
      params,
    });

    return response.data;
  }
}; 