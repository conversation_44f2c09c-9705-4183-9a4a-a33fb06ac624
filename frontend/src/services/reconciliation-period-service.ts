import { api } from '@/lib/api';

export interface ReconciliationPeriod {
  id: number;
  month: number;
  year: number;
  name: string;
  status: string;
  notes?: string;
  created_by: number;
  created_at: string;
  updated_at: string;
}

export interface PaginatedReconciliationPeriods {
  success: boolean;
  message: string;
  total: number;
  data: ReconciliationPeriod[];
}

export interface ReconciliationPeriodResponse {
  success: boolean;
  message: string;
  data?: ReconciliationPeriod;
}

export interface ReconciliationPeriodCreate {
  month: number;
  year: number;
  name: string;
  notes?: string;
}

export interface ReconciliationPeriodUpdate {
  name?: string;
  status?: string;
  notes?: string;
}

class ReconciliationPeriodService {
  private baseUrl = '/v1/reconciliation-periods';

  async getReconciliationPeriods(
    page = 1,
    size = 10,
    year?: number,
    month?: number,
    status?: string
  ): Promise<PaginatedReconciliationPeriods> {
    try {
      const response = await api.get<PaginatedReconciliationPeriods>(this.baseUrl, {
        params: {
          skip: (page - 1) * size,
          limit: size,
          year,
          month,
          status
        }
      });
      return response.data;
    } catch (error) {
      console.error('[reconciliationPeriodService] Get periods failed:', error);
      throw error;
    }
  }

  async getReconciliationPeriod(id: number): Promise<ReconciliationPeriodResponse> {
    try {
      const response = await api.get<ReconciliationPeriodResponse>(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`[reconciliationPeriodService] Get period ${id} failed:`, error);
      throw error;
    }
  }

  async createReconciliationPeriod(
    data: ReconciliationPeriodCreate
  ): Promise<ReconciliationPeriodResponse> {
    try {
      const response = await api.post<ReconciliationPeriodResponse>(this.baseUrl, data);
      return response.data;
    } catch (error) {
      console.error('[reconciliationPeriodService] Create period failed:', error);
      throw error;
    }
  }

  async updateReconciliationPeriod(
    id: number,
    data: ReconciliationPeriodUpdate
  ): Promise<ReconciliationPeriodResponse> {
    try {
      const response = await api.put<ReconciliationPeriodResponse>(`${this.baseUrl}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`[reconciliationPeriodService] Update period ${id} failed:`, error);
      throw error;
    }
  }

  async deleteReconciliationPeriod(id: number): Promise<void> {
    try {
      await api.delete(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`[reconciliationPeriodService] Delete period ${id} failed:`, error);
      throw error;
    }
  }
}

export const reconciliationPeriodService = new ReconciliationPeriodService(); 