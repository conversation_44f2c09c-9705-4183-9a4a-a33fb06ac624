import { Partner, PartnerFormData } from "@/types/partner";
import { api } from "@/lib/api";
import { PaginatedResponse } from "@/types/pricing";

// Đ<PERSON>nh nghĩa kiểu cho params của getPartners
interface GetPartnersParams {
  skip?: number;
  limit?: number;
  // Thêm các tham số filter khác nếu cần
}

export const partnerService = {
  // Get all partners with pagination
  getPartners: async (params?: GetPartnersParams): Promise<PaginatedResponse<Partner>> => {
    const response = await api.get<PaginatedResponse<Partner>>("/v1/partners", {
      params: params, // Truyền params vào query string
    });
    // Đảm bảo trả về đúng cấu trúc PaginatedResponse
    return response.data && Array.isArray(response.data.items) && typeof response.data.total === 'number'
      ? response.data
      : { items: [], total: 0 };
  },

  // Create a new partner
  createPartner: async (data: PartnerFormData): Promise<Partner> => {
    const response = await api.post("/v1/partners", data);
    return response.data;
  },

  // Update an existing partner
  updatePartner: async (id: number, data: PartnerFormData): Promise<Partner> => {
    const response = await api.put(`/v1/partners/${id}`, data);
    return response.data;
  },

  // Delete a partner
  deletePartner: async (id: number): Promise<void> => {
    await api.delete(`/v1/partners/${id}`);
  },

  // Toggle partner active status
  togglePartnerStatus: async (id: number): Promise<Partner> => {
    const response = await api.patch(`/v1/partners/${id}/toggle-status`);
    return response.data;
  },
};