import { api } from '@/lib/api';

export interface DoiSoatCodinh {
  id: number;
  period_id?: number;
  template_id?: number;
  is_template_data: boolean;
  dau_so: string;
  dich_vu: string;
  so_luong: number;
  thanh_tien: number;
  created_at: string;
  updated_at: string;
}

export interface DoiSoatCuoc {
  id: number;
  period_id?: number;
  template_id?: number;
  is_template_data: boolean;
  mang: string;
  loai_cuoc: string;
  so_luong: number;
  thanh_tien: number;
  created_at: string;
  updated_at: string;
}

export interface DoiSoat1800_1900 {
  id: number;
  period_id?: number;
  template_id?: number;
  is_template_data: boolean;
  dau_so: string;
  loai: string;
  so_luong: number;
  thanh_tien: number;
  created_at: string;
  updated_at: string;
}

export interface DoiSoatCoDinhResponse {
  total: number;
  items: DoiSoatCodinh[];
}

export interface DoiSoatCuocResponse {
  total: number;
  items: DoiSoatCuoc[];
}

export interface DoiSoat1800_1900Response {
  total: number;
  items: DoiSoat1800_1900[];
}

const API_DOI_SOAT = '/api/v1/doi-soat';

export const doiSoatService = {
  // Đối soát cố định
  getDoiSoatCoDinh: async (
    periodId?: number,
    skip: number = 0,
    limit: number = 100
  ): Promise<DoiSoatCoDinhResponse> => {
    const params = {
      skip,
      limit,
      period_id: periodId
    };
    
    const response = await api.get(`${API_DOI_SOAT}/co-dinh`, { params });
    return response.data;
  },
  
  // Đối soát cước
  getDoiSoatCuoc: async (
    periodId?: number,
    skip: number = 0,
    limit: number = 100
  ): Promise<DoiSoatCuocResponse> => {
    const params = {
      skip,
      limit,
      period_id: periodId
    };
    
    const response = await api.get(`${API_DOI_SOAT}/cuoc`, { params });
    return response.data;
  },
  
  // Đối soát 1800/1900
  getDoiSoat1800_1900: async (
    periodId?: number,
    skip: number = 0,
    limit: number = 100
  ): Promise<DoiSoat1800_1900Response> => {
    const params = {
      skip,
      limit,
      period_id: periodId
    };
    
    const response = await api.get(`${API_DOI_SOAT}/1800-1900`, { params });
    return response.data;
  },
  
  // Upload file đối soát
  uploadDoiSoatFile: async (
    file: File,
    manualType?: string,
    partnerId?: number
  ): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);
    
    if (manualType) {
      formData.append('manual_type', manualType);
    }
    
    if (partnerId) {
      formData.append('partner_id', partnerId.toString());
    }
    
    const response = await api.post(`${API_DOI_SOAT}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  },
  
  // Xử lý file đối soát đã upload
  processDoiSoatFile: async (
    fileId: string,
    doiSoatType: string
  ): Promise<any> => {
    const response = await api.post(`${API_DOI_SOAT}/process`, {
      file_id: fileId,
      doi_soat_type: doiSoatType
    });
    
    return response.data;
  }
}; 