import { api } from "@/lib/api";

export interface StatisticsResponse {
  totalMinutes: number;
  description?: string;
  metadata?: {
    period?: string;
    partner_id?: number;
    partner_name?: string;
    start_date?: string;
    end_date?: string;
    error?: string;
    service_group?: string;
    service_display?: string;
    call_type?: string;
    display_name?: string;
  };
}

// Hàm ánh xạ từ tên thống kê frontend sang endpoint backend
const mapStatTypeToEndpoint = (statType: string): string => {
  // Ánh xạ từ phía frontend sang endpoint thực tế của backend
  const mapping: Record<string, string> = {
    // Hướng từ HTC đến Viettel
    'CD_TO_CD': 'htc_to_viettel_cd_to_cd',
    'CD_TO_DD': 'htc_to_viettel_cd_to_dd',
    'CD_PORT_OUT': 'htc_to_viettel_cd_port_out',
    'HTC_INTL_OUT': 'htc_to_viettel_cd_intl',
    
    // Hướng từ Viettel đến HTC
    'CD_TO_CD_REV': 'viettel_to_htc_cd_to_cd',
    'DD_TO_CD_REV': 'viettel_to_htc_dd_to_cd',
    'VIETTEL_INTL_OUT': 'viettel_to_htc_cd_intl',
    
    // Dịch vụ 19004 (theo nhóm dịch vụ)
    'DV_19004_G1': 'viettel_to_htc_19004/group1',             // 190046(00-25; 90-99)
    'DV_19004_G2': 'viettel_to_htc_19004/group2',             // 190045(00-39); 19004567
    'DV_19004_G3': 'viettel_to_htc_19004/group3',             // 190047(40-99);190046(26-49)
    'DV_19004_G4': 'viettel_to_htc_19004/19004001xx',         // 19004001xx
    'DV_19004_G5': 'viettel_to_htc_19004/group5',             // 190047(00-39)
    
    // Dịch vụ 18004 (theo loại cuộc gọi)
    'DV_18004_FIXED': 'viettel_to_htc_18004/fixed',           // 18004xxx - Cố định
    'DV_18004_MOBILE': 'viettel_to_htc_18004/mobile',         // 18004xxx - Di động
  };
  
  return mapping[statType] || statType;
};

export const callStatisticsApi = {
  /**
   * Get call statistics based on specified parameters
   * @param statType The type of statistic to retrieve (e.g., 'CD_TO_CD_REV')
   * @param period The period in format 'YYYY-MM' (year-month)
   * @param partnerIdOrName ID hoặc tên của đối tác
   * @param isName Flag để xác định tham số truyền vào là tên (true) hay ID (false)
   * @returns Promise with statistics data
   */
  getStatistics: async (
    statType: string,
    period?: string,
    partnerIdOrName?: number | string,
    isName: boolean = false
  ): Promise<StatisticsResponse> => {
    console.log(`[CallStatistics] Fetching statistics for ${statType} with params:`, { period, partnerIdOrName, isName });
    
    try {
      // Ánh xạ tên thống kê sang endpoint backend
      const endpointName = mapStatTypeToEndpoint(statType);
      console.log(`[CallStatistics] Mapped ${statType} to endpoint ${endpointName}`);
      
      // Build query parameters (excluding stat_type which is a path parameter)
      const params: Record<string, string | number> = {};
      
      if (period) {
        params.period = period;
      }
      
      if (partnerIdOrName !== undefined) {
        if (isName) {
          params.partner_name = partnerIdOrName as string;
        } else {
          params.partner_id = partnerIdOrName as number;
        }
      }
      
      // Sử dụng api instance và proxy được cấu hình trong Vite
      const response = await api.get<StatisticsResponse>(`/api/v1/statistics/${endpointName}`, {
        params,
      });
      
      console.log(`[CallStatistics] API response for ${statType}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`[CallStatistics] Error fetching statistics for ${statType}:`, error);
      
      // Return a default response on error
      return {
        totalMinutes: 0,
        description: 'Error fetching data',
        metadata: {
          error: error instanceof Error ? error.message : String(error)
        }
      };
    }
  }
};

export interface StatisticsRequestParams {
  period?: string;
  partner_id?: number;
  partner_name?: string;
}

/**
 * Fetch call statistics from the API
 * @param statType Type of statistic to fetch
 * @param params Request parameters
 */
export const fetchStatistics = async (
  statType: string, 
  params: StatisticsRequestParams
): Promise<StatisticsResponse> => {
  console.log(`[CallStatistics] Fetching statistics for ${statType} with params:`, params);
  
  try {
    // Ánh xạ tên thống kê sang endpoint backend
    const endpointName = mapStatTypeToEndpoint(statType);
    
    const response = await api.get<StatisticsResponse>(
      `/api/v1/statistics/${endpointName}`,
      { params }
    );
    
    console.log(`[CallStatistics] API response for ${statType}:`, response.data);
    return response.data;
  } catch (error) {
    console.error(`[CallStatistics] Error fetching statistics for ${statType}:`, error);
    // Return default value on error
    return {
      totalMinutes: 0,
      description: 'Error fetching data',
      metadata: { 
        error: error instanceof Error ? error.message : String(error) 
      }
    };
  }
};

/**
 * Fetch call statistics from the API with formatted parameters
 * @param statType Type of statistic to fetch
 * @param period Period for statistics (YYYY-MM)
 * @param partnerIdOrName Partner ID or name
 * @param isName If true, partnerIdOrName is a name, otherwise an ID
 */
export const getCallStatistics = async (
  statType: string,
  period?: string,
  partnerIdOrName?: number | string,
  isName: boolean = false
): Promise<StatisticsResponse> => {
  console.log(`[CallStatistics] Fetching statistics for ${statType} with params:`, { period, partnerIdOrName, isName });
  
  try {
    // Ánh xạ tên thống kê sang endpoint backend
    const endpointName = mapStatTypeToEndpoint(statType);
    
    // Build query parameters (excluding stat_type which is a path parameter)
    const params: Record<string, string | number> = {};
    
    if (period) {
      params.period = period;
    }
    
    if (partnerIdOrName !== undefined) {
      if (isName) {
        params.partner_name = partnerIdOrName as string;
      } else {
        params.partner_id = partnerIdOrName as number;
      }
    }
    
    // Use API client with proxy
    const response = await api.get<StatisticsResponse>(`/api/v1/statistics/${endpointName}`, {
      params
    });
    
    console.log(`[CallStatistics] API response for ${statType}:`, response.data);
    return response.data;
  } catch (error) {
    console.error(`[CallStatistics] Error fetching statistics for ${statType}:`, error);
    // Return a default response on error
    return {
      totalMinutes: 0,
      description: 'Error fetching data',
      metadata: {
        error: error instanceof Error ? error.message : String(error)
      }
    };
  }
}; 