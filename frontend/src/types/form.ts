import { FieldValues, UseFormReturn } from 'react-hook-form';

export interface FormFieldProps<T extends FieldValues = FieldValues> {
  form: UseFormReturn<T>;
  name: keyof T;
  label: string;
  placeholder?: string;
  type?: string;
  autoComplete?: string;
  disabled?: boolean;
}

export interface FormProps<T extends FieldValues = FieldValues> {
  form: UseFormReturn<T>;
  onSubmit: (data: T) => Promise<void>;
  children: React.ReactNode;
} 