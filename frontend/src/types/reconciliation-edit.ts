import * as z from 'zod';
// Remove schema import from here, schemas should live with validation logic
// import { dscdEditFormSchema, dscEditFormSchema } from './reconciliation'; 

// Remove Zod schema definitions from this file
/*
export const dscDauSoDichVuEditSchema = z.object({ ... });
export type DscDauSoDichVuEdit = z.infer<typeof dscDauSoDichVuEditSchema>;
export const dscEditFormSchema = z.object({ ... });
export type DscEditFormData = z.infer<typeof dscEditFormSchema>; 
*/

// --- Keep the manually defined Form Data Types ---

// Define the shape of the data within the 'du_lieu' array for DSC form
export type DscDauSoDichVuFormData = {
  id: number; // Keep the original ID
  vnm_san_luong_adjusted: number | null;
  vnm_thanh_tien_adjusted: number | null;
  viettel_san_luong_adjusted: number | null;
  viettel_thanh_tien_adjusted: number | null;
  vnpt_san_luong_adjusted: number | null;
  vnpt_thanh_tien_adjusted: number | null;
  vms_san_luong_adjusted: number | null;
  vms_thanh_tien_adjusted: number | null;
  khac_san_luong_adjusted: number | null;
  khac_thanh_tien_adjusted: number | null;
  // tong_thanh_toan_adjusted is not directly edited, it's calculated
};

// Define the main form data structure for DSC
export type DscEditFormData = {
  du_lieu: DscDauSoDichVuFormData[];
  // Add other top-level editable fields for DSC if any (e.g., ghi_chu_hieu_chinh)
  // ghi_chu_hieu_chinh?: string | null; 
};

// Define the shape for nested CuocGoi in DSCD form
type DscdCuocGoiFormData = {
    thoi_gian_goi_adjusted: number | null;
    cuoc_adjusted: number | null;
};

// Define the shape for nested CuocThueBao in DSCD form
type DscdCuocThueBaoFormData = {
    thue_bao_thang_adjusted: number | null;
    cam_ket_thang_adjusted: number | null;
    tra_truoc_thang_adjusted: number | null;
};

// Define the shape of the data within the 'du_lieu' array for DSCD form
export type DscdDauSoDichVuFormData = {
    id: number; // Keep the original ID
    cuoc_thu_khach_adjusted: number | null;
    cuoc_tra_htc_adjusted: number | null;
    co_dinh_noi_hat?: DscdCuocGoiFormData | null;
    co_dinh_lien_tinh?: DscdCuocGoiFormData | null;
    di_dong?: DscdCuocGoiFormData | null;
    cuoc_1900?: DscdCuocGoiFormData | null;
    quoc_te?: DscdCuocGoiFormData | null;
    cuoc_thue_bao?: DscdCuocThueBaoFormData | null;
};

// Define the shape for TongKet in DSCD form
type DscdTongKetFormData = {
    cong_tien_dich_vu_adjusted: number | null;
    tien_thue_gtgt_adjusted: number | null;
    // tong_cong_tien_adjusted is not directly edited
};

// Define the main form data structure for DSCD
export type DscdEditFormData = {
    tong_ket?: DscdTongKetFormData | null; // TongKet might be optional or handled differently
    du_lieu: DscdDauSoDichVuFormData[];
    // Add other top-level editable fields for DSCD if any
};

// Union type for easier handling in the main page component
export type EditFormDataUnion = DscdEditFormData | DscEditFormData;

// --- Zod Schemas should be imported from elsewhere when needed for validation --- 