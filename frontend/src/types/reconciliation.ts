// frontend/src/types/reconciliation.ts

import * as z from 'zod'; // Import Zod

// Based on backend/src/models/enums.py
export enum ReconciliationType {
    DSCD = "DSCD",
    DSC = "DSC",
    TELCO_1800_1900 = "TELCO_1800_1900"
}

export enum ReconciliationStatus {
    TEMPLATE = "template",     // Trạng thái dữ liệu gốc từ template
    PROCESSING = "processing", // Đang xử lý
    CALCULATED = "calculated", // Đã tính toán từ call logs
    ADJUSTED = "adjusted",     // Đã hiệu chỉnh
    FINALIZED = "finalized",   // Đã chốt
    ERROR = "error"           // X<PERSON> lý thất bại
}

// Minimal partner info for lists
export interface MinimalPartnerInfo {
  id: number;
  name: string;
}

// Item structure for the unified list - matches backend schema ReconciliationListItem
export interface ReconciliationListItem {
  id: number;
  reconciliation_type: ReconciliationType;
  ky_doi_soat: string;
  partner: MinimalPartnerInfo | null;
  status: ReconciliationStatus;
  created_at: string; // ISO 8601 date string
  updated_at: string; // ISO 8601 date string
}

// API response structure for the unified list - matches backend ReconciliationListResponse
export interface ReconciliationListResponse {
  items: ReconciliationListItem[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// Define parameters for the list API call
export interface GetReconciliationsParams {
  reconciliation_type?: ReconciliationType;
  ky_doi_soat?: string; 
  partner_id?: number;
  status?: ReconciliationStatus;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  skip?: number;
  limit?: number;
}

// --- START: Detail View Types for DSCD ---

// Matches backend schemas/dscd.py CuocGoiDetail
export interface CuocGoiDetail {
  id: number;
  thoi_gian_goi: number;
  cuoc: number;
}

// Matches backend schemas/dscd.py CuocThueBaoDetail
export interface CuocThueBaoDetail {
    id: number;
    thue_bao_thang: number;
    cam_ket_thang: number;
    tra_truoc_thang: number;
}

// Matches backend schemas/dscd.py DauSoDichVuDetail
export interface DauSoDichVuDetail {
    id: number;
    dau_so_type: string;
    standardized_display: string;
    number_count?: number | null;
    start_num_str?: string | null;
    end_num_str?: string | null;
    prefix?: string | null;
    raw_dau_so?: string | null;
    cuoc_thu_khach: number;
    cuoc_tra_htc: number;
    // Nested details
    co_dinh_noi_hat?: CuocGoiDetail | null;
    co_dinh_lien_tinh?: CuocGoiDetail | null;
    di_dong?: CuocGoiDetail | null;
    cuoc_1900?: CuocGoiDetail | null;
    quoc_te?: CuocGoiDetail | null;
    cuoc_thue_bao?: CuocThueBaoDetail | null;
}

// Matches backend schemas/dscd.py TongKetDetail
export interface TongKetDetail {
    id: number;
    cong_tien_dich_vu: number;
    tien_thue_gtgt: number;
    tong_cong_tien: number;
}

// Matches backend ReconciliationTemplateBase (or similar minimal template info)
export interface MinimalTemplateInfo {
    id: number;
    name: string;
    file_name?: string | null;
}

// Base interface for common reconciliation details
interface BaseReconciliationDetail {
    id: number;
    ky_doi_soat: string;
    is_template_data: boolean; // Should likely be false for detail view
    status: ReconciliationStatus;
    created_at: string;
    updated_at: string;
    partner: MinimalPartnerInfo | null;
    template: MinimalTemplateInfo | null;
    task_id?: string | null; // Celery Task ID
    error_message?: string | null; // Error message from processing
}

// Specific detail type for DSCD - Matches backend schemas/dscd.py DoiSoatCoDinhDetailResponse
export interface DscdReconciliationDetail extends BaseReconciliationDetail {
    reconciliation_type: ReconciliationType.DSCD;
    tong_ket: TongKetDetail | null;
    du_lieu: DauSoDichVuDetail[]; // List of detailed service data for DSCD
}

// --- END: Detail View Types for DSCD ---

// --- START: Detail View Types for DSC ---

// Matches backend schemas/dsc.py DauSoDichVuCuoc
export interface DauSoDichVuCuocDetail {
    id: number;
    stt: number;
    dau_so: string;
    // VNM
    vnm_san_luong: number;
    vnm_ty_le_cp: number;
    vnm_thanh_tien: number;
    vnm_san_luong_adjusted?: number | null;
    vnm_thanh_tien_adjusted?: number | null;
    // VIETTEL
    viettel_san_luong: number;
    viettel_ty_le_cp: number;
    viettel_thanh_tien: number;
    viettel_san_luong_adjusted?: number | null;
    viettel_thanh_tien_adjusted?: number | null;
    // VNPT
    vnpt_san_luong: number;
    vnpt_ty_le_cp: number;
    vnpt_thanh_tien: number;
    vnpt_san_luong_adjusted?: number | null;
    vnpt_thanh_tien_adjusted?: number | null;
    // VMS
    vms_san_luong: number;
    vms_ty_le_cp: number;
    vms_thanh_tien: number;
    vms_san_luong_adjusted?: number | null;
    vms_thanh_tien_adjusted?: number | null;
    // Khac
    khac_san_luong: number;
    khac_ty_le_cp: number;
    khac_thanh_tien: number;
    khac_san_luong_adjusted?: number | null;
    khac_thanh_tien_adjusted?: number | null;
    // Tong
    tong_thanh_toan: number;
    tong_thanh_toan_adjusted?: number | null; // Add adjusted for total as well if needed
}

// Matches backend schemas/dsc.py TongKetCuoc
export interface TongKetCuocDetail {
    id: number;
    cong_tien_dich_vu: number;
    tien_thue_gtgt: number;
    tong_cong_tien: number;
}

// Specific detail type for DSC - Matches backend schemas/dsc.py DoiSoatCuocDetailResponse
export interface DscReconciliationDetail extends BaseReconciliationDetail {
    reconciliation_type: ReconciliationType.DSC;
    tu_mang?: string | null;
    den_doi_tac?: string | null;
    hop_dong_so?: string | null;
    file_name?: string | null;
    // DSC specific details
    tong_ket: TongKetCuocDetail | null;
    du_lieu: DauSoDichVuCuocDetail[];
}

// Union type for general detail usage
export type ReconciliationDetail = DscdReconciliationDetail | DscReconciliationDetail;

// --- END: Detail View Types for DSC ---


// --- START: Adjustment Payload Types (Renamed fields) ---

// DSCD Adjustment Payloads
export interface CuocGoiAdjustmentsPayload {
  thoi_gian_goi?: number | null;
  cuoc?: number | null;
}

export interface CuocThueBaoAdjustmentsPayload {
    thue_bao_thang?: number | null;
    cam_ket_thang?: number | null;
    tra_truoc_thang?: number | null;
}

export interface DauSoDichVuAdjustmentsPayload {
    id: number;
    cuoc_thu_khach?: number | null;
    cuoc_tra_htc?: number | null;
    // Nested update payloads
    co_dinh_noi_hat?: CuocGoiAdjustmentsPayload | null;
    co_dinh_lien_tinh?: CuocGoiAdjustmentsPayload | null;
    di_dong?: CuocGoiAdjustmentsPayload | null;
    cuoc_1900?: CuocGoiAdjustmentsPayload | null;
    quoc_te?: CuocGoiAdjustmentsPayload | null;
    cuoc_thue_bao?: CuocThueBaoAdjustmentsPayload | null;
}

export interface TongKetAdjustmentsPayload {
    cong_tien_dich_vu?: number | null;
    tien_thue_gtgt?: number | null;
}

export interface DscdAdjustmentsPayload {
    tong_ket?: TongKetAdjustmentsPayload | null;
    du_lieu?: DauSoDichVuAdjustmentsPayload[];
    ghi_chu_hieu_chinh?: string | null;
}

// DSC Adjustment Payloads
export interface DauSoDichVuCuocAdjustmentsPayload {
    id: number;
    vnm_san_luong?: number | null;
    vnm_thanh_tien?: number | null;
    viettel_san_luong?: number | null;
    viettel_thanh_tien?: number | null;
    vnpt_san_luong?: number | null;
    vnpt_thanh_tien?: number | null;
    vms_san_luong?: number | null;
    vms_thanh_tien?: number | null;
    khac_san_luong?: number | null;
    khac_thanh_tien?: number | null;
}

export interface DscAdjustmentsPayload {
    du_lieu?: DauSoDichVuCuocAdjustmentsPayload[];
    ghi_chu_hieu_chinh?: string | null;
}

// --- END: Adjustment Payload Types ---


// --- START: Edit Form Schemas & Types (DSCD) ---

// Schema for individual nested CuocGoi adjustment in the form
const cuocGoiEditSchema = z.object({
  thoi_gian_goi_adjusted: z.number().nullable().optional(),
  cuoc_adjusted: z.number().nullable().optional(),
});

// Schema for individual nested CuocThueBao adjustment in the form
const cuocThueBaoEditSchema = z.object({
  thue_bao_thang_adjusted: z.number().nullable().optional(),
  cam_ket_thang_adjusted: z.number().nullable().optional(),
  tra_truoc_thang_adjusted: z.number().nullable().optional(),
});

// Schema for a single DauSoDichVu row in the DSCD edit form
const dauSoDichVuEditSchema = z.object({
  id: z.number(), // Keep ID to link back
  cuoc_thu_khach_adjusted: z.number().nullable().optional(),
  cuoc_tra_htc_adjusted: z.number().nullable().optional(),
  // Include nested schemas
  co_dinh_noi_hat: cuocGoiEditSchema.optional(),
  co_dinh_lien_tinh: cuocGoiEditSchema.optional(),
  di_dong: cuocGoiEditSchema.optional(),
  cuoc_1900: cuocGoiEditSchema.optional(),
  quoc_te: cuocGoiEditSchema.optional(),
  cuoc_thue_bao: cuocThueBaoEditSchema.optional(),
});

// Schema for the TongKet section of the DSCD edit form
const tongKetEditSchema = z.object({
  cong_tien_dich_vu_adjusted: z.number().nullable().optional(),
  tien_thue_gtgt_adjusted: z.number().nullable().optional(),
  // tong_cong_tien_adjusted is likely calculated, so maybe omit from direct form input
});

// Main schema for the entire DSCD Edit Form data structure
export const dscdEditFormSchema = z.object({
  tong_ket: tongKetEditSchema.optional(),
  du_lieu: z.array(dauSoDichVuEditSchema).optional(),
});

// Infer the TypeScript type from the Zod schema for DSCD form data
export type DscdEditFormData = z.infer<typeof dscdEditFormSchema>;

// --- END: Edit Form Schemas & Types (DSCD) ---


// --- START: Edit Form Schemas & Types (DSC) ---

// Schema for a single DauSoDichVuCuoc row in the DSC edit form
const dauSoDichVuCuocEditSchema = z.object({
    id: z.number(), // Required to identify the row
    vnm_san_luong_adjusted: z.number().nullable().optional(),
    vnm_thanh_tien_adjusted: z.number().nullable().optional(),
    viettel_san_luong_adjusted: z.number().nullable().optional(),
    viettel_thanh_tien_adjusted: z.number().nullable().optional(),
    vnpt_san_luong_adjusted: z.number().nullable().optional(),
    vnpt_thanh_tien_adjusted: z.number().nullable().optional(),
    vms_san_luong_adjusted: z.number().nullable().optional(),
    vms_thanh_tien_adjusted: z.number().nullable().optional(),
    khac_san_luong_adjusted: z.number().nullable().optional(),
    khac_thanh_tien_adjusted: z.number().nullable().optional(),
    // tong_thanh_toan_adjusted is likely calculated backend/frontend
});

// Schema for the TongKet section of the DSC edit form
const tongKetCuocEditSchema = z.object({
    cong_tien_dich_vu_adjusted: z.number().nullable().optional(),
    tien_thue_gtgt_adjusted: z.number().nullable().optional(),
    tong_cong_tien_adjusted: z.number().nullable().optional(), // Let's include based on backend payload
});

// Main schema for the entire DSC Edit Form data structure
export const dscEditFormSchema = z.object({
    tong_ket: tongKetCuocEditSchema.optional(),
    du_lieu: z.array(dauSoDichVuCuocEditSchema).optional(),
});

// Infer the TypeScript type from the Zod schema for DSC form data
export type DscEditFormData = z.infer<typeof dscEditFormSchema>;

// --- END: Edit Form Schemas & Types (DSC) ---


// Type guard functions
export function isDscdDetail(detail: ReconciliationDetail | null): detail is DscdReconciliationDetail {
    console.log("[Debug TypeGuard] Checking isDscdDetail for:", detail?.reconciliation_type, "ID:", detail?.id);
    if (!detail) return false;
    try {
        // DSCD has nested CuocGoiDetail/CuocThueBaoDetail which DSC does not.
        // Check for a property unique to the DauSoDichVuDetail within DSCD's du_lieu.
        const result = detail.reconciliation_type === ReconciliationType.DSCD &&
                       (detail.du_lieu && detail.du_lieu.length > 0 && 'standardized_display' in detail.du_lieu[0]);
        console.log("[Debug TypeGuard] isDscdDetail result:", result);
        return result;
    } catch (e) {
        console.error("[Debug TypeGuard] Error in isDscdDetail:", e);
        return false;
    }
}

export function isDscDetail(detail: ReconciliationDetail | null): detail is DscReconciliationDetail {
    console.log("[Debug TypeGuard] Checking isDscDetail for:", detail?.reconciliation_type, "ID:", detail?.id);
    if (!detail) return false;
    try {
        // DSC has fields like vnm_san_luong directly in du_lieu items.
        // Check for a property unique to the DauSoDichVuCuocDetail within DSC's du_lieu.
        const result = detail.reconciliation_type === ReconciliationType.DSC &&
                       (detail.du_lieu && detail.du_lieu.length > 0 && 'vnm_san_luong' in detail.du_lieu[0]);
        console.log("[Debug TypeGuard] isDscDetail result:", result);
        return result;
    } catch (e) {
        console.error("[Debug TypeGuard] Error in isDscDetail:", e);
        return false;
    }
}

// --- START: Deprecated Types (Keep for reference or remove) ---
// This seems to be the old detail type, now replaced by specific Dscd/Dsc types
/*
export interface ReconciliationDetail {
    id: number;
    reconciliation_type: ReconciliationType; // Add type field
    ky_doi_soat: string;
    is_template_data: boolean; // Should likely be false for detail view
    status: ReconciliationStatus;
    created_at: string; 
    updated_at: string; 
    partner: MinimalPartnerInfo | null;
    template: MinimalTemplateInfo | null;
    tong_ket: TongKetDetail | null; // This matches DSCD TongKetDetail
    du_lieu: DauSoDichVuDetail[]; // This matches DSCD DauSoDichVuDetail
}
*/

// This seems to be the old update payload, now replaced by specific Dscd/Dsc types
/*
export interface ReconciliationUpdatePayload {
    tong_ket?: TongKetUpdatePayload | null; // Matches DSCD TongKetUpdatePayload
    du_lieu?: DauSoDichVuUpdatePayload[]; // Matches DSCD DauSoDichVuUpdatePayload
}
*/
// --- END: Deprecated Types --- 