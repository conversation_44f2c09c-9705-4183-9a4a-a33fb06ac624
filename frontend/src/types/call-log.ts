export enum FileType {
  CALL_IN = "call_in",
  CALL_OUT = "call_out"
}

export enum CallType {
  IN = "in",
  OUT = "out"
}

export enum FileStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed"
}

export enum NumberType {
  MOBILE = "mobile",
  FIXED = "fixed",
  INTL = "intl",
  PORTED = "ported",
  UNKNOWN = "unknown"
}

export interface CallLogFile {
  id: number;
  filename: string;
  file_type: FileType;
  status: FileStatus;
  uploaded_at: string;
  total_rows: number | null;
  processed_rows: number;
  error_count: number;
  error_message: string | null;
  processing_time: number | null;
  task_id: string | null;
}

export interface PaginatedCallLogFiles {
  items: CallLogFile[];
  total: number;
  page: number;
  size: number;
}

export interface PaginatedCallLogs {
  items: CallLog[];
  total: number;
  page: number;
  size: number;
}

export interface CallLog {
  id: number;
  file_id: number;
  call_type: CallType;
  caller: string;
  callee: string;
  begin_time: string;
  end_time: string;
  duration: number;
  caller_gateway: string;
  called_gateway: string;
  call_date: string;
  call_hour: number;
  caller_type: string;
  callee_type: string;
  caller_note?: string;
  callee_note?: string;
  area_prefix?: string;
  area_name?: string;
} 