export type ServiceCategory = 'FIXED_LINE' | 'MOBILE' | '1900' | '1800' | 'INTERNATIONAL';

export type BillingMethod = 'subscription' | 'block_6s' | 'one_sec_plus' | 'one_min_plus';

export type VolumeUnit = 'VND' | 'minute';

export interface BaseEntity {
  id: number;
  created_at?: string;
  updated_at?: string;
}

export interface ServiceType {
  code: string;
  name: string;
}

export interface VolumeRange extends BaseEntity {
  min_value: number | null;
  max_value: number | null;
  unit: VolumeUnit;
  description: string;
}

export interface PricingBase extends BaseEntity {
  partner_id: number;
  billing_method: BillingMethod;
  service_type: string | null;
  volume_range_id: number | null;
  price: number;
  is_active: boolean;
}

export interface RevenuePricing extends PricingBase {
  service_type_name?: string;
  volume_range?: VolumeRange;
}

export interface CostPricing extends PricingBase {
  description?: string;
  service_type_name?: string;
  volume_range?: VolumeRange;
}

export interface BulkPricingServiceItem {
  service_type?: string;
  volume_range_id?: number;
  price: number;
  description?: string;
}

export interface BulkPricingCreate {
  partner_id: number;
  billing_method: BillingMethod;
  items: BulkPricingServiceItem[];
}

export interface FormData {
  service_types: ServiceType[];
  volume_ranges: VolumeRange[];
  billing_methods: BillingMethod[];
}

export interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
}

export interface ApiError {
  status: number;
  message: string;
  errors?: Record<string, string[]>;
}

export interface PricingFormData {
  partnerId: number;
  billingMethod: BillingMethod;
  items: Array<{
    serviceType?: string;
    volumeRangeId?: number;
    price: number;
    description?: string;
  }>;
}

export interface PricingRule {
  id: number;
  partner_id: number;
  billing_method: BillingMethod;
  service_type?: string;
  service_type_name?: string;
  volume_range_id?: number;
  price: number;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  volume_range?: VolumeRange;
}

export interface RevenuePricingRule extends PricingRule {
  type: 'revenue';
}

export interface CostPricingRule extends PricingRule {
  type: 'cost';
}

export type PricingRuleType = 'revenue' | 'cost'; 