import {
    ReconciliationType,
    type ReconciliationDetail,
    type DscdReconciliationDetail,
    type DscReconciliationDetail
} from './reconciliation';

/**
 * Type guard to check if the detail object is for DSCD.
 * @param detail The reconciliation detail object.
 * @returns True if the object is DscdReconciliationDetail, false otherwise.
 */
export function isDscdDetail(detail: ReconciliationDetail | null | undefined): detail is DscdReconciliationDetail {
    return detail?.reconciliation_type === ReconciliationType.DSCD;
}

/**
 * Type guard to check if the detail object is for DSC.
 * @param detail The reconciliation detail object.
 * @returns True if the object is DscReconciliationDetail, false otherwise.
 */
export function isDscDetail(detail: ReconciliationDetail | null | undefined): detail is DscReconciliationDetail {
    return detail?.reconciliation_type === ReconciliationType.DSC;
}

// Add more type guards here if needed for other reconciliation types in the future 