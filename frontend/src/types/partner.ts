export type PartnerType = 'telco' | 'cp';

export interface PartnerRawName {
    id: number;
    partner_id: number;
    raw_name: string;
    created_at: string;
    updated_at: string;
}

export interface Partner {
    id: number;
    type: PartnerType;
    name: string;
    raw_names: PartnerRawName[];
    description?: string;
    is_active: boolean;
    error_adjustment: number;
    contact_name?: string;
    contact_email?: string;
    contact_phone?: string;
    created_at: string;
    updated_at: string;
}

export interface PartnerFormData {
    type: PartnerType;
    name: string;
    raw_names: string[];
    description?: string;
    error_adjustment: number;
    contact_name?: string;
    contact_email?: string;
    contact_phone?: string;
}

export interface PartnerFilters {
    type?: PartnerType;
    is_active?: boolean;
} 