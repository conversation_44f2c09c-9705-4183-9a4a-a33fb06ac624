import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthState, LoginRequest, RegisterRequest, User } from '../types/auth';
import axiosInstance from '../lib/axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

interface AuthStore extends AuthState {
  login: (data: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<string>;
  updateUser: () => Promise<void>;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      updateUser: async () => {
        try {
          const token = localStorage.getItem('access_token');
          if (!token) {
            return;
          }

          const userResponse = await axiosInstance.get(`/v1/auth/me`);
          const user = userResponse.data;
          
          set({
            user,
            isAuthenticated: true,
            error: null
          });
        } catch (error) {
          console.error("[AUTH] Error fetching user data:", error);
          // Don't clear tokens here, let the axios interceptor handle 401 errors
        }
      },

      login: async (data) => {
        set({ isLoading: true, error: null });
        try {
          console.log("[AUTH] Login with:", {
            email: data.username_or_email,
            password: data.password.substring(0, 3) + '***'
          });
          
          // Send JSON data with email and password
          const response = await axiosInstance.post(`/v1/auth/login`, {
            email: data.username_or_email,
            password: data.password
          });
          
          const { access_token, refresh_token } = response.data;
          
          // Store tokens
          localStorage.setItem('access_token', access_token);
          if (refresh_token) {
            localStorage.setItem('refresh_token', refresh_token);
          }
          
          // Fetch user data
          const userResponse = await axiosInstance.get(`/v1/auth/me`);
          const user = userResponse.data;
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
        } catch (error) {
          console.error("[AUTH] Login error:", error);
          set({
            error: error instanceof Error ? error.message : 'An error occurred',
            isLoading: false
          });
          throw error;
        }
      },

      register: async (data) => {
        set({ isLoading: true, error: null });
        try {
          const response = await axiosInstance.post(`/v1/auth/register`, data);
          // If the registration response includes tokens directly, store them
          const { access_token, refresh_token } = response.data;
          
          if (access_token) {
            localStorage.setItem('access_token', access_token);
            
            if (refresh_token) {
              localStorage.setItem('refresh_token', refresh_token);
            }
            
            // Fetch user data
            const userResponse = await axiosInstance.get(`/v1/auth/me`);
            const user = userResponse.data;
            
            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null
            });
          } else {
            set({ isLoading: false });
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'An error occurred',
            isLoading: false
          });
          throw error;
        }
      },

      logout: () => {
        // Clear tokens
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        
        set({
          user: null,
          isAuthenticated: false,
          error: null,
          isLoading: false
        });
      },

      refreshToken: async () => {
        const refresh_token = localStorage.getItem('refresh_token');
        if (!refresh_token) {
          throw new Error('No refresh token found');
        }

        try {
          const response = await axiosInstance.post(`/v1/auth/refresh-token`, {
            refresh_token
          });
          const { access_token, refresh_token: new_refresh_token } = response.data;
          
          localStorage.setItem('access_token', access_token);
          if (new_refresh_token) {
            localStorage.setItem('refresh_token', new_refresh_token);
          }
          
          return access_token;
        } catch (error) {
          set({
            user: null,
            isAuthenticated: false,
            error: 'Session expired'
          });
          throw error;
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
); 