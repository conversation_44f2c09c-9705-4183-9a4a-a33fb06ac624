import React, { useEffect, useState, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

import { reconciliationService } from '@/services/reconciliation-service';
import { 
    ReconciliationType, 
    ReconciliationStatus, 
    isDscdDetail, 
    isDscDetail, 
    dscdEditFormSchema,
    dscEditFormSchema,
    type ReconciliationDetail, 
    type DscdReconciliationDetail, 
    type DscReconciliationDetail,
    type DauSoDichVuCuocDetail,
    type DscdAdjustmentsPayload,
    type DscAdjustmentsPayload,
    type TongKetAdjustmentsPayload,
    type DauSoDichVuAdjustmentsPayload,
    type DauSoDichVuCuocAdjustmentsPayload,
} from '@/types/reconciliation';

import type { 
    DscdEditFormData, 
    DscEditFormData, 
    EditFormDataUnion 
} from '@/types/reconciliation-edit';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { ArrowLeft, Save, AlertCircle } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { formatCurrency } from '@/lib/utils';
import { DauSoDichVuEditTable } from '@/components/reconciliation/dau-so-dich-vu-edit-table'; 
import { DscEditTable } from '@/components/reconciliation/dsc-edit-table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { DscNestedEditForm } from '@/components/reconciliation/dsc-nested-edit-form';

type AdjustmentsPayloadUnion = DscdAdjustmentsPayload | DscAdjustmentsPayload;

export function ReconciliationEditPage() {
    console.log("[DEBUG EditPage] ReconciliationEditPage component function is executing. URL params:", useParams());

    const { id, type } = useParams<{ id: string; type: string }>();
    const navigate = useNavigate();
    const { toast } = useToast();

    const [initialData, setInitialData] = useState<ReconciliationDetail | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [reconciliationType, setReconciliationType] = useState<ReconciliationType | null>(null);

    const [isDscModalOpen, setIsDscModalOpen] = useState(false);
    const [currentDscEditIndex, setCurrentDscEditIndex] = useState<number | null>(null);

    const numericId = parseInt(id || '0', 10);

    const currentSchema = useMemo(() => {
        if (reconciliationType === ReconciliationType.DSCD) return dscdEditFormSchema;
        if (reconciliationType === ReconciliationType.DSC) return dscEditFormSchema;
        return z.object({});
    }, [reconciliationType]);

    const methods = useForm<EditFormDataUnion>({
        resolver: zodResolver(currentSchema),
        defaultValues: {},
    });

    const { handleSubmit, reset, control, watch, formState: { isDirty } } = methods;

    const isFinalized = initialData?.status === ReconciliationStatus.FINALIZED;

    useEffect(() => {
        const typeFromUrl = Object.values(ReconciliationType).includes(type as ReconciliationType)
            ? type as ReconciliationType
            : null;
        
        setReconciliationType(typeFromUrl);

        if (!numericId || !typeFromUrl) {
            setError("ID hoặc loại đối soát không hợp lệ trong URL.");
            setIsLoading(false);
            return;
        }

        setIsLoading(true);
        reconciliationService.getReconciliationById(numericId, typeFromUrl)
            .then(data => {
                if (data.status === ReconciliationStatus.FINALIZED) {
                     setError("Không thể chỉnh sửa bản ghi đối soát đã chốt.");
                     setInitialData(data);
                     setIsLoading(false);
                     return;
                 }
                setInitialData(data);
                setError(null);

                if (isDscdDetail(data)) {
                    const dscdDefaults: DscdEditFormData = {
                    tong_ket: {
                            cong_tien_dich_vu_adjusted: data.tong_ket?.cong_tien_dich_vu ?? null,
                            tien_thue_gtgt_adjusted: data.tong_ket?.tien_thue_gtgt ?? null,
                    },
                    du_lieu: data.du_lieu?.map(d => ({
                        id: d.id,
                            cuoc_thu_khach_adjusted: d.cuoc_thu_khach ?? null,
                            cuoc_tra_htc_adjusted: d.cuoc_tra_htc ?? null,
                        co_dinh_noi_hat: { 
                                thoi_gian_goi_adjusted: d.co_dinh_noi_hat?.thoi_gian_goi ?? null,
                                cuoc_adjusted: d.co_dinh_noi_hat?.cuoc ?? null,
                        },
                        co_dinh_lien_tinh: { 
                                thoi_gian_goi_adjusted: d.co_dinh_lien_tinh?.thoi_gian_goi ?? null,
                                cuoc_adjusted: d.co_dinh_lien_tinh?.cuoc ?? null,
                        },
                        di_dong: {
                               thoi_gian_goi_adjusted: d.di_dong?.thoi_gian_goi ?? null,
                                cuoc_adjusted: d.di_dong?.cuoc ?? null,
                        },
                       cuoc_1900: {
                               thoi_gian_goi_adjusted: d.cuoc_1900?.thoi_gian_goi ?? null,
                                cuoc_adjusted: d.cuoc_1900?.cuoc ?? null,
                        },
                       quoc_te: {
                               thoi_gian_goi_adjusted: d.quoc_te?.thoi_gian_goi ?? null,
                                cuoc_adjusted: d.quoc_te?.cuoc ?? null,
                            },
                           cuoc_thue_bao: {
                                    thue_bao_thang_adjusted: d.cuoc_thue_bao?.thue_bao_thang ?? null,
                                    cam_ket_thang_adjusted: d.cuoc_thue_bao?.cam_ket_thang ?? null,
                                    tra_truoc_thang_adjusted: d.cuoc_thue_bao?.tra_truoc_thang ?? null,
                                },
                        })) || [],
                    };
                    console.log("[DSCD] Resetting form with (using original values for adjusted fields):", dscdDefaults);
                    reset(dscdDefaults);
                } else if (isDscDetail(data)) {
                    const dscDefaults: DscEditFormData = {
                        du_lieu: data.du_lieu?.map(d => ({
                            ...d,
                            id: d.id,
                            vnm_san_luong_adjusted: d.vnm_san_luong_adjusted ?? null,
                            vnm_thanh_tien_adjusted: d.vnm_thanh_tien_adjusted ?? null,
                            viettel_san_luong_adjusted: d.viettel_san_luong_adjusted ?? null,
                            viettel_thanh_tien_adjusted: d.viettel_thanh_tien_adjusted ?? null,
                            vnpt_san_luong_adjusted: d.vnpt_san_luong_adjusted ?? null,
                            vnpt_thanh_tien_adjusted: d.vnpt_thanh_tien_adjusted ?? null,
                            vms_san_luong_adjusted: d.vms_san_luong_adjusted ?? null,
                            vms_thanh_tien_adjusted: d.vms_thanh_tien_adjusted ?? null,
                            khac_san_luong_adjusted: d.khac_san_luong_adjusted ?? null,
                            khac_thanh_tien_adjusted: d.khac_thanh_tien_adjusted ?? null,
                        })) || [],
                    };
                    console.log("[DSC] Resetting form with default values (including originals):", dscDefaults);
                    reset(dscDefaults);
                } else {
                    setError(`Loại đối soát không được hỗ trợ để chỉnh sửa: ${type}`);
                    reset({});
                }
            })
            .catch(err => {
                console.error("Lỗi khi tải chi tiết đối soát:", err);
                const errorMsg = err.response?.data?.detail || err.message || "Không thể tải dữ liệu.";
                setError(errorMsg);
                setInitialData(null);
            })
            .finally(() => {
                setIsLoading(false);
            });
    }, [numericId, type, reset]);
    
    const onSubmit = async (formData: EditFormDataUnion) => {
        console.log("Form Data on Submit:", JSON.stringify(formData, null, 2));
        if (!initialData || isFinalized || !reconciliationType) {
             toast({ title: "Lỗi", description: "Không thể gửi cập nhật cho bản ghi này.", variant: "destructive" });
             return;
         }
        
        setIsSubmitting(true);
        let payload: AdjustmentsPayloadUnion | null = null;
        let hasChanges = false;

        if (isDscdDetail(initialData) && reconciliationType === ReconciliationType.DSCD) {
            const dscdPayload: DscdAdjustmentsPayload = {};
            const formTongKet = (formData as DscdEditFormData).tong_ket;
            const initialTongKet = initialData.tong_ket;

            if (formTongKet) {
                const tongKetChanges: TongKetAdjustmentsPayload = {};
                const initialCongTien = initialTongKet?.cong_tien_dich_vu ?? null;
                const initialThue = initialTongKet?.tien_thue_gtgt ?? null;

                if (formTongKet.cong_tien_dich_vu_adjusted !== initialCongTien && formTongKet.cong_tien_dich_vu_adjusted !== null && formTongKet.cong_tien_dich_vu_adjusted !== undefined) {
                    tongKetChanges.cong_tien_dich_vu = formTongKet.cong_tien_dich_vu_adjusted;
                 hasChanges = true;
             }
                if (formTongKet.tien_thue_gtgt_adjusted !== initialThue && formTongKet.tien_thue_gtgt_adjusted !== null && formTongKet.tien_thue_gtgt_adjusted !== undefined) {
                    tongKetChanges.tien_thue_gtgt = formTongKet.tien_thue_gtgt_adjusted;
                 hasChanges = true;
             }

                if (Object.keys(tongKetChanges).length > 0) {
                    dscdPayload.tong_ket = tongKetChanges;
                }
            }

            const formDuLieu = (formData as DscdEditFormData).du_lieu || [];
            const duLieuChanges: DauSoDichVuAdjustmentsPayload[] = formDuLieu.map(formItem => {
                 const initialItem = initialData.du_lieu.find(d => d.id === formItem.id);
            if (!initialItem) return null;

                 const itemChanges: Partial<DauSoDichVuAdjustmentsPayload> & { id: number } = { id: formItem.id };
            let itemHasChanges = false;

                 const checkAndAddChange = (
                     payloadKey: keyof Omit<DauSoDichVuAdjustmentsPayload, 'id' | 'co_dinh_noi_hat' | 'co_dinh_lien_tinh' | 'di_dong' | 'cuoc_1900' | 'quoc_te' | 'cuoc_thue_bao'>,
                     formValue: number | null | undefined,
                     initialOriginalValue: number | null | undefined
                 ) => {
                     if (formValue !== initialOriginalValue && formValue !== null && formValue !== undefined) {
                         (itemChanges as any)[payloadKey] = formValue;
                 itemHasChanges = true;
             }
                 };

                 checkAndAddChange('cuoc_thu_khach', formItem.cuoc_thu_khach_adjusted, initialItem.cuoc_thu_khach);
                 checkAndAddChange('cuoc_tra_htc', formItem.cuoc_tra_htc_adjusted, initialItem.cuoc_tra_htc);

                 const processNested = (
                     payloadKey: keyof Pick<DauSoDichVuAdjustmentsPayload, 'co_dinh_noi_hat' | 'co_dinh_lien_tinh' | 'di_dong' | 'cuoc_1900' | 'quoc_te' | 'cuoc_thue_bao'>,
                     formNested: any,
                     initialNested: any
                 ) => {
                    if (!formNested) return;
                    const nestedChanges: any = {};
                    let nestedHasChanges = false;

                    const checkNestedChange = (
                        nestedPayloadKey: string,
                        nestedFormValue: number | null | undefined,
                        nestedInitialOriginal: number | null | undefined
                    ) => {
                         if (nestedFormValue !== nestedInitialOriginal && nestedFormValue !== null && nestedFormValue !== undefined) {
                             nestedChanges[nestedPayloadKey] = nestedFormValue;
                             nestedHasChanges = true;
                             itemHasChanges = true;
                         }
                    };

                    if ('thoi_gian_goi_adjusted' in formNested) {
                        checkNestedChange('thoi_gian_goi', formNested.thoi_gian_goi_adjusted, initialNested?.thoi_gian_goi);
                    }
                     if ('cuoc_adjusted' in formNested) {
                         checkNestedChange('cuoc', formNested.cuoc_adjusted, initialNested?.cuoc);
                     }
                     if ('thue_bao_thang_adjusted' in formNested) {
                        checkNestedChange('thue_bao_thang', formNested.thue_bao_thang_adjusted, initialNested?.thue_bao_thang);
                     }
                     if ('cam_ket_thang_adjusted' in formNested) {
                         checkNestedChange('cam_ket_thang', formNested.cam_ket_thang_adjusted, initialNested?.cam_ket_thang);
                     }
                    if ('tra_truoc_thang_adjusted' in formNested) {
                         checkNestedChange('tra_truoc_thang', formNested.tra_truoc_thang_adjusted, initialNested?.tra_truoc_thang);
                     }

                    if (nestedHasChanges) {
                         (itemChanges as any)[payloadKey] = nestedChanges;
                    }
                 };

                 processNested('co_dinh_noi_hat', formItem.co_dinh_noi_hat, initialItem.co_dinh_noi_hat);
                 processNested('co_dinh_lien_tinh', formItem.co_dinh_lien_tinh, initialItem.co_dinh_lien_tinh);
                 processNested('di_dong', formItem.di_dong, initialItem.di_dong);
                 processNested('cuoc_1900', formItem.cuoc_1900, initialItem.cuoc_1900);
                 processNested('quoc_te', formItem.quoc_te, initialItem.quoc_te);
                 processNested('cuoc_thue_bao', formItem.cuoc_thue_bao, initialItem.cuoc_thue_bao);

                 return itemHasChanges ? itemChanges as DauSoDichVuAdjustmentsPayload : null;
             }).filter((item): item is DauSoDichVuAdjustmentsPayload => item !== null);

             if (duLieuChanges.length > 0) {
                 dscdPayload.du_lieu = duLieuChanges;
                 hasChanges = true;
             }

            if (hasChanges) {
                payload = dscdPayload;
            }

        } else if (isDscDetail(initialData) && reconciliationType === ReconciliationType.DSC) {
            const dscPayload: DscAdjustmentsPayload = { du_lieu: [] };
            const formDuLieu = (formData as DscEditFormData).du_lieu || [];

            const duLieuChanges: DauSoDichVuCuocAdjustmentsPayload[] = formDuLieu.map(formItem => {
                const initialItem = (initialData as DscReconciliationDetail).du_lieu.find(d => d.id === formItem.id);
                if (!initialItem) return null;

                const itemChanges: Partial<DauSoDichVuCuocAdjustmentsPayload> & { id: number } = { id: formItem.id };
                let itemHasChanges = false;

                const checkDscChange = (
                    payloadKey: keyof Omit<DauSoDichVuCuocAdjustmentsPayload, 'id'>,
                    formValue: number | null | undefined,
                    initialOriginalValue: number | null | undefined
                ) => {
                    if (formValue !== initialOriginalValue && formValue !== null && formValue !== undefined) {
                        (itemChanges as any)[payloadKey] = formValue;
                        itemHasChanges = true;
                   }
                };

                checkDscChange('vnm_san_luong', formItem.vnm_san_luong_adjusted, initialItem.vnm_san_luong);
                checkDscChange('vnm_thanh_tien', formItem.vnm_thanh_tien_adjusted, initialItem.vnm_thanh_tien);
                checkDscChange('viettel_san_luong', formItem.viettel_san_luong_adjusted, initialItem.viettel_san_luong);
                checkDscChange('viettel_thanh_tien', formItem.viettel_thanh_tien_adjusted, initialItem.viettel_thanh_tien);
                checkDscChange('vnpt_san_luong', formItem.vnpt_san_luong_adjusted, initialItem.vnpt_san_luong);
                checkDscChange('vnpt_thanh_tien', formItem.vnpt_thanh_tien_adjusted, initialItem.vnpt_thanh_tien);
                checkDscChange('vms_san_luong', formItem.vms_san_luong_adjusted, initialItem.vms_san_luong);
                checkDscChange('vms_thanh_tien', formItem.vms_thanh_tien_adjusted, initialItem.vms_thanh_tien);
                checkDscChange('khac_san_luong', formItem.khac_san_luong_adjusted, initialItem.khac_san_luong);
                checkDscChange('khac_thanh_tien', formItem.khac_thanh_tien_adjusted, initialItem.khac_thanh_tien);

                return itemHasChanges ? itemChanges as DauSoDichVuCuocAdjustmentsPayload : null;
            }).filter((item): item is DauSoDichVuCuocAdjustmentsPayload => item !== null);

            if (duLieuChanges.length > 0) {
                dscPayload.du_lieu = duLieuChanges;
                hasChanges = true;
            }

            if (hasChanges) {
                payload = dscPayload;
            }
        }

        if (payload && hasChanges) {
        console.log("Submitting Payload:", JSON.stringify(payload, null, 2));
            try {
                const updatedData = await reconciliationService.updateReconciliationAdjustments(
                    numericId,
                    reconciliationType,
                    payload
                );
                setInitialData(updatedData);

                if (isDscdDetail(updatedData)) {
                     const updatedDscdDefaults: DscdEditFormData = {
                        tong_ket: {
                            cong_tien_dich_vu_adjusted: updatedData.tong_ket?.cong_tien_dich_vu ?? null,
                            tien_thue_gtgt_adjusted: updatedData.tong_ket?.tien_thue_gtgt ?? null,
                        },
                        du_lieu: updatedData.du_lieu?.map(d => ({
                            id: d.id,
                            cuoc_thu_khach_adjusted: d.cuoc_thu_khach ?? null,
                            cuoc_tra_htc_adjusted: d.cuoc_tra_htc ?? null,
                            co_dinh_noi_hat: { thoi_gian_goi_adjusted: d.co_dinh_noi_hat?.thoi_gian_goi ?? null, cuoc_adjusted: d.co_dinh_noi_hat?.cuoc ?? null },
                            co_dinh_lien_tinh: { thoi_gian_goi_adjusted: d.co_dinh_lien_tinh?.thoi_gian_goi ?? null, cuoc_adjusted: d.co_dinh_lien_tinh?.cuoc ?? null },
                            di_dong: { thoi_gian_goi_adjusted: d.di_dong?.thoi_gian_goi ?? null, cuoc_adjusted: d.di_dong?.cuoc ?? null },
                            cuoc_1900: { thoi_gian_goi_adjusted: d.cuoc_1900?.thoi_gian_goi ?? null, cuoc_adjusted: d.cuoc_1900?.cuoc ?? null },
                            quoc_te: { thoi_gian_goi_adjusted: d.quoc_te?.thoi_gian_goi ?? null, cuoc_adjusted: d.quoc_te?.cuoc ?? null },
                            cuoc_thue_bao: { thue_bao_thang_adjusted: d.cuoc_thue_bao?.thue_bao_thang ?? null, cam_ket_thang_adjusted: d.cuoc_thue_bao?.cam_ket_thang ?? null, tra_truoc_thang_adjusted: d.cuoc_thue_bao?.tra_truoc_thang ?? null },
                        })) || [],
                    };
                     reset(updatedDscdDefaults);
                 } else if (isDscDetail(updatedData)) {
                     const updatedDscDefaults: DscEditFormData = {
                        du_lieu: updatedData.du_lieu?.map(d => ({
                            id: d.id,
                            vnm_san_luong_adjusted: null, 
                            vnm_thanh_tien_adjusted: null,
                            viettel_san_luong_adjusted: null,
                            viettel_thanh_tien_adjusted: null,
                            vnpt_san_luong_adjusted: null,
                            vnpt_thanh_tien_adjusted: null,
                            vms_san_luong_adjusted: null,
                            vms_thanh_tien_adjusted: null,
                            khac_san_luong_adjusted: null,
                            khac_thanh_tien_adjusted: null,
                        })) || [],
                    };
                     reset(updatedDscDefaults);
                 }

                toast({
                    title: "Thành công",
                    description: "Đã cập nhật hiệu chỉnh đối soát.",
                });
        } catch (err: any) {
                console.error("Failed to update adjustments:", err);
                const errorMsg = err.response?.data?.detail || err.message || "Lỗi khi cập nhật hiệu chỉnh.";
            toast({ 
                    title: "Lỗi cập nhật",
                    description: errorMsg,
                variant: "destructive",
                });
                setError(errorMsg);
            }
        } else {
            toast({
                title: "Không có thay đổi",
                description: "Không có thay đổi nào để lưu.",
                variant: "default",
            });
        }

        setIsSubmitting(false);
    };

    const handleOpenDscEditModal = (index: number) => {
        if (isDscDetail(initialData)) {
            const itemData = initialData.du_lieu?.[index] ?? null;
            console.log(`[DEBUG] Opening DSC Edit Modal for index ${index}, Data:`, JSON.stringify(itemData, null, 2));
            setCurrentDscEditIndex(index);
            setIsDscModalOpen(true);
        }
    };

    if (isLoading) {
        return (
            <div className="container mx-auto p-4 space-y-4">
                <Skeleton className="h-8 w-1/4" />
                <Skeleton className="h-4 w-1/2" />
                <Card>
                    <CardHeader><Skeleton className="h-6 w-1/3" /></CardHeader>
                    <CardContent className="space-y-4">
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-40 w-full" />
                    </CardContent>
                </Card>
                </div>
        );
    }

    if (error) {
        return (
            <div className="container mx-auto p-4">
                <Button variant="outline" size="sm" onClick={() => navigate(-1)} className="mb-4">
                    <ArrowLeft className="mr-2 h-4 w-4" /> Quay lại
                </Button>
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Lỗi</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
                 {initialData && (
                     <Card className="mt-4">
                         <CardHeader>
                             <CardTitle>Chi tiết đối soát (ID: {initialData.id})</CardTitle>
                              <CardDescription>
                                 Loại: {initialData.reconciliation_type} | Kỳ: {initialData.ky_doi_soat} | Trạng thái: {initialData.status}
                             </CardDescription>
                         </CardHeader>
                     </Card>
                 )}
            </div>
        );
    }
    
     if (!initialData) {
         return (
             <div className="container mx-auto p-4">
                 <Button variant="outline" size="sm" onClick={() => navigate(-1)} className="mb-4">
                     <ArrowLeft className="mr-2 h-4 w-4" /> Quay lại
                 </Button>
                 <Alert variant="destructive">
                     <AlertCircle className="h-4 w-4" />
                     <AlertTitle>Lỗi</AlertTitle>
                     <AlertDescription>Không thể tải dữ liệu đối soát.</AlertDescription>
                 </Alert>
             </div>
         );
     }

    return (
        <FormProvider {...methods}>
            <Form {...methods}>
                {/* START: Restore form content */}
                <form onSubmit={handleSubmit(onSubmit)} className="container mx-auto p-4 space-y-6">
                     <div className="flex justify-between items-center">
                         <h1 className="text-2xl font-bold">
                             Hiệu chỉnh đối soát {reconciliationType} - Kỳ {initialData.ky_doi_soat} (ID: {initialData.id})
                         </h1>
                         <div className="flex gap-2">
                             <Button variant="outline" size="sm" onClick={() => navigate(-1)} type="button">
                                 <ArrowLeft className="mr-2 h-4 w-4" /> Quay lại
                             </Button>
                             <Button type="submit" disabled={isSubmitting || isFinalized || !isDirty} size="sm">
                                 <Save className="mr-2 h-4 w-4" />
                                 {isSubmitting ? 'Đang lưu...' : 'Lưu thay đổi'}
                             </Button>
                         </div>
                      </div>
 
                      <Card>
                          <CardHeader>
                              <CardTitle>Thông tin chung</CardTitle>
                              <CardDescription>
                                  Thông tin cơ bản của bản ghi đối soát. Trạng thái hiện tại: <span className={`font-semibold ${isFinalized ? 'text-red-600' : 'text-green-600'}`}>{initialData.status}</span>
                              </CardDescription>
                          </CardHeader>
                          <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                              <div><span className="font-medium">Đối tác:</span> {initialData.partner?.name ?? 'N/A'}</div>
                              <div><span className="font-medium">Loại:</span> {initialData.reconciliation_type}</div>
                              <div><span className="font-medium">Kỳ đối soát:</span> {initialData.ky_doi_soat}</div>
                              <div><span className="font-medium">Ngày tạo:</span> {new Date(initialData.created_at).toLocaleString()}</div>
                              <div><span className="font-medium">Cập nhật cuối:</span> {new Date(initialData.updated_at).toLocaleString()}</div>
                              {isDscdDetail(initialData) && (
                                  <>
                                      <div><span className="font-medium">Từ mạng (DSCD):</span> {initialData.tu_mang ?? 'N/A'}</div>
                                      <div><span className="font-medium">Hợp đồng số (DSCD):</span> {initialData.hop_dong_so ?? 'N/A'}</div>
                                  </>
                              )}
                              {isDscDetail(initialData) && (
                                  <>
                                      <div><span className="font-medium">Từ mạng (DSC):</span> {initialData.tu_mang ?? 'N/A'}</div>
                                      <div><span className="font-medium">Đến đối tác (DSC):</span> {initialData.den_doi_tac ?? 'N/A'}</div>
                                      <div><span className="font-medium">Hợp đồng số (DSC):</span> {initialData.hop_dong_so ?? 'N/A'}</div>
                                  </>
                              )}
                          </CardContent>
                      </Card>
 
                     {isFinalized && (
                          <Alert variant="warning">
                              <AlertCircle className="h-4 w-4" />
                              <AlertTitle>Bản ghi đã chốt</AlertTitle>
                              <AlertDescription>
                                  Bản ghi đối soát này đã được chốt và không thể chỉnh sửa thêm.
                              </AlertDescription>
                          </Alert>
                      )}
 
                      {!isFinalized && (
                              <Card>
                                  <CardHeader>
                                  <CardTitle>Chi tiết hiệu chỉnh</CardTitle>
                                  <CardDescription>
                                       Nhập các giá trị hiệu chỉnh vào các ô tương ứng. Để trống hoặc xóa giá trị để sử dụng giá trị gốc.
                                       Tổng cộng sẽ được tính toán lại dựa trên các hiệu chỉnh bạn nhập.
                                  </CardDescription>
                                  </CardHeader>
                              <CardContent>
                                  {console.log("[Debug EditPage] Checking table render conditions. isDscdDetail:", isDscdDetail(initialData), "isDscDetail:", isDscDetail(initialData))}
                                  {isDscdDetail(initialData) && <DauSoDichVuEditTable initialData={initialData.du_lieu} />}
                                  {isDscDetail(initialData) && <DscEditTable 
                                     initialData={(initialData as DscReconciliationDetail).du_lieu}
                                     onEditRow={handleOpenDscEditModal} 
                                 />}
                                  {!isDscdDetail(initialData) && !isDscDetail(initialData) && (
                                      <p>Loại đối soát này hiện không hỗ trợ chỉnh sửa chi tiết.</p>
                                  )}
                                  </CardContent>
                              </Card>
                      )}
             
                              <Card>
                                  <CardHeader>
                              <CardTitle>Tổng kết</CardTitle>
                              <CardDescription>Giá trị tổng kết dựa trên dữ liệu gốc và các hiệu chỉnh (nếu có).</CardDescription>
                                  </CardHeader>
                          <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                               {isDscdDetail(initialData) && initialData.tong_ket && (
                                  <>
                                      <div><span className="font-medium">Cộng tiền DV (Gốc):</span> {formatCurrency(initialData.tong_ket.cong_tien_dich_vu)}</div>
                                      <div><span className="font-medium">Thuế GTGT (Gốc):</span> {formatCurrency(initialData.tong_ket.tien_thue_gtgt)}</div>
                                      <div><span className="font-medium">Tổng cộng (Gốc):</span> {formatCurrency(initialData.tong_ket.tong_cong_tien)}</div>
                                  </>
                               )}
                              {isDscDetail(initialData) && initialData.tong_ket && (
                                  <>
                                      <div><span className="font-medium">Cộng tiền DV (Gốc):</span> {formatCurrency(initialData.tong_ket.cong_tien_dich_vu)}</div>
                                      <div><span className="font-medium">Thuế GTGT (Gốc):</span> {formatCurrency(initialData.tong_ket.tien_thue_gtgt)}</div>
                                      <div><span className="font-medium">Tổng cộng (Gốc):</span> {formatCurrency(initialData.tong_ket.tong_cong_tien)}</div>
                                  </>
                               )}
                              {(!initialData.tong_ket) && <p>Không có dữ liệu tổng kết.</p>}
                                  </CardContent>
                              </Card>
             
                     <div className="flex justify-end gap-2">
                          <Button variant="outline" onClick={() => navigate(-1)} type="button">
                              Hủy
                                  </Button>
                          <Button type="submit" disabled={isSubmitting || isFinalized || !isDirty}>
                              <Save className="mr-2 h-4 w-4" />
                              {isSubmitting ? 'Đang lưu...' : 'Lưu thay đổi'}
                                  </Button>
                              </div>
                 </form>
                 {/* END: Restore form content */}
                 
                 {/* Remove simple test render */}
                 {/* 
                 <div className="container mx-auto p-4">
                     <p>--- TEST RENDER INSIDE FORM PROVIDER ---</p>
                 </div>
                 */}
             </Form>

             {/* --- DSCD Modal --- */}
             {console.log("[Debug EditPage] Checking DSCD Dialog Render. isDscdModalOpen:", methods.watch('du_lieu') !== undefined)} {/* Replace condition with actual DSCD open state if different */}
              {/* Assuming there's a Dialog for DSCD similar to DSC */}
              {/* <Dialog open={isDscdModalOpen} onOpenChange={setIsDscdModalOpen}> ... </Dialog> */}


             {/* --- DSC Modal --- */}
             {console.log("[Debug EditPage] Checking DSC Dialog Render. isDscModalOpen:", isDscModalOpen, "currentDscEditIndex:", currentDscEditIndex)}
             <Dialog open={isDscModalOpen} onOpenChange={setIsDscModalOpen}>
                {console.log("[Debug Modal Render] Index:", currentDscEditIndex, "Dữ liệu dòng:", initialData?.du_lieu?.[currentDscEditIndex ?? -1])}
                <DialogContent className="sm:max-w-[600px] max-h-[85vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>
                             Hiệu chỉnh Chi tiết Đầu số: 
                             {isDscDetail(initialData) && currentDscEditIndex !== null
                                 ? (initialData.du_lieu?.[currentDscEditIndex]?.dau_so ?? 'N/A')
                                 : 'N/A'}
                         </DialogTitle>
                         <DialogDescription>
                             Chỉnh sửa sản lượng và thành tiền chi tiết cho đầu số này. 
                             Nhấn "Lưu" ở form chính để áp dụng các thay đổi.
                         </DialogDescription>
                     </DialogHeader>
                     {console.log("[Debug EditPage] Checking DscNestedEditForm Render Condition:", isDscDetail(initialData), currentDscEditIndex)}
                     {isDscDetail(initialData) && currentDscEditIndex !== null && (
                         <>
                             {console.log("[Debug EditPage] RENDERING DscNestedEditForm for index:", currentDscEditIndex)}
                             <DscNestedEditForm 
                                 fieldIndex={currentDscEditIndex} 
                                 initialItemData={initialData.du_lieu?.[currentDscEditIndex] ?? null} 
                             /> 
                         </>
                     )}
                     <DialogFooter className="sm:justify-end">
                         <Button type="button" variant="secondary" onClick={() => setIsDscModalOpen(false)}>
                             Đóng
                         </Button>
                     </DialogFooter>
                 </DialogContent>
             </Dialog>
        </FormProvider>
    );
} 