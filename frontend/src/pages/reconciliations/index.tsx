import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileSpreadsheet, Upload, FileUp, Refresh<PERSON>w, Trash, <PERSON><PERSON><PERSON>, Play, Download, FileSearch, Eye, Filter, Loader2 } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { reconciliationService } from "@/services/reconciliation-service";
import { DoiSoatTemplate } from "@/models/template";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { formatDate } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import TemplateUploadDialog from "@/components/templates/TemplateUploadDialog";
import { useNavigate } from "react-router-dom";
import { TemplateTypes } from "@/models/template";
import { ReconciliationTable } from "@/components/reconciliation/reconciliation-table";
import type { ReconciliationListItem, GetReconciliationsParams } from "@/types/reconciliation";
import { ReconciliationType, ReconciliationStatus } from "@/types/reconciliation";
import { ReusablePagination } from "@/components/ui/reusable-pagination";
import { templateService, GetTemplatesParams } from "@/services/template-service";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { CreateReconciliationDialog } from "@/components/reconciliation/create-reconciliation-dialog";

export default function ReconciliationsPage() {
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [templates, setTemplates] = useState<DoiSoatTemplate[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);
  const [activeTab, setActiveTab] = useState<"reconciliations" | "templates">("reconciliations");
  const [processingStatus, setProcessingStatus] = useState<{[key: number]: boolean}>({});
  const [totalTemplates, setTotalTemplates] = useState(0);
  const [templateCurrentPage, setTemplateCurrentPage] = useState(1);
  const [templateTypeFilter, setTemplateTypeFilter] = useState<string | undefined>(undefined);
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const pageSize = 10;

  // --- START: Add state for Reconciliation List ---
  const [reconciliations, setReconciliations] = useState<ReconciliationListItem[]>([]);
  const [isReconciliationsLoading, setIsReconciliationsLoading] = useState(true);
  const [reconciliationsError, setReconciliationsError] = useState<string | null>(null);
  const [totalReconciliations, setTotalReconciliations] = useState(0);
  const [reconciliationCurrentPage, setReconciliationCurrentPage] = useState(1);
  const [reconciliationFilterParams, setReconciliationFilterParams] = useState<Partial<GetReconciliationsParams>>({});
  const [reconciliationSortParams, setReconciliationSortParams] = useState<{ sort_by: string; sort_direction: 'asc' | 'desc' } | null>(null);
  // --- END: Add state for Reconciliation List ---

  const reconciliationPageSize = 10; // Or make configurable

  // --- START: State for Confirmation Dialog --- 
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<'finalize' | 'delete' | null>(null);
  const [selectedItem, setSelectedItem] = useState<ReconciliationListItem | null>(null);
  const [isConfirmActionLoading, setIsConfirmActionLoading] = useState(false);
  // --- END: State for Confirmation Dialog --- 

  // --- START: State for Polling --- 
  const [pollingIds, setPollingIds] = useState<Set<number>>(new Set());
  // --- END: State for Polling --- 

  // --- START: Polling Logic --- 
  const startPolling = useCallback(async (id: number, type: ReconciliationType) => {
    if (pollingIds.has(id)) return; // Already polling this ID

    console.log(`[Polling] Starting for ID: ${id}, Type: ${type}`);
    setPollingIds(prev => new Set(prev).add(id));

    try {
      await reconciliationService.pollReconciliationStatus(id, type);
      console.log(`[Polling] Finished successfully for ID: ${id}`);
      toast({ title: "Xử lý hoàn tất", description: `Đối soát #${id} đã xử lý xong.` });
      fetchReconciliations(); // Refetch list after successful polling
    } catch (error: any) {
      console.error(`[Polling] Error for ID: ${id}:`, error);
      toast({ 
        title: "Lỗi xử lý", 
        description: `Có lỗi xảy ra khi xử lý đối soát #${id}: ${error.message || 'Unknown error'}`,
        variant: "destructive" 
      });
       // Still refetch to potentially show ERROR status
       fetchReconciliations();
    } finally {
      setPollingIds(prev => {
        const next = new Set(prev);
        next.delete(id);
        return next;
      });
    }
  }, [pollingIds, toast]); // Include toast in dependencies
  // --- END: Polling Logic ---

   // --- START: Check for PROCESSING status on data fetch --- 
   useEffect(() => {
     if (reconciliations && reconciliations.length > 0) {
       reconciliations.forEach(item => {
         if (item.status === ReconciliationStatus.PROCESSING && !pollingIds.has(item.id)) {
           startPolling(item.id, item.reconciliation_type);
         }
       });
     }
   }, [reconciliations, pollingIds, startPolling]); // Depend on reconciliations, pollingIds, and the startPolling function
   // --- END: Check for PROCESSING status on data fetch --- 

  useEffect(() => {
    fetchTemplates();
  }, [activeTab, templateCurrentPage, templateTypeFilter]);

  useEffect(() => {
    setTemplateCurrentPage(1);
  }, [templateTypeFilter]);

  useEffect(() => {
      if (activeTab === 'reconciliations') { 
          fetchReconciliations();
      }
  }, [activeTab, reconciliationCurrentPage, reconciliationFilterParams, reconciliationSortParams]);

  const fetchTemplates = async () => {
    try {
      setIsLoadingTemplates(true);
      const params: GetTemplatesParams = {
          skip: (templateCurrentPage - 1) * pageSize,
          limit: pageSize,
          template_type: templateTypeFilter,
      };
      const response = await templateService.getTemplates(params);
      setTemplates(response.items);
      setTotalTemplates(response.total);
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách mẫu đối soát",
        variant: "destructive"
      });
    } finally {
      setIsLoadingTemplates(false);
    }
  };

  const fetchReconciliations = useCallback(async () => {
      // If already loading, don't start another fetch (prevents race conditions)
      // Polling completion will trigger its own fetch.
      if (isReconciliationsLoading && reconciliations.length > 0) return;
      
      try {
          setIsReconciliationsLoading(true);
          setReconciliationsError(null);
          const params: GetReconciliationsParams = {
              page: reconciliationCurrentPage,
              size: reconciliationPageSize,
              ...reconciliationFilterParams,
              sort_by: reconciliationSortParams?.sort_by ?? "created_at",
              sort_order: reconciliationSortParams?.sort_direction ?? "desc",
          };
          console.log("[fetchReconciliations] Sending params:", params);
          const response = await reconciliationService.getReconciliations(params);
          setReconciliations(response.items || []);
          setTotalReconciliations(response.total || 0);
      } catch (error: any) {
          console.error("Failed to fetch reconciliations:", error);
          setReconciliationsError(error.message || "Không thể tải danh sách đối soát.");
          setReconciliations([]);
          setTotalReconciliations(0);
      } finally {
          setIsReconciliationsLoading(false);
      }
  }, [reconciliationCurrentPage, reconciliationPageSize, reconciliationFilterParams, reconciliationSortParams, isReconciliationsLoading, reconciliations.length]);

  const handleDelete = async (id: number) => {
    try {
      await templateService.deleteTemplate(id);
      toast({
        title: "Xóa thành công",
        description: "Mẫu đối soát đã được xóa",
      });
      fetchTemplates();
    } catch (error) {
      toast({
        title: "Xóa thất bại",
        description: "Có lỗi xảy ra khi xóa mẫu đối soát",
        variant: "destructive"
      });
    }
  };
  
  const handleProcess = async (id: number) => {
    try {
      setProcessingStatus((prev: { [key: number]: boolean }) => ({ ...prev, [id]: true }));
      const response = await templateService.processTemplate(id);
      
      toast({
        title: "Yêu cầu đã được gửi",
        description: response.message || "Mẫu đối soát đang được xử lý",
      });
      
      fetchTemplates();
      
      const checkInterval = setInterval(async () => {
        try {
          const statusResponse = await templateService.getTemplateStatus(id);
          
          if (statusResponse.status !== 'processing') {
            clearInterval(checkInterval);
            fetchTemplates();
            
            if (statusResponse.status === 'completed') {
              toast({
                title: "Phân tích hoàn tất",
                description: "Mẫu đối soát đã được phân tích thành công",
                variant: "default"
              });
            } else if (statusResponse.status === 'failed') {
              toast({
                title: "Phân tích thất bại",
                description: statusResponse.message || "Có lỗi khi phân tích mẫu đối soát",
                variant: "destructive"
              });
            }
          }
        } catch (error) {
          console.error("Error checking template status:", error);
          clearInterval(checkInterval);
        }
      }, 3000);
      
      setTimeout(() => {
        clearInterval(checkInterval);
      }, 300000);
      
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể gửi yêu cầu phân tích mẫu đối soát",
        variant: "destructive"
      });
    } finally {
      setProcessingStatus((prev: { [key: number]: boolean }) => ({ ...prev, [id]: false }));
    }
  };

  const renderStatus = (status: string) => {
    switch(status) {
      case 'pending':
        return <Badge>Đang chờ</Badge>;
      case 'processing':
        return <Badge className="bg-secondary">Đang xử lý</Badge>;
      case 'completed':
        return <Badge className="bg-green-500 hover:bg-green-600">Hoàn thành</Badge>;
      case 'failed':
        return <Badge className="bg-red-500 hover:bg-red-600">Lỗi</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const handleReconciliationPageChange = (page: number) => {
        setReconciliationCurrentPage(page);
    };

  // --- START: Handlers for Reconciliation Table Actions & Sorting ---
  const handleSortChange = (columnId: string) => {
      setReconciliationSortParams((prev) => {
          const currentDirection = prev?.sort_by === columnId ? prev.sort_direction : null;
          const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
          return { sort_by: columnId, sort_direction: newDirection };
      });
      setReconciliationCurrentPage(1);
  };

  const handleViewDetails = (item: ReconciliationListItem) => {
    navigate(`/reconciliations/${item.reconciliation_type.toLowerCase()}/${item.id}`);
  };

  const handleEdit = (item: ReconciliationListItem) => {
    if (item.status !== ReconciliationStatus.FINALIZED) {
        navigate(`/reconciliations/${item.reconciliation_type.toLowerCase()}/${item.id}/edit`);
    } else {
        toast({ title: "Không thể sửa", description: "Bản ghi đã chốt không thể sửa.", variant: "warning" });
    }
  };

  const handleRequestFinalize = (item: ReconciliationListItem) => {
      setSelectedItem(item);
      setConfirmAction('finalize');
      setIsConfirmDialogOpen(true);
  };
  
  const onRequestDelete = (item: ReconciliationListItem) => {
      setSelectedItem(item);
      setConfirmAction('delete');
      setIsConfirmDialogOpen(true);
  };

    // Action confirmation handler (remains mostly the same, ensures item is not null)
    const handleConfirmAction = async () => {
       if (!selectedItem || !confirmAction) return;

       setIsConfirmActionLoading(true);
       const { id, reconciliation_type } = selectedItem;

       try {
           if (confirmAction === 'finalize') {
                await reconciliationService.finalizeReconciliation(id, reconciliation_type);
                toast({ title: "Thành công", description: "Đã chốt đối soát." });
           } else if (confirmAction === 'delete') {
                await reconciliationService.deleteReconciliation(id, reconciliation_type);
                toast({ title: "Thành công", description: "Đã xóa đối soát." });
           }
           fetchReconciliations(); // Refresh list after action
       } catch (err: any) {
           toast({
               variant: "destructive",
               title: "Lỗi",
               description: err.message || `Không thể ${confirmAction === 'finalize' ? 'chốt' : 'xóa'} đối soát.`,
           });
       } finally {
           setIsConfirmActionLoading(false);
           setIsConfirmDialogOpen(false);
           setSelectedItem(null);
           setConfirmAction(null);
       }
    };
  // --- END: Handlers for Reconciliation Table Actions & Sorting ---

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  return (
    <div className="container py-6 space-y-8">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Đối soát</h1>
        <p className="text-muted-foreground">
          Quản lý và xem dữ liệu đối soát giữa cuộc gọi và thanh toán
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Danh sách đối soát</CardTitle>
          <CardDescription>
            Kết quả các lần đối soát đã thực hiện.
          </CardDescription>
        </CardHeader>
        <CardContent>
            {isReconciliationsLoading ? (
                <div className="space-y-2">
                    {Array.from({ length: 5 }).map((_, index) => (
                        <Skeleton key={index} className="h-10 w-full" />
                    ))}
                </div>
            ) : reconciliationsError ? (
                <div className="text-red-600 text-center py-4">
                    {reconciliationsError}
                </div>
            ) : (
                 <ReconciliationTable 
                     data={reconciliations} 
                     isLoading={isReconciliationsLoading} 
                     error={reconciliationsError ? new Error(reconciliationsError) : null} 
                     sortBy={reconciliationSortParams?.sort_by || 'created_at'} 
                     sortOrder={reconciliationSortParams?.sort_direction || 'desc'} 
                     onSortChange={handleSortChange} 
                     onViewDetails={handleViewDetails}
                     onEdit={handleEdit}
                     onFinalize={handleRequestFinalize}
                     onDelete={onRequestDelete}
                     pollingIds={pollingIds}
                 />
             )}
             {!isReconciliationsLoading && !reconciliationsError && totalReconciliations > 0 && (
                <ReusablePagination 
                    currentPage={reconciliationCurrentPage}
                    totalItems={totalReconciliations}
                    itemsPerPage={reconciliationPageSize}
                    onPageChange={handleReconciliationPageChange}
                />
             )}
             {!isReconciliationsLoading && !reconciliationsError && totalReconciliations === 0 && (
                  <div className="text-center py-4 text-muted-foreground">Không tìm thấy bản ghi đối soát nào.</div>
             )}
        </CardContent>
      </Card>

      <TemplateUploadDialog
        open={isUploadDialogOpen}
        onOpenChange={setIsUploadDialogOpen}
        onSuccess={fetchTemplates}
      />

      <CreateReconciliationDialog
          isOpen={isCreateDialogOpen}
          onClose={() => setIsCreateDialogOpen(false)}
          onSuccess={() => {
             fetchReconciliations(); // Refetch list after creation
             setActiveTab('reconciliations'); // Switch to reconciliation tab
             // Polling will be triggered by the useEffect hook
          }}
       />

      {/* --- START: Confirmation Dialog --- */} 
      <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
          <AlertDialogContent>
              <AlertDialogHeader>
                  <AlertDialogTitle>Xác nhận {confirmAction === 'finalize' ? 'Chốt' : 'Xóa'}?</AlertDialogTitle>
                  <AlertDialogDescription>
                      {confirmAction === 'finalize'
                          ? "Hành động này sẽ chốt bản ghi đối soát và không thể hoàn tác."
                          : "Hành động này không thể hoàn tác. Bạn có chắc chắn muốn xóa?"}
                  </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                  <AlertDialogCancel disabled={isConfirmActionLoading}>Hủy</AlertDialogCancel>
                  <AlertDialogAction 
                      onClick={handleConfirmAction} 
                      disabled={isConfirmActionLoading}
                      className={confirmAction === 'delete' ? "bg-destructive text-destructive-foreground hover:bg-destructive/90" : ""}
                  >
                      {isConfirmActionLoading ? 'Đang xử lý...' : `Xác nhận ${confirmAction === 'finalize' ? 'chốt' : 'xóa'}`}
                  </AlertDialogAction>
              </AlertDialogFooter>
          </AlertDialogContent>
      </AlertDialog>
      {/* --- END: Confirmation Dialog --- */} 
    </div>
  );
} 