import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { reconciliationService } from '@/services/reconciliation-service';
import type { ReconciliationDetail, ReconciliationType, DauSoDichVuDetail, CuocGoiDetail, CuocThueBaoDetail } from '@/types/reconciliation';
import { Badge } from "@/components/ui/badge";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft, AlertCircle, Save, XCircle, Pencil } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { DauSoDichVuTable } from '@/components/reconciliation/dau-so-dich-vu-table';
import { toast } from 'sonner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { formatCurrency, formatNumber } from '@/lib/utils';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter
} from "@/components/ui/dialog";
import { DauSoDichVuNestedEditForm } from '@/components/reconciliation/dau-so-dich-vu-nested-edit-form';
import { FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { EditFormData, editFormSchema, DauSoDichVuDetail as FormDauSoDichVuDetail, ReconciliationStatus } from '@/types/reconciliation';
import * as z from 'zod';
import { isDscDetail, isDscdDetail } from '@/types/reconciliation-guards';
import { DscNestedEditForm } from '@/components/reconciliation/dsc-nested-edit-form';

// --- Define Schema for a Single Row for the Dialog Form ---
const singleDauSoSchema = z.object({
    id: z.number(),
    cuoc_tra_htc_adjusted: z.union([z.number(), z.null()]).optional(),
    co_dinh_noi_hat: z.object({
        thoi_gian_goi_adjusted: z.union([z.number(), z.null()]).optional(),
        cuoc_adjusted: z.union([z.number(), z.null()]).optional(),
    }).optional(),
    co_dinh_lien_tinh: z.object({
        thoi_gian_goi_adjusted: z.union([z.number(), z.null()]).optional(),
        cuoc_adjusted: z.union([z.number(), z.null()]).optional(),
    }).optional(),
    di_dong: z.object({
        thoi_gian_goi_adjusted: z.union([z.number(), z.null()]).optional(),
        cuoc_adjusted: z.union([z.number(), z.null()]).optional(),
    }).optional(),
    cuoc_1900: z.object({
        thoi_gian_goi_adjusted: z.union([z.number(), z.null()]).optional(),
        cuoc_adjusted: z.union([z.number(), z.null()]).optional(),
    }).optional(),
    quoc_te: z.object({
        thoi_gian_goi_adjusted: z.union([z.number(), z.null()]).optional(),
        cuoc_adjusted: z.union([z.number(), z.null()]).optional(),
    }).optional(),
    cuoc_thue_bao: z.object({
        thue_bao_thang_adjusted: z.union([z.number(), z.null()]).optional(),
        cam_ket_thang_adjusted: z.union([z.number(), z.null()]).optional(),
        tra_truoc_thang_adjusted: z.union([z.number(), z.null()]).optional(),
    }).optional(),
});

const getStatusVariant = (status: string | undefined): "default" | "secondary" | "outline" | "destructive" => {
  switch (status?.toLowerCase()) {
    case 'finalized': return 'default';
    case 'adjusted': return 'secondary';
    case 'calculated': return 'outline';
    case 'processing': return 'destructive';
    default: return 'outline';
  }
};

const formatDateSafe = (dateInput: string | Date | undefined): string => {
  if (!dateInput) return 'N/A';
  try {
    const date = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
    return format(date, 'dd/MM/yyyy HH:mm:ss');
  } catch (error) {
    console.error("Error formatting date:", error);
    return 'Invalid Date';
  }
};

export function ReconciliationDetailPage() {
  const { id, type } = useParams<{ id: string; type: string }>();
  const navigate = useNavigate();
  const [data, setData] = useState<ReconciliationDetail | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [isInEditMode, setIsInEditMode] = useState<boolean>(false);
  const [adjustedData, setAdjustedData] = useState<ReconciliationDetail | null>(null);
  const [activeTab, setActiveTab] = useState<string>("thuc-tinh");
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [editingRowIndex, setEditingRowIndex] = useState<number | null>(null);

  const dialogFormMethods = useForm<FormDauSoDichVuDetail>({
      resolver: zodResolver(singleDauSoSchema),
  });

  const reconciliationId = useMemo(() => (id ? parseInt(id, 10) : NaN), [id]);
  const reconciliationType = useMemo(() => (type?.toUpperCase() as ReconciliationType | undefined), [type]);

  const fetchData = useCallback(async () => {
    if (isNaN(reconciliationId) || !reconciliationType) {
      setError(new Error("Invalid ID or Type provided in URL."));
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const result = await reconciliationService.getReconciliationById(reconciliationId, reconciliationType);
      setData(result);
      setAdjustedData(JSON.parse(JSON.stringify(result))); 
      setIsInEditMode(result.status !== ReconciliationStatus.FINALIZED && result.status !== ReconciliationStatus.PROCESSING);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to load reconciliation details.'));
      setData(null);
      setAdjustedData(null);
    } finally {
      setIsLoading(false);
    }
  }, [reconciliationId, reconciliationType]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const recalculateAdjustedTotals = useCallback(() => {
    setAdjustedData(prev => {
      if (!prev || !prev.du_lieu || !isInEditMode) return prev;

      let newCongTienDichVu = 0;
      prev.du_lieu.forEach((item: DauSoDichVuDetail) => {
        const getVal = (obj: any, key: string) => obj?.[`${key}_adjusted`] ?? obj?.[key] ?? 0;

        const cdnhCuoc = getVal(item.co_dinh_noi_hat, 'cuoc');
        const cdltCuoc = getVal(item.co_dinh_lien_tinh, 'cuoc');
        const ddCuoc = getVal(item.di_dong, 'cuoc');
        const c1900Cuoc = getVal(item.cuoc_1900, 'cuoc');
        const qtCuoc = getVal(item.quoc_te, 'cuoc');
        const tbThang = getVal(item.cuoc_thue_bao, 'thue_bao_thang');
        const ckThang = getVal(item.cuoc_thue_bao, 'cam_ket_thang');
        const ttThang = getVal(item.cuoc_thue_bao, 'tra_truoc_thang');

        const itemTotal = cdnhCuoc + cdltCuoc + ddCuoc + c1900Cuoc + qtCuoc + tbThang + ckThang - ttThang;
        newCongTienDichVu += itemTotal;
      });

      const VAT_RATE = 0.10;
      const newTienThueGTGT = newCongTienDichVu * VAT_RATE;
      const newTongCongTien = newCongTienDichVu + newTienThueGTGT;

      const currentTongKet = prev.tong_ket ?? { id: 0, cong_tien_dich_vu: 0, tien_thue_gtgt: 0, tong_cong_tien: 0 };
      
      if (
          currentTongKet.cong_tien_dich_vu_adjusted === newCongTienDichVu &&
          currentTongKet.tien_thue_gtgt_adjusted === newTienThueGTGT &&
          currentTongKet.tong_cong_tien_adjusted === newTongCongTien
      ) {
          return prev;
      }

      return {
        ...prev,
        tong_ket: {
          ...currentTongKet,
          cong_tien_dich_vu_adjusted: newCongTienDichVu,
          tien_thue_gtgt_adjusted: newTienThueGTGT,
          tong_cong_tien_adjusted: newTongCongTien,
        },
      };
    });
  }, [isInEditMode]);

  useEffect(() => {
      if (isInEditMode && adjustedData?.du_lieu) {
           recalculateAdjustedTotals();
      }
   }, [adjustedData?.du_lieu, isInEditMode, recalculateAdjustedTotals]);
  

  const handleDetailAdjustmentSave = useCallback((index: number, updatedFormData: Partial<FormDauSoDichVuDetail>) => {
    setAdjustedData(prev => {
        if (!prev || !prev.du_lieu || index < 0 || index >= prev.du_lieu.length) return prev;
    
        const itemToUpdate = JSON.parse(JSON.stringify(prev.du_lieu[index]));
    
        const mergeAdjustments = (target: any, source: any) => {
            if (!source || typeof source !== 'object') return;
            
            Object.keys(source).forEach(key => {
                if (key === 'id') return;
                
                const sourceValue = source[key];
                
                if (sourceValue !== null && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
                    if (!target[key] || typeof target[key] !== 'object') { 
                        target[key] = {};
                    }
                    mergeAdjustments(target[key], sourceValue);
                } 
                else if (key.endsWith('_adjusted') && sourceValue !== undefined) { 
                    target[key] = sourceValue;
                } 
            });
        };

        mergeAdjustments(itemToUpdate, updatedFormData);

        const getVal = (obj: any, key: string) => obj?.[`${key}_adjusted`] ?? obj?.[key] ?? 0;
        const cdnhCuoc = getVal(itemToUpdate.co_dinh_noi_hat, 'cuoc');
        const cdltCuoc = getVal(itemToUpdate.co_dinh_lien_tinh, 'cuoc');
        const ddCuoc = getVal(itemToUpdate.di_dong, 'cuoc');
        const c1900Cuoc = getVal(itemToUpdate.cuoc_1900, 'cuoc');
        const qtCuoc = getVal(itemToUpdate.quoc_te, 'cuoc');
        const tbThang = getVal(itemToUpdate.cuoc_thue_bao, 'thue_bao_thang');
        const ckThang = getVal(itemToUpdate.cuoc_thue_bao, 'cam_ket_thang');
        const ttThang = getVal(itemToUpdate.cuoc_thue_bao, 'tra_truoc_thang');

        itemToUpdate.cuoc_tra_htc_adjusted = cdnhCuoc + cdltCuoc + ddCuoc + c1900Cuoc + qtCuoc + tbThang + ckThang - ttThang;

        const newDuLieu = [...prev.du_lieu];
        newDuLieu[index] = itemToUpdate;

        return {
            ...prev,
            du_lieu: newDuLieu
        };
    });

    setIsDialogOpen(false);
    setEditingRowIndex(null);
  }, []);

  const handleDialogFormSubmit = (formData: FormDauSoDichVuDetail) => {
    if (editingRowIndex === null) return;
    console.log("Dialog form submitted data:", formData);
    handleDetailAdjustmentSave(editingRowIndex, formData); 
  };

  const openEditDialog = (index: number) => {
    const rowData = adjustedData?.du_lieu?.[index];
    if (!rowData) return;
    
    const defaultValues: Partial<FormDauSoDichVuDetail> = {
      id: rowData.id,
      cuoc_tra_htc_adjusted: rowData.cuoc_tra_htc_adjusted ?? rowData.cuoc_tra_htc,
      co_dinh_noi_hat: {
        thoi_gian_goi_adjusted: rowData.co_dinh_noi_hat?.thoi_gian_goi_adjusted ?? rowData.co_dinh_noi_hat?.thoi_gian_goi,
        cuoc_adjusted: rowData.co_dinh_noi_hat?.cuoc_adjusted ?? rowData.co_dinh_noi_hat?.cuoc,
      },
      co_dinh_lien_tinh: {
        thoi_gian_goi_adjusted: rowData.co_dinh_lien_tinh?.thoi_gian_goi_adjusted ?? rowData.co_dinh_lien_tinh?.thoi_gian_goi,
        cuoc_adjusted: rowData.co_dinh_lien_tinh?.cuoc_adjusted ?? rowData.co_dinh_lien_tinh?.cuoc,
      },
      di_dong: {
        thoi_gian_goi_adjusted: rowData.di_dong?.thoi_gian_goi_adjusted ?? rowData.di_dong?.thoi_gian_goi,
        cuoc_adjusted: rowData.di_dong?.cuoc_adjusted ?? rowData.di_dong?.cuoc,
      },
      cuoc_1900: {
        thoi_gian_goi_adjusted: rowData.cuoc_1900?.thoi_gian_goi_adjusted ?? rowData.cuoc_1900?.thoi_gian_goi,
        cuoc_adjusted: rowData.cuoc_1900?.cuoc_adjusted ?? rowData.cuoc_1900?.cuoc,
      },
      quoc_te: {
        thoi_gian_goi_adjusted: rowData.quoc_te?.thoi_gian_goi_adjusted ?? rowData.quoc_te?.thoi_gian_goi,
        cuoc_adjusted: rowData.quoc_te?.cuoc_adjusted ?? rowData.quoc_te?.cuoc,
      },
      cuoc_thue_bao: {
        thue_bao_thang_adjusted: rowData.cuoc_thue_bao?.thue_bao_thang_adjusted ?? rowData.cuoc_thue_bao?.thue_bao_thang,
        cam_ket_thang_adjusted: rowData.cuoc_thue_bao?.cam_ket_thang_adjusted ?? rowData.cuoc_thue_bao?.cam_ket_thang,
        tra_truoc_thang_adjusted: rowData.cuoc_thue_bao?.tra_truoc_thang_adjusted ?? rowData.cuoc_thue_bao?.tra_truoc_thang,
      },
    };

    dialogFormMethods.reset(defaultValues);
    setEditingRowIndex(index);
    setIsDialogOpen(true);
  };

  const handleEditClick = () => {
    if (data) {
      setAdjustedData(JSON.parse(JSON.stringify(data))); 
    }
    setIsInEditMode(true);
    setActiveTab("hieu-chinh");
  };

  const handleCancelClick = () => {
    if (data) {
      setAdjustedData(JSON.parse(JSON.stringify(data))); 
    }
    setIsInEditMode(false);
    setActiveTab("thuc-tinh");
  };

  const handleSaveClick = async () => {
    if (!adjustedData || !reconciliationType || isNaN(reconciliationId)) {
      toast.error("Dữ liệu hoặc loại đối soát không hợp lệ.");
      return;
    }
    if (!data) {
      toast.error("Dữ liệu gốc không tồn tại để so sánh.");
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      type AdjustmentApiPayload = {
          tong_ket?: { [key: string]: number | null } | null;
          du_lieu?: { id: number; [key: string]: any }[] | null;
      };
      const payload: AdjustmentApiPayload = {};
      let hasChanges = false;

      if (adjustedData.tong_ket && data.tong_ket) {
          const tkChanges: { [key: string]: number | null } = {};
          const adjTk = adjustedData.tong_ket;
          const origTk = data.tong_ket;

          const fieldsToCompare: (keyof typeof adjTk)[] = [
              'cong_tien_dich_vu_adjusted', 
              'tien_thue_gtgt_adjusted', 
              'tong_cong_tien_adjusted'
          ];

          fieldsToCompare.forEach(field => {
              const originalValue = origTk[field] ?? (field === 'cong_tien_dich_vu_adjusted' ? origTk.cong_tien_dich_vu : 
                                      field === 'tien_thue_gtgt_adjusted' ? origTk.tien_thue_gtgt : 
                                      field === 'tong_cong_tien_adjusted' ? origTk.tong_cong_tien : null);
                                      
              if (adjTk[field] !== originalValue && adjTk[field] !== undefined) { 
                  tkChanges[field] = adjTk[field];
                  hasChanges = true;
              }
          });

          if (Object.keys(tkChanges).length > 0) {
              payload.tong_ket = tkChanges;
          }
      }

      if (adjustedData.du_lieu && data.du_lieu) {
          const duLieuChanges = adjustedData.du_lieu.map((adjItem: DauSoDichVuDetail) => {
              const origItem = data.du_lieu?.find(d => d.id === adjItem.id);
              if (!origItem) return null;

              const itemChanges: { id: number; [key: string]: any } = { id: adjItem.id };
              let itemHasChanged = false;

              const compareNested = (adjNested: any, origNested: any, fields: string[], updateKey: string) => {
                  if (!adjNested && !origNested) return;

                  const nestedChanges: { [key: string]: number | null } = {};
                  let nestedHasChanged = false;
                  
                  fields.forEach(baseField => {
                      const adjFieldKey = `${baseField}_adjusted`;
                      const adjValue = adjNested?.[adjFieldKey];
                      const originalValue = origNested?.[adjFieldKey] ?? origNested?.[baseField]; 
                      
                      if (adjValue !== originalValue && adjValue !== undefined) {
                          nestedChanges[adjFieldKey] = adjValue;
                          nestedHasChanged = true;
                      }
                  });

                  if (nestedHasChanged) {
                      itemChanges[updateKey] = nestedChanges;
                      itemHasChanged = true;
                  }
              };

              const topLevelAdjFields: (keyof Pick<DauSoDichVuDetail, 'cuoc_thu_khach_adjusted' | 'cuoc_tra_htc_adjusted'>)[] = [
                   'cuoc_thu_khach_adjusted', 'cuoc_tra_htc_adjusted'
              ];
               topLevelAdjFields.forEach(field => {
                    const adjValue = adjItem[field];
                    const originalValue = origItem[field] ?? origItem[field.replace('_adjusted', '') as keyof DauSoDichVuDetail];
                    if (adjValue !== originalValue && adjValue !== undefined) {
                         itemChanges[field] = adjValue;
                         itemHasChanged = true;
                    }
               });

              compareNested(adjItem.co_dinh_noi_hat, origItem.co_dinh_noi_hat, ['thoi_gian_goi', 'cuoc'], 'co_dinh_noi_hat');
              compareNested(adjItem.co_dinh_lien_tinh, origItem.co_dinh_lien_tinh, ['thoi_gian_goi', 'cuoc'], 'co_dinh_lien_tinh');
              compareNested(adjItem.di_dong, origItem.di_dong, ['thoi_gian_goi', 'cuoc'], 'di_dong');
              compareNested(adjItem.cuoc_1900, origItem.cuoc_1900, ['thoi_gian_goi', 'cuoc'], 'cuoc_1900');
              compareNested(adjItem.quoc_te, origItem.quoc_te, ['thoi_gian_goi', 'cuoc'], 'quoc_te');
              compareNested(adjItem.cuoc_thue_bao, origItem.cuoc_thue_bao, ['thue_bao_thang', 'cam_ket_thang', 'tra_truoc_thang'], 'cuoc_thue_bao');

              return itemHasChanged ? itemChanges : null;
          }).filter(item => item !== null);

          if (duLieuChanges.length > 0) {
              payload.du_lieu = duLieuChanges;
              hasChanges = true;
          }
      }

      if (!hasChanges) {
        toast.info("Không có thay đổi nào để lưu.");
        setIsSaving(false);
        setIsInEditMode(false);
        setActiveTab("thuc-tinh");
        return;
      }

      console.log("Sending adjustment payload to API:", JSON.stringify(payload, null, 2));

      const updatedDataResult = await reconciliationService.updateReconciliationAdjustments(
        reconciliationId,
        reconciliationType,
        payload
      );

      toast.success("Hiệu chỉnh đã được lưu thành công!");
      setIsInEditMode(false);
      setActiveTab("thuc-tinh");

      setData(updatedDataResult); 
      setAdjustedData(JSON.parse(JSON.stringify(updatedDataResult)));

    } catch (err) {
      console.error("Failed to save adjustments:", err);
      let errorMessage = 'Lưu hiệu chỉnh thất bại. Vui lòng thử lại.';
      if (err instanceof Error) {
         errorMessage = err.message;
       } else if (typeof err === 'string') {
         errorMessage = err;
       }
      toast.error(errorMessage);
      setError(new Error(errorMessage));
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 space-y-4">
        <Skeleton className="h-8 w-1/4" /> 
        <Skeleton className="h-4 w-1/2 mb-6" /> 
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
           <Skeleton className="h-20 w-full" />
           <Skeleton className="h-20 w-full" />
           <Skeleton className="h-20 w-full" />
        </div>
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (error && !isSaving) {
    return (
      <div className="container mx-auto py-6">
        <Button variant="outline" onClick={() => navigate('/reconciliations')} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> Quay lại Danh sách
        </Button>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Lỗi</AlertTitle>
          <AlertDescription>{error.message}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!data || !adjustedData) {
    return (
      <div className="container mx-auto py-6">
        <Button variant="outline" onClick={() => navigate('/reconciliations')} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> Quay lại Danh sách
        </Button>
        <p>Không tìm thấy dữ liệu đối soát.</p>
      </div>
    );
  }

  const canEdit = data.status !== ReconciliationStatus.FINALIZED && data.status !== ReconciliationStatus.PROCESSING;

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-2xl font-bold">
            Đối soát chi tiết - Kỳ {data.thang_doi_soat ?? 'N/A'}
          </h1>
          <p className="text-sm text-muted-foreground">
            ID: {data.id} | Đối tác: {data.partner_name ?? data.partner_id ?? 'N/A'} | Loại: {reconciliationType}
          </p>
        </div>
        <Button variant="outline" onClick={() => navigate('/reconciliations')}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Quay lại Danh sách
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Trạng thái</CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={getStatusVariant(data.status)}>{data.status ?? 'N/A'}</Badge>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Ngày tạo</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{formatDateSafe(data.created_at)}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Cập nhật lần cuối</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{formatDateSafe(data.updated_at)}</p>
          </CardContent>
        </Card>
      </div>

      {canEdit && (
        <div className="flex justify-end space-x-2">
          {!isInEditMode ? (
            <Button onClick={handleEditClick}>
              <Pencil className="mr-2 h-4 w-4" /> Hiệu chỉnh
            </Button>
          ) : (
            <>
              <Button variant="outline" onClick={handleCancelClick} disabled={isSaving}>
                <XCircle className="mr-2 h-4 w-4" /> Hủy
              </Button>
              <Button onClick={handleSaveClick} disabled={isSaving}>
                <Save className="mr-2 h-4 w-4" /> {isSaving ? 'Đang lưu...' : 'Lưu Hiệu chỉnh'}
              </Button>
            </>
          )}
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="thuc-tinh" disabled={isInEditMode && !canEdit}>Thực tính</TabsTrigger>
          <TabsTrigger value="hieu-chinh" disabled={!canEdit}>Hiệu chỉnh</TabsTrigger>
        </TabsList>

        <TabsContent value="thuc-tinh" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tổng kết (Thực tính)</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <TongKetFieldDisplay 
                label="Cộng tiền dịch vụ" 
                originalValue={data.tong_ket?.cong_tien_dich_vu} 
                adjustedValue={data.tong_ket?.cong_tien_dich_vu_adjusted} 
              />
              <TongKetFieldDisplay 
                label="Tiền thuế GTGT" 
                originalValue={data.tong_ket?.tien_thue_gtgt} 
                adjustedValue={data.tong_ket?.tien_thue_gtgt_adjusted} 
              />
               <TongKetFieldDisplay 
                label="Tổng cộng tiền" 
                originalValue={data.tong_ket?.tong_cong_tien} 
                adjustedValue={data.tong_ket?.tong_cong_tien_adjusted} 
                isBold={true}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Chi tiết Dữ Liệu Dịch vụ (Thực tính)</CardTitle>
            </CardHeader>
            <CardContent>
              <DauSoDichVuTable
                data={data.du_lieu ?? []}
                adjustedData={data.du_lieu ?? []} 
                isEditing={false}
                displayMode="actual"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="hieu-chinh" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tổng kết (Hiệu chỉnh)</CardTitle>
              <p className="text-xs text-muted-foreground">Giá trị được tự động tính toán lại sau khi hiệu chỉnh chi tiết.</p>
            </CardHeader>
            <CardContent className="space-y-2">
              <TongKetFieldDisplay 
                label="Cộng tiền dịch vụ" 
                originalValue={data.tong_ket?.cong_tien_dich_vu} 
                adjustedValue={adjustedData.tong_ket?.cong_tien_dich_vu_adjusted} 
              />
              <TongKetFieldDisplay 
                label="Tiền thuế GTGT" 
                originalValue={data.tong_ket?.tien_thue_gtgt} 
                adjustedValue={adjustedData.tong_ket?.tien_thue_gtgt_adjusted} 
              />
               <TongKetFieldDisplay 
                label="Tổng cộng tiền" 
                originalValue={data.tong_ket?.tong_cong_tien} 
                adjustedValue={adjustedData.tong_ket?.tong_cong_tien_adjusted} 
                isBold={true}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle>Chi tiết Dữ Liệu Dịch vụ (Hiệu chỉnh)</CardTitle>
              {!isInEditMode && data?.status !== ReconciliationStatus.FINALIZED && data?.status !== ReconciliationStatus.PROCESSING && (
                  <Button onClick={handleEditClick} size="sm">
                      <Pencil className="mr-2 h-4 w-4" /> Bắt đầu hiệu chỉnh
                  </Button>
              )}
               {(isInEditMode || activeTab === 'hieu-chinh') && (
                <div className="flex space-x-2">
                    <Button onClick={handleCancelClick} variant="outline" size="sm">
                        <XCircle className="mr-2 h-4 w-4" /> Hủy
                    </Button>
                    <Button onClick={handleSaveClick} size="sm" disabled={isSaving}>
                        <Save className="mr-2 h-4 w-4" /> {isSaving ? 'Đang lưu...' : 'Lưu Hiệu chỉnh'}
                    </Button>
                </div>
               )}
            </CardHeader>
            <CardContent>
              <DauSoDichVuTable
                data={data.du_lieu ?? []} 
                adjustedData={adjustedData.du_lieu ?? []}
                isEditing={isInEditMode}
                displayMode="adjusted"
                onAdjustedDataChange={() => {}}
                onEditRow={openEditDialog}
              />
            </CardContent>
          </Card>

          <Dialog open={isDialogOpen} onOpenChange={(open) => { if (!open) { setEditingRowIndex(null); setIsDialogOpen(false); } else {setIsDialogOpen(true); } }}>
            <DialogContent className="sm:max-w-[700px]">
              <DialogHeader>
                <DialogTitle>
                    Hiệu chỉnh Chi tiết: {
                        isDscdDetail(data) ? (adjustedData as DscdReconciliationDetail)?.du_lieu?.[editingRowIndex ?? -1]?.standardized_display
                      : isDscDetail(data) ? (adjustedData as DscReconciliationDetail)?.du_lieu?.[editingRowIndex ?? -1]?.dau_so
                      : 'N/A'
                    }
                </DialogTitle>
                <DialogDescription>
                  Chỉnh sửa thời gian và cước chi tiết cho đầu số này. Nhấn Lưu chi tiết để cập nhật tạm thời, sau đó Lưu Hiệu chỉnh để lưu vào hệ thống.
                </DialogDescription>
              </DialogHeader>
              
              {editingRowIndex !== null && adjustedData?.du_lieu?.[editingRowIndex] ? (
                <div className="py-4 max-h-[60vh] overflow-y-auto">
                  {isDscdDetail(data) && (
                      <DauSoDichVuNestedEditForm 
                        fieldIndex={editingRowIndex}
                        initialItemData={(adjustedData as DscdReconciliationDetail)?.du_lieu?.[editingRowIndex]} 
                        onSave={(updatedRowData) => handleDetailAdjustmentSave(editingRowIndex, updatedRowData)} 
                      />
                  )}
                   {isDscDetail(data) && (
                      <DscNestedEditForm 
                        fieldIndex={editingRowIndex} 
                        initialItemData={(adjustedData as DscReconciliationDetail)?.du_lieu?.[editingRowIndex] ?? null} 
                        onSave={(updatedRowData) => handleDetailAdjustmentSave(editingRowIndex, updatedRowData)} 
                      />
                  )}
                  <DialogFooter className="mt-4">
                    <Button type="button" variant="outline" onClick={() => { setIsDialogOpen(false); setEditingRowIndex(null); }}>Hủy</Button>
                    <Button 
                      type="button" 
                      onClick={() => {
                        console.warn("Dialog save button needs mechanism to trigger nested form save.");
                      }}
                    >
                      Lưu chi tiết (Tạm thời vô hiệu) 
                    </Button>
                  </DialogFooter>
                </div>
              ) : (
                <p>Không thể tải dữ liệu để chỉnh sửa.</p>
              )}
            </DialogContent>
          </Dialog>
        </TabsContent>
      </Tabs>
    </div>
  );
}

interface TongKetDisplayProps {
  label: string;
  originalValue: number | null | undefined;
  adjustedValue: number | null | undefined;
  isBold?: boolean;
}

const TongKetFieldDisplay: React.FC<TongKetDisplayProps> = ({ label, originalValue, adjustedValue, isBold = false }) => {
  const displayValue = adjustedValue ?? originalValue;
  const showOriginal = adjustedValue !== null && adjustedValue !== undefined && adjustedValue !== originalValue;

  return (
    <div className={`flex justify-between items-center ${isBold ? 'text-lg' : ''}`}>
      <Label>{label}:</Label>
      <div>
        {showOriginal && (
          <span className="text-xs text-muted-foreground line-through mr-2">{formatCurrency(originalValue)}</span>
        )}
        <span className={`font-medium ${isBold ? 'font-bold' : ''}`}>{formatCurrency(displayValue)}</span>
      </div>
    </div>
  );
};

export default ReconciliationDetailPage; 