import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { PartnersList } from "@/components/partners/partners-list";
import { PartnerForm } from "@/components/partners/partner-form";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Partner, PartnerFormData, PartnerType } from "@/types/partner";
import { partnerService } from "@/services/partner-service";
import { toast } from "@/components/ui/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { PaginatedResponse } from "@/types/pricing";

export default function PartnersPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [partnerToEdit, setPartnerToEdit] = useState<Partner | null>(null);
  const [partnerTypeFilter, setPartnerTypeFilter] = useState<PartnerType | null>(null);

  const queryClient = useQueryClient();

  // Fetch partners
  const { data: partnerData, isLoading } = useQuery<PaginatedResponse<Partner>>({
    queryKey: ["partners"],
    queryFn: () => partnerService.getPartners({ limit: 1000 }),
  });
  
  const partners = partnerData?.items ?? [];
  const totalPartners = partnerData?.total ?? 0;

  // Filter partners by type if filter is active
  const filteredPartners = partnerTypeFilter 
    ? partners.filter(partner => partner.type === partnerTypeFilter)
    : partners;

  // Create partner mutation
  const createPartner = useMutation({
    mutationFn: partnerService.createPartner,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["partners"] });
      setIsCreateDialogOpen(false);
      toast({
        title: "Success",
        description: "Partner created successfully",
      });
    },
  });

  // Update partner mutation
  const updatePartner = useMutation({
    mutationFn: ({ id, data }: { id: number; data: PartnerFormData }) =>
      partnerService.updatePartner(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["partners"] });
      setPartnerToEdit(null);
      toast({
        title: "Success",
        description: "Partner updated successfully",
      });
    },
  });

  // Delete partner mutation
  const deletePartner = useMutation({
    mutationFn: (partner: Partner) => partnerService.deletePartner(partner.id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["partners"] });
      toast({
        title: "Success",
        description: "Partner deleted successfully",
      });
    },
  });

  // Toggle partner status mutation
  const togglePartnerStatus = useMutation({
    mutationFn: (partner: Partner) => partnerService.togglePartnerStatus(partner.id),
    onSuccess: (_: unknown, partner: Partner) => {
      queryClient.invalidateQueries({ queryKey: ["partners"] });
      toast({
        title: "Success",
        description: `Partner ${partner.is_active ? "deactivated" : "activated"} successfully`,
      });
    },
  });

  const handleCreateSubmit = async (data: PartnerFormData) => {
    await createPartner.mutateAsync(data);
  };

  const handleUpdateSubmit = async (data: PartnerFormData) => {
    if (partnerToEdit) {
      await updatePartner.mutateAsync({ id: partnerToEdit.id, data });
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-[200px] items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Partners</h1>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Partner
        </Button>
      </div>

      <PartnersList
        partners={filteredPartners}
        onEdit={setPartnerToEdit}
        onDelete={deletePartner.mutateAsync}
        onToggleActive={togglePartnerStatus.mutateAsync}
        onFilterChange={setPartnerTypeFilter}
        activeFilter={partnerTypeFilter}
      />

      {/* Create Partner Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create Partner</DialogTitle>
          </DialogHeader>
          <PartnerForm
            onSubmit={handleCreateSubmit}
            isLoading={createPartner.isPending}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Partner Dialog */}
      <Dialog open={!!partnerToEdit} onOpenChange={() => setPartnerToEdit(null)}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Partner</DialogTitle>
          </DialogHeader>
          {partnerToEdit && (
            <PartnerForm
              initialData={partnerToEdit}
              onSubmit={handleUpdateSubmit}
              isLoading={updatePartner.isPending}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 