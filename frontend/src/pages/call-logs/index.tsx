import { useState } from "react";
import { CallLogUpload } from "@/components/call-logs/CallLogUpload";
import { CallLogFileList } from "@/components/call-logs/CallLogFileList";
import { CallLogFile } from "@/types/call-log";

export default function CallLogsPage() {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleUploadSuccess = (file: CallLogFile) => {
    // Trigger a refresh of the file list
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="container py-6 space-y-8">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Upload</h1>
        <p className="text-muted-foreground">
          Upload and manage call log files for pricing analysis
        </p>
      </div>

      <div className="grid grid-cols-1 gap-8">
        <CallLogUpload onUploadSuccess={handleUploadSuccess} />
        <CallLogFileList 
          refreshTrigger={refreshTrigger} 
          onRefresh={() => setRefreshTrigger(prev => prev + 1)} 
        />
      </div>
    </div>
  );
} 