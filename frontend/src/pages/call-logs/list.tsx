import { useState } from "react";
import { CallLogList } from "@/components/call-logs/CallLogList";

export default function CallLogListPage() {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="container py-6 space-y-8">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Nhật Ký Cuộc Gọi</h1>
        <p className="text-muted-foreground">
          Xem và tìm kiếm nhật ký cuộc gọi từ tất cả các tệp đã tải lên
        </p>
      </div>

      <CallLogList 
        refreshTrigger={refreshTrigger}
        onRefresh={handleRefresh}
      />
    </div>
  );
} 