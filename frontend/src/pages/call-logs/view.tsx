import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { CallLogList } from "@/components/call-logs/CallLogList";
import { callLogService } from "@/services/call-log-service";
import { CallLogFile, FileStatus } from "@/types/call-log";

export default function ViewCallLogsPage() {
  const { fileId } = useParams<{ fileId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [file, setFile] = useState<CallLogFile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    if (!fileId) {
      navigate("/call-logs");
      return;
    }

    const fetchFile = async () => {
      setIsLoading(true);
      try {
        const data = await callLogService.getFile(parseInt(fileId));
        setFile(data);
        
        // Nếu file vẫn đang xử lý, thiết lập polling
        if (data.status === FileStatus.PROCESSING) {
          const interval = setInterval(() => {
            updateFileStatus();
          }, 3000);
          
          return () => clearInterval(interval);
        }
      } catch (error: any) {
        toast({
          variant: "destructive",
          title: "Lỗi khi tải file",
          description: error.message || "Đã xảy ra lỗi không xác định",
        });
        navigate("/call-logs");
      } finally {
        setIsLoading(false);
      }
    };

    fetchFile();
  }, [fileId, navigate, refreshTrigger]);

  const updateFileStatus = async () => {
    if (!fileId) return;
    
    try {
      const data = await callLogService.getFile(parseInt(fileId));
      setFile(data);
    } catch (error) {
      console.error("Lỗi khi cập nhật trạng thái file:", error);
    }
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="container py-6 space-y-8">
      <div className="flex items-center gap-4">
        <Button 
          variant="outline" 
          size="icon"
          onClick={() => navigate("/call-logs")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex flex-col gap-1">
          <h1 className="text-3xl font-bold tracking-tight">Nhật Ký Cuộc Gọi</h1>
          {file && (
            <p className="text-muted-foreground">
              Xem nhật ký từ file: {file.filename}
            </p>
          )}
        </div>
      </div>

      <CallLogList 
        fileId={fileId ? parseInt(fileId) : undefined}
        refreshTrigger={refreshTrigger}
        onRefresh={handleRefresh}
      />
    </div>
  );
} 