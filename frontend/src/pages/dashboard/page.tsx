import { useAuth } from '@/hooks/auth/useAuth';
import { StatsCard } from '@/components/dashboard/stats-card';
import { Phone, Users, Clock, TrendingUp } from 'lucide-react';

export default function DashboardPage() {
  const { user } = useAuth();

  const stats = [
    {
      title: 'Total Calls',
      value: '2,345',
      icon: Phone,
      description: 'Last 30 days',
      trend: { value: 12, isPositive: true },
    },
    {
      title: 'Active Users',
      value: '18',
      icon: Users,
      description: 'Currently online',
    },
    {
      title: 'Average Duration',
      value: '15m',
      icon: Clock,
      description: 'Per call',
      trend: { value: 5, isPositive: true },
    },
    {
      title: 'Success Rate',
      value: '98.5%',
      icon: TrendingUp,
      description: 'Last 7 days',
      trend: { value: 2, isPositive: true },
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        <p className="text-muted-foreground">
          Welcome back, {user?.full_name}
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <StatsCard key={stat.title} {...stat} />
        ))}
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <div className="rounded-lg border bg-card p-6">
          <h3 className="font-semibold">Recent Activity</h3>
          {/* Add recent activity list here */}
        </div>
        <div className="rounded-lg border bg-card p-6">
          <h3 className="font-semibold">Quick Actions</h3>
          {/* Add quick actions here */}
        </div>
      </div>
    </div>
  );
} 