import { useAuth } from "@/hooks/auth/useAuth";
import { StatsCard } from "@/components/dashboard/stats-card";
import { Activity, Users, FileText, CheckCircle } from "lucide-react";

const stats = [
  {
    title: "Total Audits",
    value: "89",
    icon: FileText,
    description: "Audits completed this month",
  },
  {
    title: "Active Users",
    value: "12",
    icon: Users,
    description: "Users currently online",
  },
  {
    title: "Recent Activity",
    value: "24",
    icon: Activity,
    description: "Actions in last 24 hours",
  },
  {
    title: "Completed Tasks",
    value: "156",
    icon: CheckCircle,
    description: "Tasks completed this week",
  },
];

export default function DashboardPage() {
  const { user } = useAuth();

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Welcome back, {user?.full_name}</h2>
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <StatsCard
            key={stat.title}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            description={stat.description}
          />
        ))}
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <div className="col-span-4">
          <div className="flex h-full items-center justify-center rounded-lg border bg-card p-8">
            <div className="flex flex-col items-center space-y-2 text-center">
              <p className="text-sm text-muted-foreground">
                Recent Activity section coming soon...
              </p>
            </div>
          </div>
        </div>
        <div className="col-span-3">
          <div className="flex h-full items-center justify-center rounded-lg border bg-card p-8">
            <div className="flex flex-col items-center space-y-2 text-center">
              <p className="text-sm text-muted-foreground">
                Quick Actions section coming soon...
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 