import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { 
  reconciliationPeriodService, 
  ReconciliationPeriod 
} from '@/services/reconciliation-period-service';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Loader2, ArrowLeft, FileText, Clock, Calendar, Info } from 'lucide-react';

interface ReportCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: React.ReactNode;
}

function ReportCard({ title, value, description, icon }: ReportCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
      </CardContent>
    </Card>
  );
}

function StatusBadge({ status }: { status: string }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-blue-500';
      case 'processing':
        return 'bg-yellow-500';
      case 'completed':
        return 'bg-green-500';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Chưa xử lý';
      case 'processing':
        return 'Đang xử lý';
      case 'completed':
        return 'Hoàn thành';
      case 'failed':
        return 'Lỗi';
      default:
        return status;
    }
  };

  return (
    <Badge className={getStatusColor(status)}>{getStatusText(status)}</Badge>
  );
}

export default function ReconciliationPeriodDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [period, setPeriod] = useState<ReconciliationPeriod | null>(null);
  const { toast } = useToast();

  const fetchPeriod = async () => {
    if (!id) return;
    
    try {
      setIsLoading(true);
      const response = await reconciliationPeriodService.getReconciliationPeriod(parseInt(id));
      setPeriod(response.data || null);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Lỗi khi tải dữ liệu',
        description: error.message || 'Đã có lỗi xảy ra',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPeriod();
  }, [id]);

  if (isLoading) {
    return (
      <div className="container py-8 space-y-4">
        <Skeleton className="h-10 w-1/4" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (!period) {
    return (
      <div className="container py-8">
        <div className="text-center space-y-4">
          <h2 className="text-2xl font-bold">Không tìm thấy kỳ đối soát</h2>
          <p>Kỳ đối soát không tồn tại hoặc đã bị xóa.</p>
          <Button onClick={() => navigate('/reconciliation-periods')}>
            Quay lại danh sách
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Button
          variant="outline"
          className="mb-4"
          onClick={() => navigate('/reconciliation-periods')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Quay lại danh sách
        </Button>
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">{period.name}</h1>
            <div className="flex items-center gap-2 mt-1">
              <p className="text-gray-500">Tháng: {period.month}/{period.year}</p>
              <StatusBadge status={period.status} />
            </div>
          </div>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 mb-6">
        <ReportCard
          title="Trạng thái"
          value={<StatusBadge status={period.status} />}
          icon={<Info className="h-4 w-4 text-muted-foreground" />}
        />
        <ReportCard
          title="Ngày tạo"
          value={new Date(period.created_at).toLocaleDateString('vi-VN')}
          icon={<Calendar className="h-4 w-4 text-muted-foreground" />}
        />
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Tổng quan</TabsTrigger>
          <TabsTrigger value="doi-soat-co-dinh">Đối soát cố định</TabsTrigger>
          <TabsTrigger value="doi-soat-cuoc">Đối soát cước</TabsTrigger>
          <TabsTrigger value="doi-soat-1800-1900">Đối soát 1800/1900</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="p-4 border rounded-md mt-2">
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Thông tin kỳ đối soát</h3>
              <div className="mt-2 space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">ID:</div>
                  <div>{period.id}</div>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">Tháng:</div>
                  <div>{period.month}</div>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">Trạng thái:</div>
                  <div><StatusBadge status={period.status} /></div>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">Người tạo:</div>
                  <div>{period.created_by}</div>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">Ngày tạo:</div>
                  <div>{new Date(period.created_at).toLocaleDateString('vi-VN')}</div>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">Cập nhật lần cuối:</div>
                  <div>{new Date(period.updated_at).toLocaleDateString('vi-VN')}</div>
                </div>
                {period.notes && (
                  <div className="grid grid-cols-2 gap-2">
                    <div className="font-medium">Ghi chú:</div>
                    <div>{period.notes}</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="doi-soat-co-dinh" className="p-4 border rounded-md mt-2">
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium">Đối soát cố định</h3>
            <p className="text-gray-500 mt-1">
              {period.status === 'completed' 
                ? 'Dữ liệu đối soát cố định đã được xử lý. Xem chi tiết tại trang Đối soát.' 
                : 'Chưa có dữ liệu đối soát cố định cho kỳ này.'}
            </p>
            {period.status === 'completed' && (
              <Button className="mt-4" variant="outline" asChild>
                <Link to="/doi-soat?period_id={period.id}&type=co-dinh">
                  Xem đối soát cố định
                </Link>
              </Button>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="doi-soat-cuoc" className="p-4 border rounded-md mt-2">
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium">Đối soát cước</h3>
            <p className="text-gray-500 mt-1">
              {period.status === 'completed' 
                ? 'Dữ liệu đối soát cước đã được xử lý. Xem chi tiết tại trang Đối soát.' 
                : 'Chưa có dữ liệu đối soát cước cho kỳ này.'}
            </p>
            {period.status === 'completed' && (
              <Button className="mt-4" variant="outline" asChild>
                <Link to="/doi-soat?period_id={period.id}&type=cuoc">
                  Xem đối soát cước
                </Link>
              </Button>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="doi-soat-1800-1900" className="p-4 border rounded-md mt-2">
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium">Đối soát 1800/1900</h3>
            <p className="text-gray-500 mt-1">
              {period.status === 'completed' 
                ? 'Dữ liệu đối soát 1800/1900 đã được xử lý. Xem chi tiết tại trang Đối soát.' 
                : 'Chưa có dữ liệu đối soát 1800/1900 cho kỳ này.'}
            </p>
            {period.status === 'completed' && (
              <Button className="mt-4" variant="outline" asChild>
                <Link to="/doi-soat?period_id={period.id}&type=1800-1900">
                  Xem đối soát 1800/1900
                </Link>
              </Button>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 