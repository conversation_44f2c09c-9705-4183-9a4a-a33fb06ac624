import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  reconciliationPeriodService, 
  ReconciliationPeriod,
  PaginatedReconciliationPeriods 
} from '@/services/reconciliation-period-service';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { CreatePeriodDialog } from '@/components/reconciliation-periods/CreatePeriodDialog';

export default function ReconciliationPeriodsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [periods, setPeriods] = useState<PaginatedReconciliationPeriods | null>(null);
  const [page, setPage] = useState(1);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const { toast } = useToast();

  const fetchPeriods = async () => {
    try {
      setIsLoading(true);
      const result = await reconciliationPeriodService.getReconciliationPeriods(page);
      setPeriods(result);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Lỗi khi tải dữ liệu',
        description: error.message || 'Đã có lỗi xảy ra',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPeriods();
  }, [page]);

  const handleCreateSuccess = () => {
    setIsCreateDialogOpen(false);
    fetchPeriods();
    toast({
      title: 'Tạo kỳ đối soát thành công',
      description: 'Kỳ đối soát mới đã được tạo',
    });
  };

  const renderStatus = (status: string) => {
    switch (status) {
      case 'pending':
        return <span className="text-blue-500">Chưa xử lý</span>;
      case 'processing':
        return <span className="text-yellow-500">Đang xử lý</span>;
      case 'completed':
        return <span className="text-green-500">Hoàn thành</span>;
      case 'failed':
        return <span className="text-red-500">Lỗi</span>;
      default:
        return <span>{status}</span>;
    }
  };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Kỳ đối soát</h1>
        <Button onClick={() => setIsCreateDialogOpen(true)}>Tạo kỳ đối soát mới</Button>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
        </div>
      ) : (
        <>
          <div className="rounded-md border">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tên kỳ
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tháng
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Năm
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trạng thái
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Thao tác
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {periods?.data && periods.data.length > 0 ? (
                  periods.data.map((period) => (
                    <tr key={period.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Link
                          to={`/reconciliation-periods/${period.id}`}
                          className="text-blue-600 hover:underline"
                        >
                          {period.name}
                        </Link>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">{period.month}</td>
                      <td className="px-6 py-4 whitespace-nowrap">{period.year}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {renderStatus(period.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex space-x-2">
                          <Link to={`/reconciliation-periods/${period.id}`}>
                            <Button variant="outline" size="sm">
                              Chi tiết
                            </Button>
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center">
                      Không có dữ liệu
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {periods && periods.total > 0 && (
            <div className="flex justify-between items-center mt-4">
              <div>
                Hiển thị {(page - 1) * 10 + 1} đến{' '}
                {Math.min(page * 10, periods.total)} trong số {periods.total} kỳ đối soát
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={page === 1}
                >
                  Trước
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={page * 10 >= periods.total}
                >
                  Sau
                </Button>
              </div>
            </div>
          )}
        </>
      )}

      {/* Dialog tạo kỳ đối soát mới */}
      <CreatePeriodDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSuccess={handleCreateSuccess}
      />
    </div>
  );
} 