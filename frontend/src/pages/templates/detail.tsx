import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { templateService } from "@/services/template-service";
import { DoiSoatTemplate } from "@/models/template";
import { formatDate, formatNumber } from "@/lib/utils";
import { 
  FileSpreadsheet, 
  Download, 
  Play, 
  ArrowLeft, 
  FileSearch, 
  RefreshCw, 
  Calendar, 
  Building, 
  Table
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Table as UITable,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ScrollArea } from "@/components/ui/scroll-area";

export default function TemplateDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [template, setTemplate] = useState<DoiSoatTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedData, setProcessedData] = useState<any>(null);
  const [isLoadingResults, setIsLoadingResults] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (id) {
      fetchTemplate(Number(id));
    }
  }, [id]);

  const fetchTemplate = async (templateId: number) => {
    setIsLoading(true);
    try {
      const data = await templateService.getTemplate(templateId);
      setTemplate(data);
      
      // Nếu template đã xử lý xong, tải luôn kết quả
      if (data.status === 'completed') {
        // Tạm lưu template để có thể gọi fetchProcessedData vì nó cần template
        const tempTemplate = data;
        // Gọi fetchProcessedData với template.id từ dữ liệu mới
        setIsLoadingResults(true);
        try {
          const resultData = await templateService.getTemplateStructuredData(tempTemplate.id);
          console.log("API Response for processed data:", resultData);
          if (resultData) {
            console.log("Processed data structure:", {
              keys: Object.keys(resultData),
              hasData: !!resultData,
              hasDauSoDichVu: resultData && !!resultData.dau_so_dich_vu,
              hasDichVu: resultData && !!resultData.dich_vu,
              hasDuLieu: resultData && !!resultData.du_lieu,
              hasDauSo: resultData && !!resultData.dau_so
            });
            setProcessedData(resultData);
          }
        } catch (error) {
          console.error("Error fetching processed data during template load:", error);
        } finally {
          setIsLoadingResults(false);
        }
        
        // Tải dữ liệu xem trước
        fetchTemplatePreview(templateId);
      }
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể tải thông tin mẫu đối soát",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTemplatePreview = async (templateId: number) => {
    setIsLoadingPreview(true);
    try {
      const preview = await templateService.getTemplatePreview(templateId);
      console.log("Preview data loaded:", preview);
      setPreviewData(preview || []);
    } catch (error) {
      console.error("Error fetching template preview:", error);
      toast({
        title: "Lỗi",
        description: "Không thể tải bản xem trước dữ liệu",
        variant: "destructive"
      });
    } finally {
      setIsLoadingPreview(false);
    }
  };

  const fetchTemplateResults = async (templateId: number) => {
    setIsLoadingResults(true);
    try {
      const data = await templateService.getTemplateStructuredData(templateId);
      console.log("Template results loaded:", data);
      setProcessedData(data);
    } catch (error) {
      console.error("Error fetching template results:", error);
    } finally {
      setIsLoadingResults(false);
    }
  };

  const fetchProcessedData = async () => {
    if (!template) return;
    
    try {
      setIsLoadingResults(true);
      const data = await templateService.getTemplateStructuredData(template.id);
      console.log("API Response for structured data:", data);
      
      if (data) {
        console.log("Structured data format:", {
          keys: Object.keys(data),
          hasData: !!data,
          hasDauSoDichVu: data && !!data.dau_so_dich_vu,
          hasDichVu: data && !!data.dich_vu,
          hasDuLieu: data && !!data.du_lieu,
          hasDauSo: data && !!data.dau_so
        });
        setProcessedData(data);
      } else {
        setProcessedData(null);
      }
    } catch (error) {
      console.error("Error fetching structured data:", error);
      toast({
        title: "Lỗi",
        description: "Không thể tải dữ liệu xử lý mẫu",
        variant: "destructive"
      });
    } finally {
      setIsLoadingResults(false);
    }
  };

  const handleProcess = async () => {
    if (!template) return;
    
    setIsProcessing(true);
    try {
      const response = await templateService.processTemplate(template.id);
      toast({
        title: "Yêu cầu đã được gửi",
        description: "Mẫu đối soát đang được xử lý, vui lòng đợi trong giây lát"
      });
      
      // Cập nhật status của template
      setTimeout(() => {
        fetchTemplate(template.id);
      }, 3000);
      
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể gửi yêu cầu phân tích mẫu đối soát",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRefreshStatus = async () => {
    if (!template) return;
    
    try {
      const status = await templateService.getTemplateStatus(template.id);
      if (status.status !== template.status) {
        fetchTemplate(template.id);
        if (status.status === 'completed') {
          fetchTemplateResults(template.id);
        }
        toast({
          title: "Trạng thái đã cập nhật",
          description: `Trạng thái mới: ${renderStatusText(status.status)}`
        });
      } else {
        toast({
          title: "Không có thay đổi",
          description: "Trạng thái mẫu không thay đổi"
        });
      }
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật trạng thái mẫu",
        variant: "destructive"
      });
    }
  };

  const renderStatus = (status: string) => {
    switch(status) {
      case 'pending':
        return <Badge>Đang chờ</Badge>;
      case 'processing':
        return <Badge variant="secondary">Đang xử lý</Badge>;
      case 'completed':
        return <Badge variant="success">Hoàn thành</Badge>;
      case 'failed':
        return <Badge variant="destructive">Lỗi</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const renderStatusText = (status: string) => {
    switch(status) {
      case 'pending':
        return 'Đang chờ';
      case 'processing':
        return 'Đang xử lý';
      case 'completed':
        return 'Hoàn thành';
      case 'failed':
        return 'Lỗi';
      default:
        return status;
    }
  };

  const renderTemplateType = (type?: string) => {
    // Thêm debug để xem giá trị chi tiết hơn
    console.log("Template type debug (extended):", {
      type: type,
      detected_type: template?.detected_type,
      template_type: template?.template_type,
      template: template
    });
    
    // Lấy giá trị type từ nhiều nguồn khác nhau để đảm bảo luôn có giá trị
    // Ưu tiên: type từ tham số -> template.detected_type -> template.template_type
    const templateType = (type || template?.detected_type || template?.template_type || '').toLowerCase();
    
    // Debug giá trị templateType sau khi xử lý
    console.log("Processed template type:", templateType);
    
    // Nếu templateType trống hoặc undefined hoặc là chuỗi 'undefined' hoặc 'null'
    if (!templateType || templateType === 'undefined' || templateType === 'null') {
      return 'Chưa xác định';
    }
    
    if (templateType.includes('dscd')) {
      return 'Đối soát cố định';
    } else if (templateType.includes('dstt')) {
      return 'Đối soát trực tiếp';
    } else if (templateType.includes('dsc')) {
      return 'Đối soát cước';
    } else if (templateType.includes('1800') || templateType.includes('1900') || templateType.includes('dst')) {
      return 'Đối soát 1800/1900';
    } else {
      return templateType; // Hiển thị giá trị gốc nếu không thuộc các trường hợp đã biết
    }
  };

  const handleDownloadExcel = async () => {
    if (!template) return;
    
    try {
      const blob = await templateService.downloadTemplate(template.id);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${template.file_name.split('.')[0]}_result.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể tải xuống file Excel",
        variant: "destructive"
      });
    }
  };

  // Hàm helper để xác định và hiển thị dữ liệu bảng cho đối soát cước
  const renderDscTableData = (data: any) => {
    if (!data || !data.dau_so_dich_vu || !Array.isArray(data.dau_so_dich_vu) || data.dau_so_dich_vu.length === 0) {
        // Nếu không có dữ liệu chi tiết, thử kiểm tra các cấu trúc khác hoặc hiển thị JSON
        if (data && data.tong_ket) {
            // Chỉ có tổng kết, không có chi tiết
      return (
        <div>
                    <h3 className="text-lg font-medium mb-2">Tổng kết</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 bg-gray-50 p-4 rounded-md">
                        <div>
                            <span className="text-sm text-muted-foreground block">Cộng tiền dịch vụ:</span>
                            <span className="font-medium">{formatNumber(data.tong_ket.cong_tien_dich_vu)} VNĐ</span>
          </div>
                        <div>
                            <span className="text-sm text-muted-foreground block">Tiền thuế GTGT:</span>
                            <span className="font-medium">{formatNumber(data.tong_ket.tien_thue_gtgt)} VNĐ</span>
        </div>
        <div>
                            <span className="text-sm text-muted-foreground block">Tổng cộng tiền:</span>
                            <span className="font-bold text-lg">{formatNumber(data.tong_ket.tong_cong_tien)} VNĐ</span>
                        </div>
          </div>
        </div>
      );
    }
        // Fallback nếu không có cả chi tiết và tổng kết hợp lệ
      return (
        <div>
                <h3 className="text-lg font-medium mb-2">Dữ liệu chi tiết</h3>
                <p className="text-sm text-muted-foreground">Không tìm thấy dữ liệu chi tiết đầu số dịch vụ hợp lệ.</p>
                {/* Tùy chọn: Hiển thị JSON gốc nếu cần debug
                <pre className="p-4 rounded-md bg-gray-100 dark:bg-gray-800 overflow-auto text-sm mt-2">
                  {JSON.stringify(data, null, 2)}
            </pre>
                */}
        </div>
      );
    }
    
    // Có dữ liệu chi tiết dau_so_dich_vu
    return (
      <div>
        <h3 className="text-lg font-medium mb-2">Dữ liệu chi tiết ({data.dau_so_dich_vu.length} đầu số)</h3>
        <div className="overflow-x-auto rounded-lg border">
          <UITable className="min-w-full divide-y divide-gray-200 text-xs">
            <TableHeader>
              <TableRow className="bg-gray-100">
                <TableHead className="px-2 py-2 text-left font-medium text-gray-600 sticky left-0 bg-gray-100 z-10">STT</TableHead>
                <TableHead className="px-2 py-2 text-left font-medium text-gray-600 sticky left-10 bg-gray-100 z-10">Đầu số</TableHead>
                {/* VNM */}
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">SL VNM</TableHead>
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">CP% VNM</TableHead>
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">Tiền VNM</TableHead>
                {/* VIETTEL */}
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">SL VT</TableHead>
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">CP% VT</TableHead>
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">Tiền VT</TableHead>
                {/* VNPT */}
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">SL VNPT</TableHead>
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">CP% VNPT</TableHead>
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">Tiền VNPT</TableHead>
                {/* VMS */}
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">SL VMS</TableHead>
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">CP% VMS</TableHead>
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">Tiền VMS</TableHead>
                {/* KHAC */}
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">SL Khác</TableHead>
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">CP% Khác</TableHead>
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600">Tiền Khác</TableHead>
                {/* TONG */}
                <TableHead className="px-2 py-2 text-center font-medium text-gray-600 sticky right-0 bg-gray-100 z-10">Tổng TT</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.dau_so_dich_vu.map((item: any, index: number) => (
                <TableRow key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                  <TableCell className="px-2 py-2 whitespace-nowrap text-gray-500 sticky left-0 bg-inherit z-10">{item.stt || index + 1}</TableCell>
                  <TableCell className="px-2 py-2 whitespace-nowrap font-medium sticky left-10 bg-inherit z-10">{item.dau_so}</TableCell>
                  {/* VNM */}
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{formatNumber(item.vnm_san_luong)}</TableCell>
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{item.vnm_ty_le_cp}%</TableCell>
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{formatNumber(item.vnm_thanh_tien)}</TableCell>
                  {/* VIETTEL */}
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{formatNumber(item.viettel_san_luong)}</TableCell>
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{item.viettel_ty_le_cp}%</TableCell>
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{formatNumber(item.viettel_thanh_tien)}</TableCell>
                  {/* VNPT */}
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{formatNumber(item.vnpt_san_luong)}</TableCell>
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{item.vnpt_ty_le_cp}%</TableCell>
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{formatNumber(item.vnpt_thanh_tien)}</TableCell>
                  {/* VMS */}
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{formatNumber(item.vms_san_luong)}</TableCell>
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{item.vms_ty_le_cp}%</TableCell>
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{formatNumber(item.vms_thanh_tien)}</TableCell>
                  {/* KHAC */}
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{formatNumber(item.khac_san_luong)}</TableCell>
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{item.khac_ty_le_cp}%</TableCell>
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right text-gray-500">{formatNumber(item.khac_thanh_tien)}</TableCell>
                  {/* TONG */}
                  <TableCell className="px-2 py-2 whitespace-nowrap text-right font-semibold sticky right-0 bg-inherit z-10">{formatNumber(item.tong_thanh_toan)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
            {data.tong_ket && (
              <tfoot>
                <TableRow className="bg-gray-100 font-medium">
                  <TableCell colSpan={2} className="px-2 py-2 text-right sticky left-0 bg-gray-100 z-10"></TableCell>
                  {/* VNM Sum */}
                  <TableCell colSpan={3} className="px-2 py-2 text-right"></TableCell> {/* Placeholder or Sum */} 
                  {/* VIETTEL Sum */}
                  <TableCell colSpan={3} className="px-2 py-2 text-right"></TableCell> {/* Placeholder or Sum */} 
                  {/* VNPT Sum */}
                  <TableCell colSpan={3} className="px-2 py-2 text-right"></TableCell> {/* Placeholder or Sum */} 
                  {/* VMS Sum */}
                  <TableCell colSpan={3} className="px-2 py-2 text-right"></TableCell> {/* Placeholder or Sum */} 
                  {/* KHAC Sum */}
                  <TableCell colSpan={3} className="px-2 py-2 text-right"></TableCell> {/* Placeholder or Sum */} 
                  {/* TONG */}
                  <TableCell className="px-2 py-2 text-right font-bold sticky right-0 bg-gray-100 z-10">{formatNumber(data.tong_ket.cong_tien_dich_vu)}</TableCell>
                </TableRow>
                <TableRow className="bg-gray-100 font-medium">
                    <TableCell colSpan={17} className="px-2 py-2 text-right font-semibold">Cộng tiền dịch vụ:</TableCell>
                    <TableCell className="px-2 py-2 text-right font-bold sticky right-0 bg-gray-100 z-10">{formatNumber(data.tong_ket.cong_tien_dich_vu)}</TableCell>
                </TableRow>
                <TableRow className="bg-gray-100 font-medium">
                    <TableCell colSpan={17} className="px-2 py-2 text-right font-semibold">Tiền thuế GTGT:</TableCell>
                    <TableCell className="px-2 py-2 text-right font-bold sticky right-0 bg-gray-100 z-10">{formatNumber(data.tong_ket.tien_thue_gtgt)}</TableCell>
                </TableRow>
                <TableRow className="bg-gray-100 font-medium">
                    <TableCell colSpan={17} className="px-2 py-2 text-right font-bold text-lg">Tổng cộng tiền thanh toán:</TableCell>
                    <TableCell className="px-2 py-2 text-right font-bold text-lg sticky right-0 bg-gray-100 z-10">{formatNumber(data.tong_ket.tong_cong_tien)}</TableCell>
                </TableRow>
              </tfoot>
            )}
          </UITable>
        </div>
      </div>
    );
  };

  // Render bố cục cho Đối soát cước
  const renderDscTemplate = () => {
    if (!processedData) return null;
    
    // Thêm logging detail
    console.log("Rendering DSC template with data (detailed):", {
      rawData: processedData,
      hasDauSoDichVu: !!processedData.dau_so_dich_vu,
      dauSoDichVuLength: processedData.dau_so_dich_vu ? processedData.dau_so_dich_vu.length : 0,
      hasTongKet: !!processedData.tong_ket,
      tongKetData: processedData.tong_ket,
      // Thử tìm kiếm các thuộc tính có thể có khác
      findArrayProperties: Object.keys(processedData).filter(key => Array.isArray(processedData[key])),
      findObjectProperties: Object.keys(processedData).filter(key => 
        typeof processedData[key] === 'object' && 
        processedData[key] !== null && 
        !Array.isArray(processedData[key])
      ),
      // Cấu trúc dữ liệu chi tiết
      allPossibleListData: [
        processedData.dau_so_dich_vu, 
        processedData.dich_vu, 
        processedData.du_lieu, 
        processedData.result, 
        processedData.data, 
        processedData.items,
        processedData.chi_tiet
      ].filter(Boolean)
    });
    
    return (
      <div className="space-y-6">
        <div className="bg-gray-50 rounded-md p-4">
          <h3 className="text-lg font-medium mb-2">Thông tin đối soát cước</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <span className="text-sm text-muted-foreground block">Tháng đối soát:</span>
              <span className="font-medium">{processedData.thang_doi_soat || processedData.nam_doi_soat || 'N/A'}</span>
            </div>
            <div>
              <span className="text-sm text-muted-foreground block">Từ mạng:</span>
              <span className="font-medium">{processedData.tu_mang || 'N/A'}</span>
            </div>
            <div>
              <span className="text-sm text-muted-foreground block">Đến đối tác:</span>
              <span className="font-medium">{processedData.den_doi_tac || 'N/A'}</span>
            </div>
            {processedData.hop_dong_so && (
              <div>
                <span className="text-sm text-muted-foreground block">Hợp đồng số:</span>
                <span className="font-medium">{processedData.hop_dong_so}</span>
              </div>
            )}
          </div>
        </div>
        
        {/* Sử dụng hàm helper để hiển thị dữ liệu chi tiết phù hợp */}
        {renderDscTableData(processedData)}
      </div>
    );
  };

  // Render bố cục cho Đối soát cố định
  const renderDscdTemplate = () => {
    if (!processedData) return null;
    
    console.log("Rendering DSCD template with data:", {
      rawData: processedData,
      hasDuLieu: !!processedData.du_lieu,
      duLieuLength: processedData.du_lieu ? processedData.du_lieu.length : 0,
      hasTongKet: !!processedData.tong_ket,
      findArrayProperties: Object.keys(processedData).filter(key => Array.isArray(processedData[key])),
      findObjectProperties: Object.keys(processedData).filter(key => 
        typeof processedData[key] === 'object' && 
        processedData[key] !== null && 
        !Array.isArray(processedData[key])
      )
    });
    
    return (
      <div className="space-y-6">
        <div className="bg-gray-50 rounded-md p-4">
          <h3 className="text-lg font-medium mb-2">Thông tin đối soát cố định</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {processedData.thang_doi_soat && (
              <div>
                <span className="text-sm text-muted-foreground block">Tháng đối soát:</span>
                <span className="font-medium">{processedData.thang_doi_soat}</span>
              </div>
            )}
            {processedData.tu_mang && (
              <div>
                <span className="text-sm text-muted-foreground block">Từ mạng:</span>
                <span className="font-medium">{processedData.tu_mang}</span>
              </div>
            )}
            {processedData.den_doi_tac && (
              <div>
                <span className="text-sm text-muted-foreground block">Đến đối tác:</span>
                <span className="font-medium">{processedData.den_doi_tac}</span>
              </div>
            )}
            {processedData.hop_dong_so && (
              <div>
                <span className="text-sm text-muted-foreground block">Hợp đồng số:</span>
                <span className="font-medium">{processedData.hop_dong_so}</span>
              </div>
            )}
          </div>
        </div>
        
        {/* Hiển thị dữ liệu dịch vụ */}
        {renderDscdTableData(processedData)}
      </div>
    );
  };

  // Helper function để hiển thị bảng dữ liệu DSCD
  const renderDscdTableData = (data: any) => {
    if (!data) return null;
    
    console.log("Rendering DSCD table data:", {
      dataKeys: Object.keys(data),
      hasDuLieu: !!data.du_lieu,
      duLieuLength: data.du_lieu ? data.du_lieu.length : 0,
      hasDauSo: !!data.dau_so,
      dauSoLength: data.dau_so ? data.dau_so.length : 0,
      sampleDuLieu: data.du_lieu && data.du_lieu.length > 0 ? data.du_lieu[0] : null,
      sampleDauSo: data.dau_so && data.dau_so.length > 0 ? data.dau_so[0] : null
    });
    
    // Ưu tiên sử dụng mảng du_lieu (cấu trúc mới từ structured-data API)
    if (data.du_lieu && Array.isArray(data.du_lieu) && data.du_lieu.length > 0) {
      return (
        <div>
          <h3 className="text-lg font-medium mb-2">Dữ liệu chi tiết dịch vụ ({data.du_lieu.length} đầu số)</h3>
          <div className="overflow-x-auto rounded-lg border">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-gray-100">
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">STT</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Đầu số</th>
                  <th colSpan={2} className="px-3 py-2 text-center text-xs font-medium text-gray-500">CĐ Nội hạt</th>
                  <th colSpan={2} className="px-3 py-2 text-center text-xs font-medium text-gray-500">CĐ Liên tỉnh</th>
                  <th colSpan={2} className="px-3 py-2 text-center text-xs font-medium text-gray-500">Di động</th>
                  <th colSpan={2} className="px-3 py-2 text-center text-xs font-medium text-gray-500">1900</th>
                  <th colSpan={2} className="px-3 py-2 text-center text-xs font-medium text-gray-500">Quốc tế</th>
                  <th colSpan={3} className="px-3 py-2 text-center text-xs font-medium text-gray-500">Cước thuê bao</th>
                  <th className="px-3 py-2 text-center text-xs font-medium text-gray-500">Cước thu khách</th>
                  <th className="px-3 py-2 text-center text-xs font-medium text-gray-500">Cước trả HTC</th>
                </tr>
                <tr className="bg-gray-50">
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500"></th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500"></th>
                  
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Thời gian</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Cước</th>
                  
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Thời gian</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Cước</th>
                  
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Thời gian</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Cước</th>
                  
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Thời gian</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Cước</th>
                  
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Thuê bao</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Cam kết</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Trả trước</th>
                  
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500"></th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500"></th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.du_lieu.map((item: any, index: number) => (
                  <tr key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{item.stt}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{item.standardized_display}</td>
                    
                    {/* CĐ Nội hạt */}
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.co_dinh_noi_hat?.thoi_gian_goi.toLocaleString() || 0}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.co_dinh_noi_hat?.cuoc.toLocaleString() || 0}
                    </td>
                    
                    {/* CĐ Liên tỉnh */}
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.co_dinh_lien_tinh?.thoi_gian_goi.toLocaleString() || 0}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.co_dinh_lien_tinh?.cuoc.toLocaleString() || 0}
                    </td>
                    
                    {/* Di động */}
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.di_dong?.thoi_gian_goi.toLocaleString() || 0}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.di_dong?.cuoc.toLocaleString() || 0}
                    </td>
                    
                    {/* 1900 */}
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.cuoc_1900?.thoi_gian_goi.toLocaleString() || 0}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.cuoc_1900?.cuoc.toLocaleString() || 0}
                    </td>
                    
                    {/* Quốc tế */}
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.quoc_te?.thoi_gian_goi.toLocaleString() || 0}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.quoc_te?.cuoc.toLocaleString() || 0}
                    </td>
                    
                    {/* Cước thuê bao */}
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.cuoc_thue_bao?.thue_bao_thang.toLocaleString() || 0}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.cuoc_thue_bao?.cam_ket_thang.toLocaleString() || 0}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {item.cuoc_thue_bao?.tra_truoc_thang.toLocaleString() || 0}
                    </td>
                    
                    {/* Cước thu khách & Cước trả HTC */}
                    <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-blue-600">
                      {item.cuoc_thu_khach.toLocaleString() || 0}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-red-600">
                      {item.cuoc_tra_htc.toLocaleString() || 0}
                    </td>
                  </tr>
                ))}
              </tbody>
              {data.tong_ket && (
                <tfoot>
                  <tr className="bg-gray-100">
                    <td colSpan={16} className="px-3 py-2 text-right text-sm font-medium text-gray-700">Tổng cộng:</td>
                    <td className="px-3 py-2 text-sm font-medium text-blue-600">
                      {data.tong_ket.cong_tien_dich_vu.toLocaleString() || 0}
                    </td>
                  </tr>
                  <tr className="bg-gray-100">
                    <td colSpan={16} className="px-3 py-2 text-right text-sm font-medium text-gray-700">Thuế GTGT:</td>
                    <td className="px-3 py-2 text-sm font-medium text-blue-600">
                      {data.tong_ket.tien_thue_gtgt.toLocaleString() || 0}
                    </td>
                  </tr>
                  <tr className="bg-gray-100">
                    <td colSpan={16} className="px-3 py-2 text-right text-sm font-medium text-gray-700">Tổng cộng tiền:</td>
                    <td className="px-3 py-2 text-sm font-medium text-blue-600">
                      {data.tong_ket.tong_cong_tien.toLocaleString() || 0}
                    </td>
                  </tr>
                </tfoot>
              )}
            </table>
          </div>
        </div>
      );
    }
    
    // Hỗ trợ tương thích ngược: kiểm tra dau_so
    if (data.dau_so && Array.isArray(data.dau_so) && data.dau_so.length > 0) {
      return (
        <div>
          <h3 className="text-lg font-medium mb-2">Dữ liệu chi tiết dịch vụ ({data.dau_so.length} đầu số)</h3>
          <div className="overflow-x-auto rounded-lg border">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-gray-100">
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">STT</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Đầu số</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Sản lượng</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Tỷ lệ CP</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Thành tiền</th>
                </tr>
              </thead>
              <tbody>
                {data.dau_so.map((item: any, index: number) => (
                  <tr key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm font-medium">{item.dau_so}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {typeof item.san_luong === 'number' ? item.san_luong.toLocaleString('vi-VN') : item.san_luong}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{item.ty_le_cp}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {typeof item.thanh_tien === 'number' ? item.thanh_tien.toLocaleString('vi-VN') : item.thanh_tien} VNĐ
                    </td>
                  </tr>
                ))}
              </tbody>
              {data.tong_ket && (
                <tfoot>
                  <tr className="bg-gray-100 font-medium">
                    <td colSpan={4} className="px-3 py-2 text-right text-sm">Tổng cộng:</td>
                    <td className="px-3 py-2 text-sm font-bold">
                      {typeof data.tong_ket.tong_cong === 'number' 
                        ? data.tong_ket.tong_cong.toLocaleString('vi-VN') 
                        : data.tong_ket.tong_cong || data.tong_ket.tong_thanh_tien || 'N/A'} VNĐ
                    </td>
                  </tr>
                </tfoot>
              )}
            </table>
          </div>
        </div>
      );
    }
    
    // Nếu không có dữ liệu hoặc cấu trúc không đúng, trả về thông báo
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Không tìm thấy dữ liệu chi tiết cho mẫu đối soát này</p>
      </div>
    );
  };

  // Render bố cục cho Đối soát 1800/1900
  const renderDst1800Template = () => {
    if (!processedData) return null;
    
    console.log("Rendering DST 1800/1900 template with data:", {
      rawData: processedData,
      hasNhomDichVu: !!processedData.nhom_dich_vu,
      nhomDichVuLength: processedData.nhom_dich_vu ? processedData.nhom_dich_vu.length : 0,
      hasThanhToan: !!processedData.thanh_toan,
      findArrayProperties: Object.keys(processedData).filter(key => Array.isArray(processedData[key])),
      findObjectProperties: Object.keys(processedData).filter(key => 
        typeof processedData[key] === 'object' && 
        processedData[key] !== null && 
        !Array.isArray(processedData[key])
      )
    });
    
    return (
      <div className="space-y-6">
        <div className="bg-gray-50 rounded-md p-4">
          <h3 className="text-lg font-medium mb-2">Thông tin đối soát 1800/1900</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {processedData.thang_doi_soat && (
              <div>
                <span className="text-sm text-muted-foreground block">Tháng đối soát:</span>
                <span className="font-medium">{processedData.thang_doi_soat}</span>
              </div>
            )}
            {processedData.dau_so && (
              <div>
                <span className="text-sm text-muted-foreground block">Đầu số:</span>
                <span className="font-medium">{processedData.dau_so}</span>
              </div>
            )}
            {processedData.ten_khach_hang && (
              <div>
                <span className="text-sm text-muted-foreground block">Tên khách hàng:</span>
                <span className="font-medium">{processedData.ten_khach_hang}</span>
              </div>
            )}
            {processedData.hop_dong_so && (
              <div>
                <span className="text-sm text-muted-foreground block">Hợp đồng số:</span>
                <span className="font-medium">{processedData.hop_dong_so}</span>
              </div>
            )}
          </div>
        </div>
        
        {/* Sử dụng helper function để hiển thị bảng dữ liệu */}
        {renderDst1800TableData(processedData)}
      </div>
    );
  };

  // Helper function để hiển thị bảng dữ liệu DST 1800/1900
  const renderDst1800TableData = (data: any) => {
    if (!data) return null;
    
    // 1. Tìm kiếm nhóm dịch vụ
    if (data.nhom_dich_vu && Array.isArray(data.nhom_dich_vu) && data.nhom_dich_vu.length > 0) {
      return (
        <div>
          <h3 className="text-lg font-medium mb-2">Nhóm dịch vụ ({data.nhom_dich_vu.length})</h3>
          <div className="overflow-x-auto rounded-lg border">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-gray-100">
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">STT</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Nhóm</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Sản lượng</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Doanh thu</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Tỷ lệ CP</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Thành tiền</th>
                </tr>
              </thead>
              <tbody>
                {data.nhom_dich_vu.map((item: any, index: number) => (
                  <tr key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm font-medium">{item.ten_nhom || 'N/A'}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {typeof item.san_luong === 'number' ? item.san_luong.toLocaleString('vi-VN') : item.san_luong || 'N/A'}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {typeof item.doanh_thu === 'number' ? item.doanh_thu.toLocaleString('vi-VN') : item.doanh_thu || 'N/A'}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{item.ty_le_cp || 'N/A'}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {typeof item.thanh_tien === 'number' ? item.thanh_tien.toLocaleString('vi-VN') : item.thanh_tien || 'N/A'} VNĐ
                    </td>
                  </tr>
                ))}
              </tbody>
              {data.thanh_toan && (
                <tfoot>
                  <tr className="bg-gray-100 font-medium">
                    <td colSpan={5} className="px-3 py-2 text-right text-sm">Tổng thanh toán:</td>
                    <td className="px-3 py-2 text-sm font-bold">
                      {typeof data.thanh_toan.sau_bu_tru === 'number' 
                        ? data.thanh_toan.sau_bu_tru.toLocaleString('vi-VN') 
                        : data.thanh_toan.sau_bu_tru || 'N/A'} VNĐ
                    </td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td colSpan={5} className="px-3 py-2 text-right text-sm">Bên thanh toán:</td>
                    <td className="px-3 py-2 text-sm">{data.thanh_toan.ben_thanh_toan || 'N/A'}</td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td colSpan={5} className="px-3 py-2 text-right text-sm">Bên nhận:</td>
                    <td className="px-3 py-2 text-sm">{data.thanh_toan.ben_nhan || 'N/A'}</td>
                  </tr>
                </tfoot>
              )}
            </table>
          </div>
        </div>
      );
    }
    
    // 2. Tìm kiếm các mảng chi tiết khác
    const arrayProps = Object.keys(data).filter(key => Array.isArray(data[key]) && data[key].length > 0);
    if (arrayProps.length > 0) {
      const firstArrayKey = arrayProps[0];
      const items = data[firstArrayKey];
      
      return (
        <div>
          <h3 className="text-lg font-medium mb-2">Dữ liệu chi tiết ({firstArrayKey}: {items.length} mục)</h3>
          <ScrollArea className="h-[400px]">
            <pre className="p-4 rounded-md bg-gray-100 dark:bg-gray-800 overflow-auto text-sm">
              {JSON.stringify(items, null, 2)}
            </pre>
          </ScrollArea>
        </div>
      );
    }
    
    // 3. Hiển thị thông tin thanh toán nếu có
    if (data.thanh_toan && typeof data.thanh_toan === 'object') {
      return (
        <div>
          <h3 className="text-lg font-medium mb-2">Thông tin thanh toán</h3>
          <div className="bg-gray-50 p-4 rounded-md">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-muted-foreground block">Tổng thanh toán:</span>
                <span className="font-medium">
                  {typeof data.thanh_toan.sau_bu_tru === 'number' 
                  ? data.thanh_toan.sau_bu_tru.toLocaleString('vi-VN') 
                  : data.thanh_toan.sau_bu_tru || 'N/A'} VNĐ
                </span>
              </div>
              <div>
                <span className="text-sm text-muted-foreground block">Bên thanh toán:</span>
                <span className="font-medium">{data.thanh_toan.ben_thanh_toan || 'N/A'}</span>
              </div>
              <div>
                <span className="text-sm text-muted-foreground block">Bên nhận:</span>
                <span className="font-medium">{data.thanh_toan.ben_nhan || 'N/A'}</span>
              </div>
            </div>
          </div>
        </div>
      );
    }
    
    // 4. Fallback: Hiển thị dữ liệu gốc
    return (
      <div>
        <h3 className="text-lg font-medium mb-2">Dữ liệu chi tiết</h3>
        <div className="bg-gray-50 p-4 rounded-md">
          <p className="text-sm text-gray-500 mb-2">Chưa nhận diện được cấu trúc dữ liệu chi tiết. Dưới đây là dữ liệu gốc:</p>
          <ScrollArea className="h-[300px]">
            <pre className="p-4 rounded-md bg-gray-100 dark:bg-gray-800 overflow-auto text-sm">
              {JSON.stringify(data, null, 2)}
            </pre>
          </ScrollArea>
        </div>
      </div>
    );
  };

  // Render template tương ứng với loại mẫu
  const renderTemplateLayout = () => {
    if (!template || !processedData) {
      return (
        <div className="text-center py-8 text-gray-500">
          <p>Chưa có thông tin bố cục mẫu</p>
        </div>
      );
    }
    
    // Debug để kiểm tra loại mẫu được sử dụng
    console.log("Template layout debug:", {
      detected_type: template.detected_type,
      template_type: template.template_type,
      processedData
    });
    
    // Ưu tiên sử dụng template_type nếu detected_type không có
    const templateType = (template.detected_type || template.template_type || '').toLowerCase();
    
    // Debug thêm thông tin về thuộc tính của mẫu để dễ dàng xử lý lỗi
    console.log("Final template type for rendering:", templateType);
    console.log("Template layout decision:", {
      isDscd: templateType.includes('dscd'),
      isDsc: templateType.includes('dsc'),
      isDst: templateType.includes('1800') || templateType.includes('1900') || templateType.includes('dst')
    });
    
    // Sửa logic: Kiểm tra loại cụ thể hơn (dscd) trước loại chung (dsc)
    if (templateType.includes('dscd')) {
      return renderDscdTemplate();
    } else if (templateType.includes('dsc')) {
      return renderDscTemplate();
    } else if (templateType.includes('1800') || templateType.includes('1900') || templateType.includes('dst')) {
      return renderDst1800Template();
    } else {
      // Nếu không xác định được loại mẫu cụ thể, hiển thị dạng JSON
      return (
        <div className="space-y-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium">Dữ liệu gốc (JSON)</h3>
            <Badge variant="outline">Loại mẫu không xác định</Badge>
          </div>
          <ScrollArea className="h-[400px]">
            <pre className="p-4 rounded-md bg-gray-100 dark:bg-gray-800 overflow-auto text-sm">
              {JSON.stringify(processedData, null, 2)}
            </pre>
          </ScrollArea>
        </div>
      );
    }
  };

  return (
    <div className="container mx-auto py-4">
      <div className="flex items-center mb-6">
        <Button variant="outline" size="sm" onClick={() => navigate('/templates')} className="mr-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Quay lại
        </Button>
        <h1 className="text-2xl font-bold">Chi tiết mẫu đối soát</h1>
      </div>

      {isLoading ? (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <Skeleton className="h-4 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-40 w-full" />
            </div>
          </CardContent>
        </Card>
      ) : template ? (
        <>
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-xl">{template.file_name}</CardTitle>
                  <CardDescription>
                    Đã tải lên vào {formatDate(template.created_at)}
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  {template.status === 'pending' && (
                    <Button onClick={handleProcess} disabled={isProcessing}>
                      <Play className="h-4 w-4 mr-2" />
                      {isProcessing ? 'Đang xử lý...' : 'Xử lý mẫu'}
                    </Button>
                  )}
                  {template.status === 'completed' && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button onClick={handleDownloadExcel}>
                            <Download className="h-4 w-4 mr-2" />
                            Tải Excel
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Tải về kết quả dưới dạng Excel</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  <Button variant="outline" onClick={handleRefreshStatus}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Làm mới
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-center">
                      <div className="font-medium">Trạng thái</div>
                      {renderStatus(template.status)}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-center">
                      <div className="font-medium">Loại mẫu</div>
                      <div>{renderTemplateType(template.detected_type)}</div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {template.error_message && (
                <Card className="mb-4 bg-red-50 border-red-200">
                  <CardContent className="pt-6">
                    <div className="text-red-600">
                      <div className="font-medium mb-1">Lỗi xử lý:</div>
                      <div>{template.error_message}</div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList>
                  <TabsTrigger value="overview">
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    Tổng quan
                  </TabsTrigger>
                  <TabsTrigger value="preview">
                    <FileSearch className="h-4 w-4 mr-2" />
                    Xem trước dữ liệu
                  </TabsTrigger>
                  {template.status === 'completed' && (
                    <TabsTrigger value="template">
                      <Table className="h-4 w-4 mr-2" />
                      Bố cục mẫu đối soát
                    </TabsTrigger>
                  )}
                </TabsList>

                <TabsContent value="overview">
                  <Card className="mb-4">
                    <CardHeader>
                      <CardTitle>Thông tin tệp</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <div className="mb-2 text-sm font-medium">Tên tệp</div>
                          <div className="flex items-center">
                            <FileSpreadsheet className="h-4 w-4 mr-2 text-gray-500" />
                            {template.file_name}
                          </div>
                        </div>
                        <div>
                          <div className="mb-2 text-sm font-medium">Thời gian tải lên</div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                            {formatDate(template.created_at)}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="preview">
                  <Card>
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <CardTitle>Xem trước dữ liệu</CardTitle>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => fetchTemplatePreview(Number(id))}
                          disabled={isLoadingPreview}
                        >
                          <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingPreview ? 'animate-spin' : ''}`} />
                          Tải dữ liệu
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {isLoadingPreview ? (
                        <div className="text-center py-4">
                          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-800"></div>
                          <div className="mt-2">Đang tải dữ liệu...</div>
                        </div>
                      ) : previewData.length > 0 ? (
                        <ScrollArea className="h-[400px]">
                          <pre className="p-4 rounded-md bg-gray-100 dark:bg-gray-800 overflow-auto text-sm">
                            {JSON.stringify(previewData, null, 2)}
                          </pre>
                        </ScrollArea>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <FileSearch className="h-10 w-10 mx-auto mb-2" />
                          <p>Chưa có dữ liệu xem trước</p>
                          <p className="text-sm mt-1">Nhấn nút "Tải dữ liệu" để xem dữ liệu mẫu</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="template">
                  <Card>
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <CardTitle>Bố cục mẫu đối soát</CardTitle>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => fetchProcessedData()}
                          disabled={isLoadingResults}
                        >
                          <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingResults ? 'animate-spin' : ''}`} />
                          Làm mới dữ liệu
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {isLoadingResults ? (
                        <div className="text-center py-4">
                          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-800"></div>
                          <div className="mt-2">Đang tải thông tin mẫu...</div>
                        </div>
                      ) : processedData ? (
                        renderTemplateLayout()
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <Table className="h-10 w-10 mx-auto mb-2" />
                          <p>Không có thông tin bố cục mẫu</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </>
      ) : (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8 text-gray-500">
              <p>Không tìm thấy mẫu đối soát</p>
              <Button 
                variant="outline" 
                className="mt-4" 
                onClick={() => navigate('/templates')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại danh sách
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 