import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import TemplateUploadDialog from '@/components/templates/TemplateUploadDialog';
import { Plus, FileSpreadsheet, Upload, RefreshCw, Trash2, Play, Download, FileSearch, Eye, Filter, MoreHorizontal } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { templateService, GetTemplatesParams } from '@/services/template-service';
import { DoiSoatTemplate, TemplateTypes } from '@/models/template';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { formatDate } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import { ReusablePagination } from "@/components/ui/reusable-pagination";
import { partnerService, Partner } from "@/services/partner-service";
import { CreateReconciliationDialog } from '@/components/reconciliation/create-reconciliation-dialog';

export default function TemplatesPage() {
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [templates, setTemplates] = useState<DoiSoatTemplate[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<{[key: number]: boolean}>({});
  const [totalTemplates, setTotalTemplates] = useState(0);
  const [templateCurrentPage, setTemplateCurrentPage] = useState(1);
  const [templateTypeFilter, setTemplateTypeFilter] = useState<string | undefined>(undefined);
  const [partnerIdFilter, setPartnerIdFilter] = useState<string | undefined>(undefined);
  const [partners, setPartners] = useState<Partner[]>([]);
  const [isLoadingPartners, setIsLoadingPartners] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const templatePageSize = 10;

  useEffect(() => {
    fetchTemplates();
  }, [templateCurrentPage, templateTypeFilter, partnerIdFilter]);

  useEffect(() => {
    fetchPartners();
  }, []);

  useEffect(() => {
    setTemplateCurrentPage(1);
  }, [templateTypeFilter, partnerIdFilter]);

  const fetchPartners = async () => {
    setIsLoadingPartners(true);
    try {
      const response = await partnerService.getPartners({ limit: 1000 });
      setPartners(response.items);
    } catch (error) {
      console.error("Failed to fetch partners:", error);
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách đối tác",
        variant: "destructive",
      });
    } finally {
      setIsLoadingPartners(false);
    }
  };

  const fetchTemplates = async () => {
    try {
      setIsLoadingTemplates(true);
      const params: GetTemplatesParams = {
          skip: (templateCurrentPage - 1) * templatePageSize,
          limit: templatePageSize,
          template_type: templateTypeFilter,
          partner_id: partnerIdFilter ? parseInt(partnerIdFilter, 10) : undefined,
      };
      const response = await templateService.getTemplates(params);
      setTemplates(response.items);
      setTotalTemplates(response.total);
    } catch (error) {
      toast({ title: "Lỗi", description: "Không thể tải danh sách mẫu đối soát", variant: "destructive" });
    } finally {
      setIsLoadingTemplates(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      await templateService.deleteTemplate(id);
      toast({ title: "Xóa thành công", description: "Mẫu đối soát đã được xóa" });
      fetchTemplates();
    } catch (error) {
      toast({ title: "Xóa thất bại", description: "Có lỗi xảy ra khi xóa mẫu đối soát", variant: "destructive" });
    }
  };
  
  const handleProcess = async (id: number) => {
    try {
      setProcessingStatus((prev: { [key: number]: boolean }) => ({ ...prev, [id]: true }));
      const response = await templateService.processTemplate(id);
      toast({ title: "Yêu cầu đã được gửi", description: response.message || "Mẫu đối soát đang được xử lý" });
      fetchTemplates(); 
      
      const checkInterval = setInterval(async () => {
        try {
          const statusResponse = await templateService.getTemplateStatus(id);
          if (statusResponse.status !== 'processing') {
            clearInterval(checkInterval);
            fetchTemplates();
            if (statusResponse.status === 'completed') {
              toast({ title: "Phân tích hoàn tất", description: "Mẫu đối soát đã được phân tích thành công", variant: "default" });
            } else if (statusResponse.status === 'failed') {
              toast({ title: "Phân tích thất bại", description: statusResponse.message || "Có lỗi khi phân tích mẫu đối soát", variant: "destructive" });
            }
          }
        } catch (error) { console.error("Error checking template status:", error); clearInterval(checkInterval); }
      }, 3000);
      setTimeout(() => { clearInterval(checkInterval); }, 300000);
      
    } catch (error) {
      toast({ title: "Lỗi", description: "Không thể gửi yêu cầu phân tích mẫu đối soát", variant: "destructive" });
    } finally {
      setProcessingStatus((prev: { [key: number]: boolean }) => ({ ...prev, [id]: false }));
    }
  };

  const handleDownload = async (id: number) => {
    try {
      const blob = await templateService.downloadTemplate(id);
      const template = templates.find(t => t.id === id);
      const filename = template?.file_name || `template_${id}.xlsx`;

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      toast({ title: "Tải xuống thất bại", description: "Không thể tải xuống file mẫu", variant: "destructive" });
    }
  };

  const renderStatus = (status: string) => {
       switch(status?.toLowerCase()) {
          case 'pending': return <Badge variant="outline">Đang chờ</Badge>;
          case 'processing': return <Badge variant="secondary">Đang xử lý</Badge>;
          case 'completed': return <Badge variant="default">Hoàn thành</Badge>;
          case 'failed': return <Badge variant="destructive">Lỗi</Badge>;
          default: return <Badge>{status}</Badge>;
       }
  };

  const handleTemplatePageChange = (page: number) => {
    setTemplateCurrentPage(page);
  };

  const handleOpenCreateReconciliationDialog = (templateId: number) => {
    setSelectedTemplateId(templateId);
    setIsCreateDialogOpen(true);
  };

  const handleCreateSuccess = () => {
    fetchTemplates();
    toast({ title: "Thành công", description: "Bản ghi đối soát mới đã được tạo." });
  };

  return (
    <div className="container py-6 space-y-8">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Quản lý Mẫu đối soát</h1>
        <p className="text-muted-foreground">
          Tải lên, xem và quản lý các mẫu file đối soát.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Upload mẫu đối soát</CardTitle>
          <CardDescription>
            Tải lên file mẫu đối soát mới để hệ thống xử lý
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => setIsUploadDialogOpen(true)}>
            <Upload className="h-4 w-4 mr-2" />
            Tải lên mẫu đối soát
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Danh sách mẫu đối soát</CardTitle>
            <CardDescription>
              Các mẫu đối soát đã được tải lên
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Select 
              value={partnerIdFilter ?? 'all'}
              onValueChange={(value: string) => setPartnerIdFilter(value === "all" ? undefined : value)}
              disabled={isLoadingPartners}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Lọc theo đối tác..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả đối tác</SelectItem>
                {partners.map((partner) => (
                  <SelectItem key={partner.id} value={partner.id.toString()}>
                    {partner.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select 
              value={templateTypeFilter ?? 'all'}
              onValueChange={(value: string) => setTemplateTypeFilter(value === "all" ? undefined : value)}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Lọc theo loại mẫu..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả loại mẫu</SelectItem>
                {Object.entries(TemplateTypes).map(([key, value]: [string, string]) => (
                  <SelectItem key={key} value={key}>{value}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" onClick={fetchTemplates} disabled={isLoadingTemplates}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingTemplates ? 'animate-spin' : ''}`} />
              Làm mới
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isLoadingTemplates ? (
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
          ) : templates.length > 0 ? (
            <div>
              <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>Tên mẫu</TableHead>
                        <TableHead>Loại mẫu</TableHead>
                        <TableHead>Đối tác</TableHead>
                        <TableHead>Trạng thái</TableHead>
                        <TableHead>Ngày tải lên</TableHead>
                        <TableHead className="w-[120px] text-right">Hành động</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                  {templates.map((template: DoiSoatTemplate) => (
                    <TableRow key={template.id}>
                        <TableCell>{template.name}</TableCell>
                        <TableCell>{TemplateTypes[template.template_type as keyof typeof TemplateTypes] || template.template_type}</TableCell>
                        <TableCell>{template.partner?.name ?? '-'}</TableCell>
                        <TableCell>{renderStatus(template.status)}</TableCell>
                        <TableCell>{formatDate(template.created_at)}</TableCell>
                        <TableCell className="text-right">
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon" className="h-8 w-8">
                                        <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => navigate(`/templates/${template.id}`)}><Eye className="mr-2 h-4 w-4" />Xem chi tiết</DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleDownload(template.id)}><Download className="mr-2 h-4 w-4" />Tải xuống</DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleProcess(template.id)} disabled={processingStatus[template.id] || template.status === 'processing'}><Play className="mr-2 h-4 w-4" />Phân tích</DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleOpenCreateReconciliationDialog(template.id)} disabled={template.status !== 'completed'}>
                                        <Play className="mr-2 h-4 w-4 text-blue-500" /> Tạo Đối soát
                                    </DropdownMenuItem>
                                    <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(template.id)}><Trash2 className="mr-2 h-4 w-4" />Xóa</DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {!isLoadingTemplates && totalTemplates > 0 && (
                <ReusablePagination 
                    currentPage={templateCurrentPage}
                    totalItems={totalTemplates}
                    itemsPerPage={templatePageSize}
                    onPageChange={handleTemplatePageChange}
                />
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">Không tìm thấy mẫu đối soát nào.</div>
          )}
        </CardContent>
      </Card>

      <TemplateUploadDialog
        open={isUploadDialogOpen}
        onOpenChange={setIsUploadDialogOpen}
        onSuccess={fetchTemplates}
      />

      <CreateReconciliationDialog 
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        templateId={selectedTemplateId}
        onSuccess={handleCreateSuccess}
      />
    </div>
  );
} 