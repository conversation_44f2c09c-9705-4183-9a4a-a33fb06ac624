import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { ReconciliationTable } from '@/components/reconciliation/reconciliation-table';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
// --- Shadcn UI for Filters ---
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
// --- START: Imports for Dialog and Toast ---
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/components/ui/use-toast"
// --- END: Imports for Dialog and Toast ---
// Use the exported object directly
import { reconciliationService } from '@/services/reconciliation-service'; 
// --- START: Update Enum/Type Imports ---
// Import Enums normally to use their values
import { ReconciliationStatus, ReconciliationType } from '@/types/reconciliation';
// Import Interfaces using 'type'
import type { 
    ReconciliationListResponse, 
    GetReconciliationsParams,
    ReconciliationListItem, // Import item type for handlers
} from '@/types/reconciliation';
// --- END: Update Enum/Type Imports ---

// --- Mock Partner Data (Replace with actual API call) ---
// Assuming MinimalPartnerInfo is also defined in types/reconciliation
const mockPartners: { id: number; name: string }[] = [
    { id: 1, name: "Viettel" },
    { id: 2, name: "VNPT" },
    { id: 3, name: "Mobifone" },
];
// -------------------------------------------------------

// --- START: Helper function for dynamic pagination --- 
const generatePaginationItems = (currentPage: number, totalPages: number, siblingCount = 1) => {
    const totalNumbers = siblingCount + 5; // siblingCount + firstPage + lastPage + currentPage + 2*ellipsis

    /*
      Case 1: If the number of pages is less than the page numbers we want to show in our
      paginationComponent, we return the range [1..totalPages]
    */
    if (totalPages <= totalNumbers) {
        return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    /*
      Calculate left and right sibling index and make sure they are within range [1..totalPages]
    */
    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages);

    /*
      We do not show dots just when there is just one page number to be inserted between the extremes of sibling and the limits i.e 1 and totalPages. Hence we are using leftSiblingIndex > 2 and rightSiblingIndex < totalPages - 2
    */
    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < totalPages - 1; // Adjusted logic slightly

    const firstPageIndex = 1;
    const lastPageIndex = totalPages;

    const DOTS = '...';

    /*
      Case 2: No left dots to show, but rights dots to be shown
    */
    if (!shouldShowLeftDots && shouldShowRightDots) {
        let leftItemCount = 3 + 2 * siblingCount;
        let leftRange = Array.from({ length: leftItemCount }, (_, i) => i + 1);
        return [...leftRange, DOTS, lastPageIndex];
    }

    /*
      Case 3: No right dots to show, but left dots to be shown
    */
    if (shouldShowLeftDots && !shouldShowRightDots) {
        let rightItemCount = 3 + 2 * siblingCount;
        let rightRange = Array.from({ length: rightItemCount }, (_, i) => totalPages - rightItemCount + 1 + i);
        return [firstPageIndex, DOTS, ...rightRange];
    }
     
    /*
      Case 4: Both left and right dots to be shown
    */
    if (shouldShowLeftDots && shouldShowRightDots) {
        let middleRange = Array.from({ length: rightSiblingIndex - leftSiblingIndex + 1 }, (_, i) => leftSiblingIndex + i);
        return [firstPageIndex, DOTS, ...middleRange, DOTS, lastPageIndex];
    }

    // Default case (shouldn't be reached with the logic above)
    return []; 
};
// --- END: Helper function for dynamic pagination --- 

export function ReconciliationListPage() {
  const [data, setData] = useState<ReconciliationListResponse | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10); 
  const [filters, setFilters] = useState<Partial<GetReconciliationsParams>>({});
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // --- Fetch Partner List (Example - should ideally use React Query/SWR) ---
  // const [partnerList, setPartnerList] = useState<{id: number, name: string}[]>([]);
  // useEffect(() => {
  //    async function fetchPartners() {
  //       // const partners = await partnerService.getPartnerList(); // Assuming partnerService exists
  //       setPartnerList(mockPartners);
  //    }
  //    fetchPartners();
  // }, []);
  const partnerList = mockPartners; // Use mock data for now
  const navigate = useNavigate(); // Initialize navigate
  const { toast } = useToast(); // Initialize toast

  // --- START: State for Confirmation Dialog --- 
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<'finalize' | 'delete' | null>(null);
  const [selectedItem, setSelectedItem] = useState<ReconciliationListItem | null>(null);
  // --- END: State for Confirmation Dialog --- 

  // --- START: Define fetchData with useCallback --- 
  const fetchData = useCallback(async () => {
      setIsLoading(true);
      setError(null);
      const params: GetReconciliationsParams = {
          skip: (currentPage - 1) * pageSize,
          limit: pageSize,
          sort_by: sortBy,
          sort_order: sortOrder,
          ...filters,
      };
      try {
          // Ensure reconciliationService is stable or included in deps if needed
          const result = await reconciliationService.getReconciliations(params);
          setData(result);
      } catch (err) {
          setError(err instanceof Error ? err : new Error('An unknown error occurred'));
          setData(null); 
      } finally {
          setIsLoading(false);
      }
  // Add dependencies used inside fetchData
  }, [currentPage, pageSize, filters, sortBy, sortOrder, setData, setError, setIsLoading]); 
  // --- END: Define fetchData with useCallback --- 

  // --- Data Fetching Effect --- 
  useEffect(() => {
    fetchData(); // Call the memoized fetchData function
  }, [fetchData]); // Depend only on the memoized function

  const handlePageChange = (newPage: number) => {
      if (newPage >= 1 && (!data || newPage <= data.pages)) {
        setCurrentPage(newPage);
      } 
  };

  // Update handler with explicit type for Select onValueChange 'value'
  const handleFilterChange = (
      filterName: keyof GetReconciliationsParams,
      value: string | number | undefined 
  ) => {
      console.log(`Filter changed: ${filterName} = ${value}`); // Debug log
      setFilters((prevFilters: Partial<GetReconciliationsParams>) => ({
          ...prevFilters,
          // Ensure empty string from Select is treated as undefined
          [filterName]: value === '' || value === undefined ? undefined : value 
      }));
      setCurrentPage(1); 
  };

  // --- START: Implement Sorting Handler --- 
  const handleSortChange = (columnId: string) => {
      if (sortBy === columnId) {
          // If already sorting by this column, toggle order
          setSortOrder((prevOrder: 'asc' | 'desc') => prevOrder === 'asc' ? 'desc' : 'asc');
      } else {
          // If sorting by a new column, set it and default to ascending order
          setSortBy(columnId);
          setSortOrder('asc'); 
      }
      setCurrentPage(1); // Reset to first page when sorting changes
  };
  // --- END: Implement Sorting Handler --- 

  // --- START: Handler for clearing filters --- 
  const handleClearFilters = () => {
      setFilters({});
      setCurrentPage(1);
      setSortBy('created_at'); // Reset sort order as well?
      setSortOrder('desc');
  };
  // --- END: Handler for clearing filters --- 

  // --- START: Action Handlers --- 
  const handleViewDetails = (item: ReconciliationListItem) => {
      console.log("View details:", item);
      // Navigate to detail page (Route needs to be defined in App.tsx)
      // Example assumes route structure /reconciliations/{type}/{id}
      navigate(`/reconciliations/${item.reconciliation_type.toLowerCase()}/${item.id}`);
  };

  const requestFinalize = (item: ReconciliationListItem) => {
      console.log("Request finalize:", item);
      setSelectedItem(item);
      setConfirmAction('finalize');
      setIsConfirmDialogOpen(true);
  };

  const onRequestDelete = (item: ReconciliationListItem) => {
      console.log("[Dialog] Request delete:", item);
      setSelectedItem(item);
      setConfirmAction('delete');
      setIsConfirmDialogOpen(true);
      console.log("[Dialog] State after setting:", {
          selectedItem: item,
          confirmAction: 'delete',
          isConfirmDialogOpen: true
      });
  };

  const handleConfirmAction = async () => {
      console.log('[Dialog] handleConfirmAction triggered with state:', {
          confirmAction,
          selectedItem,
          isConfirmDialogOpen
      });

      if (!confirmAction || !selectedItem) {
          console.log('[Dialog] Missing required data:', {
              confirmAction,
              selectedItem
          });
          return;
      }

      const action = confirmAction;
      const item = selectedItem;
      
      console.log('[Dialog] Processing action:', {
          action,
          item,
          currentState: {
              isConfirmDialogOpen,
              isLoading
          }
      });
      
      try {
          setIsLoading(true);
          if (action === 'delete') {
              console.log(`[Dialog] Starting delete operation for:`, {
                  id: item.id,
                  type: item.reconciliation_type,
                  status: item.status,
                  period: item.ky_doi_soat
              });
              
              await reconciliationService.deleteReconciliation(item.id, item.reconciliation_type);
              console.log(`[Dialog] Successfully deleted reconciliation ID: ${item.id}`);
              
              toast({ 
                  title: "Success", 
                  description: `Đã xóa bản ghi đối soát ${item.id} thành công.` 
              });
              
              // Only reset states after successful API call
              setIsConfirmDialogOpen(false);
              setConfirmAction(null);
              setSelectedItem(null);
              
              fetchData();
          }
      } catch (err: any) {
          console.error('[Dialog] Error details:', err);
          console.error('[Dialog] Error name:', err.name);
          console.error('[Dialog] Error message:', err.message);
          console.error('[Dialog] Error stack:', err.stack);
          console.error('[Dialog] Error response:', err.response);
          console.error('[Dialog] Error request:', err.request);
          console.error('[Dialog] Error config:', err.config);
          
          toast({ 
              variant: "destructive", 
              title: "Lỗi", 
              description: err.message || `Không thể xóa bản ghi đối soát.` 
          });
      } finally {
          setIsLoading(false);
      }
  };
  // --- END: Action Handlers --- 

  // Calculate pagination items using the helper
  const paginationItems = data ? generatePaginationItems(currentPage, data.pages) : [];

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-semibold mb-4">Danh sách Đối soát</h1>
      
      {/* --- START: Filter Components using Shadcn UI --- */}
      <div className="mb-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-4 items-end">
         {/* Filter: Kỳ Đối soát */}
         <div className="space-y-1">
             <label htmlFor="ky-doi-soat" className="text-sm font-medium">Kỳ ĐS</label>
             <Input 
                 id="ky-doi-soat"
                 type="month"
                 placeholder="YYYY-MM" 
                 value={filters.ky_doi_soat || ''}
                 onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleFilterChange('ky_doi_soat', e.target.value)}
                 className="text-sm"
             />
         </div>
         {/* Filter: Partner */}
          <div className="space-y-1">
              <label htmlFor="partner-filter" className="text-sm font-medium">Đối tác</label>
              <Select 
                  value={filters.partner_id?.toString() ?? ''} 
                  onValueChange={(value: string) => handleFilterChange('partner_id', value ? Number(value) : undefined)}
              >
                  <SelectTrigger id="partner-filter" className="text-sm">
                      <SelectValue placeholder="Tất cả đối tác" />
                  </SelectTrigger>
                  <SelectContent>
                      <SelectItem value="">Tất cả đối tác</SelectItem>
                      {partnerList.map(partner => (
                          <SelectItem key={partner.id} value={partner.id.toString()}>
                              {partner.name}
                          </SelectItem>
                      ))}
                  </SelectContent>
              </Select>
          </div>
           {/* Filter: Type */}
            <div className="space-y-1">
                <label htmlFor="type-filter" className="text-sm font-medium">Loại ĐS</label>
                <Select 
                    value={filters.reconciliation_type ?? ''} 
                    onValueChange={(value: string) => handleFilterChange('reconciliation_type', value as ReconciliationType | undefined)}
                >
                    <SelectTrigger id="type-filter" className="text-sm">
                        <SelectValue placeholder="Tất cả loại" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="">Tất cả loại</SelectItem>
                        {Object.values(ReconciliationType).map(type => (
                            <SelectItem key={type} value={type}>{type}</SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>
             {/* Filter: Status */}
              <div className="space-y-1">
                  <label htmlFor="status-filter" className="text-sm font-medium">Trạng thái</label>
                  <Select 
                      value={filters.status ?? ''} 
                      onValueChange={(value: string) => handleFilterChange('status', value as ReconciliationStatus | undefined)}
                  >
                      <SelectTrigger id="status-filter" className="text-sm">
                          <SelectValue placeholder="Tất cả trạng thái" />
                      </SelectTrigger>
                      <SelectContent>
                          <SelectItem value="">Tất cả trạng thái</SelectItem>
                          {Object.values(ReconciliationStatus).map(status => (
                              <SelectItem key={status} value={status} className="capitalize">{status.replace(/_/g, ' ')}</SelectItem>
                          ))}
                      </SelectContent>
                  </Select>
              </div>
              {/* --- Add Clear Filters Button --- */}
              <Button 
                  variant="outline" 
                  onClick={handleClearFilters} 
                  className="text-sm"
                  disabled={Object.keys(filters).length === 0} // Disable if no filters are set
              >
                  Xóa bộ lọc
              </Button>
      </div>
      {/* --- END: Filter Components using Shadcn UI --- */}

      <ReconciliationTable
        data={data?.items ?? []}
        isLoading={isLoading}
        error={error}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={handleSortChange}
        // --- Pass Action Handlers --- 
        onViewDetails={handleViewDetails}
        onRequestFinalize={requestFinalize}
        onRequestDelete={onRequestDelete}
        // --- End Pass Action Handlers --- 
      />

      {/* Pagination Section */}
      <div className="mt-4 flex flex-col sm:flex-row justify-between items-center gap-4">
         {/* Items count display */}
         {data && data.total > 0 ? (
            <p className="text-sm text-muted-foreground order-last sm:order-first">
                Showing {((data.page - 1) * data.size) + 1} - {Math.min(data.page * data.size, data.total)} of {data.total} items
            </p>
         ) : (
            <p className="text-sm text-muted-foreground order-last sm:order-first">
                {!isLoading ? 'No items found.' : 'Loading...'} 
            </p>
         )
         }
         {/* Pagination Controls */}
         {data && data.pages > 1 && (
             <Pagination className="order-first sm:order-last">
                 <PaginationContent>
                     <PaginationItem>
                         <PaginationPrevious 
                             href="#"
                             onClick={(e: React.MouseEvent) => { e.preventDefault(); handlePageChange(currentPage - 1); }}
                             className={currentPage <= 1 || isLoading ? "pointer-events-none opacity-50" : undefined}
                             aria-disabled={currentPage <= 1 || isLoading}
                         />
                     </PaginationItem>
                     {/* Render page numbers and ellipses */} 
                     {paginationItems.map((page, index) => {
                         if (page === '...') {
                             return <PaginationItem key={`ellipsis-${index}`}><PaginationEllipsis /></PaginationItem>;
                         }
                         return (
                             <PaginationItem key={page}>
                                 <PaginationLink 
                                     href="#" 
                                     isActive={currentPage === page}
                                     onClick={(e: React.MouseEvent) => { e.preventDefault(); handlePageChange(page as number); }}
                                     aria-disabled={isLoading}
                                     className={isLoading ? "pointer-events-none opacity-50" : undefined}
                                 >
                                     {page}
                                 </PaginationLink>
                             </PaginationItem>
                         );
                     })}
                     <PaginationItem>
                         <PaginationNext 
                             href="#"
                             onClick={(e: React.MouseEvent) => { e.preventDefault(); handlePageChange(currentPage + 1); }}
                             className={currentPage >= data.pages || isLoading ? "pointer-events-none opacity-50" : undefined}
                             aria-disabled={currentPage >= data.pages || isLoading}
                         />
                     </PaginationItem>
                 </PaginationContent>
             </Pagination>
         )}
      </div>

       {/* --- START: Confirmation Dialog --- */} 
       <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Xác nhận hành động</AlertDialogTitle>
              <AlertDialogDescription>
                {confirmAction === 'delete' ? (
                    <>
                        Bạn có chắc chắn muốn xóa bản ghi đối soát 
                        ID: <strong>{selectedItem?.id}</strong> ({selectedItem?.reconciliation_type} - {selectedItem?.ky_doi_soat})?
                        Hành động này không thể hoàn tác.
                    </>
                ) : (
                    <>
                        Bạn có chắc chắn muốn chốt bản ghi đối soát 
                        ID: <strong>{selectedItem?.id}</strong> ({selectedItem?.reconciliation_type} - {selectedItem?.ky_doi_soat})?
                        Hành động này sẽ khóa bản ghi.
                    </>
                )}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => {
                  console.log("[Dialog] Cancel clicked");
                  setIsConfirmDialogOpen(false);
              }}>
                  Hủy
              </AlertDialogCancel>
              <AlertDialogAction 
                  onClick={() => {
                      console.log("[Dialog] Confirm clicked");
                      handleConfirmAction();
                  }}
                  className={confirmAction === 'delete' ? "bg-red-600 hover:bg-red-700" : ""}
              >
                  Xác nhận
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
       {/* --- END: Confirmation Dialog --- */} 

    </div>
  );
}

export default ReconciliationListPage; 