import React, { useState, useEffect, useMemo } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { reconciliationService } from '@/services/reconciliation-service';
import {
    ReconciliationType,
    type ReconciliationDetail,
    type DscdReconciliationDetail,
    type DscReconciliationDetail,
    ReconciliationStatus,
} from '@/types/reconciliation';
import { isDscdDetail, isDscDetail } from '@/types/reconciliation-guards';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { format, parseISO } from 'date-fns';
import { ArrowLeft, AlertCircle, Edit, CheckCircle, Trash2 } from 'lucide-react';
import { formatCurrency, formatNumber } from '@/lib/utils';
import { DauSoDichVuTable } from '@/components/reconciliation/dau-so-dich-vu-table';
import { DscDetailTable } from '@/components/reconciliation/dsc-detail-table';

export function ReconciliationDetailPage() {
    const { id, type } = useParams<{ id: string; type: string }>();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [data, setData] = useState<ReconciliationDetail | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isFinalizeDialogOpen, setIsFinalizeDialogOpen] = useState(false);
    const [isActionLoading, setIsActionLoading] = useState(false);

    const reconciliationId = useMemo(() => (id ? parseInt(id, 10) : NaN), [id]);
    const reconciliationType = useMemo(() => (type?.toUpperCase() as ReconciliationType | undefined), [type]);

    useEffect(() => {
        const fetchData = async () => {
            if (isNaN(reconciliationId) || !reconciliationType) {
                setError(new Error("Invalid ID or Type provided in URL."));
                setIsLoading(false);
                return;
            }
            
            setIsLoading(true);
            try {
                const result = await reconciliationService.getReconciliationById(reconciliationId, reconciliationType);
                setData(result);
                setError(null);
            } catch (err) {
                console.error('Error fetching reconciliation details:', err);
                setError(err instanceof Error ? err : new Error('Failed to fetch reconciliation details'));
                setData(null);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [reconciliationId, reconciliationType]);

    const handleEdit = () => {
        if (!isNaN(reconciliationId) && reconciliationType) {
            navigate(`/reconciliations/${reconciliationType.toLowerCase()}/${reconciliationId}/edit`);
        }
    };

    const handleDelete = async () => {
        if (!data) return;
        
        setIsDeleteDialogOpen(false);
        setIsActionLoading(true);
        
        try {
            await reconciliationService.deleteReconciliation(data.id, data.reconciliation_type);
            toast({
                title: "Success",
                description: "Đã xóa bản ghi đối soát thành công.",
            });
            navigate('/reconciliations');
        } catch (err) {
            console.error('Error deleting reconciliation:', err);
            toast({
                variant: "destructive",
                title: "Lỗi",
                description: err instanceof Error ? err.message : "Không thể xóa bản ghi đối soát.",
            });
        } finally {
            setIsActionLoading(false);
        }
    };

    const handleFinalize = async () => {
        if (!data) return;
        
        setIsFinalizeDialogOpen(false);
        setIsActionLoading(true);

        try {
            const finalizedData = await reconciliationService.finalizeReconciliation(data.id, data.reconciliation_type);
            setData(finalizedData);
            toast({
                title: "Success",
                description: "Đã chốt đối soát thành công.",
            });
        } catch (err) {
            console.error('Error finalizing reconciliation:', err);
            toast({
                variant: "destructive",
                title: "Lỗi",
                description: err instanceof Error ? err.message : "Không thể chốt đối soát.",
            });
        } finally {
            setIsActionLoading(false);
        }
    };

    if (isLoading) {
        return (
            <div className="container mx-auto py-6 space-y-4">
                <Skeleton className="h-8 w-1/3" />
                <Card>
                    <CardHeader>
                        <Skeleton className="h-6 w-1/4" />
                        <Skeleton className="h-4 w-1/2" />
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-40 w-full" />
                    </CardContent>
                </Card>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mx-auto py-6">
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Lỗi</AlertTitle>
                    <AlertDescription>{error.message}</AlertDescription>
                </Alert>
                 <Button variant="outline" onClick={() => navigate('/reconciliations')} className="mt-4">
                     Quay lại danh sách
                 </Button>
            </div>
        );
    }

    if (!data) {
        return (
            <div className="container mx-auto py-6 text-center">
                <p>Không tìm thấy bản ghi đối soát.</p>
                 <Button variant="outline" onClick={() => navigate('/reconciliations')} className="mt-4">
                     Quay lại danh sách
                 </Button>
            </div>
        );
    }

    const getStatusBadgeVariant = (status: ReconciliationStatus) => {
        switch (status) {
            case ReconciliationStatus.FINALIZED: return "success";
            case ReconciliationStatus.ADJUSTED: return "info";
            case ReconciliationStatus.CALCULATED: return "default";
            case ReconciliationStatus.PROCESSING: return "warning";
            case ReconciliationStatus.ERROR: return "destructive";
            default: return "secondary";
        }
    };

    const renderDetailTable = () => {
        if (isDscdDetail(data)) {
            return <DauSoDichVuTable 
                       data={data.du_lieu} 
                       isEditing={false}
                   />;
        } else if (isDscDetail(data)) {
            return <DscDetailTable data={data.du_lieu} />;
        } else {
            return <p className="text-sm text-muted-foreground">Không thể hiển thị chi tiết cho loại đối soát này.</p>;
        }
    };
    
    const renderSummary = () => {
        const tongKet = isDscdDetail(data) ? data.tong_ket : (isDscDetail(data) ? data.tong_ket : null);
        
        if (tongKet) {
             return (
                 <div className="space-y-2">
                     <div className="flex justify-between">
                         <span className="text-muted-foreground">Cộng tiền DV:</span>
                         <span>{formatCurrency(tongKet.cong_tien_dich_vu_adjusted ?? tongKet.cong_tien_dich_vu)}</span>
                     </div>
                     <div className="flex justify-between">
                         <span className="text-muted-foreground">Thuế GTGT:</span>
                         <span>{formatCurrency(tongKet.tien_thue_gtgt_adjusted ?? tongKet.tien_thue_gtgt)}</span>
                     </div>
                     <div className="flex justify-between font-semibold text-lg">
                         <span>Tổng cộng:</span>
                         <span>{formatCurrency(tongKet.tong_cong_tien_adjusted ?? tongKet.tong_cong_tien)}</span>
                     </div>
                 </div>
             );
        } else {
            return <p className="text-sm text-muted-foreground">Không có thông tin tổng kết.</p>;
        }
    };

    const canEdit = data.status !== ReconciliationStatus.FINALIZED && !isActionLoading;
    const canFinalize = (data.status === ReconciliationStatus.CALCULATED || data.status === ReconciliationStatus.ADJUSTED) && !isActionLoading;
    const canDelete = data.status !== ReconciliationStatus.FINALIZED && !isActionLoading;

    return (
        <div className="container mx-auto py-6">
            <div className="flex justify-between items-center mb-6">
                 <Button variant="outline" size="icon" onClick={() => navigate('/reconciliations')} aria-label="Go back">
                     <ArrowLeft className="h-4 w-4" />
                 </Button>
                <h1 className="text-2xl font-semibold ml-4 flex-1">Chi tiết Đối soát #{data.id}</h1>
                <div className="flex items-center space-x-2">
                    {canEdit && (
                        <Button 
                            variant="outline" 
                            onClick={handleEdit} 
                            disabled={isActionLoading}
                        >
                            <Edit className="mr-2 h-4 w-4" />
                            Hiệu chỉnh
                         </Button>
                    )}
                    
                    {canFinalize && (
                        <AlertDialog open={isFinalizeDialogOpen} onOpenChange={setIsFinalizeDialogOpen}>
                             <AlertDialogTrigger asChild>
                                 <Button 
                                     variant="default" 
                                     disabled={isActionLoading}
                                >
                                     <CheckCircle className="mr-2 h-4 w-4" />
                                     {isActionLoading ? 'Đang xử lý...' : 'Chốt đối soát'}
                                 </Button>
                             </AlertDialogTrigger>
                             <AlertDialogContent>
                                 <AlertDialogHeader>
                                     <AlertDialogTitle>Xác nhận Chốt đối soát?</AlertDialogTitle>
                                     <AlertDialogDescription>
                                         Hành động này sẽ chốt bản ghi đối soát này và không thể hoàn tác hoặc chỉnh sửa thêm.
                                     </AlertDialogDescription>
                                 </AlertDialogHeader>
                                 <AlertDialogFooter>
                                     <AlertDialogCancel disabled={isActionLoading}>Hủy</AlertDialogCancel>
                                     <AlertDialogAction onClick={handleFinalize} disabled={isActionLoading}>
                                         {isActionLoading ? 'Đang chốt...' : 'Xác nhận chốt'}
                                     </AlertDialogAction>
                                 </AlertDialogFooter>
                             </AlertDialogContent>
                         </AlertDialog>
                    )}
                    
                     {canDelete && (
                         <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                             <AlertDialogTrigger asChild>
                                 <Button 
                                    variant="destructive" 
                                    disabled={isActionLoading}
                                >
                                     <Trash2 className="mr-2 h-4 w-4" />
                                     {isActionLoading ? 'Đang xử lý...' : 'Xóa'}
                                 </Button>
                             </AlertDialogTrigger>
                             <AlertDialogContent>
                                 <AlertDialogHeader>
                                     <AlertDialogTitle>Xác nhận Xóa?</AlertDialogTitle>
                                     <AlertDialogDescription>
                                         Hành động này không thể hoàn tác. Bạn có chắc chắn muốn xóa bản ghi đối soát này không?
                                     </AlertDialogDescription>
                                 </AlertDialogHeader>
                                 <AlertDialogFooter>
                                     <AlertDialogCancel disabled={isActionLoading}>Hủy</AlertDialogCancel>
                                     <AlertDialogAction 
                                        onClick={handleDelete} 
                                        disabled={isActionLoading}
                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                    >
                                         {isActionLoading ? 'Đang xóa...' : 'Xác nhận xóa'}
                                     </AlertDialogAction>
                                 </AlertDialogFooter>
                             </AlertDialogContent>
                         </AlertDialog>
                     )}
                 </div>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Thông tin chung</CardTitle>
                    <CardDescription>
                        Kỳ đối soát: {data.ky_doi_soat} - Đối tác: {data.partner?.name || 'N/A'}
                    </CardDescription>
                    <div className="flex justify-between items-center pt-2">
                         <span>Trạng thái: <Badge variant={getStatusBadgeVariant(data.status)}>{data.status}</Badge></span>
                         <span className="text-sm text-muted-foreground">Cập nhật: {format(parseISO(data.updated_at), 'dd/MM/yyyy HH:mm')}</span>
                     </div>
                 </CardHeader>
                 <CardContent className="space-y-6">
                    {data.status === ReconciliationStatus.ERROR && data.error_message && (
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Lỗi xử lý</AlertTitle>
                            <AlertDescription>{data.error_message}</AlertDescription>
                        </Alert>
                    )}
                    
                    <div>
                        <h3 className="text-lg font-semibold mb-2">Chi tiết Đối soát</h3>
                        {renderDetailTable()}
                    </div>
                    
                     <div>
                        <h3 className="text-lg font-semibold mb-2">Tổng kết</h3>
                        {renderSummary()}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
} 