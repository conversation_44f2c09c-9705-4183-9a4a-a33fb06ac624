import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { doiSoatService, type DoiSoat1800_1900 as DoiSoat1800_1900Type } from '@/services/doi-soat-service';

interface DoiSoat1800_1900Props {
  periodId?: number;
}

export default function DoiSoat1800_1900({ periodId }: DoiSoat1800_1900Props) {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<DoiSoat1800_1900Type[]>([]);
  const [total, setTotal] = useState(0);
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const response = await doiSoatService.getDoiSoat1800_1900(periodId);
        setData(response.items || []);
        setTotal(response.total || 0);
      } catch (error: any) {
        toast({
          variant: 'destructive',
          title: 'Lỗi khi tải dữ liệu',
          description: error.message || 'Đã có lỗi xảy ra',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [periodId, toast]);

  if (isLoading) {
    return (
      <div className="py-8 flex justify-center">
        <div className="animate-spin h-8 w-8 border-2 border-primary rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (!periodId) {
    return (
      <Card className="mt-4">
        <CardContent className="py-8">
          <div className="text-center text-muted-foreground">
            Vui lòng chọn kỳ đối soát để xem dữ liệu
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card className="mt-4">
        <CardContent className="py-8">
          <div className="text-center text-muted-foreground">
            Không có dữ liệu đối soát 1800/1900 cho kỳ này
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mt-4">
      <CardContent className="py-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Đối soát 1800/1900 - Kỳ #{periodId}</h3>
          <p className="text-sm text-muted-foreground">Tổng số: {total} bản ghi</p>
          
          <div className="rounded-md border">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Đầu số</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số lượng</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thành tiền</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.dau_so}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.loai}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.so_luong.toLocaleString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.thanh_tien.toLocaleString()} VNĐ</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 