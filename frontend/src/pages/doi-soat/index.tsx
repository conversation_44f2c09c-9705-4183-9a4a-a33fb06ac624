import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { 
  reconciliationPeriodService, 
  ReconciliationPeriod
} from '@/services/reconciliation-period-service';
import { templateService } from '@/services/template-service';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';

import {
  FileText,
  BarChart3,
  CalendarRange,
  Upload,
  Plus
} from 'lucide-react';

// Import đối soát components
import DoiSoatCoDinh from './components/DoiSoatCoDinh';
import DoiSoatCuoc from './components/DoiSoatCuoc';
import DoiSoat1800_1900 from './components/DoiSoat1800_1900';

// Import component cho mẫu đối soát
import { TemplateList } from '@/components/templates/TemplateList';
import TemplateUploadDialog from '@/components/templates/TemplateUploadDialog';

// Import component cho kỳ đối soát
import { CreatePeriodDialog } from '@/components/reconciliation-periods/CreatePeriodDialog';

export default function DoiSoatPage() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [periods, setPeriods] = useState<ReconciliationPeriod[]>([]);
  const [selectedPeriodId, setSelectedPeriodId] = useState<string>('');
  const [selectedSection, setSelectedSection] = useState<string>(searchParams.get('section') || 'results');
  const [selectedType, setSelectedType] = useState<string>(searchParams.get('type') || 'co-dinh');
  const [isLoading, setIsLoading] = useState(false);
  const [isUploadTemplateDialogOpen, setIsUploadTemplateDialogOpen] = useState(false);
  const [isCreatePeriodDialogOpen, setIsCreatePeriodDialogOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const { toast } = useToast();

  // Get parameters from URL
  useEffect(() => {
    const periodId = searchParams.get('period_id');
    const type = searchParams.get('type');
    const section = searchParams.get('section');
    
    if (periodId) {
      setSelectedPeriodId(periodId);
    }
    
    if (type) {
      setSelectedType(type);
    }

    if (section) {
      setSelectedSection(section);
    }
  }, [searchParams]);

  // Fetch all completed periods
  useEffect(() => {
    const fetchPeriods = async () => {
      try {
        setIsLoading(true);
        const result = await reconciliationPeriodService.getReconciliationPeriods(1, 100, undefined, 'completed');
        setPeriods(result.data || []);
      } catch (error: any) {
        toast({
          variant: 'destructive',
          title: 'Lỗi khi tải kỳ đối soát',
          description: error.message || 'Đã có lỗi xảy ra',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchPeriods();
  }, [refreshTrigger]);

  // Update URL when parameters change
  useEffect(() => {
    const newParams = new URLSearchParams();
    
    if (selectedPeriodId) {
      newParams.set('period_id', selectedPeriodId);
    }
    
    if (selectedType) {
      newParams.set('type', selectedType);
    }
    
    if (selectedSection) {
      newParams.set('section', selectedSection);
    }
    
    setSearchParams(newParams);
  }, [selectedPeriodId, selectedType, selectedSection]);

  const handlePeriodChange = (value: string) => {
    setSelectedPeriodId(value);
  };

  const handleTypeChange = (value: string) => {
    setSelectedType(value);
  };

  const handleSectionChange = (value: string) => {
    setSelectedSection(value);
  };

  const handleUploadTemplateSuccess = () => {
    toast({
      title: 'Upload thành công',
      description: 'Mẫu đối soát đã được tải lên thành công',
    });
    setIsUploadTemplateDialogOpen(false);
    setRefreshTrigger(prev => prev + 1);
  };

  const handleCreatePeriodSuccess = () => {
    setIsCreatePeriodDialogOpen(false);
    setRefreshTrigger(prev => prev + 1);
    toast({
      title: 'Tạo kỳ đối soát thành công',
      description: 'Kỳ đối soát mới đã được tạo',
    });
  };

  return (
    <div className="container py-8">
      <div className="flex flex-col gap-2 mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Đối soát</h1>
        <p className="text-muted-foreground">
          Quản lý và thực hiện đối soát giữa cuộc gọi và thanh toán
        </p>
      </div>

      <Tabs value={selectedSection} onValueChange={handleSectionChange} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="results" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            <span>Kết quả đối soát</span>
          </TabsTrigger>
          <TabsTrigger value="templates" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span>Mẫu đối soát</span>
          </TabsTrigger>
          <TabsTrigger value="periods" className="flex items-center gap-2">
            <CalendarRange className="h-4 w-4" />
            <span>Kỳ đối soát</span>
          </TabsTrigger>
        </TabsList>

        {/* Tab kết quả đối soát */}
        <TabsContent value="results">
          <Card>
            <CardHeader>
              <CardTitle>Kết quả đối soát</CardTitle>
              <CardDescription>
                Xem kết quả đối soát theo kỳ và loại đối soát
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-6 space-y-4">
                <div className="flex flex-col gap-4 md:flex-row md:items-end">
                  <div className="w-full md:w-1/2">
                    <Label htmlFor="period-select">Kỳ đối soát</Label>
                    <Select 
                      value={selectedPeriodId} 
                      onValueChange={handlePeriodChange}
                      disabled={isLoading}
                    >
                      <SelectTrigger id="period-select">
                        <SelectValue placeholder="Chọn kỳ đối soát" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Tất cả</SelectItem>
                        {periods.map(period => (
                          <SelectItem key={period.id} value={period.id.toString()}>
                            {period.name} ({period.period_code})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Tabs value={selectedType} onValueChange={handleTypeChange} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="co-dinh">Đối soát cố định</TabsTrigger>
                  <TabsTrigger value="cuoc">Đối soát cước</TabsTrigger>
                  <TabsTrigger value="1800-1900">Đối soát 1800/1900</TabsTrigger>
                </TabsList>
                
                <TabsContent value="co-dinh">
                  <DoiSoatCoDinh periodId={selectedPeriodId ? parseInt(selectedPeriodId) : undefined} />
                </TabsContent>
                
                <TabsContent value="cuoc">
                  <DoiSoatCuoc periodId={selectedPeriodId ? parseInt(selectedPeriodId) : undefined} />
                </TabsContent>
                
                <TabsContent value="1800-1900">
                  <DoiSoat1800_1900 periodId={selectedPeriodId ? parseInt(selectedPeriodId) : undefined} />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab mẫu đối soát */}
        <TabsContent value="templates">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Quản lý mẫu đối soát</CardTitle>
                <CardDescription>
                  Tạo và quản lý các mẫu đối soát
                </CardDescription>
              </div>
              <Button onClick={() => setIsUploadTemplateDialogOpen(true)}>
                <Upload className="h-4 w-4 mr-2" />
                Tải lên mẫu mới
              </Button>
            </CardHeader>
            <CardContent>
              <TemplateList 
                refreshTrigger={refreshTrigger} 
                onRefresh={() => setRefreshTrigger(prev => prev + 1)} 
              />
            </CardContent>
          </Card>

          <TemplateUploadDialog 
            open={isUploadTemplateDialogOpen} 
            onOpenChange={setIsUploadTemplateDialogOpen}
            onSuccess={handleUploadTemplateSuccess}
          />
        </TabsContent>

        {/* Tab kỳ đối soát */}
        <TabsContent value="periods">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Quản lý kỳ đối soát</CardTitle>
                <CardDescription>
                  Tạo và quản lý các kỳ đối soát
                </CardDescription>
              </div>
              <Button onClick={() => setIsCreatePeriodDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Tạo kỳ đối soát mới
              </Button>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="rounded-md border">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tên kỳ
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Mã kỳ
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Trạng thái
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Thời gian
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Thao tác
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {isLoading ? (
                      <tr>
                        <td colSpan={5} className="px-6 py-4 text-center">
                          <div className="flex justify-center">
                            <div className="animate-spin h-5 w-5 border-2 border-primary rounded-full border-t-transparent"></div>
                          </div>
                        </td>
                      </tr>
                    ) : periods && periods.length > 0 ? (
                      periods.map((period) => (
                        <tr key={period.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900 hover:underline cursor-pointer"
                              onClick={() => {
                                setSelectedPeriodId(period.id.toString());
                                setSelectedSection("results");
                              }}
                            >
                              {period.name}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {period.period_code}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                              ${period.status === 'completed' ? 'bg-green-100 text-green-800' : 
                                period.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                                period.status === 'failed' ? 'bg-red-100 text-red-800' :
                                'bg-blue-100 text-blue-800'}`}>
                              {period.status === 'completed' ? 'Hoàn thành' :
                              period.status === 'processing' ? 'Đang xử lý' :
                              period.status === 'failed' ? 'Lỗi' : 'Chưa xử lý'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(period.start_date).toLocaleDateString('vi-VN')} - {new Date(period.end_date).toLocaleDateString('vi-VN')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedPeriodId(period.id.toString());
                                setSelectedSection("results");
                              }}
                            >
                              Xem kết quả
                            </Button>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                          Không có dữ liệu
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          <CreatePeriodDialog
            open={isCreatePeriodDialogOpen}
            onOpenChange={setIsCreatePeriodDialogOpen}
            onSuccess={handleCreatePeriodSuccess}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
} 