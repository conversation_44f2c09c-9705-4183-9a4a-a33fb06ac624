"use client"

import { useState, useRef } from "react"
import { PricingTable } from "@/components/pricing/PricingTable"
import { PricingGrid } from "@/components/pricing/PricingGrid"
import { PricingDialog } from "@/components/pricing/PricingDialog"
import type { PricingFormValues } from "@/components/pricing/PricingForm"
import { useToast } from "@/components/ui/use-toast"
import { usePartnerStore } from "@/stores/partner-store"
import { PartnerSelector } from "@/components/partners/partner-selector"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { TableIcon, GridIcon } from "lucide-react"

export default function PricingPage() {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedRule, setSelectedRule] = useState<(PricingFormValues & { id?: number }) | undefined>()
  const [selectedService, setSelectedService] = useState<{id?: number, name?: string, code?: string}>({})
  const [activeTab, setActiveTab] = useState<'revenue' | 'cost'>('revenue')
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table')
  const { selectedPartner } = usePartnerStore()
  const { toast } = useToast()
  const refreshFnRef = useRef<() => Promise<void>>()

  const handleAddRule = (serviceId?: number, serviceName?: string, serviceCode?: string, billingMethod?: string, volumeRangeId?: number) => {
    if (!selectedPartner) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select a partner first"
      })
      return
    }
    
    // Nếu có billingMethod hoặc volumeRangeId, tạo một rule mới với các giá trị đã biết
    if (billingMethod || volumeRangeId) {
      setSelectedRule({
        type: activeTab, // Luôn sử dụng activeTab làm type
        billingMethod: billingMethod || "", // Đảm bảo billingMethod không bao giờ là null
        volumeRangeId: volumeRangeId || null,
        serviceType: serviceCode || null,
        price: 0,
        description: ""
      });
    } else {
      // Trường hợp thêm mới hoàn toàn, vẫn sử dụng activeTab làm type mặc định
      setSelectedRule({
        type: activeTab
      });
    }
    
    setSelectedService({
      id: serviceId,
      name: serviceName,
      code: serviceCode
    })
    
    setIsDialogOpen(true)
  }

  const handleEditRule = (rule: PricingFormValues & { id?: number, service_type?: any, volume_range?: any, billing_method?: string }) => {
    console.log("Editing rule:", rule);
    console.log("Rule type before formatting:", rule.type);
    console.log("Active tab:", activeTab);
    
    // Chuyển đổi dữ liệu từ API format sang format của form
    const formattedRule: PricingFormValues & { id?: number } = {
      id: rule.id,
      // Đảm bảo giữ nguyên type từ quy tắc đang được chỉnh sửa
      // Nếu rule không có type, sử dụng activeTab
      type: rule.type || activeTab,
      // Ưu tiên sử dụng billingMethod nếu có, nếu không thì dùng billing_method
      billingMethod: rule.billingMethod || rule.billing_method || "",
      // Lấy service_type nếu có
      serviceType: rule.serviceType || rule.service_type || null,
      // Lấy ID của volume_range nếu có
      volumeRangeId: rule.volumeRangeId || (rule.volume_range ? rule.volume_range.id : null),
      price: rule.price,
      description: rule.description || ""
    };
    
    console.log("Formatted rule for form:", formattedRule);
    console.log("Rule type after formatting:", formattedRule.type);
    
    setSelectedRule(formattedRule);
    setSelectedService({});
    setIsDialogOpen(true);
  }

  const handleSubmit = async (data: PricingFormValues) => {
    console.log("=== SUBMIT DEBUG START ===");
    console.log("Submitting data:", data);
    console.log("Selected rule:", selectedRule);
    console.log("Selected partner:", selectedPartner);
    console.log("Active tab:", activeTab);
    
    if (!selectedPartner) {
      console.error("No partner selected");
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select a partner first"
      })
      return
    }

    // Đảm bảo dữ liệu hợp lệ trước khi gửi
    if (!data.billingMethod) {
      console.error("Missing billing method");
      toast({
        variant: "destructive",
        title: "Error",
        description: "Billing method is required"
      })
      return
    }

    if (!data.serviceType) {
      console.error("Missing service type");
      toast({
        variant: "destructive",
        title: "Error",
        description: "Service type is required"
      })
      return
    }

    if (!data.volumeRangeId) {
      console.error("Missing volume range");
      toast({
        variant: "destructive",
        title: "Error",
        description: "Volume range is required"
      })
      return
    }

    // Sử dụng type từ dữ liệu form thay vì ghi đè bằng activeTab
    // Chỉ sử dụng activeTab khi tạo mới và không có type trong dữ liệu form
    const ruleType = data.type || (selectedRule?.id ? selectedRule.type : activeTab);
    console.log("Rule type from data:", data.type);
    console.log("Rule type from selectedRule:", selectedRule?.type);
    console.log("Final rule type used:", ruleType);
    
    const isRevenuePricing = ruleType === 'revenue';
    const baseUrl = isRevenuePricing ? '/api/v1/pricing/revenue-pricing' : '/api/v1/pricing/cost-pricing';
    
    const url = selectedRule?.id
      ? `${baseUrl}/${selectedRule.id}`
      : baseUrl

    // Use PATCH instead of PUT for updates
    const method = selectedRule?.id ? "PATCH" : "POST"

    // Transform data to match backend schema
    const transformedData = {
      partner_id: selectedPartner.id,
      billing_method: data.billingMethod,
      service_type: data.serviceType,
      volume_range_id: data.volumeRangeId,
      price: typeof data.price === 'string' ? parseFloat(data.price) : data.price,
      description: data.description,
      is_active: true
    }

    try {
      console.log('Submitting pricing rule:', transformedData);
      console.log('URL:', url);
      console.log('Method:', method);
      console.log('Rule type:', ruleType);
      console.log('Is revenue pricing:', isRevenuePricing);

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(transformedData),
      })

      console.log('Response status:', response.status);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        console.error('Error response:', errorData);
        throw new Error(
          errorData?.message || 
          `Failed to ${selectedRule?.id ? 'update' : 'create'} ${isRevenuePricing ? 'revenue' : 'cost'} pricing rule`
        )
      }

      const responseData = await response.json()
      console.log('API Response:', responseData)

      toast({
        title: "Success",
        description: `${isRevenuePricing ? 'Revenue' : 'Cost'} pricing rule ${selectedRule?.id ? 'updated' : 'created'} successfully`,
      })

      setIsDialogOpen(false)
      // Refresh the table using the ref
      if (refreshFnRef.current) {
        console.log('Refreshing pricing rules after submit')
        await refreshFnRef.current()
      }
    } catch (error) {
      console.error('Error submitting pricing rule:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
      })
    }
    console.log("=== SUBMIT DEBUG END ===");
  }

  const handleDeleteRule = async (id: number, type: 'revenue' | 'cost') => {
    console.log("=== DELETE RULE DEBUG ===");
    console.log("Deleting rule with ID:", id);
    console.log("Rule type:", type);
    
    const baseUrl = type === 'revenue' ? '/api/v1/pricing/revenue-pricing' : '/api/v1/pricing/cost-pricing'
    console.log("API endpoint:", `${baseUrl}/${id}`);
    
    try {
      const response = await fetch(`${baseUrl}/${id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        throw new Error(
          errorData?.message || 
          `Failed to delete ${type} pricing rule`
        )
      }

      toast({
        title: "Success",
        description: `${type === 'revenue' ? 'Revenue' : 'Cost'} pricing rule deleted successfully`,
      })

      // Refresh the table after successful deletion
      if (refreshFnRef.current) {
        await refreshFnRef.current()
      }
    } catch (error) {
      console.error('Error deleting pricing rule:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
      })
    }
  }

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-4">Quy tắc tính cước</h1>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Chọn đối tác:</span>
            <PartnerSelector />
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Chế độ xem:</span>
            <div className="flex border rounded-md overflow-hidden">
              <Button
                variant={viewMode === 'table' ? 'default' : 'ghost'}
                size="sm"
                className="rounded-none"
                onClick={() => setViewMode('table')}
              >
                <TableIcon className="h-4 w-4 mr-2" />
                Bảng
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                className="rounded-none"
                onClick={() => setViewMode('grid')}
              >
                <GridIcon className="h-4 w-4 mr-2" />
                Lưới
              </Button>
            </div>
          </div>
        </div>
      </div>

      <Tabs 
        defaultValue="revenue" 
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as 'revenue' | 'cost')}
        className="mb-6"
      >
        <TabsList className="grid w-[400px] grid-cols-2">
          <TabsTrigger value="revenue">Thu (Revenue)</TabsTrigger>
          <TabsTrigger value="cost">Chi (Cost)</TabsTrigger>
        </TabsList>
        
        <TabsContent value="revenue">
          {viewMode === 'table' ? (
            <PricingTable 
              type="revenue"
              onAddRule={handleAddRule} 
              onEditRule={handleEditRule}
              onDeleteRule={handleDeleteRule}
              onRefresh={(refreshFn) => {
                refreshFnRef.current = refreshFn;
              }}
            />
          ) : (
            <PricingGrid
              type="revenue"
              onAddRule={handleAddRule} 
              onEditRule={handleEditRule}
              onDeleteRule={handleDeleteRule}
              onRefresh={(refreshFn) => {
                refreshFnRef.current = refreshFn;
              }}
            />
          )}
        </TabsContent>
        
        <TabsContent value="cost">
          {viewMode === 'table' ? (
            <PricingTable 
              type="cost"
              onAddRule={handleAddRule} 
              onEditRule={handleEditRule}
              onDeleteRule={handleDeleteRule}
              onRefresh={(refreshFn) => {
                refreshFnRef.current = refreshFn;
              }}
            />
          ) : (
            <PricingGrid
              type="cost"
              onAddRule={handleAddRule} 
              onEditRule={handleEditRule}
              onDeleteRule={handleDeleteRule}
              onRefresh={(refreshFn) => {
                refreshFnRef.current = refreshFn;
              }}
            />
          )}
        </TabsContent>
      </Tabs>
      
      <PricingDialog
        isOpen={isDialogOpen}
        onClose={() => {
          setIsDialogOpen(false)
          setSelectedRule(undefined)
          setSelectedService({})
        }}
        onSubmit={handleSubmit}
        initialData={selectedRule}
        preSelectedServiceId={selectedService.id}
        preSelectedServiceName={selectedService.name}
        preSelectedServiceCode={selectedService.code}
      />
    </div>
  )
} 