export interface DoiSoatTemplate {
    id: number;
    name: string;
    description: string | null;
    template_type: string;
    file_path: string;
    uploaded_by: number;
    is_active: boolean;
    status: string;
    created_at: string;
    updated_at: string;
    detected_type?: string;
    detection_confidence?: number;
}

export const TemplateTypes = {
    dsc: '<PERSON><PERSON><PERSON> soát cước',
    dscd: 'Đ<PERSON><PERSON> soát cố định',
    dst_1800_1900: 'Đối soát 1800/1900',
} as const;

export type TemplateType = keyof typeof TemplateTypes; 