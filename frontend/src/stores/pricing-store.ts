import { create } from 'zustand';
import { StateCreator } from 'zustand';
import { 
  FormData, 
  BulkPricingCreate, 
  RevenuePricing, 
  CostPricing,
  ApiResponse,
  PaginatedResponse,
  ApiError,
  ServiceType,
  VolumeRange,
  BillingMethod
} from '@/types/pricing';
import { api } from '@/lib/api';

interface PricingState {
  formData: FormData | null;
  revenuePricing: RevenuePricing[];
  costPricing: CostPricing[];
  isLoading: boolean;
  error: string | null;
}

interface PricingActions {
  fetchFormData: () => Promise<FormData>;
  fetchRevenuePricing: (partnerId: number) => Promise<RevenuePricing[]>;
  fetchCostPricing: (partnerId: number) => Promise<CostPricing[]>;
  createBulkRevenuePricing: (data: BulkPricingCreate) => Promise<RevenuePricing[]>;
  createBulkCostPricing: (data: BulkPricingCreate) => Promise<CostPricing[]>;
  resetError: () => void;
  reset: () => void;
  createServiceType: (data: Partial<ServiceType>) => Promise<ServiceType>;
  createVolumeRange: (data: Partial<VolumeRange>) => Promise<VolumeRange>;
}

type PricingStore = PricingState & PricingActions;

type PricingStoreState = StateCreator<
  PricingStore,
  [],
  [],
  PricingState
>;

const initialState: PricingState = {
  formData: null,
  revenuePricing: [],
  costPricing: [],
  isLoading: false,
  error: null,
};

export const usePricingStore = create<PricingStore>((set, get) => ({
  ...initialState,

  fetchFormData: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.get<FormData>('/v1/pricing/form-data');
      const data = response.data;
      set({ formData: data, isLoading: false });
      return data;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to fetch form data';
      set({ error: message, isLoading: false });
      throw error;
    }
  },

  fetchRevenuePricing: async (partnerId: number) => {
    set({ isLoading: true, error: null });
    try {
      const { data } = await api.get<ApiResponse<RevenuePricing[]>>(
        `/v1/pricing/revenue-pricing/by-partner/${partnerId}`
      );
      set({ revenuePricing: data.data });
      return data.data;
    } catch (error) {
      const apiError = error as ApiError;
      const message = apiError.message || 'Failed to fetch revenue pricing';
      set({ error: message });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  fetchCostPricing: async (partnerId: number) => {
    set({ isLoading: true, error: null });
    try {
      const { data } = await api.get<ApiResponse<CostPricing[]>>(
        `/v1/pricing/cost-pricing/by-partner/${partnerId}`
      );
      set({ costPricing: data.data });
      return data.data;
    } catch (error) {
      const apiError = error as ApiError;
      const message = apiError.message || 'Failed to fetch cost pricing';
      set({ error: message });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  createBulkRevenuePricing: async (data: BulkPricingCreate) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.post<ApiResponse<RevenuePricing[]>>(
        '/v1/pricing/revenue-pricing/bulk',
        data
      );
      const newPricing = response.data.data;
      set((state: PricingState) => ({
        revenuePricing: [...state.revenuePricing, ...newPricing]
      }));
      return newPricing;
    } catch (error) {
      const apiError = error as ApiError;
      const message = apiError.message || 'Failed to create revenue pricing';
      set({ error: message });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  createBulkCostPricing: async (data: BulkPricingCreate) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.post<ApiResponse<CostPricing[]>>(
        '/v1/pricing/cost-pricing/bulk',
        data
      );
      const newPricing = response.data.data;
      set((state: PricingState) => ({
        costPricing: [...state.costPricing, ...newPricing]
      }));
      return newPricing;
    } catch (error) {
      const apiError = error as ApiError;
      const message = apiError.message || 'Failed to create cost pricing';
      set({ error: message });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  resetError: () => set({ error: null }),
  reset: () => set(initialState),

  createServiceType: async (data) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.post<ServiceType>('/v1/pricing/service-types', data);
      const serviceType = response.data;
      
      // Update the store with the new service type
      const currentFormData = get().formData;
      if (currentFormData) {
        set({
          formData: {
            ...currentFormData,
            serviceTypes: [...currentFormData.serviceTypes, serviceType]
          }
        });
      }
      
      set({ isLoading: false });
      return serviceType;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to create service type';
      set({ error: message, isLoading: false });
      throw error;
    }
  },

  createVolumeRange: async (data) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.post<VolumeRange>('/v1/pricing/volume-ranges', data);
      const volumeRange = response.data;
      
      // Update the store with the new volume range
      const currentFormData = get().formData;
      if (currentFormData) {
        set({
          formData: {
            ...currentFormData,
            volumeRanges: [...currentFormData.volumeRanges, volumeRange]
          }
        });
      }
      
      set({ isLoading: false });
      return volumeRange;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to create volume range';
      set({ error: message, isLoading: false });
      throw error;
    }
  }
})); 