import { create } from 'zustand';
import { api } from '@/lib/api';

export interface Partner {
  id: number;
  name: string;
  code: string;
  is_active: boolean;
}

interface PartnerState {
  partners: Partner[];
  selectedPartner: Partner | null;
  isLoading: boolean;
  error: string | null;
}

interface PartnerActions {
  fetchPartners: () => Promise<Partner[]>;
  selectPartner: (partner: Partner) => void;
}

type PartnerStore = PartnerState & PartnerActions;

export const usePartnerStore = create<PartnerStore>((set) => ({
  partners: [],
  selectedPartner: null,
  isLoading: false,
  error: null,

  fetchPartners: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.get<Partner[]>('/api/v1/partners');
      const data = response.data;
      set({ partners: data });
      return data;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to fetch partners';
      set({ error: message });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  selectPartner: (partner: Partner) => {
    set({ selectedPartner: partner });
  }
}));