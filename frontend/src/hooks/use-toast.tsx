import { toast } from "@/components/ui/use-toast";

interface ToastOptions {
  duration?: number;
}

export function useToast() {
  return {
    success: (message: string, options?: ToastOptions) => {
      toast({
        title: "Success",
        description: message,
        duration: options?.duration || 3000,
      });
    },
    error: (message: string, options?: ToastOptions) => {
      toast({
        title: "Error",
        description: message,
        variant: "destructive",
        duration: options?.duration || 5000,
      });
    },
    warning: (message: string, options?: ToastOptions) => {
      toast({
        title: "Warning",
        description: message,
        variant: "warning",
        duration: options?.duration || 4000,
      });
    },
    info: (message: string, options?: ToastOptions) => {
      toast({
        title: "Info",
        description: message,
        duration: options?.duration || 3000,
      });
    },
  };
}