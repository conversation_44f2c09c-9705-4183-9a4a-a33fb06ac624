import { toast } from "@/components/ui/use-toast";

interface NotifyOptions {
  duration?: number;
}

export function useNotify() {
  return {
    success: (message: string, options?: NotifyOptions) => {
      toast({
        title: "Success",
        description: message,
        duration: options?.duration || 3000,
      });
    },
    error: (message: string, options?: NotifyOptions) => {
      toast({
        title: "Error",
        description: message,
        variant: "destructive",
        duration: options?.duration || 5000,
      });
    },
    warning: (message: string, options?: NotifyOptions) => {
      toast({
        title: "Warning",
        description: message,
        variant: "warning",
        duration: options?.duration || 4000,
      });
    },
    info: (message: string, options?: NotifyOptions) => {
      toast({
        title: "Info",
        description: message,
        duration: options?.duration || 3000,
      });
    },
  };
} 