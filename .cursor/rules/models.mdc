---
description: 
globs: **/models/*.py,**/schemas/*.py
alwaysApply: false
---
 ---
description: <PERSON>uy tắc chi tiết về models trong AuditCall
globs: ["**/models/*.py", "**/schemas/*.py"]
alwaysApply: false
---

# AuditCall Models Rules

## 1. Tổng quan về Models

Models trong AuditCall là các lớp đại diện cho cấu trúc dữ liệu chính của hệ thống, đ<PERSON><PERSON><PERSON> ánh xạ vào cơ sở dữ liệu thông qua SQLAlchemy ORM. Models cung cấp cấu trúc dữ liệu cho các loại đố<PERSON> so<PERSON> (DSC, DSCD, DST_1800_1900) và các thông tin liên quan.

## 2. Cấu trúc chung của Model

Mỗi model cần tuân thủ cấu trúc chung sau:

```python
# Import các thư viện cần thiết
from sqlalchemy import Column, Integer, String, Float, ForeignKey, <PERSON><PERSON>an, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
import datetime

# Khai báo Base
Base = declarative_base()

class ModelName(Base):
    """Docstring mô tả model"""
    
    __tablename__ = "table_name"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Các field cơ bản
    name = Column(String, index=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # Relationships
    related_items = relationship("RelatedModel", back_populates="model_name")
    
    def __repr__(self):
        return f"<ModelName(id={self.id}, name={self.name})>"
```

## 3. Quy tắc chung cho Models

1. **Chia nhỏ hợp lý**: Mỗi file model chỉ chứa các models liên quan đến một domain cụ thể
2. **Docstrings đầy đủ**: Mỗi model và field quan trọng cần có docstring mô tả rõ ràng
3. **Typed**: Sử dụng type hints cho các phương thức và thuộc tính
4. **Normalized**: Tuân thủ nguyên tắc chuẩn hóa cơ sở dữ liệu (tránh dư thừa dữ liệu)
5. **Indexes**: Đặt index cho các cột thường được tìm kiếm hoặc filter

## 4. Models chính trong hệ thống

### 4.1. User Model

```python
class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    full_name = Column(String)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
```

- **Trách nhiệm**: Quản lý thông tin người dùng và phân quyền
- **Mối quan hệ**: Liên kết với các thông tin log, audit trail

### 4.2. Partner Model

```python
class Partner(Base):
    __tablename__ = "partners"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    code = Column(String, unique=True, index=True)
    contact_info = Column(String)
    is_active = Column(Boolean, default=True)
```

- **Trách nhiệm**: Quản lý thông tin đối tác viễn thông
- **Mối quan hệ**: Liên kết với DoiSoatCuoc, DoiSoatCoDinh, Templates

### 4.3. DoiSoatCuoc (DSC) Models

#### 4.3.1. DoiSoatCuoc

```python
class DoiSoatCuoc(Base):
    __tablename__ = "doi_soat_cuoc"
    
    id = Column(Integer, primary_key=True, index=True)
    thang_doi_soat = Column(String, index=True)
    nam_doi_soat = Column(String, index=True)
    tu_mang = Column(String, index=True)
    den_doi_tac = Column(String, index=True)
    ngay_tao = Column(DateTime, default=datetime.datetime.utcnow)
    
    # Relationships
    dau_so_dich_vu = relationship("DauSoDichVuCuoc", back_populates="doi_soat_cuoc")
    tong_ket = relationship("TongKetCuoc", uselist=False, back_populates="doi_soat_cuoc")
```

- **Trách nhiệm**: Lưu thông tin chung của đối soát cước
- **Mối quan hệ**: One-to-many với DauSoDichVuCuoc, one-to-one với TongKetCuoc

#### 4.3.2. DauSoDichVuCuoc

```python
class DauSoDichVuCuoc(Base):
    __tablename__ = "dau_so_dich_vu_cuoc"
    
    id = Column(Integer, primary_key=True, index=True)
    doi_soat_cuoc_id = Column(Integer, ForeignKey("doi_soat_cuoc.id"))
    dau_so = Column(String, index=True)
    
    # Thông tin nhà mạng
    vnm_san_luong = Column(Integer)
    vnm_thanh_tien = Column(Float)
    viettel_san_luong = Column(Integer)
    viettel_thanh_tien = Column(Float)
    vnpt_san_luong = Column(Integer)
    vnpt_thanh_tien = Column(Float)
    vms_san_luong = Column(Integer)
    vms_thanh_tien = Column(Float)
    
    # Tổng
    tong_san_luong = Column(Integer)
    tong_thanh_tien = Column(Float)
    
    # Relationship
    doi_soat_cuoc = relationship("DoiSoatCuoc", back_populates="dau_so_dich_vu")
```

### 4.4. DoiSoatCoDinh (DSCD) Models

#### 4.4.1. DoiSoatCoDinh

```python
class DoiSoatCoDinh(Base):
    __tablename__ = "doi_soat_co_dinh"
    
    id = Column(Integer, primary_key=True, index=True)
    thang_doi_soat = Column(String, index=True)
    nam_doi_soat = Column(String, index=True)
    tu_mang = Column(String, index=True)
    den_doi_tac = Column(String, index=True)
    ngay_tao = Column(DateTime, default=datetime.datetime.utcnow)
    
    # Relationships
    dau_so_dich_vu = relationship("DauSoDichVu", back_populates="doi_soat_co_dinh")
    cuoc_goi = relationship("CuocGoi", back_populates="doi_soat_co_dinh")
    cuoc_thue_bao = relationship("CuocThueBao", back_populates="doi_soat_co_dinh")
    tong_ket = relationship("TongKet", uselist=False, back_populates="doi_soat_co_dinh")
```

#### 4.4.2. CuocGoi, CuocThueBao và các model liên quan

- Cấu trúc phức tạp với nhiều loại cước khác nhau
- Thiết kế linh hoạt để hỗ trợ nhiều định dạng file khác nhau
- Quan hệ one-to-many với DoiSoatCoDinh

### 4.5. DST_1800_1900 Models

```python
class DST1800_1900(Base):
    __tablename__ = "dst_1800_1900"
    
    id = Column(Integer, primary_key=True, index=True)
    thang_doi_soat = Column(String, index=True)
    nam_doi_soat = Column(String, index=True)
    dau_so_dich_vu = Column(String, index=True)
    loai_dau_so = Column(String)  # 1800 hoặc 1900
    
    # Thông tin chi tiết
    tong_so_cuoc_goi = Column(Integer)
    tong_thoi_gian = Column(Integer)  # Tổng thời gian (giây)
    tong_thanh_tien = Column(Float)
```

## 5. Schemas (Pydantic Models)

Schemas được sử dụng để:

1. **Validation dữ liệu đầu vào**: Kiểm tra dữ liệu đầu vào API
2. **Định dạng dữ liệu đầu ra**: Chuẩn hóa response của API
3. **Tài liệu tự động**: Tạo docs cho API

### 5.1. Cấu trúc Schema

```python
from pydantic import BaseModel, Field, validator
from typing import List, Optional
import datetime

class UserBase(BaseModel):
    email: str
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserUpdate(UserBase):
    password: Optional[str] = None
    is_active: Optional[bool] = None

class User(UserBase):
    id: int
    is_active: bool
    is_admin: bool
    
    class Config:
        orm_mode = True
```

### 5.2. Quy tắc Schema

1. **Tách biệt Base/Create/Update**: Tạo các schema riêng cho các operation khác nhau
2. **Validators**: Thêm các validator cho dữ liệu phức tạp
3. **ORM Mode**: Sử dụng orm_mode=True để tương thích với SQLAlchemy ORM
4. **Docs**: Sử dụng Field để cung cấp mô tả cho OpenAPI

## 6. Migrations

Migrations được quản lý bởi Alembic và tuân thủ các quy tắc sau:

1. **Mô tả rõ ràng**: Mỗi migration có mô tả rõ ràng về mục đích
2. **Backwards compatible**: Đảm bảo có thể rollback an toàn
3. **Kiểm tra trước khi triển khai**: Chạy thử nghiệm trước khi áp dụng
4. **Versioning**: Theo dõi version của schema để dễ quản lý

## 7. Relationships và Data Integrity

### 7.1. Cascade Delete

Cần cẩn trọng khi sử dụng cascade delete để tránh mất dữ liệu quan trọng:

```python
# Ví dụ về cascade delete
items = relationship("Item", cascade="all, delete-orphan")
```

### 7.2. Lazy Loading vs Eager Loading

Tùy thuộc vào trường hợp sử dụng, chọn phương pháp loading phù hợp:

```python
# Lazy loading (mặc định)
items = relationship("Item")

# Eager loading với joinedload
items = relationship("Item", lazy="joined")

# Eager loading với selectinload
items = relationship("Item", lazy="selectin")
```

## 8. Performance Considerations

1. **Indexes**: Đặt index cho các cột thường được tìm kiếm
2. **Composite indexes**: Sử dụng cho các truy vấn phức tạp
3. **Bulk operations**: Sử dụng bulk insert/update cho dữ liệu lớn
4. **Limiting relationship loads**: Hạn chế eager loading khi không cần thiết

## 9. Testing Models

1. **Factory Pattern**: Sử dụng factories để tạo test data
2. **Fixtures**: Tận dụng fixtures để tái sử dụng test setup
3. **Isolation**: Đảm bảo mỗi test có môi trường độc lập

## 10. Extending Models

Khi mở rộng hoặc thay đổi models, cần:

1. **Viết migration**: Tạo migration để cập nhật schema
2. **Cập nhật schemas**: Cập nhật Pydantic schemas tương ứng
3. **Kiểm tra tính tương thích**: Đảm bảo không ảnh hưởng đến code hiện có
4. **Cập nhật docs**: Cập nhật tài liệu mô tả models