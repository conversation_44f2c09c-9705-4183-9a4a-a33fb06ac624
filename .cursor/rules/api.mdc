---
description: 
globs: **/api/**/*.py,**/auth/**/*.py
alwaysApply: false
---
---
description: Quy tắc chi tiết về API endpoints trong AuditCall
globs: ["**/api/**/*.py", "**/*router*.py", "**/*endpoint*.py"]
alwaysApply: false
---

# AuditCall API Rules

## 1. Tổng quan về API

API trong AuditCall được xây dựng trên FastAPI, tổ chức theo cấu trúc RESTful và phân chia theo phiên bản. API cung cấp các endpoints để quản lý người dùng, đ<PERSON><PERSON> tác, đối soát dữ liệu, và các chức năng khác của hệ thống.

## 2. Cấu trúc API

```
backend/
  └── src/
      └── api/
          └── v1/
              ├── api.py            # Main router aggregator
              └── endpoints/        # Endpoint module directory
                  ├── auth.py       # Authentication endpoints
                  ├── users.py      # User management
                  ├── partners.py   # Partner management
                  ├── doi_soat.py   # Đối soát processing
                  ├── call_logs.py  # Call logs
                  ├── templates.py  # Template management
                  ├── pricing.py    # Pricing rules
                  └── statistics.py # Statistics and reporting
```

## 3. Quy tắc chung cho API Endpoints

### 3.1. Cấu trúc endpoint

```python
@router.get(
    "/{item_id}",
    response_model=schemas.Item,
    summary="Get an item by ID",
    status_code=status.HTTP_200_OK,
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Item not found"},
        status.HTTP_403_FORBIDDEN: {"description": "Permission denied"}
    }
)
async def get_item(
    item_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user)
):
    """
    Lấy thông tin của một item theo ID.
    
    - **item_id**: ID của item cần lấy thông tin
    """
    item = crud.item.get(db, id=item_id)
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Item not found"
        )
    
    # Kiểm tra quyền truy cập
    if not crud.user.is_admin(current_user) and item.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
        
    return item
```

### 3.2. Naming conventions

1. **URL Paths**: Sử dụng kebab-case (hyphen-separated) cho URL paths
   ```
   /doi-soat/upload
   /call-logs/process
   ```

2. **Query Parameters**: Sử dụng snake_case cho query parameters
   ```
   /users?is_active=true&role_type=admin
   ```

3. **Request Bodies**: Tuân theo Pydantic model naming conventions

### 3.3. HTTP Methods

- **GET**: Lấy dữ liệu, không thay đổi trạng thái
- **POST**: Tạo mới resource
- **PUT**: Cập nhật toàn bộ resource
- **PATCH**: Cập nhật một phần resource
- **DELETE**: Xóa resource

## 4. Phân quyền và Bảo mật

### 4.1. Xác thực người dùng

```python
def get_current_user(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> models.User:
    """
    Xác thực và lấy thông tin user từ token.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
        
    user = crud.user.get_by_email(db, email=username)
    if user is None:
        raise credentials_exception
        
    return user
```

### 4.2. Kiểm tra quyền admin

```python
def get_current_active_admin(
    current_user: models.User = Depends(get_current_active_user),
) -> models.User:
    """
    Kiểm tra user có quyền admin không.
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges"
        )
    return current_user
```

## 5. API chính trong hệ thống

### 5.1. Authentication API (`/api/v1/auth`)

- **POST /login**: Đăng nhập và lấy access token
- **POST /logout**: Đăng xuất và vô hiệu hóa token
- **POST /refresh-token**: Làm mới access token

### 5.2. Users API (`/api/v1/users`)

- **GET /**: Lấy danh sách users (admin only)
- **POST /**: Tạo user mới (admin only)
- **GET /me**: Lấy thông tin user hiện tại
- **PUT /me**: Cập nhật thông tin user hiện tại
- **GET /{user_id}**: Lấy thông tin user theo ID (admin only)
- **PUT /{user_id}**: Cập nhật thông tin user theo ID (admin only)
- **DELETE /{user_id}**: Xóa user theo ID (admin only)

### 5.3. Đối Soát API (`/api/v1/doi-soat`)

- **POST /upload**: Upload file đối soát và phân loại tự động
- **POST /process**: Xử lý file đối soát đã upload
- **GET /status/{file_id}**: Kiểm tra trạng thái xử lý
- **GET /result/{file_id}**: Lấy kết quả xử lý

### 5.4. Templates API (`/api/v1/templates`)

- **GET /**: Lấy danh sách templates
- **POST /**: Tạo template mới
- **GET /{template_id}**: Lấy thông tin template
- **PUT /{template_id}**: Cập nhật template
- **DELETE /{template_id}**: Xóa template
- **POST /{template_id}/process**: Xử lý template

### 5.5. Partners API (`/api/v1/partners`)

- **GET /**: Lấy danh sách đối tác
- **POST /**: Tạo đối tác mới
- **GET /{partner_id}**: Lấy thông tin đối tác
- **PUT /{partner_id}**: Cập nhật thông tin đối tác
- **DELETE /{partner_id}**: Xóa đối tác

### 5.6. Pricing API (`/api/v1/pricing`)

- **GET /**: Lấy danh sách pricing rules
- **POST /**: Tạo pricing rule mới
- **GET /{rule_id}**: Lấy thông tin pricing rule
- **PUT /{rule_id}**: Cập nhật pricing rule
- **DELETE /{rule_id}**: Xóa pricing rule

## 6. Response Format

### 6.1. Success Response

```json
{
  "success": true,
  "data": {
    // Response data here
  },
  "message": "Operation completed successfully"
}
```

### 6.2. Error Response

```json
{
  "success": false,
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "The requested resource was not found",
    "details": {
      // Additional error details
    }
  }
}
```

### 6.3. Pagination Response

```json
{
  "items": [
    // Array of items
  ],
  "total": 100,
  "page": 1,
  "size": 10,
  "pages": 10
}
```

## 7. API Documentation

### 7.1. OpenAPI Schema

- Đảm bảo tất cả các endpoints đều có `summary`, `description`, và `response_model`
- Mô tả rõ các parameters và request bodies
- Khai báo đầy đủ các response status codes có thể xảy ra

### 7.2. Docstrings

- Mỗi endpoint phải có docstring mô tả chức năng
- Liệt kê các parameters và ý nghĩa
- Nêu rõ các điều kiện và ràng buộc

## 8. Error Handling

### 8.1. Định nghĩa Custom Exceptions

```python
class NotFoundError(Exception):
    """Exception raised when a resource is not found."""
    def __init__(self, resource_type: str, resource_id: Any):
        self.resource_type = resource_type
        self.resource_id = resource_id
        self.message = f"{resource_type} with id {resource_id} not found"
        super().__init__(self.message)

class PermissionDeniedError(Exception):
    """Exception raised when user does not have permission."""
    def __init__(self, resource_type: str, action: str):
        self.resource_type = resource_type
        self.action = action
        self.message = f"Permission denied to {action} {resource_type}"
        super().__init__(self.message)
```

### 8.2. Global Exception Handler

```python
@app.exception_handler(NotFoundError)
async def not_found_exception_handler(request: Request, exc: NotFoundError):
    return JSONResponse(
        status_code=status.HTTP_404_NOT_FOUND,
        content={
            "success": False,
            "error": {
                "code": "RESOURCE_NOT_FOUND",
                "message": exc.message,
                "details": {
                    "resource_type": exc.resource_type,
                    "resource_id": exc.resource_id
                }
            }
        }
    )
```

## 9. API Versioning

- Sử dụng URL path versioning (`/api/v1/...`)
- Khi có thay đổi breaking change, tăng major version
- Giữ tương thích ngược với các phiên bản cũ trong một thời gian

## 10. Best Practices

1. **Validation**: Sử dụng Pydantic để validate tất cả input data
2. **Background Tasks**: Sử dụng background tasks cho các xử lý nặng
3. **Rate Limiting**: Áp dụng rate limiting cho các API công khai
4. **Caching**: Sử dụng caching cho các endpoints đọc nhiều
5. **Logging**: Log tất cả các requests quan trọng để debug
6. **Testing**: Viết tests đầy đủ cho tất cả endpoints