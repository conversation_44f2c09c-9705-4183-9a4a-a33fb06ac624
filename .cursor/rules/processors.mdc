---
description: <PERSON><PERSON> tắc chi tiết về processors trong AuditCall
globs: **/utils/*.py,**/worker/*py
alwaysApply: false
---

# AuditCall Processors Rules

## 1. Tổng quan về Processors

Processors là các module xử lý file đối soát, chịu trách nhiệm phân loại, trích xuất và phân tích dữ liệu từ các file Excel do đối tác cung cấp. Mỗi loại file đối soát (DSC, DSCD, DST_1800_1900, CKN) có một processor chuyên biệt.

## 2. C<PERSON>u trúc chung của Processor

Mỗi processor cần tuân thủ cấu trúc chung sau:

```python
# Import các thư viện cần thiết
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
import logging

# Cấu hình logging
logger = logging.getLogger(__name__)

# Các pattern nhận dạng file
FILE_PATTERNS = [...]

# Các keywords để tìm cột dữ liệu
COLUMN_KEYWORDS = {...}

def is_x_file(filename: str) -> bool:
    """Kiểm tra xem file có phải là loại X không"""
    # Implementation

def extract_month_year(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """Trích xuất thông tin tháng và năm từ tên file"""
    # Implementation

def extract_partners(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """Trích xuất thông tin đối tác từ tên file"""
    # Implementation

def process_x_file(file_path: str, **kwargs) -> Dict[str, Any]:
    """Xử lý file và trả về dữ liệu đã xử lý"""
    # Implementation
```

## 3. Quy tắc chung cho tất cả Processors

1. **Validation trước khi xử lý**: Luôn kiểm tra tính hợp lệ của file trước khi xử lý
2. **Logging đầy đủ**: Ghi log chi tiết ở mỗi bước quan trọng và khi có lỗi
3. **Xử lý ngoại lệ**: Bắt và xử lý tất cả các exception có thể xảy ra
4. **Tối ưu hiệu suất**: Sử dụng các phương pháp xử lý hiệu quả cho file lớn
5. **Khả năng mở rộng**: Thiết kế để dễ dàng thêm các định dạng file mới

## 4. DSC Processor (Đối soát cước)

### 4.1. Đặc điểm nhận dạng
- Tên file thường chứa: "doi soat cuoc", "DSC", "cuoc", "doanh thu cuoc"
- Cấu trúc file chứa thông tin sản lượng và thành tiền cho các đầu số dịch vụ

### 4.2. Các bước xử lý
1. Xác định file bằng `is_dsc_file()`
2. Trích xuất thông tin tháng/năm từ tên file
3. Trích xuất thông tin đối tác từ tên file
4. Đọc nội dung file Excel và xác định vị trí dữ liệu
5. Phân tích cấu trúc bảng và map các cột
6. Trích xuất dữ liệu thành các đối tượng dữ liệu cấu trúc
7. Tính toán các giá trị tổng hợp nếu cần

### 4.3. Keywords quan trọng
```python
SAN_LUONG_KEYWORDS = ["SẢN LƯỢNG", "SAN LUONG", "CUỘC GỌI"]
THANH_TIEN_KEYWORDS = ["THÀNH TIỀN", "THANH TIEN", "DOANH THU"]
TELCO_KEYWORDS = {
    "vnm": ["VIETNAMOBILE", "VNM", "VIETNAM MOBILE"],
    "viettel": ["VIETTEL", "VT"],
    "vnpt": ["VNPT", "VINAPHONE"],
    "vms": ["MOBIFONE", "VMS", "MOBI"]
}
```

## 5. DSCD Processor (Đối soát cố định)

### 5.1. Đặc điểm nhận dạng
- Tên file thường chứa: "co dinh", "DSCD", "thue bao"
- Cấu trúc file phức tạp với nhiều sheet và các loại cước khác nhau

### 5.2. Các bước xử lý
1. Xác định file bằng `is_dscd_file()`
2. Trích xuất thông tin tháng/năm từ tên file
3. Trích xuất thông tin đối tác từ tên file
4. Đọc từng sheet trong file Excel
5. Phân tích cấu trúc từng loại bảng (cước thuê bao, cước cuộc gọi...)
6. Trích xuất và tổng hợp dữ liệu từ các bảng
7. Tạo các đối tượng dữ liệu cấu trúc

### 5.3. Các loại cước cần xử lý
- Cước thuê bao
- Cước cuộc gọi cố định nội hạt
- Cước cuộc gọi cố định liên tỉnh
- Cước cuộc gọi cố định đến di động
- Cước cuộc gọi quốc tế
- Cước dịch vụ giá trị gia tăng

## 6. DST 1800/1900 Processor

### 6.1. Đặc điểm nhận dạng
- Tên file thường chứa: "1800", "1900", "BBDS"
- Cấu trúc file đặc thù cho dịch vụ tổng đài 1800/1900

### 6.2. Các bước xử lý
1. Xác định file bằng `is_dst_1800_1900_file()`
2. Trích xuất thông tin tháng/năm từ tên file
3. Đọc các sheet trong file Excel
4. Xác định loại đầu số (1800 hoặc 1900)
5. Phân tích cấu trúc bảng dữ liệu
6. Trích xuất thông tin theo từng đầu số
7. Tổng hợp dữ liệu và tính toán các giá trị tổng

### 6.3. Những điểm đặc biệt
- Cần phân biệt mô hình tính cước khác nhau giữa 1800 và 1900
- Chú ý các quy tắc đặc thù cho từng đầu số
- Xử lý các trường hợp đặc biệt (khuyến mãi, ưu đãi...)

## 7. CKN Processor (Cước kết nối)

### 7.1 Đặc điểm nhận dạng
- Tên file thường chứa: "CKN", "cuoc ket noi", "BBDS CKN", "BBLL"
- Cấu trúc theo biên bản đối soát cước kết nối

### 7.2 Các bước xử lý
1. Xác định file bằng `is_ckn_file()`
2. Trích xuất thông tin tháng/năm và đối tác
3. Đọc nội dung file và phân tích cấu trúc
4. Trích xuất thông tin sản lượng và doanh thu
5. Tính toán và đối soát các giá trị tổng

## 8. Quy trình mở rộng Processor

Khi cần thêm processor mới hoặc mở rộng processor hiện tại:

1. **Nghiên cứu định dạng file**: Phân tích kỹ các mẫu file mới
2. **Thêm patterns nhận dạng**: Cập nhật các pattern để nhận dạng đúng loại file
3. **Viết tests**: Viết unit tests và integration tests cho các tính năng mới
4. **Đảm bảo khả năng tương thích**: Processor mới không ảnh hưởng đến các processor hiện tại
5. **Tài liệu hóa**: Cập nhật tài liệu về cách sử dụng processor mới

## 9. Tối ưu hiệu suất

1. **Đọc file hiệu quả**: Chỉ đọc các sheet và vùng dữ liệu cần thiết
2. **Xử lý song song**: Sử dụng concurrent.futures cho các tác vụ độc lập
3. **Caching**: Cache các kết quả trung gian cho các file lớn
4. **Giới hạn bộ nhớ**: Kiểm soát việc sử dụng bộ nhớ khi xử lý file lớn
5. **Profile và benchmark**: Đo hiệu suất và tối ưu các bottleneck

## 10. Testing và Validation

1. **Unit tests**: Kiểm tra từng function trong processor
2. **Integration tests**: Kiểm tra quy trình xử lý hoàn chỉnh
3. **Test với dữ liệu thực**: Sử dụng các file thực tế để kiểm tra
4. **Test hiệu suất**: Kiểm tra thời gian xử lý và sử dụng bộ nhớ
5. **Validation dữ liệu**: Kiểm tra tính nhất quán của dữ liệu kết quả 