---
description: 
globs: 
alwaysApply: false
---

# AuditCall Project Rules

## 1. Tổng quan dự án

AuditCall là một ứng dụng kiểm toán cuộc gọi (call auditing) phát triển với FastAPI (backend) và React/TypeScript (frontend). Dự án hỗ trợ đối soát cước giữa các đối tác viễn thông.

## 2. Thuật ngữ và khái niệm

- **DSC (Đối Soát Cước)**: Đ<PERSON>i soát tiền cước giữa các nhà mạng
- **DSCD (Đối Soát Cố Định)**: Đ<PERSON><PERSON> soát cho các dịch vụ cố định
- **DST (Đối Soát Tổng)**: <PERSON><PERSON><PERSON> so<PERSON><PERSON> tổng hợp, bao gồm các dịch vụ 1800/1900
- **CKN (Cước Kết Nối)**: <PERSON><PERSON><PERSON> soát cước kết nối giữa các nhà mạng
- **HTC**: <PERSON><PERSON><PERSON> mạng nguồn/chính
- **<PERSON><PERSON><PERSON> tá<PERSON>**: <PERSON><PERSON><PERSON> nhà mạng như VNPT, Viettel, Mobifone, VNM

## 3. Cấu trúc dự án

### 3.1. Backend (Python/FastAPI)
- **src/api/v1/endpoints/**: API endpoints (auth.py, call_logs.py, doi_soat.py, etc.)
- **src/models/**: Database models (dsc.py, dscd.py, dst_1800_1900.py, user.py, etc.)
- **src/schemas/**: Pydantic schemas cho API validation
- **src/utils/**: Processors và utilities (dsc_processor.py, dscd_processor.py, ckn_processor.py, doi_soat_classifier.py, etc.)
- **src/database/**: Database configs và migrations

### 3.2. Frontend (TypeScript/React)
- **src/components/**: UI components theo nhóm chức năng (auth/, doi-soat/, etc.)
- **src/pages/**: Các trang chính (reconciliations/, templates/, etc.)
- **src/services/**: API services
- **src/hooks/**: Custom React hooks
- **src/types/**: TypeScript interfaces và types

## 4. Database Models

### 4.1. Đối Soát Cước (DSC)
- **DoiSoatCuoc**: Thông tin chung về đối soát (thang_doi_soat, tu_mang, den_doi_tac)
- **DauSoDichVuCuoc**: Chi tiết đầu số với thông tin các nhà mạng (VNM, Viettel, VNPT, VMS)
- **TongKetCuoc**: Tổng kết cước phí (cong_tien_dich_vu, tien_thue_gtgt, tong_cong_tien)

### 4.2. Đối Soát Cố Định (DSCD)
- **DoiSoatCoDinh**: Thông tin chung về đối soát cố định
- **DauSoDichVu**: Chi tiết đầu số dịch vụ
- **CuocGoi**: Thông tin cước gọi theo loại (co_dinh_noi_hat, co_dinh_lien_tinh, di_dong, etc.)
- **CuocThueBao**: Thông tin cước thuê bao
- **TongKet**: Tổng kết chi phí

### 4.3. DST 1800/1900 Models
- **DST1800_1900**: Thông tin chung về đối soát 1800/1900
- **ChiTietDauSo**: Chi tiết đầu số 1800/1900

### 4.4. CKN (Cước Kết Nối) Models
- **CuocKetNoi**: Thông tin về cước kết nối giữa các nhà mạng
- **ChiTietCuocKetNoi**: Chi tiết cước kết nối theo từng nhóm dịch vụ

## 5. Processors (Module Tính Toán Đối Soát)

Các "processors" là những module chứa logic nghiệp vụ cốt lõi để tính toán và tổng hợp dữ liệu cho từng loại hình đối soát, dựa trên dữ liệu gốc từ Call Logs và các quy tắc định nghĩa trong Mẫu Đối Soát (Template). Chúng *không* trực tiếp xử lý file dữ liệu được upload trong luồng đối soát chính.

### 5.1. DSC Processor
- **Chức năng**: Tính toán và tổng hợp dữ liệu cho Đối Soát Cước (DSC) từ Call Logs theo Template DSC.
- **Luồng xử lý (ví dụ)**: `trigger_dsc_calculation` (input: template_id, kỳ đối soát) → `fetch_call_logs` → `apply_dsc_pricing_rules` → `aggregate_dsc_data` → `format_dsc_output`.
- **Keywords**: Các keywords (SAN_LUONG_KEYWORDS, THANH_TIEN_KEYWORDS) có thể vẫn liên quan đến việc *định nghĩa cấu trúc* trong Template, nhưng không dùng để đọc cột từ file Excel trong luồng chính.

### 5.2. DSCD Processor
- **Chức năng**: Tính toán và tổng hợp dữ liệu cho Đối Soát Cố Định (DSCD) từ Call Logs theo Template DSCD.
- **Luồng xử lý (ví dụ)**: `trigger_dscd_calculation` → `fetch_call_logs` → `classify_call_types` (cố định, di động, 1900, quốc tế) → `apply_dscd_pricing_rules` → `aggregate_dscd_data`.
- **Phân loại cước**: Logic phân loại cước hoạt động trên dữ liệu Call Logs lấy từ DB.

### 5.3. DST 1800/1900 Processor
- **Chức năng**: Tính toán và tổng hợp dữ liệu cho Đối Soát Tổng 1800/1900 từ Call Logs theo Template DST.
- **Luồng xử lý (ví dụ)**: `trigger_dst_calculation` → `fetch_call_logs` → `apply_dst_pricing_rules` → `aggregate_dst_data`.
- **Đặc điểm**: Logic cần linh hoạt để xử lý các quy tắc khác nhau được định nghĩa trong Template DST.

### 5.4. CKN Processor
- **Chức năng**: Tính toán và tổng hợp dữ liệu cho Đối Soát Cước Kết Nối (CKN) từ Call Logs theo Template CKN.
- **Luồng xử lý (ví dụ)**: `trigger_ckn_calculation` → `fetch_call_logs` → `apply_ckn_rules` → `aggregate_ckn_data`.
- **Các pattern nhận dạng**: Các pattern này có thể liên quan đến việc *định nghĩa Template* hoặc nhận dạng loại Template, không phải file dữ liệu.

### 5.5. DoiSoatClassifier
- **Chức năng**: Tự động phân loại **loại hình đối soát** dựa trên thông tin đầu vào, chủ yếu phục vụ cho **Luồng Quản lý Mẫu Đối Soát (Template Management)**. Nó giúp xác định loại Template (DSC, DSCD, DST, CKN) khi người dùng tạo hoặc upload một Template mới, dựa trên tên file mẫu, nội dung mẫu, hoặc các cấu hình do người dùng cung cấp.
- **Sử dụng trong luồng chính**: Ít có vai trò trực tiếp trong luồng tạo đối soát chính, vì loại hình đối soát đã được xác định bởi Template được chọn.
- **Phương thức chính**: `classify_template()` - trả về loại hình đối soát của Template.
- **Kỹ thuật phân loại**: Dựa trên tên file mẫu, nội dung mẫu (nếu có upload), và các pattern đặc trưng được định nghĩa cho từng loại hình.

### 5.6. FileProcessor (Reconciliation Coordinator)
- **Chức năng**: Đổi tên thành **Reconciliation Coordinator** hoặc tương tự để phản ánh đúng vai trò. Quản lý và điều phối quá trình **tạo bản ghi đối soát** mới, *không* phải xử lý file upload trong luồng chính.
- **Phương thức chính**:
    - `create_reconciliation_async()` (thay thế `process_file_async`): Nhận yêu cầu tạo đối soát (bao gồm `template_id`, `month`, `year`), xác định loại hình đối soát từ template, truy vấn call logs, kích hoạt module tính toán (processor) phù hợp trong background (sử dụng `BackgroundTasks`), và lưu kết quả vào DB.
    - `save_upload_file()`: Phương thức này chủ yếu liên quan đến **Luồng Quản lý Mẫu Đối Soát**, dùng để lưu file *mẫu* do người dùng upload.
- **Xử lý background**: Sử dụng `BackgroundTasks` của FastAPI để thực hiện việc truy vấn call logs và tính toán đối soát không đồng bộ.


### 5.7. Tiện ích hỗ trợ
- **enhanced_phone_utils**: Cung cấp các hàm xử lý số điện thoại như chuẩn hóa số, xác định nhà mạng, v.v.
- **partner_utils**: Hỗ trợ quản lý thông tin đối tác, mapping đối tác từ tên file và dữ liệu
- **db_helpers**: Hỗ trợ các thao tác với database như bulk insert, search, v.v.

## 6. Quy tắc code

### 6.1. Backend
- **Naming**: snake_case cho file, function, biến; PascalCase cho class
- **Type hints**: Bắt buộc cho tất cả function public
- **Docstrings**: Google style docstrings
- **Error handling**: Sử dụng try/except với logging chi tiết

### 6.2. Frontend
- **Naming**: PascalCase cho component; camelCase cho function, biến; kebab-case cho thư mục
- **TypeScript**: Interface cho props component và response API
- **Component structure**: Functional components với hooks
- **Styling**: Tailwind CSS + Shadcn UI

## 7. API Endpoints

### 7.1. Auth & Users
- **/api/v1/auth/login**: Đăng nhập
- **/api/v1/users/**: Quản lý người dùng

### 7.2. Đối Soát & Templates
- **/api/v1/doi-soat/**: Quản lý đối soát
- **/api/v1/templates/**: Quản lý templates

### 7.3. Partners & Pricing
- **/api/v1/partners/**: Quản lý đối tác
- **/api/v1/pricing/**: Quản lý pricing rules

### 7.4. Call Logs & Statistics
- **/api/v1/call-logs/**: Quản lý logs cuộc gọi
- **/api/v1/statistics/**: Thống kê và báo cáo

## 8. Workflows

### 8.1. Luồng Tạo Đối Soát Chính (Main Reconciliation Workflow)

Đây là luồng cốt lõi để tạo ra một bản ghi đối soát cho một kỳ cụ thể, dựa trên một Mẫu Đối Soát đã được định nghĩa trước và dữ liệu Call Logs đã có trong hệ thống. **Luồng này không bao gồm việc upload file dữ liệu đối soát thực tế.**

1.  **Khởi tạo (Initiation):** Người dùng chọn tạo một kỳ đối soát mới từ giao diện, chỉ định **Mẫu Đối Soát** (Template) muốn sử dụng và **Kỳ Đối Soát** (tháng/năm). Yêu cầu được gửi đến API (ví dụ: `POST /api/v1/doi-soat/`).
2.  **Xác định Thông tin (Information Retrieval):** Backend nhận `template_id` và `kỳ đối soát`. Hệ thống lấy thông tin chi tiết của Template từ DB (bao gồm loại hình đối soát - DSC/DSCD/etc., đối tác, quy tắc tính cước liên quan, cấu trúc dữ liệu cần tổng hợp).
3.  **Truy vấn Dữ liệu Gốc (Call Log Fetching):** Dựa trên thông tin từ Template và Kỳ Đối Soát, hệ thống thực hiện truy vấn vào bảng `call_logs` trong DB để lấy ra tất cả các bản ghi cuộc gọi phù hợp (đúng đối tác, đúng khoảng thời gian, đúng loại dịch vụ nếu có).
4.  **Tính toán & Tổng hợp (Calculation & Aggregation):** Dữ liệu Call Logs được chuyển đến module tính toán (Processor) tương ứng với loại hình đối soát của Template. Processor này áp dụng các quy tắc nghiệp vụ (pricing, block tính cước, phân loại cuộc gọi) và tổng hợp dữ liệu theo cấu trúc yêu cầu (ví dụ: tổng sản lượng, tổng tiền theo từng dịch vụ/hướng gọi).
5.  **Lưu trữ Kết quả (Persistence):** Kết quả tính toán cuối cùng được định dạng thành một bản ghi đối soát hoàn chỉnh (ví dụ: object `DoiSoatCuoc`, `DoiSoatCoDinh`) và được lưu vào bảng dữ liệu tương ứng trong DB. Bản ghi này liên kết với Template và Kỳ Đối Soát đã sử dụng.
6.  **Phản hồi (Response):** API trả về thông báo thành công và thông tin về bản ghi đối soát vừa được tạo (ví dụ: ID). Quá trình tính toán ở bước 4 và 5 có thể diễn ra bất đồng bộ (background task).


### 8.2. Pricing
1. Phân biệt revenue pricing (thu) và cost pricing (chi)
2. Apply billing method (block_6s, etc.)
3. Calculate total based on volume ranges

### 8.3. Template Management
1.  Upload template → **Classify template type (using DoiSoatClassifier)** → store template definition (metadata, rules, potentially the example file).
2.  Edit template → apply rules → test (optional: test template against sample call logs).
3.  Use template **as input for the Main Reconciliation Workflow**. 

## 9. Testing

### 9.1. Unit Testing
- **Test processors**: Kiểm tra chức năng của từng processor riêng biệt
- **Test classifiers**: Kiểm tra khả năng phân loại file
- **Test models**: Kiểm tra logic của các models

### 9.2. Integration Testing
- **Folders**: Kiểm tra xử lý cả thư mục chứa nhiều file
- **Specific files**: Kiểm tra các file có định dạng đặc biệt
- **Scenarios**: Kiểm tra các tình huống thực tế

### 9.3. Test Organization
- **Test files**: Mỗi module có file test riêng (test_dsc_processor.py, test_dscd_processor.py, etc.)
- **Test data**: Sử dụng test data đại diện cho các trường hợp khác nhau
- **Test classification**: Kiểm tra khả năng phân loại giữa các loại file khác nhau

## 10. Deployment

### 10.1. Docker Environment
- **docker-compose.local.yml**: Cấu hình cho môi trường phát triển
- **docker-compose.prod.yml**: Cấu hình cho môi trường production
- **start-local.sh & start-prod.sh**: Scripts khởi động các môi trường

### 10.2. Database Management
- **migrations**: Quản lý schema bằng Alembic
- **makemigrations-local.sh**: Script tạo migration trên môi trường local

### 10.3. Environment Configuration
- **.env files**: Quản lý cấu hình theo môi trường (.env, .env.production, .env.local)
- **.env.db**: Cấu hình riêng cho database

## 11. Contributing to the Project
- **Đọc và hiểu toàn bộ dự án**: Trước khi thực hiện bất kỳ thay đổi hoặc thêm tính năng mới, cần phải đọc và hiểu toàn bộ codebase
- **Hiểu business logic**: Nắm vững các khái niệm và quy trình đối soát cước viễn thông
- **Tuân thủ quy ước**: Đảm bảo tuân thủ các quy ước và mẫu thiết kế đã được thiết lập
- **Kiểm tra kỹ lưỡng**: Kiểm tra các thay đổi để đảm bảo không ảnh hưởng đến các tính năng hiện có
- **Giải thích thay đổi**: Ghi chú rõ ràng về các thay đổi và cách chúng hoạt động
