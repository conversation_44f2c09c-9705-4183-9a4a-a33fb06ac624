---
description: 
globs: **/*.tsx,**/*.ts,**/*.css
alwaysApply: false
---
 ---
description: Quy tắc chi tiết về frontend trong AuditCall
globs: ["**/*.tsx", "**/*.ts", "**/*.css"]
alwaysApply: false
---

# AuditCall Frontend Rules

## 1. Tổng quan về Frontend

Frontend của AuditCall được xây dựng trên React với TypeScript, sử dụng Vite làm build tool và Tailwind CSS kết hợp với Shadcn UI để styling. Frontend cung cấp giao diện quản lý cho người dùng để upload, xử lý và phân tích dữ liệu đối soát cước viễn thông.

## 2. Cấu trúc thư mục

```
frontend/
  ├── public/               # Static assets
  ├── src/
  │   ├── components/       # Reusable UI components
  │   │   ├── ui/           # Basic UI components (Shadcn UI)
  │   │   ├── auth/         # Authentication components
  │   │   ├── layout/       # Layout components
  │   │   ├── doi-soat/     # Reconciliation components
  │   │   ├── partners/     # Partner management components
  │   │   └── ...
  │   ├── pages/            # Page components
  │   │   ├── auth/         # Auth pages
  │   │   ├── dashboard/    # Dashboard pages
  │   │   ├── reconciliations/ # Reconciliation pages
  │   │   └── ...
  │   ├── hooks/            # Custom React hooks
  │   ├── services/         # API services
  │   ├── contexts/         # React contexts
  │   ├── types/            # TypeScript type definitions
  │   ├── lib/              # Utility functions
  │   ├── App.tsx           # Main App component
  │   └── main.tsx          # Entry point
  ├── index.html            # HTML template
  ├── tailwind.config.js    # Tailwind configuration
  ├── tsconfig.json         # TypeScript configuration
  └── vite.config.ts        # Vite configuration
```

## 3. Quy tắc chung cho Components

### 3.1. Cấu trúc Component

```tsx
import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { SomeIcon } from 'lucide-react';
import { someApiService } from '@/services/some-api-service';
import type { SomeDataType } from '@/types/some-types';

interface ComponentProps {
  initialData?: SomeDataType;
  onSubmit: (data: SomeDataType) => void;
  isLoading?: boolean;
}

export function SomeComponent({ 
  initialData, 
  onSubmit, 
  isLoading = false 
}: ComponentProps) {
  const [data, setData] = useState<SomeDataType | undefined>(initialData);
  const { toast } = useToast();
  
  useEffect(() => {
    // Effect logic here
  }, []);
  
  const handleClick = () => {
    try {
      // Handle click logic
      onSubmit(data!);
      toast({
        title: "Success",
        description: "Operation completed successfully"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Operation failed",
        variant: "destructive"
      });
    }
  };
  
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Component Title</h2>
      {/* Component content */}
      <Button 
        onClick={handleClick} 
        disabled={isLoading}
      >
        <SomeIcon className="mr-2 h-4 w-4" />
        Submit
      </Button>
    </div>
  );
}
```

### 3.2. Naming Conventions

1. **Components**: PascalCase
   ```tsx
   export function UserProfile() { ... }
   export function DoiSoatUploader() { ... }
   ```

2. **Files**: 
   - Component files: PascalCase
   - Utility files: kebab-case
   - Types and hooks: camelCase

3. **Directories**: kebab-case
   ```
   components/
     ├── auth/
     ├── doi-soat/
     └── call-logs/
   ```

4. **Variables/Functions**: camelCase
   ```tsx
   const handleSubmit = () => { ... }
   const [isLoading, setIsLoading] = useState(false);
   ```

### 3.3. Import Order

1. React & frameworks imports
2. Third-party libraries
3. Components
4. Hooks
5. Services
6. Utils
7. Types
8. Assets & styles

```tsx
// 1. React & frameworks
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// 2. Third-party libraries
import { format } from 'date-fns';
import { Upload } from 'lucide-react';

// 3. Components
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

// 4. Hooks
import { useAuth } from '@/hooks/useAuth';

// 5. Services
import { doiSoatService } from '@/services/doi-soat-service';

// 6. Utils
import { formatCurrency } from '@/lib/utils';

// 7. Types
import type { DoiSoatData } from '@/types/doi-soat';

// 8. Assets & styles
import './some-styles.css';
```

## 4. UI Components

### 4.1. Shadcn UI Components

Sử dụng các components từ Shadcn UI cho giao diện nhất quán:

- **Button**: Sử dụng cho tất cả các nút trong ứng dụng
- **Card**: Sử dụng cho containers
- **Dialog**: Sử dụng cho modals và popups
- **Form elements**: Input, Select, Checkbox, etc.
- **Toast**: Sử dụng cho notifications
- **Table**: Sử dụng cho hiển thị dữ liệu dạng bảng

### 4.2. Layout Components

```tsx
// Layout.tsx
export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <Sidebar />
      <main className="flex-1 p-4 md:p-6 lg:p-8">
        {children}
      </main>
      <Footer />
    </div>
  );
}

// ProtectedLayout.tsx
export function ProtectedLayout({ 
  children,
  requireAdmin = false
}: { 
  children: React.ReactNode;
  requireAdmin?: boolean;
}) {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();
  
  useEffect(() => {
    if (!isLoading && !user) {
      navigate('/login');
    }
    
    if (!isLoading && requireAdmin && !user?.isAdmin) {
      navigate('/dashboard');
    }
  }, [user, isLoading, navigate, requireAdmin]);
  
  if (isLoading) {
    return <LoadingSpinner />;
  }
  
  return <Layout>{children}</Layout>;
}
```

## 5. State Management

### 5.1. Local State

Sử dụng React hooks cho local state:

```tsx
function Counter() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <p>Count: {count}</p>
      <Button onClick={() => setCount(count + 1)}>Increment</Button>
    </div>
  );
}
```

### 5.2. Context API

Sử dụng Context API cho shared state:

```tsx
// AuthContext.tsx
import { createContext, useContext, useState, useEffect } from 'react';
import { authService } from '@/services/auth-service';

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    // Check if user is already logged in
    const checkAuth = async () => {
      try {
        const userData = await authService.getCurrentUser();
        setUser(userData);
      } catch (error) {
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkAuth();
  }, []);
  
  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const userData = await authService.login(email, password);
      setUser(userData);
    } finally {
      setIsLoading(false);
    }
  };
  
  const logout = () => {
    authService.logout();
    setUser(null);
  };
  
  return (
    <AuthContext.Provider value={{ user, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
```

### 5.3. React Query

Sử dụng React Query để quản lý server state:

```tsx
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { doiSoatService } from '@/services/doi-soat-service';

// Fetch data
function useDoiSoatList() {
  return useQuery({
    queryKey: ['doi-soat'],
    queryFn: () => doiSoatService.getList(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Mutate data
function useCreateDoiSoat() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateDoiSoatRequest) => doiSoatService.create(data),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['doi-soat'] });
    },
  });
}
```

## 6. API Integration

### 6.1. Service Structure

```tsx
// doi-soat-service.ts
import { API_BASE_URL } from '@/config';

export interface DoiSoatResponse {
  id: string;
  filename: string;
  status: string;
  // ...other fields
}

export interface CreateDoiSoatRequest {
  file: File;
  type?: string;
}

async function getList(): Promise<DoiSoatResponse[]> {
  const response = await fetch(`${API_BASE_URL}/doi-soat`);
  if (!response.ok) {
    throw new Error('Failed to fetch doi soat list');
  }
  return response.json();
}

async function getById(id: string): Promise<DoiSoatResponse> {
  const response = await fetch(`${API_BASE_URL}/doi-soat/${id}`);
  if (!response.ok) {
    throw new Error(`Failed to fetch doi soat with id ${id}`);
  }
  return response.json();
}

async function create(data: CreateDoiSoatRequest): Promise<DoiSoatResponse> {
  const formData = new FormData();
  formData.append('file', data.file);
  if (data.type) {
    formData.append('manual_type', data.type);
  }
  
  const response = await fetch(`${API_BASE_URL}/doi-soat/upload`, {
    method: 'POST',
    body: formData,
    credentials: 'include',
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.detail || 'Failed to upload file');
  }
  
  return response.json();
}

export const doiSoatService = {
  getList,
  getById,
  create,
};
```

### 6.2. API Error Handling

```tsx
async function fetchData() {
  try {
    setLoading(true);
    const data = await someApiService.getData();
    return data;
  } catch (error) {
    if (error instanceof Response) {
      // Handle HTTP errors
      if (error.status === 401) {
        // Handle unauthorized
      } else if (error.status === 404) {
        // Handle not found
      } else {
        // Handle other HTTP errors
      }
    } else {
      // Handle network or other errors
      console.error('Failed to fetch data:', error);
    }
    
    throw error;
  } finally {
    setLoading(false);
  }
}
```

## 7. Form Handling

### 7.1. Form Structure with React Hook Form

```tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

// Define schema
const formSchema = z.object({
  username: z.string().min(3, {
    message: 'Username must be at least 3 characters.',
  }),
  password: z.string().min(6, {
    message: 'Password must be at least 6 characters.',
  }),
});

export function LoginForm() {
  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });
  
  function onSubmit(values: z.infer<typeof formSchema>) {
    // Handle form submission
    console.log(values);
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Username</FormLabel>
              <FormControl>
                <Input placeholder="Enter username" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input type="password" placeholder="Enter password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <Button type="submit">Login</Button>
      </form>
    </Form>
  );
}
```

## 8. Routing

### 8.1. Route Structure

```tsx
// App.tsx
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { ProtectedLayout } from '@/components/layout/protected-layout';
import LoginPage from '@/pages/auth/login-page';
import DashboardPage from '@/pages/dashboard-page';
import ReconciliationsPage from '@/pages/reconciliations';
import TemplateDetailPage from '@/pages/reconciliations/[id]';

export default function App() {
  return (
    <BrowserRouter>
      <Routes>
        {/* Public routes */}
        <Route path="/login" element={<LoginPage />} />
        
        {/* Protected routes */}
        <Route element={<ProtectedLayout />}>
          <Route path="/dashboard" element={<DashboardPage />} />
          <Route path="/reconciliations" element={<ReconciliationsPage />} />
          <Route path="/reconciliations/:id" element={<TemplateDetailPage />} />
        </Route>
        
        {/* Admin routes */}
        <Route element={<ProtectedLayout requireAdmin />}>
          <Route path="/partners" element={<PartnersPage />} />
          <Route path="/pricing" element={<PricingPage />} />
        </Route>
        
        {/* Default redirect */}
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </BrowserRouter>
  );
}
```

## 9. Styling

### 9.1. Tailwind CSS

```tsx
// Sử dụng Tailwind để styling
<div className="
  flex 
  flex-col 
  space-y-4 
  p-4 
  bg-white 
  rounded-lg 
  shadow-md 
  dark:bg-gray-800
">
  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
    Dashboard
  </h2>
  <p className="text-gray-500 dark:text-gray-400">
    Welcome to your dashboard
  </p>
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    {/* Content */}
  </div>
</div>
```

### 9.2. Component Variants with Tailwind

```tsx
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const cardVariants = cva(
  "rounded-lg p-4 shadow-sm", // Base styles
  {
    variants: {
      variant: {
        default: "bg-white dark:bg-gray-800",
        primary: "bg-primary-50 dark:bg-primary-900",
        success: "bg-success-50 dark:bg-success-900",
        warning: "bg-warning-50 dark:bg-warning-900",
        danger: "bg-danger-50 dark:bg-danger-900",
      },
      size: {
        sm: "p-2",
        default: "p-4",
        lg: "p-6",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

interface CardProps extends VariantProps<typeof cardVariants> {
  className?: string;
  children: React.ReactNode;
}

export function Card({ 
  variant, 
  size, 
  className, 
  children 
}: CardProps) {
  return (
    <div className={cn(cardVariants({ variant, size }), className)}>
      {children}
    </div>
  );
}
```

## 10. Best Practices

1. **Code Splitting**: Sử dụng React.lazy() và Suspense để lazy load components

   ```tsx
   const SomeComponent = React.lazy(() => import('@/components/some-component'));
   
   function App() {
     return (
       <Suspense fallback={<div>Loading...</div>}>
         <SomeComponent />
       </Suspense>
     );
   }
   ```

2. **Error Boundaries**: Bao components trong ErrorBoundary để xử lý lỗi

   ```tsx
   import { ErrorBoundary } from '@/components/error-boundary';
   
   function App() {
     return (
       <ErrorBoundary fallback={<div>Something went wrong</div>}>
         <SomeComponent />
       </ErrorBoundary>
     );
   }
   ```

3. **Accessibility**: Đảm bảo tất cả components đều accessible

   - Sử dụng semantic HTML
   - Thêm aria attributes khi cần
   - Đảm bảo contrast đủ
   - Kiểm tra keyboard navigation

4. **Performance**:

   - Sử dụng React.memo() cho components render nhiều
   - Sử dụng useCallback() và useMemo() để tối ưu
   - Tránh re-renders không cần thiết
   - Lazy load images và components

5. **Testing**:

   - Viết tests cho components chức năng quan trọng
   - Sử dụng Testing Library để test user interactions
   - Mock API calls trong tests