---
description: 
globs: 
alwaysApply: true
---
 ---
description: <PERSON><PERSON> tắc chi tiết về deployment trong AuditCall
globs: ["**/docker-compose*.yml", "**/Dockerfile*", "**/*.sh"]
alwaysApply: false
---

# AuditCall Deployment Rules

## 1. Tổng quan về Deployment

AuditCall được triển khai sử dụng Docker và Docker Compose, với các môi trường tách biệt cho phát triển (local), staging và production. Hệ thống bao gồm các container cho backend (FastAPI), frontend (React), database (PostgreSQL), và các dịch vụ phụ trợ.

## 2. Cấu trúc Deployment

```
AuditCall/
  ├── docker-compose.local.yml   # Cấu hình cho môi trường local
  ├── docker-compose.prod.yml    # Cấu hình cho môi trường production
  ├── backend/
  │   ├── Dockerfile             # Dockerfile cho backend
  │   ├── Dockerfile.dev         # Dockerfile cho phát triển
  │   └── start.sh               # Script khởi động backend
  ├── frontend/
  │   ├── Dockerfile             # Dockerfile cho frontend
  │   ├── Dockerfile.prod        # Dockerfile cho production
  │   └── nginx.conf             # Cấu hình Nginx
  ├── docker/
  │   ├── postgres/              # Cấu hình PostgreSQL
  │   └── nginx/                 # Cấu hình Nginx
  ├── .env.example               # Template cho biến môi trường
  ├── .env                       # Biến môi trường local
  ├── .env.prod                  # Biến môi trường production
  ├── start-local.sh             # Script khởi động môi trường local
  └── start-prod.sh              # Script khởi động môi trường production
```

## 3. Docker Configuration

### 3.1. Backend Dockerfile

```dockerfile
# Dockerfile cho backend
FROM python:3.11-slim

WORKDIR /app

# Cài đặt dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY . .

# Thiết lập entrypoint
COPY start.sh /start.sh
RUN chmod +x /start.sh

EXPOSE 8000

CMD ["/start.sh"]
```

### 3.2. Frontend Dockerfile

```dockerfile
# Dockerfile.prod cho frontend
FROM node:18-alpine AS builder

WORKDIR /app

# Cài đặt dependencies
COPY package.json package-lock.json ./
RUN npm ci

# Build ứng dụng
COPY . .
RUN npm run build

# Stage 2: Nginx
FROM nginx:alpine

# Copy build files
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy cấu hình Nginx
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 3.3. Docker Compose Configuration

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: always
    env_file:
      - .env.prod
    depends_on:
      - db
    volumes:
      - ./backend/uploads:/app/uploads
    networks:
      - app-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    restart: always
    depends_on:
      - backend
    networks:
      - app-network

  db:
    image: postgres:15
    restart: always
    env_file:
      - .env.db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - app-network

networks:
  app-network:

volumes:
  postgres_data:
```

## 4. Environment Configuration

### 4.1. Backend Environment Variables

```
# .env.example (Backend)
PROJECT_NAME=AuditCall
SECRET_KEY=your-secret-key
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:80"]

# Database
POSTGRES_SERVER=db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=auditcall

# Upload
UPLOAD_DIR=/app/uploads
```

### 4.2. Database Environment Variables

```
# .env.db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=auditcall
```

## 5. Quy trình Deployment

### 5.1. Quy trình Deployment Local

1. **Setup môi trường**:
   ```bash
   # Clone repository
   git clone https://github.com/your-org/auditcall.git
   cd auditcall
   
   # Tạo file .env từ .env.example
   cp .env.example .env
   
   # Tạo file .env.db
   echo "POSTGRES_USER=postgres" > .env.db
   echo "POSTGRES_PASSWORD=password" >> .env.db
   echo "POSTGRES_DB=auditcall" >> .env.db
   ```

2. **Khởi động ứng dụng**:
   ```bash
   # Sử dụng script start-local.sh
   chmod +x start-local.sh
   ./start-local.sh
   
   # Hoặc sử dụng docker compose trực tiếp
   docker compose -f docker-compose.local.yml up -d
   ```

3. **Kiểm tra ứng dụng**:
   - Backend: http://localhost:8000
   - Frontend: http://localhost:3000
   - API Docs: http://localhost:8000/docs

### 5.2. Quy trình Deployment Production

1. **Setup môi trường**:
   ```bash
   # Clone repository
   git clone https://github.com/your-org/auditcall.git
   cd auditcall
   
   # Tạo các file .env.prod và .env.db
   cp .env.example .env.prod
   # Chỉnh sửa .env.prod với cấu hình production
   
   # Tạo thư mục uploads
   mkdir -p backend/uploads
   ```

2. **Khởi động ứng dụng**:
   ```bash
   # Sử dụng script start-prod.sh
   chmod +x start-prod.sh
   ./start-prod.sh
   
   # Hoặc sử dụng docker compose trực tiếp
   docker compose -f docker-compose.prod.yml up -d --build
   ```

3. **Thiết lập database**:
   ```bash
   # Chạy migrations
   docker compose -f docker-compose.prod.yml exec backend alembic upgrade head
   ```

4. **Kiểm tra ứng dụng**:
   - Backend: https://api.example.com
   - Frontend: https://example.com
   - API Docs: https://api.example.com/docs

## 6. Startup Scripts

### 6.1. Backend Startup Script

```bash
#!/bin/bash
# start.sh

# Run migrations
echo "Running database migrations..."
alembic upgrade head

# Start server
echo "Starting server..."
if [ "$ENVIRONMENT" = "development" ]; then
    uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
else
    uvicorn src.main:app --host 0.0.0.0 --port 8000
fi
```

### 6.2. Local Environment Startup Script

```bash
#!/bin/bash
# start-local.sh

echo "Starting AuditCall in local development mode..."
docker compose -f docker-compose.local.yml up -d
```

### 6.3. Production Environment Startup Script

```bash
#!/bin/bash
# start-prod.sh

echo "Starting AuditCall in production mode..."
docker compose -f docker-compose.prod.yml up -d --build

echo "Running database migrations..."
docker compose -f docker-compose.prod.yml exec backend alembic upgrade head

echo "AuditCall is running in production mode."
```

## 7. Cấu hình Nginx

```nginx
# nginx.conf for frontend
server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 8. Database Migrations

Migrations quản lý bởi Alembic:

```bash
# Tạo migration mới
alembic revision --autogenerate -m "Add new table"

# Áp dụng migrations
alembic upgrade head

# Rollback một migration
alembic downgrade -1

# Kiểm tra trạng thái hiện tại
alembic current
```

## 9. Backup & Restore

### 9.1. Database Backup

```bash
# Backup database
docker compose -f docker-compose.prod.yml exec db pg_dump -U postgres auditcall > backup_$(date +%Y%m%d).sql

# Backup database và nén
docker compose -f docker-compose.prod.yml exec db pg_dump -U postgres auditcall | gzip > backup_$(date +%Y%m%d).sql.gz
```

### 9.2. Database Restore

```bash
# Restore từ backup file
cat backup.sql | docker compose -f docker-compose.prod.yml exec -T db psql -U postgres auditcall

# Restore từ backup file nén
gunzip -c backup.sql.gz | docker compose -f docker-compose.prod.yml exec -T db psql -U postgres auditcall
```

### 9.3. File Backups

```bash
# Backup uploads folder
tar -czvf uploads_backup_$(date +%Y%m%d).tar.gz backend/uploads/

# Restore uploads folder
tar -xzvf uploads_backup_20230101.tar.gz -C ./
```

## 10. Monitoring & Logging

### 10.1. Docker Logs

```bash
# Xem logs của tất cả services
docker compose -f docker-compose.prod.yml logs

# Xem logs của backend service
docker compose -f docker-compose.prod.yml logs backend

# Follow logs của backend service
docker compose -f docker-compose.prod.yml logs -f backend
```

### 10.2. Application Logs

- **Backend Logs**: Lưu trữ trong `/app/logs`
- **Nginx Logs**: Lưu trữ trong `/var/log/nginx`

### 10.3. Container Health Checks

```bash
# Kiểm tra trạng thái các container
docker compose -f docker-compose.prod.yml ps

# Kiểm tra chi tiết container
docker inspect <container_id>
```