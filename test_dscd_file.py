#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import pandas as pd
import re

def extract_phone_numbers(text):
    """Trích xuất số điện thoại từ văn bản"""
    if not text or not isinstance(text, str):
        return []
    
    # Pat<PERSON> cơ bản cho số điện thoại Việt Nam
    patterns = [
        r'\b\d{2,4}[\.\-\s]*\d{3,4}[\.\-\s]*\d{3,4}\b',  # Số điện thoại dạng XX.XXXX.XXXX
        r'\b\d{2,5}[\.\-\s]*\d{2,5}[\.\-\s]*\d{2,4}\b',  # Dãy số có dấu phân cách
        r'\b\d{7,11}\b',  # Số điện thoại liền không có dấu phân cách
    ]
    
    results = []
    for pattern in patterns:
        matches = re.finditer(pattern, text)
        for match in matches:
            results.append(match.group())
    
    # Ph<PERSON>t hiện dải số điện thoại
    range_pattern = r'(\d{2}[\.\-\s]*\d{4}[\.\-\s]*\d{4})[\s\-]*[-–—][\s\-]*(\d{2}[\.\-\s]*\d{4}[\.\-\s]*\d{4})'
    range_matches = re.finditer(range_pattern, text)
    for match in range_matches:
        start_num = match.group(1)
        end_num = match.group(2)
        full_range = f"{start_num} - {end_num}"
        results.append(full_range)
    
    return results

def extract_phone_ranges(text):
    """Trích xuất dải số điện thoại"""
    if not text or not isinstance(text, str):
        return []
    
    # Tìm dạng "số_đầu - số_cuối"
    patterns = [
        r'(\d{2}[\.\-\s]*\d{4}[\.\-\s]*\d{4})[\s\-]*[-–—][\s\-]*(\d{2}[\.\-\s]*\d{4}[\.\-\s]*\d{4})',
        r'(\d{3,4})[\s\-]*[-–—][\s\-]*(\d{3,4})'  # Dạng ngắn như "1300 - 1349"
    ]
    
    results = []
    for pattern in patterns:
        matches = re.finditer(pattern, text)
        for match in matches:
            start_num = match.group(1)
            end_num = match.group(2)
            full_range = f"{start_num} - {end_num}"
            results.append(full_range)
    
    return results

def test_file(file_path):
    """Kiểm tra cấu trúc file DSCD"""
    print(f"Đang kiểm tra file: {file_path}")
    
    try:
        # Đọc file Excel
        df = pd.read_excel(file_path)
        print(f"\nThông tin file Excel:")
        print(f"- Số dòng: {len(df)}")
        print(f"- Số cột: {len(df.columns)}")
        print(f"- Tên các cột: {df.columns.tolist()[:10]}...")
        
        # Kiểm tra dữ liệu
        print("\nDữ liệu mẫu:")
        for i in range(min(5, len(df))):
            row = df.iloc[i]
            print(f"Dòng {i+1}:")
            for j, col in enumerate(df.columns[:6]):
                val = row.iloc[j] if j < len(row) else None
                print(f"  {col}: {val}")
        
        # Tìm số điện thoại trong dữ liệu
        all_phones = []
        all_ranges = []
        
        # Tìm cột có chứa từ khóa "ĐẦU SỐ" hoặc "DỊCH VỤ"
        dau_so_column_idx = None
        for j, col in enumerate(df.columns):
            if isinstance(col, str) and ("ĐẦU SỐ" in col.upper() or "DỊCH VỤ" in col.upper()):
                dau_so_column_idx = j
                print(f"\nĐã tìm thấy cột đầu số: {col} (cột {j+1})")
                break
        
        # Xử lý dữ liệu
        for i in range(len(df)):
            # Nếu tìm thấy cột đầu số, kiểm tra cụ thể cột đó
            if dau_so_column_idx is not None:
                cell_value = df.iloc[i, dau_so_column_idx]
                if isinstance(cell_value, str):
                    # Kiểm tra dải số
                    ranges = extract_phone_ranges(cell_value)
                    if ranges:
                        all_ranges.extend(ranges)
                        print(f"Dòng {i+1}, cột {dau_so_column_idx+1}: Tìm thấy dải số: {ranges}")
                    # Kiểm tra số đơn lẻ
                    phones = extract_phone_numbers(cell_value)
                    if phones:
                        all_phones.extend(phones)
            
            # Kiểm tra toàn bộ các cột khác
            for j in range(len(df.columns)):
                if j == dau_so_column_idx:
                    continue  # Bỏ qua cột đã kiểm tra
                
                cell_value = df.iloc[i, j]
                if isinstance(cell_value, str):
                    ranges = extract_phone_ranges(cell_value)
                    if ranges:
                        all_ranges.extend(ranges)
                        print(f"Dòng {i+1}, cột {j+1}: Tìm thấy dải số: {ranges}")
                    
                    phones = extract_phone_numbers(cell_value)
                    if phones:
                        all_phones.extend(phones)
        
        print(f"\nĐã tìm thấy {len(all_phones)} số điện thoại trong file:")
        for i, phone in enumerate(all_phones[:10]):
            print(f"{i+1}. {phone}")
        
        if len(all_phones) > 10:
            print(f"... và {len(all_phones) - 10} số khác")
        
        print(f"\nĐã tìm thấy {len(all_ranges)} dải số điện thoại:")
        for i, range_str in enumerate(all_ranges):
            print(f"{i+1}. {range_str} (độ dài: {len(range_str)})")
        
        # Kiểm tra vượt quá độ dài cho phép
        long_phones = [p for p in all_phones if len(p) > 20]
        if long_phones:
            print(f"\nCó {len(long_phones)} số điện thoại dài hơn 20 ký tự:")
            for i, phone in enumerate(long_phones[:5]):
                print(f"{i+1}. {phone} (độ dài: {len(phone)})")
        
        long_ranges = [r for r in all_ranges if len(r) > 20]
        if long_ranges:
            print(f"\nCó {len(long_ranges)} dải số điện thoại dài hơn 20 ký tự:")
            for i, range_str in enumerate(long_ranges[:5]):
                print(f"{i+1}. {range_str} (độ dài: {len(range_str)})")
    
    except Exception as e:
        print(f"Lỗi khi đọc file: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Sử dụng: python test_dscd_file.py <đường_dẫn_file>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    test_file(file_path) 