#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import re

# Thêm đường dẫn hiện tại vào sys.path
sys.path.append(os.path.abspath('.'))

def normalize_text(text):
    """
    Chuẩn hóa văn bản để dễ so sánh bằng cách:
    - Chuyển về chữ thường
    - Giữ nguyên hoặc thay thế các ký tự tiếng Việt
    - Loại bỏ tất cả ký tự không phải a-z, 0-9
    """
    # Chuyển về chữ thường
    text = text.lower()
    
    # Thay thế trực tiếp các ký tự tiếng Việt phổ biến
    vietnamese_chars = {
        'á': 'a', 'à': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ắ': 'a', 'ằ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ấ': 'a', 'ầ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'é': 'e', 'è': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ế': 'e', 'ề': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'í': 'i', 'ì': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ó': 'o', 'ò': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ố': 'o', 'ồ': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ớ': 'o', 'ờ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ú': 'u', 'ù': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ứ': 'u', 'ừ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ý': 'y', 'ỳ': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd'  # Quan trọng: Chuyển đổi đ thành d
    }
    
    for vietnamese, latin in vietnamese_chars.items():
        text = text.replace(vietnamese, latin)
    
    # Loại bỏ tất cả ký tự không phải a-z và 0-9
    text = re.sub(r'[^a-z0-9]', '', text)
    return text

def main():
    # Danh sách tên file mẫu, đặc biệt là những file đã gây lỗi trước đây
    test_files = [
        "DS cố định VNTEL_T1_2025.xlsx",
        "ĐS CỐ ĐỊNH HITC T1.2025 (chốt).xlsx",
        "BIDV Hà Tây Tháng 1-2025.xlsx",
        "Doctor-check-Tháng 1-2025.xlsx",
        "Shield VN T1.2025.xlsx"
    ]
    
    # Danh sách các pattern và chuỗi tìm kiếm
    patterns = [
        r'cố\s*định',
        r'co\s*dinh',
        r'c[ố|ó]?\s*[đd]ịnh',
        r'đ[ố|ó]i\s*so[á|a]t\s*c[ố|ó]?\s*[đd]ịnh',
        r'BIDV',
        r'Doctor-check',
        r'Shield',
        r'CD',
        r'DSCD'
    ]
    
    simple_strings = [
        "codinh", "cd", "dscd", "dscodinh", 
        "doisoatcodinh", "cdinh", "bidv", "doctorcheck", "shield"
    ]
    
    print("=== Kiểm tra các file cụ thể với các mẫu regex và chuỗi đơn giản ===\n")
    
    for filename in test_files:
        normalized = normalize_text(filename)
        
        print(f"File: {filename}")
        print(f"Normalized: '{normalized}'")
        
        # Kiểm tra với regex patterns
        print("Kiểm tra với regex patterns:")
        for pattern in patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                print(f"  ✅ Khớp với pattern: '{pattern}', matched: '{match.group(0)}'")
            else:
                print(f"  ❌ Không khớp với pattern: '{pattern}'")
        
        # Kiểm tra với simple strings
        print("Kiểm tra với simple strings:")
        for simple_str in simple_strings:
            simple_normalized = normalize_text(simple_str)
            if simple_normalized in normalized:
                print(f"  ✅ Khớp với chuỗi: '{simple_str}' ('{simple_normalized}')")
            else:
                print(f"  ❌ Không khớp với chuỗi: '{simple_str}' ('{simple_normalized}')")
        
        print()

if __name__ == "__main__":
    main() 